# 🚀 GUIDE DE DÉPLOIEMENT EN PRODUCTION - MINDFLOW PRO

## 📋 Pré-requis

- [ ] Tests manuels validés (voir `GUIDE_TEST_MANUEL_PHASE4.md`)
- [ ] Compte Vercel ou Netlify actif
- [ ] Projet Supabase en production
- [ ] Domaine personnalisé (optionnel)

---

## 🔧 Configuration Production

### 1. Variables d'Environnement Production

Créer `.env.production.local` :
```env
# Supabase Production
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Feature Flags Production
NEXT_PUBLIC_USE_SUPABASE_AUTH=true
NEXT_PUBLIC_USE_SUPABASE_DATABASE=true
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_DUAL_DATABASE_MODE=false

# Sécurité Production
NEXT_PUBLIC_APP_URL=https://mindflowpro.com
```

### 2. Build de Production

```bash
cd frontend
npm run build
npm run start # Test local du build
```

---

## 📦 Déploiement Vercel (Recommandé)

### Étape 1: Installation Vercel CLI
```bash
npm i -g vercel
```

### Étape 2: Déploiement
```bash
cd frontend
vercel --prod
```

### Étape 3: Configuration Vercel
1. Connecter le repo GitHub
2. Ajouter les variables d'environnement dans Vercel Dashboard
3. Configurer le domaine personnalisé

### Étape 4: Auto-déploiement
- Push sur `main` = déploiement automatique
- Preview sur les PR

---

## 🔐 Sécurité Production

### 1. Headers de Sécurité
Ajouter dans `next.config.js` :
```javascript
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];
```

### 2. Rate Limiting Supabase
Dans Supabase Dashboard :
- Authentication > Rate Limits
- Database > RLS Policies
- API > Rate Limiting

---

## 🎯 Checklist Post-Déploiement

### Tests de Production
- [ ] ✅ Site accessible sur le domaine
- [ ] ✅ HTTPS actif
- [ ] ✅ Inscription/Connexion fonctionnelles
- [ ] ✅ Base de données connectée
- [ ] ✅ Temps réel opérationnel

### Monitoring
- [ ] Google Analytics configuré
- [ ] Sentry pour les erreurs
- [ ] Uptime monitoring (UptimeRobot)
- [ ] Logs Vercel/Supabase activés

### Performance
- [ ] Lighthouse score > 90
- [ ] Images optimisées
- [ ] CDN activé
- [ ] Caching configuré

---

## 📊 Monitoring Production

### 1. Tableau de Bord Supabase
- Surveiller : Requêtes/min, Stockage, Bandwidth
- Alertes : Configurer pour > 80% utilisation

### 2. Vercel Analytics
- Core Web Vitals
- Temps de réponse
- Erreurs 4xx/5xx

### 3. Logs Centralisés
```javascript
// frontend/src/lib/monitoring.ts
export const logError = (error: Error, context?: any) => {
  if (process.env.NODE_ENV === 'production') {
    // Envoyer à Sentry ou autre service
    console.error('Production Error:', error, context);
  }
};
```

---

## 🔄 Stratégie de Mise à Jour

### 1. Environnements
- **Development:** localhost
- **Staging:** staging.mindflowpro.com
- **Production:** mindflowpro.com

### 2. Process de Déploiement
1. Développer sur `feature/*` branches
2. PR vers `develop`
3. Test sur staging
4. Merge vers `main`
5. Déploiement auto production

### 3. Rollback
```bash
# Si problème en production
vercel rollback
# ou
git revert <commit>
git push origin main
```

---

## 📱 Configuration Mobile

### PWA Support
Ajouter dans `public/manifest.json` :
```json
{
  "name": "MindFlow Pro",
  "short_name": "MindFlow",
  "theme_color": "#0070f3",
  "background_color": "#ffffff",
  "display": "standalone",
  "orientation": "portrait",
  "scope": "/",
  "start_url": "/"
}
```

---

## 🎉 Lancement

### Communication
1. Email aux beta testeurs
2. Post sur les réseaux sociaux
3. ProductHunt (optionnel)

### Support
- Chat support (Crisp/Intercom)
- FAQ page
- Documentation utilisateur

---

## 📈 KPIs à Suivre

- Nouveaux utilisateurs/jour
- Taux de rétention 7/30 jours
- Entrées mood/journal créées
- Temps moyen de session
- Taux de conversion gratuit → premium

---

**🚀 Félicitations !** MindFlow Pro est maintenant en production !

Pour toute question : <EMAIL> 