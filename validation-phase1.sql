
-- Script de validation migration Phase 1
SELECT 'VALIDATION MIGRATION PHASE 1' as status;

-- Vérifier les tables créées
SELECT schemaname, tablename 
FROM pg_tables 
WHERE tablename IN ('journal_entries', 'ai_coaching_sessions', 'mood_analytics', 'smart_notifications');

-- Compter les enregistrements
SELECT 'journal_entries' as table_name, COUNT(*) as records FROM journal_entries
UNION ALL SELECT 'ai_coaching_sessions', COUNT(*) FROM ai_coaching_sessions  
UNION ALL SELECT 'mood_analytics', COUNT(*) FROM mood_analytics
UNION ALL SELECT 'smart_notifications', COUNT(*) FROM smart_notifications;

-- Vérifier les index
SELECT indexname, tablename FROM pg_indexes 
WHERE tablename IN ('journal_entries', 'ai_coaching_sessions', 'mood_analytics', 'smart_notifications');
