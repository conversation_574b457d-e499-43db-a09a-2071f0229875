#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 TESTS AUTOMATISÉS - MIGRATION SUPABASE');
console.log('==========================================\n');

class MigrationTester {
  constructor() {
    this.results = {
      env: { status: 'pending', details: [] },
      build: { status: 'pending', details: [] },
      pages: { status: 'pending', details: [] },
      supabase: { status: 'pending', details: [] }
    };
  }

  async runTest(name, testFunction) {
    console.log(`🔄 Test: ${name}...`);
    try {
      const result = await testFunction();
      console.log(`✅ ${name}: RÉUSSI`);
      return { status: 'success', ...result };
    } catch (error) {
      console.log(`❌ ${name}: ÉCHEC - ${error.message}`);
      return { status: 'error', error: error.message };
    }
  }

  async testEnvironmentVariables() {
    const envPath = path.join(__dirname, 'frontend', '.env.local');
    
    if (!fs.existsSync(envPath)) {
      throw new Error('Fichier .env.local manquant');
    }

    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'NEXT_PUBLIC_DUAL_DATABASE_MODE'
    ];

    const missingVars = requiredVars.filter(varName => 
      !envContent.includes(varName)
    );

    if (missingVars.length > 0) {
      throw new Error(`Variables manquantes: ${missingVars.join(', ')}`);
    }

    return {
      configuredVars: requiredVars.length - missingVars.length,
      totalVars: requiredVars.length,
      fileSize: fs.statSync(envPath).size
    };
  }

  async testBuildProcess() {
    return new Promise((resolve, reject) => {
      const buildProcess = spawn('npm', ['run', 'build'], {
        cwd: path.join(__dirname, 'frontend'),
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      buildProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      buildProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      buildProcess.on('close', (code) => {
        if (code === 0) {
          resolve({
            buildTime: this.extractBuildTime(output),
            warnings: this.extractWarnings(output),
            success: true
          });
        } else {
          reject(new Error(`Build failed with code ${code}: ${errorOutput}`));
        }
      });

      // Timeout après 2 minutes
      setTimeout(() => {
        buildProcess.kill();
        reject(new Error('Build timeout (2 minutes)'));
      }, 120000);
    });
  }

  async testPageAccessibility() {
    const testPages = [
      'http://localhost:3000/diagnostic-env',
      'http://localhost:3000/test-simple'
    ];

    const results = [];

    for (const url of testPages) {
      try {
        const response = await this.fetchWithTimeout(url, 10000);
        results.push({
          url,
          status: response.status,
          accessible: response.status === 200
        });
      } catch (error) {
        results.push({
          url,
          status: 'error',
          accessible: false,
          error: error.message
        });
      }
    }

    const successfulPages = results.filter(r => r.accessible).length;
    if (successfulPages === 0) {
      throw new Error('Aucune page de test accessible');
    }

    return {
      totalPages: testPages.length,
      successfulPages,
      details: results
    };
  }

  async testSupabaseConnection() {
    // Test via une requête à la page de diagnostic
    try {
      const response = await this.fetchWithTimeout('http://localhost:3000/api/health', 5000);
      return {
        apiHealthy: response.status === 200,
        responseTime: Date.now() - this.startTime
      };
    } catch (error) {
      // Si l'API n'existe pas, on teste via la page de diagnostic
      try {
        const response = await this.fetchWithTimeout('http://localhost:3000/diagnostic-env', 10000);
        return {
          pageAccessible: response.status === 200,
          fallbackTest: true
        };
      } catch (pageError) {
        throw new Error(`Impossible de tester Supabase: ${pageError.message}`);
      }
    }
  }

  async fetchWithTimeout(url, timeout) {
    const controller = new AbortController();
    const id = setTimeout(() => controller.abort(), timeout);

    try {
      this.startTime = Date.now();
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'User-Agent': 'MindFlow-Migration-Tester/1.0'
        }
      });
      clearTimeout(id);
      return response;
    } catch (error) {
      clearTimeout(id);
      throw error;
    }
  }

  extractBuildTime(output) {
    const match = output.match(/Compiled successfully in ([\d.]+)s/);
    return match ? parseFloat(match[1]) : null;
  }

  extractWarnings(output) {
    const warningLines = output.split('\n').filter(line => 
      line.includes('warning') || line.includes('Warning')
    );
    return warningLines.length;
  }

  async runAllTests() {
    console.log('🚀 Démarrage des tests automatisés...\n');

    // Test 1: Variables d'environnement
    this.results.env = await this.runTest(
      'Variables d\'environnement',
      () => this.testEnvironmentVariables()
    );

    // Test 2: Process de build
    this.results.build = await this.runTest(
      'Process de build Next.js',
      () => this.testBuildProcess()
    );

    // Test 3: Accessibilité des pages
    this.results.pages = await this.runTest(
      'Accessibilité des pages de test',
      () => this.testPageAccessibility()
    );

    // Test 4: Connexion Supabase
    this.results.supabase = await this.runTest(
      'Connexion Supabase',
      () => this.testSupabaseConnection()
    );

    this.generateReport();
  }

  generateReport() {
    console.log('\n📊 RAPPORT DE TESTS');
    console.log('===================');

    const successCount = Object.values(this.results).filter(r => r.status === 'success').length;
    const totalTests = Object.keys(this.results).length;

    console.log(`\n✅ Tests réussis: ${successCount}/${totalTests}`);
    console.log(`❌ Tests échoués: ${totalTests - successCount}/${totalTests}\n`);

    // Détails par test
    Object.entries(this.results).forEach(([testName, result]) => {
      const icon = result.status === 'success' ? '✅' : '❌';
      console.log(`${icon} ${testName.toUpperCase()}: ${result.status.toUpperCase()}`);
      
      if (result.status === 'success' && result.details) {
        // Afficher quelques détails clés
        if (result.configuredVars) {
          console.log(`   Variables configurées: ${result.configuredVars}/${result.totalVars}`);
        }
        if (result.buildTime) {
          console.log(`   Temps de build: ${result.buildTime}s`);
        }
        if (result.successfulPages) {
          console.log(`   Pages accessibles: ${result.successfulPages}/${result.totalPages}`);
        }
      }
      
      if (result.status === 'error') {
        console.log(`   Erreur: ${result.error}`);
      }
      console.log('');
    });

    // Recommandations
    console.log('🎯 RECOMMANDATIONS');
    console.log('==================');

    if (this.results.env.status === 'error') {
      console.log('📝 Créer/corriger le fichier frontend/.env.local avec les variables Supabase');
    }

    if (this.results.build.status === 'error') {
      console.log('🔧 Résoudre les erreurs de compilation Next.js');
    }

    if (this.results.pages.status === 'error') {
      console.log('🌐 Vérifier que le serveur Next.js fonctionne sur localhost:3000');
    }

    if (this.results.supabase.status === 'error') {
      console.log('🔗 Vérifier la configuration et la connectivité Supabase');
    }

    if (successCount === totalTests) {
      console.log('\n🎉 TOUS LES TESTS SONT RÉUSSIS! Migration Supabase prête.');
    } else {
      console.log(`\n⚠️  ${totalTests - successCount} test(s) à corriger avant la migration complète.`);
    }

    // Sauvegarder le rapport
    const reportPath = path.join(__dirname, 'test-results', 'migration-report.json');
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      results: this.results,
      summary: {
        total: totalTests,
        success: successCount,
        failed: totalTests - successCount,
        successRate: Math.round((successCount / totalTests) * 100)
      }
    }, null, 2));

    console.log(`\n📄 Rapport détaillé sauvegardé: ${reportPath}`);
  }
}

// Exécution
if (require.main === module) {
  const tester = new MigrationTester();
  tester.runAllTests().catch(error => {
    console.error('❌ Erreur fatale:', error.message);
    process.exit(1);
  });
}

module.exports = MigrationTester; 