#!/usr/bin/env node

/**
 * Script de validation automatique - Étape 3 : IA et Coaching
 * Tests des fonctionnalités avancées de MindFlow Pro
 */

const fs = require('fs');
const path = require('path');

console.log('🧠 VALIDATION ÉTAPE 3 : IA ET COACHING');
console.log('=====================================\n');

const results = {
  timestamp: new Date().toISOString(),
  tests: {},
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    score: 0
  }
};

// Fonction de test générique
function runTest(name, description, testFunction) {
  console.log(`🔍 Test: ${name}`);
  console.log(`   Description: ${description}`);
  
  try {
    const result = testFunction();
    results.tests[name] = {
      description,
      status: result ? 'PASSED' : 'FAILED',
      timestamp: new Date().toISOString(),
      details: result
    };
    
    results.summary.total++;
    if (result) {
      results.summary.passed++;
      console.log(`   ✅ RÉUSSI\n`);
    } else {
      results.summary.failed++;
      console.log(`   ❌ ÉCHEC\n`);
    }
    
    return result;
  } catch (error) {
    results.tests[name] = {
      description,
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message
    };
    
    results.summary.total++;
    results.summary.failed++;
    console.log(`   💥 ERREUR: ${error.message}\n`);
    return false;
  }
}

// Test 1: Vérification des hooks IA Coach
runTest(
  'ai_coach_hook',
  'Vérification de l\'existence et structure du hook useAICoach',
  () => {
    const hookPath = path.join(__dirname, 'frontend/src/hooks/useAICoach.ts');
    if (!fs.existsSync(hookPath)) return false;
    
    const content = fs.readFileSync(hookPath, 'utf8');
    const requiredExports = [
      'useAICoach',
      'AIMessage',
      'AISession',
      'AICoachStats',
      'AIAnalysis'
    ];
    
    return requiredExports.every(exp => content.includes(exp));
  }
);

// Test 2: Vérification des hooks d'analyse d'humeur
runTest(
  'mood_analytics_hook',
  'Vérification de l\'existence et structure du hook useMoodAnalytics',
  () => {
    const hookPath = path.join(__dirname, 'frontend/src/hooks/useMoodAnalytics.ts');
    if (!fs.existsSync(hookPath)) return false;
    
    const content = fs.readFileSync(hookPath, 'utf8');
    const requiredExports = [
      'useMoodAnalytics',
      'MoodDataPoint',
      'MoodTrend',
      'MoodPrediction',
      'MoodAnalytics'
    ];
    
    return requiredExports.every(exp => content.includes(exp));
  }
);

// Test 3: Vérification des hooks de notifications intelligentes
runTest(
  'smart_notifications_hook',
  'Vérification de l\'existence et structure du hook useSmartNotifications',
  () => {
    const hookPath = path.join(__dirname, 'frontend/src/hooks/useSmartNotifications.ts');
    if (!fs.existsSync(hookPath)) return false;
    
    const content = fs.readFileSync(hookPath, 'utf8');
    const requiredExports = [
      'useSmartNotifications',
      'SmartNotification',
      'NotificationRule',
      'NotificationPreferences'
    ];
    
    return requiredExports.every(exp => content.includes(exp));
  }
);

// Test 4: Vérification de la page IA Coach
runTest(
  'ai_coach_page',
  'Vérification de l\'existence et structure de la page IA Coach',
  () => {
    const pagePath = path.join(__dirname, 'frontend/src/app/ai-coach/page.tsx');
    if (!fs.existsSync(pagePath)) return false;
    
    const content = fs.readFileSync(pagePath, 'utf8');
    const requiredFeatures = [
      'useAICoach',
      'startSession',
      'sendMessage',
      'endSession',
      'currentSession',
      'DashboardLayout'
    ];
    
    return requiredFeatures.every(feature => content.includes(feature));
  }
);

// Test 5: Vérification de la page Analytics
runTest(
  'analytics_page',
  'Vérification de l\'existence et structure de la page Analytics',
  () => {
    const pagePath = path.join(__dirname, 'frontend/src/app/analytics/page.tsx');
    if (!fs.existsSync(pagePath)) return false;
    
    const content = fs.readFileSync(pagePath, 'utf8');
    const requiredFeatures = [
      'useMoodAnalytics',
      'getWellnessScore',
      'refreshData',
      'analytics',
      'trends'
    ];
    
    return requiredFeatures.every(feature => content.includes(feature));
  }
);

// Test 6: Vérification de la page de test
runTest(
  'test_page',
  'Vérification de l\'existence et structure de la page de test',
  () => {
    const pagePath = path.join(__dirname, 'frontend/src/app/test-ai-coaching/page.tsx');
    if (!fs.existsSync(pagePath)) return false;
    
    const content = fs.readFileSync(pagePath, 'utf8');
    const requiredFeatures = [
      'useAICoach',
      'useMoodAnalytics',
      'useSmartNotifications',
      'runAllTests',
      'testAICoachSession'
    ];
    
    return requiredFeatures.every(feature => content.includes(feature));
  }
);

// Test 7: Vérification de l'intégration des composants existants
runTest(
  'existing_components_integration',
  'Vérification de l\'intégration avec les composants existants',
  () => {
    const aiCoachQuotePath = path.join(__dirname, 'frontend/src/components/Dashboard/AICoachQuote.tsx');
    const dashboardPath = path.join(__dirname, 'frontend/src/app/dashboard/page.tsx');
    
    if (!fs.existsSync(aiCoachQuotePath) || !fs.existsSync(dashboardPath)) return false;
    
    const aiCoachContent = fs.readFileSync(aiCoachQuotePath, 'utf8');
    const dashboardContent = fs.readFileSync(dashboardPath, 'utf8');
    
    return aiCoachContent.includes('Coach IA') && dashboardContent.includes('useDashboardData');
  }
);

// Test 8: Vérification de la structure TypeScript
runTest(
  'typescript_structure',
  'Vérification de la structure et typage TypeScript',
  () => {
    const hooks = [
      'frontend/src/hooks/useAICoach.ts',
      'frontend/src/hooks/useMoodAnalytics.ts',
      'frontend/src/hooks/useSmartNotifications.ts'
    ];
    
    return hooks.every(hookPath => {
      const fullPath = path.join(__dirname, hookPath);
      if (!fs.existsSync(fullPath)) return false;
      
      const content = fs.readFileSync(fullPath, 'utf8');
      return content.includes('export interface') && 
             content.includes('useCallback') && 
             content.includes('useState');
    });
  }
);

// Test 9: Vérification des fonctionnalités avancées
runTest(
  'advanced_features',
  'Vérification de la présence des fonctionnalités avancées',
  () => {
    const aiCoachPath = path.join(__dirname, 'frontend/src/hooks/useAICoach.ts');
    const analyticsPath = path.join(__dirname, 'frontend/src/hooks/useMoodAnalytics.ts');
    
    if (!fs.existsSync(aiCoachPath) || !fs.existsSync(analyticsPath)) return false;
    
    const aiCoachContent = fs.readFileSync(aiCoachPath, 'utf8');
    const analyticsContent = fs.readFileSync(analyticsPath, 'utf8');
    
    const aiFeatures = [
      'analyzeSentiment',
      'extractEmotionalIndicators',
      'generateAIResponse',
      'moodAnalysis'
    ];
    
    const analyticsFeatures = [
      'MoodPrediction',
      'MoodCorrelation',
      'getWellnessScore',
      'generateMockData'
    ];
    
    return aiFeatures.every(feature => aiCoachContent.includes(feature)) &&
           analyticsFeatures.every(feature => analyticsContent.includes(feature));
  }
);

// Test 10: Vérification de la cohérence du système
runTest(
  'system_coherence',
  'Vérification de la cohérence globale du système',
  () => {
    const requiredFiles = [
      'frontend/src/hooks/useAICoach.ts',
      'frontend/src/hooks/useMoodAnalytics.ts',
      'frontend/src/hooks/useSmartNotifications.ts',
      'frontend/src/app/ai-coach/page.tsx',
      'frontend/src/app/analytics/page.tsx',
      'frontend/src/app/test-ai-coaching/page.tsx'
    ];
    
    const allFilesExist = requiredFiles.every(filePath => {
      const fullPath = path.join(__dirname, filePath);
      return fs.existsSync(fullPath);
    });
    
    if (!allFilesExist) return false;
    
    // Vérifier que les imports sont cohérents
    const testPagePath = path.join(__dirname, 'frontend/src/app/test-ai-coaching/page.tsx');
    const testPageContent = fs.readFileSync(testPagePath, 'utf8');
    
    return testPageContent.includes('useAICoach') &&
           testPageContent.includes('useMoodAnalytics') &&
           testPageContent.includes('useSmartNotifications');
  }
);

// Calcul du score final
results.summary.score = Math.round((results.summary.passed / results.summary.total) * 100);

// Affichage du résumé
console.log('📊 RÉSUMÉ DE LA VALIDATION');
console.log('==========================');
console.log(`✅ Tests réussis: ${results.summary.passed}`);
console.log(`❌ Tests échoués: ${results.summary.failed}`);
console.log(`📈 Score global: ${results.summary.score}%`);
console.log(`⏰ Timestamp: ${results.timestamp}\n`);

// Évaluation du niveau de complétude
let status, message, nextSteps;

if (results.summary.score >= 90) {
  status = '🎉 EXCELLENT';
  message = 'L\'Étape 3 : IA et Coaching est complètement implémentée !';
  nextSteps = [
    'Tester l\'interface utilisateur en naviguant vers /test-ai-coaching',
    'Valider les interactions IA Coach sur /ai-coach',
    'Vérifier les analytics sur /analytics',
    'Préparer l\'intégration Supabase'
  ];
} else if (results.summary.score >= 70) {
  status = '✅ BON';
  message = 'L\'Étape 3 est largement implémentée avec quelques ajustements mineurs.';
  nextSteps = [
    'Corriger les tests en échec',
    'Vérifier les imports manquants',
    'Tester les fonctionnalités principales'
  ];
} else if (results.summary.score >= 50) {
  status = '⚠️  MOYEN';
  message = 'L\'Étape 3 est partiellement implémentée, des corrections sont nécessaires.';
  nextSteps = [
    'Analyser les erreurs de structure',
    'Recréer les fichiers manquants',
    'Vérifier la cohérence des types TypeScript'
  ];
} else {
  status = '❌ INSUFFISANT';
  message = 'L\'Étape 3 nécessite des corrections importantes.';
  nextSteps = [
    'Reprendre l\'implémentation des hooks',
    'Vérifier la structure des fichiers',
    'Relancer la validation après corrections'
  ];
}

console.log(`🎯 STATUT: ${status}`);
console.log(`💬 ${message}\n`);

console.log('🚀 PROCHAINES ACTIONS:');
nextSteps.forEach((step, index) => {
  console.log(`   ${index + 1}. ${step}`);
});

// Sauvegarde des résultats
const reportPath = path.join(__dirname, 'test-results', 'etape3-validation.json');
const reportDir = path.dirname(reportPath);

if (!fs.existsSync(reportDir)) {
  fs.mkdirSync(reportDir, { recursive: true });
}

fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
console.log(`\n📄 Rapport détaillé sauvegardé: ${reportPath}`);

// Code de sortie
process.exit(results.summary.score >= 70 ? 0 : 1); 