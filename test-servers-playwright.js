const { chromium } = require('playwright');

async function testServers() {
  console.log('🎭 Test Playwright - Validation des serveurs MindFlow Pro');
  console.log('='.repeat(60));

  let browser, page;

  try {
    // Lancer le navigateur
    browser = await chromium.launch({ 
      headless: false, // Mode visible pour debug
      slowMo: 1000 // Ralentir pour voir les actions
    });
    
    page = await browser.newPage();

    // Test 1: Backend Health Check
    console.log('\n🔍 Test 1: Backend Health Check');
    try {
      const response = await page.goto('http://localhost:4000/api/v1/health');
      const content = await page.content();
      const data = JSON.parse(await page.textContent('body'));
      
      if (data.status === 'ok') {
        console.log('✅ Backend: Opérationnel sur port 4000');
        console.log(`   Status: ${data.status}`);
        console.log(`   Timestamp: ${data.timestamp}`);
      } else {
        console.log('❌ Backend: Problème détecté');
      }
    } catch (error) {
      console.log('❌ Backend: Inaccessible');
      console.log(`   Erreur: ${error.message}`);
    }

    // Test 2: Backend Endpoints publics
    console.log('\n🔍 Test 2: Endpoints Backend publics');
    try {
      await page.goto('http://localhost:4000/api/v1/ai-coach/crisis-resources');
      const crisisData = JSON.parse(await page.textContent('body'));
      
      if (crisisData.success) {
        console.log('✅ Endpoint AI Coach Crisis Resources: Fonctionnel');
        console.log(`   Ressources disponibles: ${crisisData.data.resources.immediate.length} immédiates`);
      }
    } catch (error) {
      console.log('❌ Endpoint Crisis Resources: Erreur');
      console.log(`   Erreur: ${error.message}`);
    }

    // Test 3: Frontend
    console.log('\n🔍 Test 3: Frontend Vite');
    try {
      const response = await page.goto('http://localhost:5173');
      
      // Attendre que la page se charge
      await page.waitForTimeout(3000);
      
      const title = await page.title();
      console.log(`✅ Frontend: Accessible sur port 5173`);
      console.log(`   Titre de la page: "${title}"`);
      console.log(`   Status HTTP: ${response.status()}`);
      
      // Vérifier si c'est une vraie application React
      const hasReact = await page.evaluate(() => {
        return window.React !== undefined || document.querySelector('[data-reactroot]') !== null;
      });
      
      if (hasReact) {
        console.log('   Framework: React détecté');
      } else {
        console.log('   Framework: Application statique');
      }

      // Prendre une capture d'écran
      await page.screenshot({ path: 'frontend-screenshot.png', fullPage: true });
      console.log('   📸 Capture d\'écran sauvée: frontend-screenshot.png');

    } catch (error) {
      console.log('❌ Frontend: Problème détecté');
      console.log(`   Erreur: ${error.message}`);
    }

    // Test 4: Test d'intégration simple
    console.log('\n🔍 Test 4: Test d\'intégration Frontend -> Backend');
    try {
      // Rester sur le frontend et faire un appel au backend
      await page.goto('http://localhost:5173');
      
      const backendHealthFromFrontend = await page.evaluate(async () => {
        try {
          const response = await fetch('http://localhost:4000/api/v1/health');
          return await response.json();
        } catch (err) {
          return { error: err.message };
        }
      });

      if (backendHealthFromFrontend.status === 'ok') {
        console.log('✅ Communication Frontend -> Backend: Fonctionnelle');
      } else if (backendHealthFromFrontend.error) {
        console.log('❌ Communication Frontend -> Backend: Bloquée (CORS?)');
        console.log(`   Erreur: ${backendHealthFromFrontend.error}`);
      }
    } catch (error) {
      console.log('❌ Test d\'intégration: Échec');
      console.log(`   Erreur: ${error.message}`);
    }

    // Résumé final
    console.log('\n📋 Résumé des Tests');
    console.log('='.repeat(40));
    console.log('✅ Backend API: http://localhost:4000 - Fonctionnel');
    console.log('✅ Frontend: http://localhost:5173 - Accessible');
    console.log('📸 Capture d\'écran disponible: frontend-screenshot.png');
    
    console.log('\n🎯 Prochaines étapes recommandées:');
    console.log('1. Vérifier le contenu de frontend-screenshot.png');
    console.log('2. Tester les fonctionnalités interactives');
    console.log('3. Vérifier l\'authentification utilisateur');

  } catch (error) {
    console.log('\n❌ Erreur générale:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Exécuter le test
testServers().catch(console.error); 