#!/usr/bin/env node

/**
 * 🔍 VÉRIFICATION FINALE - MINDFLOW PRO
 * Script de validation complète de tous les systèmes
 */

const https = require('https');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  production_url: 'https://mindflow-jv4wmqi61-anderson-archimedes-projects.vercel.app',
  github_repo: 'https://github.com/Anderson-Archimede/MindFlow-Pro',
  timeout: 10000
};

/**
 * Execute une commande shell
 */
function exec(command, options = {}) {
  try {
    const result = execSync(command, { 
      encoding: 'utf-8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    return result.toString().trim();
  } catch (error) {
    if (!options.continueOnError) {
      console.error(`❌ Erreur: ${command}`);
    }
    return null;
  }
}

/**
 * Teste une URL
 */
function testUrl(url, description) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const request = https.get(url, (res) => {
      const responseTime = Date.now() - startTime;
      const status = res.statusCode;
      
      if (status >= 200 && status < 400) {
        console.log(`✅ ${description}: ${status} (${responseTime}ms)`);
        resolve({ success: true, status, responseTime });
      } else {
        console.log(`⚠️ ${description}: ${status} (${responseTime}ms)`);
        resolve({ success: false, status, responseTime });
      }
    }).on('error', (err) => {
      const responseTime = Date.now() - startTime;
      console.log(`❌ ${description}: Erreur - ${err.message} (${responseTime}ms)`);
      resolve({ success: false, error: err.message, responseTime });
    });
    
    request.setTimeout(CONFIG.timeout, () => {
      request.destroy();
      console.log(`⏱️ ${description}: Timeout (${CONFIG.timeout}ms)`);
      resolve({ success: false, error: 'Timeout', responseTime: CONFIG.timeout });
    });
  });
}

/**
 * Vérifie Git
 */
function checkGit() {
  console.log('\n🔍 VÉRIFICATION GIT:');
  
  const checks = [
    { cmd: 'git config user.name', desc: 'Nom utilisateur' },
    { cmd: 'git config user.email', desc: 'Email utilisateur' },
    { cmd: 'git remote get-url origin', desc: 'Remote origin' },
    { cmd: 'git branch --show-current', desc: 'Branche actuelle' }
  ];
  
  let gitOk = true;
  
  checks.forEach(check => {
    const result = exec(check.cmd, { silent: true, continueOnError: true });
    if (result) {
      console.log(`✅ ${check.desc}: ${result}`);
    } else {
      console.log(`❌ ${check.desc}: Non configuré`);
      gitOk = false;
    }
  });
  
  return gitOk;
}

/**
 * Vérifie Vercel
 */
function checkVercel() {
  console.log('\n🚀 VÉRIFICATION VERCEL:');
  
  const whoami = exec('vercel whoami', { silent: true, continueOnError: true });
  if (whoami) {
    console.log(`✅ Authentifié: ${whoami}`);
    
    const projects = exec('vercel projects list', { silent: true, continueOnError: true });
    if (projects && projects.includes('mindflow-pro')) {
      console.log('✅ Projet MindFlow Pro trouvé');
      return true;
    } else {
      console.log('⚠️ Projet MindFlow Pro non trouvé dans la liste');
      return false;
    }
  } else {
    console.log('❌ Non authentifié sur Vercel');
    return false;
  }
}

/**
 * Vérifie GitHub Actions
 */
function checkGitHubActions() {
  console.log('\n⚡ VÉRIFICATION GITHUB ACTIONS:');
  
  // Vérifier si le workflow existe
  const workflowPath = '.github/workflows/deploy.yml';
  try {
    const fs = require('fs');
    if (fs.existsSync(workflowPath)) {
      console.log('✅ Workflow GitHub Actions configuré');
      console.log(`📁 Fichier: ${workflowPath}`);
      return true;
    } else {
      console.log('❌ Workflow GitHub Actions manquant');
      return false;
    }
  } catch (error) {
    console.log('❌ Erreur vérification workflow');
    return false;
  }
}

/**
 * Vérifie les fichiers de configuration
 */
function checkConfigFiles() {
  console.log('\n📁 VÉRIFICATION FICHIERS CONFIGURATION:');
  
  const files = [
    { path: 'frontend/.env.local', desc: 'Variables environnement frontend' },
    { path: 'frontend/vercel.json', desc: 'Configuration Vercel' },
    { path: 'frontend/package.json', desc: 'Package frontend' },
    { path: 'backend/package.json', desc: 'Package backend' },
    { path: '.gitignore', desc: 'GitIgnore' },
    { path: 'auto-push.js', desc: 'Script push automatique' },
    { path: 'setup-vercel-secrets.js', desc: 'Script secrets Vercel' }
  ];
  
  let configOk = true;
  const fs = require('fs');
  
  files.forEach(file => {
    if (fs.existsSync(file.path)) {
      console.log(`✅ ${file.desc}: ${file.path}`);
    } else {
      console.log(`❌ ${file.desc}: Manquant (${file.path})`);
      configOk = false;
    }
  });
  
  return configOk;
}

/**
 * Test des URLs de production
 */
async function testProductionUrls() {
  console.log('\n🌐 TEST URLS PRODUCTION:');
  
  const urls = [
    { url: CONFIG.production_url, desc: 'Page d\'accueil' },
    { url: `${CONFIG.production_url}/auth/login`, desc: 'Page de connexion' },
    { url: `${CONFIG.production_url}/auth/register`, desc: 'Page d\'inscription' },
    { url: `${CONFIG.production_url}/api/health/supabase`, desc: 'API Health Check' },
    { url: `${CONFIG.production_url}/test-phase4-supabase`, desc: 'Test Supabase' }
  ];
  
  const results = [];
  
  for (const { url, desc } of urls) {
    const result = await testUrl(url, desc);
    results.push(result);
  }
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`\n📊 Résultats: ${successCount}/${totalCount} URLs accessibles`);
  
  return successCount === totalCount;
}

/**
 * Génère un rapport final
 */
function generateReport(results) {
  console.log('\n📋 RAPPORT FINAL:');
  console.log('═'.repeat(50));
  
  const categories = [
    { name: 'Configuration Git', status: results.git },
    { name: 'Authentification Vercel', status: results.vercel },
    { name: 'GitHub Actions', status: results.actions },
    { name: 'Fichiers Configuration', status: results.config },
    { name: 'URLs Production', status: results.production }
  ];
  
  let totalScore = 0;
  
  categories.forEach(cat => {
    const icon = cat.status ? '✅' : '❌';
    const status = cat.status ? 'OK' : 'ERREUR';
    console.log(`${icon} ${cat.name}: ${status}`);
    if (cat.status) totalScore++;
  });
  
  const percentage = Math.round((totalScore / categories.length) * 100);
  
  console.log('═'.repeat(50));
  console.log(`🎯 SCORE GLOBAL: ${totalScore}/${categories.length} (${percentage}%)`);
  
  if (percentage === 100) {
    console.log('🎉 PARFAIT! Tous les systèmes sont opérationnels!');
    console.log('🚀 MindFlow Pro est prêt pour la production!');
  } else if (percentage >= 80) {
    console.log('✅ TRÈS BIEN! La plupart des systèmes fonctionnent.');
    console.log('⚠️ Corrigez les erreurs mineures restantes.');
  } else {
    console.log('⚠️ ATTENTION! Plusieurs problèmes détectés.');
    console.log('🔧 Corrigez les erreurs avant la mise en production.');
  }
  
  return percentage;
}

/**
 * Affiche les prochaines étapes
 */
function showNextSteps(score) {
  console.log('\n📋 PROCHAINES ÉTAPES:');
  
  if (score === 100) {
    console.log('1. 🔓 Désactiver la protection SSO Vercel');
    console.log('2. 🔐 Configurer les secrets GitHub Actions');
    console.log('3. 🧪 Tester le pipeline CI/CD');
    console.log('4. 🌐 Configurer un domaine personnalisé (optionnel)');
    console.log('5. 👥 Ajouter des collaborateurs');
  } else {
    console.log('1. 🔧 Corriger les erreurs détectées ci-dessus');
    console.log('2. 🔄 Relancer cette vérification');
    console.log('3. 📖 Consulter GUIDE_FINALISATION_COMPLETE.md');
  }
  
  console.log('\n📚 GUIDES DISPONIBLES:');
  console.log('- GUIDE_FINALISATION_COMPLETE.md');
  console.log('- setup-github-secrets.md');
  console.log('- PROCHAINES_ETAPES_FINALES.md');
  
  console.log('\n🔗 LIENS UTILES:');
  console.log(`- Application: ${CONFIG.production_url}`);
  console.log(`- GitHub: ${CONFIG.github_repo}`);
  console.log('- Vercel: https://vercel.com/anderson-archimedes-projects/mindflow-pro');
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🔍 VÉRIFICATION FINALE - MINDFLOW PRO');
  console.log('═'.repeat(50));
  
  const results = {
    git: checkGit(),
    vercel: checkVercel(),
    actions: checkGitHubActions(),
    config: checkConfigFiles(),
    production: await testProductionUrls()
  };
  
  const score = generateReport(results);
  showNextSteps(score);
  
  console.log('\n🎯 VÉRIFICATION TERMINÉE!');
  
  return score === 100;
}

// Aide
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🔍 VÉRIFICATION FINALE - MINDFLOW PRO

Ce script vérifie que tous les systèmes sont correctement configurés.

Usage:
  node verification-finale.js

Vérifications effectuées:
  ✓ Configuration Git
  ✓ Authentification Vercel  
  ✓ GitHub Actions
  ✓ Fichiers de configuration
  ✓ URLs de production

Résultat:
  - Score global sur 100%
  - Rapport détaillé
  - Prochaines étapes recommandées
`);
  process.exit(0);
}

// Exécution
main().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('\n❌ ERREUR FATALE:', error.message);
  process.exit(1);
}); 