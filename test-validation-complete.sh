#!/bin/bash

# Couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
API_URL="http://localhost:4000/api/v1"
EMAIL="<EMAIL>"
PASSWORD="password123"
NAME="Test User"

echo -e "${BLUE}=== MindFlow Pro - Test de validation complète ===${NC}"
echo -e "${YELLOW}Vérification de la disponibilité des serveurs...${NC}"

# Vérifier si le serveur backend est en ligne
echo -e "\n${PURPLE}[1/7]${NC} Test du endpoint de santé..."
HEALTH_RESPONSE=$(curl -s $API_URL/health)
if [[ $HEALTH_RESPONSE == *"success\":true"* ]]; then
  echo -e "${GREEN}✓ Le serveur backend est en ligne${NC}"
else
  echo -e "${RED}✗ Le serveur backend n'est pas accessible${NC}"
  echo "Réponse: $HEALTH_RESPONSE"
  exit 1
fi

# Test du endpoint de test
echo -e "\n${PURPLE}[2/7]${NC} Test du endpoint de test..."
TEST_RESPONSE=$(curl -s $API_URL/test)
if [[ $TEST_RESPONSE == *"success\":true"* ]]; then
  echo -e "${GREEN}✓ Le endpoint de test fonctionne correctement${NC}"
else
  echo -e "${RED}✗ Le endpoint de test a échoué${NC}"
  echo "Réponse: $TEST_RESPONSE"
  exit 1
fi

# Test d'inscription
echo -e "\n${PURPLE}[3/7]${NC} Test d'inscription..."
REGISTER_RESPONSE=$(curl -s -X POST $API_URL/auth/register \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$EMAIL\", \"password\":\"$PASSWORD\", \"name\":\"$NAME\"}")

if [[ $REGISTER_RESPONSE == *"success\":true"* ]]; then
  echo -e "${GREEN}✓ Inscription réussie${NC}"
  TOKEN=$(echo $REGISTER_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)
  echo -e "${YELLOW}Token généré: ${TOKEN:0:20}...${NC}"
else
  echo -e "${YELLOW}! L'inscription a échoué, peut-être que l'utilisateur existe déjà${NC}"
  echo "Réponse: $REGISTER_RESPONSE"
  
  # Essayer de se connecter à la place
  echo -e "\n${PURPLE}[3b/7]${NC} Tentative de connexion avec l'utilisateur existant..."
  LOGIN_RESPONSE=$(curl -s -X POST $API_URL/auth/login \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"$EMAIL\", \"password\":\"$PASSWORD\"}")
  
  if [[ $LOGIN_RESPONSE == *"success\":true"* ]]; then
    echo -e "${GREEN}✓ Connexion réussie${NC}"
    TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)
    echo -e "${YELLOW}Token généré: ${TOKEN:0:20}...${NC}"
  else
    echo -e "${RED}✗ La connexion a également échoué${NC}"
    echo "Réponse: $LOGIN_RESPONSE"
    
    # Essayer avec un utilisateur de test
    echo -e "\n${PURPLE}[3c/7]${NC} Tentative de connexion avec un utilisateur de test..."
    LOGIN_RESPONSE=$(curl -s -X POST $API_URL/auth/login \
      -H "Content-Type: application/json" \
      -d "{\"email\":\"<EMAIL>\", \"password\":\"password\"}")
    
    if [[ $LOGIN_RESPONSE == *"success\":true"* ]]; then
      echo -e "${GREEN}✓ Connexion réussie avec l'utilisateur de test${NC}"
      TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)
      echo -e "${YELLOW}Token généré: ${TOKEN:0:20}...${NC}"
    else
      echo -e "${RED}✗ Impossible de se connecter avec l'utilisateur de test${NC}"
      echo "Réponse: $LOGIN_RESPONSE"
      exit 1
    fi
  fi
fi

# Test de récupération du profil
echo -e "\n${PURPLE}[4/7]${NC} Test de récupération du profil utilisateur..."
PROFILE_RESPONSE=$(curl -s $API_URL/users/me \
  -H "Authorization: Bearer $TOKEN")

if [[ $PROFILE_RESPONSE == *"success\":true"* ]]; then
  echo -e "${GREEN}✓ Récupération du profil réussie${NC}"
  USER_ID=$(echo $PROFILE_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
  echo -e "${YELLOW}ID utilisateur: $USER_ID${NC}"
else
  echo -e "${RED}✗ La récupération du profil a échoué${NC}"
  echo "Réponse: $PROFILE_RESPONSE"
  exit 1
fi

# Test de création d'une entrée de journal
echo -e "\n${PURPLE}[5/7]${NC} Test de création d'une entrée de journal..."
JOURNAL_RESPONSE=$(curl -s -X POST $API_URL/journal \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"title\":\"Test Entry\", \"content\":\"This is a test journal entry\", \"mood\":8}")

if [[ $JOURNAL_RESPONSE == *"success\":true"* ]]; then
  echo -e "${GREEN}✓ Création d'entrée de journal réussie${NC}"
  JOURNAL_ID=$(echo $JOURNAL_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
  echo -e "${YELLOW}ID entrée: $JOURNAL_ID${NC}"
else
  echo -e "${RED}✗ La création d'entrée de journal a échoué${NC}"
  echo "Réponse: $JOURNAL_RESPONSE"
  exit 1
fi

# Test de récupération des entrées de journal
echo -e "\n${PURPLE}[6/7]${NC} Test de récupération des entrées de journal..."
JOURNALS_RESPONSE=$(curl -s $API_URL/journal \
  -H "Authorization: Bearer $TOKEN")

if [[ $JOURNALS_RESPONSE == *"success\":true"* ]]; then
  echo -e "${GREEN}✓ Récupération des entrées de journal réussie${NC}"
  ENTRIES_COUNT=$(echo $JOURNALS_RESPONSE | grep -o '"entries":\[' | wc -l)
  echo -e "${YELLOW}Nombre d'entrées: $ENTRIES_COUNT${NC}"
else
  echo -e "${RED}✗ La récupération des entrées de journal a échoué${NC}"
  echo "Réponse: $JOURNALS_RESPONSE"
  exit 1
fi

# Test de création d'un rendez-vous
echo -e "\n${PURPLE}[7/7]${NC} Test de création d'un rendez-vous..."
APPOINTMENT_RESPONSE=$(curl -s -X POST $API_URL/appointments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"professionalId\":\"3\", \"date\":\"2023-07-15\", \"time\":\"14:00\", \"notes\":\"Test appointment\"}")

if [[ $APPOINTMENT_RESPONSE == *"success\":true"* ]]; then
  echo -e "${GREEN}✓ Création de rendez-vous réussie${NC}"
  APPOINTMENT_ID=$(echo $APPOINTMENT_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
  echo -e "${YELLOW}ID rendez-vous: $APPOINTMENT_ID${NC}"
else
  echo -e "${RED}✗ La création de rendez-vous a échoué${NC}"
  echo "Réponse: $APPOINTMENT_RESPONSE"
  exit 1
fi

# Résumé des tests
echo -e "\n${BLUE}=== Résumé des tests ===${NC}"
echo -e "${GREEN}✓ Tous les tests ont réussi!${NC}"
echo -e "${YELLOW}Le système MindFlow Pro est correctement configuré et fonctionnel.${NC}"
echo -e "${BLUE}=== Fin des tests ===${NC}" 