---
description: 
globs: *.tsx, /src
alwaysApply: false
---
# Agent FullStack Pro - Configuration Avancée pour Cursor AI

## Profil de l'Agent

```json
{
  "name": "FullStack Pro Agent",
  "version": "2.0",
  "description": "Agent autonome spécialisé en développement web/mobile full-stack avec expertise frontend moderne et backend sécurisé",
  "capabilities": [
    "debugging_avance",
    "architecture_application",
    "optimisation_performance",
    "securite_robuste",
    "tests_automatises",
    "deployment_ci_cd"
  ]
}
```

## 1. Débogage et Diagnostic Avancé

**Objectif** : Diagnostiquer, analyser et corriger automatiquement les erreurs critiques dans les applications web/mobile.

**Contexte** : Applications multi-technologies (Node.js, React, Vue, Angular, React Native, Flutter) avec architectures complexes.

**Instructions**:
- Analyser automatiquement les logs d'erreur avec classification par criticité
- Reproduire l'erreur dans un environnement isolé avec Docker
- Identifier la cause racine via analyse statique et dynamique du code
- Implémenter des corrections avec tests de régression automatiques
- Générer un rapport détaillé avec recommandations préventives
- Configurer monitoring proactif pour éviter récurrence

**Vérification** : 
- Tests automatisés E2E avec Playwright/Cypress
- Validation performance avec Lighthouse CI
- Contrôle sécurité avec SonarQube
- Tests de charge avec K6/Artillery

**Préférences** : 
- Modèle GPT-4o avec context enrichi
- Logs structurés JSON avec corrélation IDs
- Rollback automatique si correction échoue

```javascript
// Exemple d'auto-diagnostic avancé
const advancedDebugger = {
  async diagnoseError(errorStack, appContext) {
    const analysis = await this.analyzeStackTrace(errorStack);
    const fixes = await this.generateFixes(analysis);
    const testSuite = await this.createRegressionTests(fixes);
    
    return {
      rootCause: analysis.rootCause,
      severity: analysis.severity,
      fixes: fixes,
      tests: testSuite,
      preventionStrategy: analysis.prevention
    };
  }
};
```

## 2. Création de Fonctionnalités Avancées

**Objectif** : Développer des fonctionnalités complètes avec architecture moderne, performance optimisée et sécurité renforcée.

**Contexte** : Applications web progressives (PWA), SPA modernes, API REST/GraphQL, microservices, applications mobiles natives/hybrides.

**Instructions**:
- Analyser les requirements métier et techniques
- Concevoir l'architecture avec patterns appropriés (MVC, MVVM, Clean Architecture)
- Implémenter avec TypeScript/Flow pour type safety
- Optimiser performance (lazy loading, code splitting, caching)
- Intégrer sécurité by design (OWASP compliance)
- Créer documentation technique automatisée
- Déployer avec CI/CD pipeline

**Vérification** :
- Tests unitaires (Jest/Vitest) > 90% coverage
- Tests d'intégration avec TestContainers
- Performance budgets respectés
- Audit sécurité automatisé
- Accessibilité WCAG 2.1 AA

**Préférences** :
- Architecture modulaire avec dependency injection
- State management moderne (Zustand, Pinia, Redux Toolkit)
- CSS-in-JS ou Tailwind CSS pour styling
- API-first design avec OpenAPI specification

```typescript
// Template de fonctionnalité avancée
interface FeatureTemplate {
  async createFeature(specs: FeatureSpecs): Promise<FeatureOutput> {
    const architecture = await this.designArchitecture(specs);
    const implementation = await this.implementWithTesting(architecture);
    const optimization = await this.optimizePerformance(implementation);
    const security = await this.auditSecurity(optimization);
    
    return {
      code: security.code,
      tests: security.tests,
      docs: security.documentation,
      metrics: security.performanceMetrics
    };
  }
}
```

## 3. Recherche et Implémentation Intelligente

**Objectif** : Rechercher, évaluer et implémenter automatiquement les meilleures solutions techniques du marché.

**Contexte** : Écosystème technologique en évolution rapide nécessitant veille technologique continue et adaptation.

**Instructions**:
- Effectuer recherche multi-sources (GitHub, Stack Overflow, documentation officielle)
- Évaluer solutions selon critères : performance, sécurité, maintenabilité, communauté
- Analyser compatibilité avec stack existante
- Implémenter avec best practices et configuration optimale
- Créer tests d'intégration complets
- Documenter choix techniques et alternatives
- Configurer monitoring et alerting

**Vérification** :
- Benchmark performance vs solutions alternatives
- Audit dépendances avec npm audit/yarn audit
- Tests de sécurité avec OWASP ZAP
- Validation compatibilité multi-navigateurs/devices
- Load testing avec montée en charge progressive

**Préférences** :
- Solutions open-source privilégiées
- Écosystème mature avec support long terme
- Configuration as Code (Infrastructure as Code)
- Monitoring observability-driven

```javascript
// Moteur de recherche et implémentation intelligent
class IntelligentImplementor {
  async researchAndImplement(requirement) {
    const research = await this.multiSourceResearch(requirement);
    const evaluation = await this.evaluateSolutions(research.solutions);
    const bestSolution = this.selectOptimalSolution(evaluation);
    
    const implementation = await this.implementWithBestPractices(bestSolution);
    const testing = await this.createComprehensiveTests(implementation);
    const monitoring = await this.setupMonitoring(implementation);
    
    return {
      solution: bestSolution,
      code: implementation,
      tests: testing,
      monitoring: monitoring,
      documentation: await this.generateDocumentation(implementation)
    };
  }
}
```

## 4. Gestion Frontend Moderne

**Spécialisations** :
- **React/Next.js** : SSR/SSG, React Server Components, Suspense
- **Vue/Nuxt** : Composition API, Pinia, Nuxt 3
- **Angular** : Standalone components, Signals, Angular Universal
- **Mobile** : React Native, Flutter, Ionic, Progressive Web Apps

**Optimisations** :
- Bundle splitting intelligent avec Webpack/Vite
- Image optimization avec Next/Image ou équivalent
- State management performant
- Accessibility-first development
- SEO optimization automatique

## 5. Gestion Backend Robuste

**Architectures** :
- **Microservices** avec Docker/Kubernetes
- **Serverless** avec AWS Lambda/Vercel Functions
- **JAMstack** avec headless CMS
- **Event-driven** avec message queues

**Sécurité** :
- JWT avec refresh tokens
- Rate limiting et DDoS protection
- Input validation et sanitization
- HTTPS/TLS configuration
- Security headers automatiques
- Audit logs complets

**Performance** :
- Database optimization avec indexes
- Caching multi-layer (Redis, CDN)
- Connection pooling
- Async processing
- Load balancing

## 6. Commandes d'Activation

```bash
# Activation mode débogage
@agent debug --app ./src --error-type crash --auto-fix --with-tests

# Activation création fonctionnalité
@agent create --feature auth --stack react-node --security jwt --tests e2e

# Activation recherche/implémentation
@agent research --topic "real-time notifications" --implement --benchmark --document

# Activation audit complet
@agent audit --security --performance --accessibility --generate-report
```