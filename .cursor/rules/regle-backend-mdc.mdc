---
description: 
globs: backend/
alwaysApply: false
---
<PERSON><PERSON>gles Backend
Architecture API
RESTful Design : Respecter les conventions REST. Utiliser les codes de statut HTTP appropriés. Structurer les endpoints de manière logique et prévisible.
GET    /api/users          # Liste des utilisateurs
POST   /api/users          # Création utilisateur
GET    /api/users/:id      # Détail utilisateur
PUT    /api/users/:id      # Mise à jour complète
PATCH  /api/users/:id      # Mise à jour partielle
DELETE /api/users/:id      # Suppression
Validation : Valider toutes les données d'entrée côté serveur. Utiliser des schémas de validation explicites avec des bibliothèques comme Joi ou Zod.
Versioning : Implémenter un versioning d'API cohérent via l'URL ou les headers.
Sécurité
Authentification : Utiliser JWT avec expiration appropriée. Implémenter le refresh token pattern. Stocker les secrets dans des variables d'environnement.
Autorisation : Vérifier les permissions à chaque endpoint sensible. Implémenter le principe du moindre privilège.
Protection : Configurer CORS restrictif. Implémenter rate limiting. Valider et échapper toutes les entrées utilisateur pour prévenir les injections.
javascript// Exemple middleware d'authentification
const authenticateToken = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Token manquant' });
  }
  
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ error: 'Token invalide' });
    req.user = user;
    next();
  });
};
Performance et Monitoring
Base de données : Optimiser les requêtes avec des index appropriés. Utiliser la pagination pour les listes importantes. Implémenter la mise en cache stratégique.
Logging : Structurer les logs avec des niveaux appropriés (error, warn, info, debug). Inclure les identifiants de corrélation pour le tracing.
Monitoring : Implémenter des health checks. Surveiller les métriques clés (temps de réponse, taux d'erreur, utilisation ressources).
4. Intégration Cursor AI
Configuration Workspace
Configurer les règles ESLint/Prettier dans le workspace Cursor. Définir des snippets personnalisés pour les patterns récurrents. Utiliser les extensions recommandées pour maintenir la cohérence.
Prompts et Assistance IA
Formuler des demandes précises avec le contexte métier nécessaire. Spécifier les contraintes techniques et les standards à respecter. Valider systématiquement le code généré avant intégration.
Révision et Qualité
Implémenter des tests unitaires pour toute logique métier critique. Utiliser les outils d'analyse statique intégrés. Effectuer des revues de code systématiques même pour le code assisté par IA.
5. Standards de Livraison
Chaque fonctionnalité doit inclure sa documentation, ses tests et sa validation sécurité. Les déploiements doivent passer par un processus de CI/CD avec validation automatisée. Maintenir une traçabilité complète des modifications avec des commits explicites et structurés.
L'objectif est de produire un code maintenable, sécurisé et performant en exploitant efficacement les capacités de Cursor AI tout en respectant les standards industriels du développement web moderne