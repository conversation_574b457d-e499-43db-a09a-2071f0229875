---
description: 
globs: frontend/
alwaysApply: false
---
Règles Frontend
HTML et CSS
Sémantique HTML : Utiliser les balises HTML5 appropriées. Respecter la hiérarchie des titres H1-H6. Implémenter les attributs d'accessibilité ARIA.
CSS : Privilégier les classes réutilisables. Éviter les sélecteurs trop spécifiques. Utiliser les variables CSS pour la cohérence visuelle. Adopter une méthodologie BEM ou équivalente.
css/* Structure BEM recommandée */
.card { /* Block */ }
.card__header { /* Element */ }
.card--featured { /* Modifier */ }
JavaScript et TypeScript
Type Safety : Privilégier TypeScript avec un typage strict. Définir des interfaces explicites pour tous les objets complexes.
typescriptinterface UserProfile {
  id: string;
  email: string;
  lastLoginAt: Date | null;
}
Gestion d'erreur : Implémenter un système de gestion d'erreur cohérent avec try-catch appropriés et logging centralisé.
Performance : Utiliser la déstructuration, éviter les mutations d'objets, implémenter le lazy loading pour les composants volumineux.
React et Frameworks
Composants : Créer des composants fonctionnels réutilisables. Séparer la logique métier de la présentation. Utiliser les hooks personnalisés pour la logique partagée.
État : Minimiser l'état local. Utiliser Context API ou Redux pour l'état global. Implémenter des reducers purs sans effets de bord.
Rendu : Optimiser avec React.memo, useMemo et useCallback pour éviter les re-rendus inutiles.
typescriptconst UserCard = React.memo(({ user, onEdit }: UserCardProps) => {
  const handleEdit = useCallback(() => onEdit(user.id), [user.id, onEdit]);
  
  return (
    <div className="user-card">
      <h3>{user.name}</h3>
      <button onClick={handleEdit}>Modifier</button>
    </div>
  );
});