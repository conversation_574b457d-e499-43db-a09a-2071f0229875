# 🔐 CONFIGURATION DES SECRETS GITHUB - MINDFLOW PRO

## 📋 SECRETS REQUIS

Pour que le CI/CD fonctionne, vous devez configurer ces secrets dans GitHub :

### 1. VERCEL_TOKEN
```bash
# Obtenir le token Vercel
vercel login
vercel --token

# Ou via Dashboard Vercel
# https://vercel.com/account/tokens
```

### 2. VERCEL_ORG_ID
```bash
# Dans le dossier frontend/
cd frontend
vercel link
cat .vercel/project.json
```

### 3. VERCEL_PROJECT_ID
```bash
# Même fichier que ci-dessus
cat .vercel/project.json
```

---

## 🛠️ CONFIGURATION ÉTAPE PAR ÉTAPE

### Étape 1 : Accéder aux Secrets GitHub
1. Aller sur : https://github.com/Anderson-Archimede/MindFlow-Pro
2. Cliquer sur **Settings**
3. Dans le menu latéral : **Secrets and variables** → **Actions**

### Étape 2 : Ajouter les Secrets
Cliquer sur **New repository secret** pour chaque secret :

#### VERCEL_TOKEN
- **Name** : `VERCEL_TOKEN`
- **Secret** : Votre token Vercel (commençant par `vercel_`)

#### VERCEL_ORG_ID  
- **Name** : `VERCEL_ORG_ID`
- **Secret** : ID de votre organisation Vercel

#### VERCEL_PROJECT_ID
- **Name** : `VERCEL_PROJECT_ID`  
- **Secret** : ID du projet MindFlow Pro

---

## 🚀 SCRIPT AUTOMATIQUE DE CONFIGURATION

### Exécuter le Script
```bash
# Générer et afficher les valeurs
node setup-vercel-secrets.js

# Copier les valeurs affichées dans GitHub Secrets
```

### Vérification
```bash
# Tester la configuration
vercel whoami
vercel projects list
```

---

## ✅ VALIDATION

### Test Local
```bash
# Simuler le workflow
cd frontend
vercel build
vercel deploy --prebuilt
```

### Test GitHub Actions
1. Faire un commit : `git commit -m "test: configuration CI/CD"`
2. Push : `git push origin main`
3. Vérifier : https://github.com/Anderson-Archimede/MindFlow-Pro/actions

---

## 🔍 DÉPANNAGE

### Erreur "Invalid token"
- Vérifier que VERCEL_TOKEN est correct
- Régénérer le token si nécessaire

### Erreur "Project not found"
- Vérifier VERCEL_PROJECT_ID
- Relancer `vercel link` dans frontend/

### Erreur "Organization not found"  
- Vérifier VERCEL_ORG_ID
- Utiliser l'ID, pas le nom

---

## 📱 URLS UTILES

- **GitHub Repository** : https://github.com/Anderson-Archimede/MindFlow-Pro
- **GitHub Actions** : https://github.com/Anderson-Archimede/MindFlow-Pro/actions
- **Vercel Dashboard** : https://vercel.com/anderson-archimedes-projects/mindflow-pro
- **Vercel Tokens** : https://vercel.com/account/tokens 