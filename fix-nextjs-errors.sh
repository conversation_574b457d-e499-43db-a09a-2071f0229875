#!/bin/bash

echo "🔧 CORRECTION DES ERREURS NEXT.JS"
echo "================================="

# 1. Créer le dossier pages vide si nécessaire
echo "1. Création du dossier pages vide..."
mkdir -p frontend/src/pages
touch frontend/src/pages/.gitkeep

# 2. Nettoyer le cache Next.js
echo "2. Nettoyage du cache Next.js..."
rm -rf frontend/.next
rm -rf frontend/node_modules/.cache

# 3. Vérifier .env.local
echo "3. Vérification du fichier .env.local..."
if [ ! -f "frontend/.env.local" ]; then
    echo "   ❌ .env.local manquant! Création..."
    cat > frontend/.env.local << 'EOF'
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUwNDY0ODQsImV4cCI6MjA1MDYyMjQ4NH0.A0HBpJZKqQlUQLGnQK5p3FsHxJKqGZHrUHOEh_Bs2gg

# Feature Flags
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true
NEXT_PUBLIC_MIGRATE_USER_DATA=true
NEXT_PUBLIC_DEBUG_MIGRATION=true

# Development
NODE_ENV=development
EOF
    echo "   ✅ .env.local créé"
else
    echo "   ✅ .env.local existe"
fi

echo ""
echo "✅ Corrections appliquées!"
echo ""
echo "📝 Prochaines étapes :"
echo "1. Arrêtez le serveur Next.js (Ctrl+C)"
echo "2. Exécutez : cd frontend && npm run dev"
echo "3. Testez : http://localhost:3000"
echo "4. Puis : http://localhost:3000/test-basic" 