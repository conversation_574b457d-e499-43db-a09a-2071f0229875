
-- =====================================================
-- MIGRATION PHASE 1: SCHEMAS SUPABASE COMPLETS
-- =====================================================

-- Table: journal_entries
CREATE TABLE IF NOT EXISTS journal_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(), -- Temporaire pour démo
    title TEXT NOT NULL,
    content TEXT,
    mood TEXT CHECK (mood IN ('very_happy', 'happy', 'neutral', 'sad', 'very_sad', 'anxious', 'peaceful', 'hopeful')),
    tags TEXT[] DEFAULT '{}',
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table: ai_coaching_sessions
CREATE TABLE IF NOT EXISTS ai_coaching_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    theme TEXT NOT NULL CHECK (theme IN ('gestion_stress', 'anxiete', 'confiance_soi', 'motivation', 'relations')),
    objective TEXT,
    session_data JSONB DEFAULT '{}',
    mood_analysis JSONB DEFAULT '{}',
    messages_count INTEGER DEFAULT 0,
    completed BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table: mood_analytics
CREATE TABLE IF NOT EXISTS mood_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    mood_score INTEGER CHECK (mood_score >= 1 AND mood_score <= 10),
    energy_level INTEGER CHECK (energy_level >= 1 AND energy_level <= 10),
    stress_level INTEGER CHECK (stress_level >= 1 AND stress_level <= 10),
    sleep_quality INTEGER CHECK (sleep_quality >= 1 AND sleep_quality <= 10),
    exercise_minutes INTEGER DEFAULT 0,
    meditation_minutes INTEGER DEFAULT 0,
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table: smart_notifications
CREATE TABLE IF NOT EXISTS smart_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    type TEXT NOT NULL CHECK (type IN ('reminder', 'suggestion', 'achievement', 'warning', 'insight', 'emergency')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT false,
    is_important BOOLEAN DEFAULT false,
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    scheduled_for TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    action_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_journal_entries_user_created ON journal_entries(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_journal_entries_mood ON journal_entries(mood);
CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_theme ON ai_coaching_sessions(user_id, theme);
CREATE INDEX IF NOT EXISTS idx_mood_analytics_user_date ON mood_analytics(user_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON smart_notifications(user_id, is_read, created_at DESC);

-- Triggers pour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_journal_entries_updated_at BEFORE UPDATE ON journal_entries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_sessions_updated_at BEFORE UPDATE ON ai_coaching_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Données de démonstration
INSERT INTO journal_entries (title, content, mood, tags, is_favorite) VALUES
('Premier jour de thérapie', 'Aujourd''hui j''ai commencé ma thérapie cognitive comportementale. Je me sens plein d''espoir pour l''avenir.', 'hopeful', ARRAY['thérapie', 'nouveau_début'], true),
('Méditation matinale', 'Séance de méditation de 20 minutes ce matin. Je me sens plus centré et calme.', 'peaceful', ARRAY['méditation', 'matin'], false),
('Journée difficile au travail', 'Beaucoup de stress aujourd''hui avec les deadlines. J''ai appliqué les techniques de respiration.', 'anxious', ARRAY['travail', 'stress'], false),
('Promenade en nature', 'Longue promenade dans le parc. La nature m''aide toujours à me reconnecter.', 'happy', ARRAY['nature', 'exercice'], true),
('Réflexions du soir', 'En repensant à ma journée, je réalise que j''ai fait des progrès, même petits.', 'neutral', ARRAY['réflexion', 'progrès'], false);

INSERT INTO ai_coaching_sessions (theme, objective, session_data, messages_count, completed) VALUES
('gestion_stress', 'Réduire le stress quotidien au travail', '{"mood_before": "anxious", "mood_after": "peaceful", "techniques_used": ["respiration", "visualisation"]}', 12, true),
('confiance_soi', 'Améliorer l''estime de soi', '{"mood_before": "sad", "mood_after": "hopeful", "techniques_used": ["affirmations", "objectifs"]}', 8, false),
('anxiete', 'Gérer les crises d''angoisse', '{"mood_before": "very_sad", "mood_after": "neutral", "techniques_used": ["grounding", "respiration"]}', 15, true),
('motivation', 'Retrouver la motivation pour les projets', '{"mood_before": "neutral", "mood_after": "happy", "techniques_used": ["planification", "récompenses"]}', 6, false);

INSERT INTO mood_analytics (date, mood_score, energy_level, stress_level, sleep_quality, exercise_minutes, meditation_minutes, notes) VALUES
(CURRENT_DATE - INTERVAL '6 days', 6, 7, 8, 6, 30, 10, 'Journée stressante mais méditation aidante'),
(CURRENT_DATE - INTERVAL '5 days', 7, 8, 6, 7, 45, 15, 'Meilleur sommeil, plus d''énergie'),
(CURRENT_DATE - INTERVAL '4 days', 5, 5, 9, 4, 0, 0, 'Journée très difficile, insomnie'),
(CURRENT_DATE - INTERVAL '3 days', 8, 9, 4, 8, 60, 20, 'Excellente journée, sport et méditation'),
(CURRENT_DATE - INTERVAL '2 days', 7, 7, 5, 7, 30, 10, 'Journée équilibrée'),
(CURRENT_DATE - INTERVAL '1 day', 6, 6, 7, 6, 20, 5, 'Légère fatigue mais état stable'),
(CURRENT_DATE, 8, 8, 3, 8, 40, 15, 'Très bonne journée, motivation élevée');

INSERT INTO smart_notifications (type, title, message, priority, scheduled_for) VALUES
('reminder', 'Méditation quotidienne', 'Il est temps pour votre séance de méditation de 10 minutes.', 'normal', NOW() + INTERVAL '1 hour'),
('suggestion', 'Exercice de respiration', 'Votre niveau de stress semble élevé. Essayez un exercice de respiration profonde.', 'high', NOW()),
('achievement', 'Félicitations !', 'Vous avez maintenu votre routine de méditation pendant 7 jours consécutifs !', 'normal', NOW()),
('insight', 'Analyse de vos données', 'Vos scores de bien-être s''améliorent. Continuez vos efforts !', 'normal', NOW() + INTERVAL '30 minutes'),
('warning', 'Niveau de stress élevé', 'Votre stress a augmenté ces derniers jours. Pensez à faire une pause.', 'high', NOW());

-- Activation RLS (Row Level Security) pour la sécurité
ALTER TABLE journal_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_coaching_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE mood_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE smart_notifications ENABLE ROW LEVEL SECURITY;

-- Policies publiques pour les tests (à restreindre en production)
CREATE POLICY "Enable all operations for all users" ON journal_entries FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON ai_coaching_sessions FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON mood_analytics FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON smart_notifications FOR ALL USING (true);

-- Fin du script SQL
