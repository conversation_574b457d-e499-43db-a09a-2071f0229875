#!/usr/bin/env node

/**
 * 🚀 SCRIPT DE PUSH AUTOMATIQUE - MINDFLOW PRO
 * Automatise les commits et pushs vers GitHub avec messages intelligents
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  defaultBranch: 'main',
  remote: 'origin',
  maxCommitLength: 72,
  autoTest: process.argv.includes('--test'),
  urgent: process.argv.includes('--urgent'),
  force: process.argv.includes('--force')
};

/**
 * Execute une commande shell
 */
function exec(command, options = {}) {
  try {
    const result = execSync(command, { 
      encoding: 'utf-8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    return result.toString().trim();
  } catch (error) {
    console.error(`❌ Erreur lors de l'exécution: ${command}`);
    console.error(error.message);
    if (!options.continueOnError) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Vérifie l'état Git
 */
function checkGitStatus() {
  console.log('🔍 Vérification de l\'état Git...');
  
  // Vérifier si on est dans un repo Git
  const isGitRepo = exec('git rev-parse --is-inside-work-tree', { silent: true, continueOnError: true });
  if (!isGitRepo) {
    console.error('❌ Pas dans un repository Git');
    process.exit(1);
  }

  // Vérifier la branche actuelle
  const currentBranch = exec('git branch --show-current', { silent: true });
  console.log(`📍 Branche actuelle: ${currentBranch}`);

  // Vérifier les fichiers modifiés
  const status = exec('git status --porcelain', { silent: true });
  if (!status) {
    console.log('✅ Aucun changement à commiter');
    return false;
  }

  console.log('📝 Fichiers modifiés détectés:');
  status.split('\n').forEach(line => {
    if (line.trim()) {
      console.log(`   ${line}`);
    }
  });

  return true;
}

/**
 * Génère un message de commit intelligent
 */
function generateCommitMessage(userMessage) {
  const timestamp = new Date().toISOString().split('T')[0];
  
  if (!userMessage) {
    // Message automatique basé sur les fichiers modifiés
    const status = exec('git status --porcelain', { silent: true });
    const files = status.split('\n').filter(line => line.trim());
    
    if (files.some(f => f.includes('package.json'))) {
      return 'chore: mise à jour des dépendances';
    }
    if (files.some(f => f.includes('.env'))) {
      return 'config: mise à jour configuration environnement';
    }
    if (files.some(f => f.includes('README'))) {
      return 'docs: mise à jour documentation';
    }
    if (files.some(f => f.includes('test'))) {
      return 'test: ajout/modification tests';
    }
    if (files.some(f => f.includes('src/'))) {
      return 'feat: développement nouvelles fonctionnalités';
    }
    
    return `update: modifications automatiques ${timestamp}`;
  }

  // Préfixer selon le type
  if (CONFIG.urgent) {
    return `🚨 URGENT: ${userMessage}`;
  }
  
  // Ajouter timestamp si pas déjà présent
  if (!userMessage.includes(timestamp)) {
    return `${userMessage} (${timestamp})`;
  }
  
  return userMessage;
}

/**
 * Exécute les tests si demandé
 */
function runTests() {
  if (!CONFIG.autoTest) return true;
  
  console.log('🧪 Exécution des tests...');
  
  // Tests frontend
  if (fs.existsSync('frontend/package.json')) {
    console.log('📱 Tests frontend...');
    exec('cd frontend && npm test -- --passWithNoTests', { continueOnError: true });
  }
  
  // Tests backend
  if (fs.existsSync('backend/package.json')) {
    console.log('🔧 Tests backend...');
    exec('cd backend && npm test', { continueOnError: true });
  }
  
  console.log('✅ Tests terminés');
  return true;
}

/**
 * Nettoie le repository
 */
function cleanup() {
  console.log('🧹 Nettoyage du repository...');
  
  // Supprimer fichiers temporaires
  const tempFiles = ['.DS_Store', '*.log', 'node_modules/.cache'];
  tempFiles.forEach(pattern => {
    exec(`find . -name "${pattern}" -delete`, { continueOnError: true, silent: true });
  });
  
  // Nettoyer cache Next.js
  if (fs.existsSync('frontend/.next')) {
    console.log('🗑️ Nettoyage cache Next.js...');
    exec('cd frontend && rm -rf .next/.cache', { continueOnError: true, silent: true });
  }
  
  console.log('✅ Nettoyage terminé');
}

/**
 * Commit et push
 */
function commitAndPush(message) {
  console.log('📦 Préparation du commit...');
  
  // Add tous les fichiers
  exec('git add .');
  
  // Commit avec message
  exec(`git commit -m "${message}"`);
  
  // Push vers GitHub
  console.log('🚀 Push vers GitHub...');
  const pushCommand = CONFIG.force ? 
    `git push ${CONFIG.remote} ${CONFIG.defaultBranch} --force` :
    `git push ${CONFIG.remote} ${CONFIG.defaultBranch}`;
  
  exec(pushCommand);
  
  console.log('✅ Push réussi!');
}

/**
 * Affiche les statistiques
 */
function showStats() {
  console.log('\n📊 STATISTIQUES DU PUSH:');
  
  const lastCommit = exec('git log -1 --oneline', { silent: true });
  const totalCommits = exec('git rev-list --count HEAD', { silent: true });
  const repoSize = exec('git count-objects -vH', { silent: true });
  
  console.log(`📝 Dernier commit: ${lastCommit}`);
  console.log(`🔢 Total commits: ${totalCommits}`);
  console.log(`💾 Taille repo: ${repoSize.split('\n')[0]}`);
  
  // URL du repository
  const remoteUrl = exec('git remote get-url origin', { silent: true });
  if (remoteUrl) {
    const webUrl = remoteUrl.replace('.git', '').replace('**************:', 'https://github.com/');
    console.log(`🌐 Repository: ${webUrl}`);
  }
}

/**
 * Fonction principale
 */
function main() {
  console.log('🚀 DÉMARRAGE DU PUSH AUTOMATIQUE - MINDFLOW PRO\n');
  
  // Récupérer le message de commit
  const commitMessage = process.argv.slice(2)
    .filter(arg => !arg.startsWith('--'))
    .join(' ');
  
  try {
    // 1. Vérifier l'état Git
    const hasChanges = checkGitStatus();
    if (!hasChanges && !CONFIG.force) {
      console.log('✅ Rien à pusher');
      return;
    }
    
    // 2. Nettoyage
    cleanup();
    
    // 3. Tests (si demandé)
    if (CONFIG.autoTest) {
      runTests();
    }
    
    // 4. Générer message de commit
    const finalMessage = generateCommitMessage(commitMessage);
    console.log(`💬 Message de commit: "${finalMessage}"`);
    
    // 5. Commit et push
    commitAndPush(finalMessage);
    
    // 6. Statistiques
    showStats();
    
    console.log('\n🎉 PUSH AUTOMATIQUE TERMINÉ AVEC SUCCÈS!');
    
    // 7. Actions post-push
    if (CONFIG.urgent) {
      console.log('🚨 PUSH URGENT - Vérifiez le déploiement Vercel');
      console.log('🔗 https://vercel.com/anderson-archimedes-projects/mindflow-pro');
    }
    
  } catch (error) {
    console.error('\n❌ ERREUR LORS DU PUSH AUTOMATIQUE:');
    console.error(error.message);
    process.exit(1);
  }
}

// Aide
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🚀 SCRIPT DE PUSH AUTOMATIQUE - MINDFLOW PRO

Usage:
  node auto-push.js [message] [options]

Exemples:
  node auto-push.js "Nouvelle fonctionnalité login"
  node auto-push.js "Fix bug dashboard" --test
  node auto-push.js "Hotfix critique" --urgent
  node auto-push.js --force

Options:
  --test     Exécute les tests avant le push
  --urgent   Marque le commit comme urgent
  --force    Force le push même sans changements
  --help     Affiche cette aide

Configuration Git actuelle:
  Utilisateur: ${exec('git config user.name', { silent: true, continueOnError: true }) || 'Non configuré'}
  Email: ${exec('git config user.email', { silent: true, continueOnError: true }) || 'Non configuré'}
  Remote: ${exec('git remote get-url origin', { silent: true, continueOnError: true }) || 'Non configuré'}
`);
  process.exit(0);
}

// Exécution
main(); 