#!/usr/bin/env node

console.log('🚀 Test d\'Intégration - Système de Rendez-vous Supabase');

const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

async function testBasicConnection() {
  if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
    console.error('❌ Variables Supabase manquantes');
    return false;
  }

  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    console.log('✅ Client Supabase créé');
    return true;
  } catch (error) {
    console.error('❌ Erreur:', error);
    return false;
  }
}

async function main() {
  const success = await testBasicConnection();
  
  if (success) {
    console.log('🎉 Test de base réussi!');
    console.log('🔗 Testez: http://localhost:3000/test-appointments-supabase');
  }
}

main(); 