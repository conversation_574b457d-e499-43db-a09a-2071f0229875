import { test, expect, Page } from '@playwright/test';

/**
 * 🚀 TESTS VALIDATION COMPLÈTE - MINDFLOW PRO
 * Suite de tests automatisés pour valider toutes les fonctionnalités
 * 
 * Phases testées :
 * ✅ Phase 1 & 2 : Fonctionnalités de base + Télémédecine
 * ✅ Phase 3 : Conformité & Sécurité  
 * ✅ Phase 4 : Intégrations B2B
 * ✅ Module 6 : Billing & Finance
 * ✅ Module 7 : Tools & Productivity
 */

// Configuration des timeouts
test.setTimeout(120000);

// URLs de base
const BASE_URL = 'http://localhost:3002'; // Port ajusté selon les logs
const API_URL = 'http://localhost:4000';

// Données de test
const TEST_DATA = {
  professional: {
    name: 'Dr. Test Playwright',
    email: '<EMAIL>',
    specialization: 'Psychiatrie'
  },
  patient: {
    name: 'Patient Test',
    age: 35,
    condition: 'Anxiété'
  }
};

test.describe('🏥 MindFlow Pro - Validation Fonctionnalités Complètes', () => {

  test.beforeEach(async ({ page }) => {
    // Configuration globale pour chaque test
    page.setDefaultTimeout(30000);
    
    // Aller à la page d'accueil
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
  });

  test('🎯 1. Validation Navigation Principales Pages', async ({ page }) => {
    console.log('🔍 Test navigation des pages principales...');
    
    const pagesToTest = [
      { url: '/', title: 'MindFlow Pro', description: 'Page d\'accueil' },
      { url: '/dashboard', title: 'Dashboard', description: 'Tableau de bord' },
      { url: '/appointments', title: 'Rendez-vous', description: 'Gestion des rendez-vous' },
      { url: '/professionals', title: 'Professionnels', description: 'Liste des professionnels' },
      { url: '/patients', title: 'Patients', description: 'Gestion des patients' },
      { url: '/telemedicine', title: 'Télémédecine', description: 'Interface télémédecine' },
      { url: '/telemedicine-advanced', title: 'Télémédecine Avancée', description: 'Télémédecine Phase 2' }
    ];

    for (const pageTest of pagesToTest) {
      console.log(`   ✅ Test ${pageTest.description}: ${pageTest.url}`);
      
      await page.goto(`${BASE_URL}${pageTest.url}`);
      await page.waitForLoadState('networkidle');
      
      // Vérifier que la page se charge sans erreur 404/500
      const pageContent = await page.textContent('body');
      expect(pageContent).not.toContain('404');
      expect(pageContent).not.toContain('500');
      expect(pageContent).not.toContain('Error');
      
      // Vérifier la présence d'éléments de base
      await expect(page.locator('h1, h2, .text-2xl, .text-3xl')).toBeVisible();
    }
    
    console.log('✅ Navigation validée avec succès');
  });

  test('🏢 2. Phase 3 - Module Conformité & Sécurité', async ({ page }) => {
    console.log('🔍 Test Phase 3 : Conformité & Sécurité...');
    
    await page.goto(`${BASE_URL}/compliance`);
    await page.waitForLoadState('networkidle');
    
    // Vérifier la présence du dashboard de conformité
    await expect(page.locator('h1')).toContainText(/Conformité|Compliance/);
    
    // Vérifier les certifications principales
    const certifications = [
      'HDS', 'ISO 27001', 'HIPAA', 'SOC 2'
    ];
    
    for (const cert of certifications) {
      console.log(`   ✅ Vérification certification ${cert}...`);
      const certElement = page.locator(`text=${cert}`);
      await expect(certElement).toBeVisible();
    }
    
    // Vérifier les métriques de sécurité
    const securityMetrics = [
      'Progression globale', 'Monitoring sécurité', 'Audit trail'
    ];
    
    for (const metric of securityMetrics) {
      const metricText = await page.textContent('body');
      expect(metricText).toContain(metric.toLowerCase().replace(' ', ''));
    }
    
    console.log('✅ Phase 3 Conformité validée');
  });

  test('🔗 3. Phase 4 - Intégrations B2B', async ({ page }) => {
    console.log('🔍 Test Phase 4 : Intégrations B2B...');
    
    await page.goto(`${BASE_URL}/integrations-b2b`);
    await page.waitForLoadState('networkidle');
    
    // Vérifier la présence du dashboard B2B
    await expect(page.locator('h1')).toContainText(/Intégrations|B2B/);
    
    // Vérifier les catégories d'intégrations
    const integrationCategories = [
      'Hôpitaux', 'HL7 FHIR', 'Laboratoires', 'Pharmacies', 'SDK'
    ];
    
    for (const category of integrationCategories) {
      console.log(`   ✅ Vérification catégorie ${category}...`);
      const categoryText = await page.textContent('body');
      expect(categoryText?.toLowerCase()).toContain(category.toLowerCase());
    }
    
    // Vérifier les métriques temps réel
    const b2bMetrics = [
      'API calls', 'uptime', 'intégrations actives'
    ];
    
    for (const metric of b2bMetrics) {
      const metricText = await page.textContent('body');
      expect(metricText?.toLowerCase()).toContain(metric.toLowerCase().replace(' ', ''));
    }
    
    console.log('✅ Phase 4 Intégrations B2B validée');
  });

  test('💰 4. Module 6 - Billing & Finance', async ({ page }) => {
    console.log('🔍 Test Module 6 : Billing & Finance...');
    
    await page.goto(`${BASE_URL}/billing`);
    await page.waitForLoadState('networkidle');
    
    // Vérifier la présence du dashboard billing
    await expect(page.locator('h1')).toContainText(/Facturation|Billing/);
    
    // Vérifier les métriques financières
    const billingMetrics = [
      'Revenus', 'Factures', 'Paiements', 'Remboursements'
    ];
    
    for (const metric of billingMetrics) {
      console.log(`   ✅ Vérification métrique ${metric}...`);
      const metricText = await page.textContent('body');
      expect(metricText?.toLowerCase()).toContain(metric.toLowerCase());
    }
    
    // Vérifier la présence de graphiques/tableaux
    await expect(page.locator('.chart, .table, .grid, [class*="chart"], [class*="table"]')).toBeVisible();
    
    // Tester les interactions de base
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    expect(buttonCount).toBeGreaterThan(0);
    
    console.log('✅ Module Billing validé');
  });

  test('🛠️ 5. Module 7 - Tools & Productivity', async ({ page }) => {
    console.log('🔍 Test Module 7 : Tools & Productivity...');
    
    await page.goto(`${BASE_URL}/tools`);
    await page.waitForLoadState('networkidle');
    
    // Vérifier la présence de la page tools
    await expect(page.locator('h1')).toContainText(/Outils|Tools|Productivité/);
    
    // Vérifier les onglets d'outils
    const toolTabs = [
      'Notes Vocales', 'Calculatrices', 'IA Recommandations', 
      'Recherche', 'Équipe', 'Inventaire'
    ];
    
    for (const tab of toolTabs) {
      console.log(`   ✅ Vérification onglet ${tab}...`);
      const tabElement = page.locator(`text=${tab}`);
      await expect(tabElement).toBeVisible();
    }
    
    // Tester l'interaction avec les onglets
    const voiceNotesTab = page.locator('text=Notes Vocales');
    if (await voiceNotesTab.isVisible()) {
      await voiceNotesTab.click();
      await page.waitForTimeout(1000);
      
      // Vérifier que le contenu de l'onglet s'affiche
      const tabContent = await page.textContent('body');
      expect(tabContent?.toLowerCase()).toContain('vocal');
    }
    
    console.log('✅ Module Tools & Productivity validé');
  });

  test('📱 6. Télémédecine Avancée - Phase 2', async ({ page }) => {
    console.log('🔍 Test Télémédecine Avancée Phase 2...');
    
    await page.goto(`${BASE_URL}/telemedicine-advanced`);
    await page.waitForLoadState('networkidle');
    
    // Vérifier la présence de l'interface télémédecine avancée
    await expect(page.locator('h1, h2')).toContainText(/Télémédecine|Telemedicine/);
    
    // Vérifier les fonctionnalités avancées
    const advancedFeatures = [
      'HD', '1080p', 'IA', 'Diagnostic', 'Stéthoscope', 'Qualité'
    ];
    
    for (const feature of advancedFeatures) {
      console.log(`   ✅ Vérification fonctionnalité ${feature}...`);
      const featureText = await page.textContent('body');
      expect(featureText?.toLowerCase()).toContain(feature.toLowerCase());
    }
    
    // Vérifier la présence d'outils diagnostiques
    const diagnosticTools = page.locator('[class*="diagnostic"], [class*="tool"]');
    const toolsCount = await diagnosticTools.count();
    expect(toolsCount).toBeGreaterThan(0);
    
    console.log('✅ Télémédecine Avancée validée');
  });

  test('👥 7. Gestion Patients & Professionnels', async ({ page }) => {
    console.log('🔍 Test Gestion Patients & Professionnels...');
    
    // Test page Patients
    await page.goto(`${BASE_URL}/patients`);
    await page.waitForLoadState('networkidle');
    
    await expect(page.locator('h1')).toContainText(/Patients/);
    
    // Vérifier la présence de la liste/tableau de patients
    const patientElements = page.locator('[class*="patient"], .card, .table, .grid');
    const patientsCount = await patientElements.count();
    expect(patientsCount).toBeGreaterThan(0);
    
    // Test page Professionnels
    await page.goto(`${BASE_URL}/professionals`);
    await page.waitForLoadState('networkidle');
    
    await expect(page.locator('h1')).toContainText(/Professionnels/);
    
    // Vérifier la présence de la liste de professionnels
    const professionalElements = page.locator('[class*="professional"], .card, .table, .grid');
    const professionalsCount = await professionalElements.count();
    expect(professionalsCount).toBeGreaterThan(0);
    
    console.log('✅ Gestion Patients & Professionnels validée');
  });

  test('📊 8. Dashboard & Analytics', async ({ page }) => {
    console.log('🔍 Test Dashboard & Analytics...');
    
    await page.goto(`${BASE_URL}/dashboard`);
    await page.waitForLoadState('networkidle');
    
    // Vérifier la présence du dashboard
    await expect(page.locator('h1, h2')).toContainText(/Dashboard|Tableau/);
    
    // Vérifier les métriques principales
    const dashboardMetrics = [
      'Rendez-vous', 'Patients', 'Revenus', 'Satisfaction'
    ];
    
    for (const metric of dashboardMetrics) {
      console.log(`   ✅ Vérification métrique ${metric}...`);
      const metricText = await page.textContent('body');
      expect(metricText?.toLowerCase()).toContain(metric.toLowerCase());
    }
    
    // Vérifier la présence de graphiques/charts
    const charts = page.locator('[class*="chart"], [class*="graph"], canvas, svg');
    const chartsCount = await charts.count();
    expect(chartsCount).toBeGreaterThan(0);
    
    console.log('✅ Dashboard & Analytics validé');
  });

  test('🧪 9. Tests Backend API', async ({ page }) => {
    console.log('🔍 Test connexion Backend API...');
    
    // Test health endpoint
    const healthResponse = await page.goto(`${API_URL}/api/v1/health`);
    expect(healthResponse?.status()).toBe(200);
    
    const healthData = await page.textContent('body');
    const healthJson = JSON.parse(healthData || '{}');
    expect(healthJson.status).toBe('ok');
    
    console.log('✅ Backend API opérationnel');
  });

  test('📱 10. Tests Responsivité Mobile', async ({ page }) => {
    console.log('🔍 Test responsivité mobile...');
    
    // Simuler viewport mobile
    await page.setViewportSize({ width: 375, height: 667 });
    
    const mobilePages = ['/', '/dashboard', '/appointments', '/patients'];
    
    for (const pageUrl of mobilePages) {
      console.log(`   ✅ Test mobile ${pageUrl}...`);
      
      await page.goto(`${BASE_URL}${pageUrl}`);
      await page.waitForLoadState('networkidle');
      
      // Vérifier que la page ne déborde pas horizontalement
      const body = page.locator('body');
      const bodyBox = await body.boundingBox();
      
      if (bodyBox) {
        expect(bodyBox.width).toBeLessThanOrEqual(375);
      }
      
      // Vérifier la présence d'un menu mobile ou navigation adaptée
      const navigation = page.locator('[class*="mobile"], [class*="nav"], [class*="menu"]');
      const navCount = await navigation.count();
      expect(navCount).toBeGreaterThan(0);
    }
    
    console.log('✅ Responsivité mobile validée');
  });

  test('⚡ 11. Tests Performance', async ({ page }) => {
    console.log('🔍 Test performance...');
    
    // Mesurer le temps de chargement de la page d'accueil
    const startTime = Date.now();
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    console.log(`   ⏱️  Temps de chargement: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(10000); // Moins de 10 secondes
    
    // Vérifier l'absence d'erreurs JavaScript
    const jsErrors: string[] = [];
    page.on('pageerror', (error) => {
      jsErrors.push(error.message);
    });
    
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    expect(jsErrors.length).toBe(0);
    
    console.log('✅ Performance validée');
  });

  test('🎯 12. Test Module de Test Healthcare', async ({ page }) => {
    console.log('🔍 Test page modules healthcare...');
    
    await page.goto(`${BASE_URL}/test-healthcare-modules`);
    await page.waitForLoadState('networkidle');
    
    // Vérifier que la page affiche les statuts des phases
    await expect(page.locator('h1')).toContainText(/Modules|Healthcare|Test/);
    
    // Vérifier les phases
    const phases = ['Phase 1', 'Phase 2', 'Phase 3', 'Phase 4'];
    
    for (const phase of phases) {
      console.log(`   ✅ Vérification ${phase}...`);
      const phaseText = await page.textContent('body');
      expect(phaseText).toContain(phase);
    }
    
    console.log('✅ Modules Healthcare validés');
  });

});

// Test de nettoyage final
test.afterAll(async () => {
  console.log('🎉 TESTS TERMINÉS - RÉCAPITULATIF:');
  console.log('✅ Navigation principales pages');
  console.log('✅ Phase 3 - Conformité & Sécurité');
  console.log('✅ Phase 4 - Intégrations B2B');
  console.log('✅ Module 6 - Billing & Finance');
  console.log('✅ Module 7 - Tools & Productivity');
  console.log('✅ Télémédecine Avancée');
  console.log('✅ Gestion Patients & Professionnels');
  console.log('✅ Dashboard & Analytics');
  console.log('✅ Backend API');
  console.log('✅ Responsivité Mobile');
  console.log('✅ Performance');
  console.log('✅ Modules Healthcare');
}); 