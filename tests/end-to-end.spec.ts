import { test, expect, Page } from '@playwright/test';

/**
 * Tests End-to-End MindFlow Pro
 * Validation complète du flux utilisateur et communication serveurs
 */

test.describe('Tests End-to-End MindFlow Pro', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    
    // Capturer toutes les requêtes réseau
    const networkLogs: string[] = [];
    page.on('request', request => {
      networkLogs.push(`➡️  ${request.method()} ${request.url()}`);
    });
    page.on('response', response => {
      networkLogs.push(`⬅️  ${response.status()} ${response.url()}`);
    });
    
    // Sauvegarder les logs à la fin du test
    test.afterEach(async () => {
      console.log('\n📡 Logs réseau:');
      networkLogs.slice(-10).forEach(log => console.log(log)); // Derniers 10 logs
    });
  });

  test('Parcours complet utilisateur - Accueil vers Dashboard', async () => {
    console.log('🚀 Début du parcours utilisateur complet');
    
    // 1. Page d'accueil
    await page.goto('/');
    await expect(page).toHaveTitle(/MindFlow Pro/);
    console.log('✅ Page d\'accueil chargée');
    
    // 2. Navigation vers login
    await page.goto('/auth/login');
    await expect(page.locator('input[type="email"]')).toBeVisible();
    console.log('✅ Page de login accessible');
    
    // 3. Tentative de navigation vers dashboard (devrait rediriger)
    await page.goto('/dashboard');
    
    // Vérifier si on est bien redirigé vers login ou si le dashboard s'affiche
    const currentUrl = page.url();
    if (currentUrl.includes('/auth/login')) {
      console.log('✅ Redirection sécurisée vers login');
      
      // 4. Test de soumission du formulaire de login
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'password123');
      
      // Intercepter la requête d'authentification
      let authRequestDetected = false;
      page.on('request', request => {
        if (request.url().includes('/auth/login')) {
          authRequestDetected = true;
          console.log('🔐 Requête d\'authentification détectée');
        }
      });
      
      await page.click('button[type="submit"]');
      await page.waitForTimeout(2000);
      
      if (authRequestDetected) {
        console.log('✅ Communication frontend-backend établie lors du login');
      }
      
    } else if (currentUrl.includes('/dashboard')) {
      console.log('✅ Dashboard accessible directement');
    }
    
    await page.screenshot({ path: 'test-results/e2e-user-journey.png' });
  });

  test('Test Communication API - Cycle complet', async () => {
    console.log('🔗 Test de communication API complète');
    
    const apiTests = [
      { endpoint: '/health', expectedStatus: 200, description: 'Health check' },
      { endpoint: '/auth/status', expectedStatus: [200, 401], description: 'Auth status' },
    ];
    
    for (const apiTest of apiTests) {
      const response = await page.request.get(`http://localhost:4000/api/v1${apiTest.endpoint}`);
      const status = response.status();
      
      const expectedStatuses = Array.isArray(apiTest.expectedStatus) 
        ? apiTest.expectedStatus 
        : [apiTest.expectedStatus];
      
      if (expectedStatuses.includes(status)) {
        console.log(`✅ ${apiTest.description}: ${status}`);
      } else {
        console.log(`❌ ${apiTest.description}: ${status} (attendu: ${apiTest.expectedStatus})`);
      }
      
      expect(expectedStatuses).toContain(status);
    }
  });

  test('Test Interface Responsive - Multi-résolutions', async () => {
    const resolutions = [
      { width: 1920, height: 1080, name: 'Desktop Large' },
      { width: 1366, height: 768, name: 'Desktop Standard' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];
    
    for (const resolution of resolutions) {
      console.log(`📱 Test résolution ${resolution.name} (${resolution.width}x${resolution.height})`);
      
      await page.setViewportSize({ width: resolution.width, height: resolution.height });
      await page.goto('/');
      
      // Vérifier que le contenu ne déborde pas
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
      const viewportWidth = resolution.width;
      
      if (bodyWidth <= viewportWidth + 20) { // 20px de marge
        console.log(`✅ ${resolution.name}: Contenu adapté (${bodyWidth}px)`);
      } else {
        console.log(`⚠️  ${resolution.name}: Débordement détecté (${bodyWidth}px > ${viewportWidth}px)`);
      }
      
      await page.screenshot({ 
        path: `test-results/responsive-${resolution.name.toLowerCase().replace(' ', '-')}.png` 
      });
    }
  });

  test('Test Performance - Métriques Web Vitals', async () => {
    console.log('⚡ Test de performance et Web Vitals');
    
    // Naviguer vers la page d'accueil
    const startTime = Date.now();
    await page.goto('/', { waitUntil: 'networkidle' });
    const loadTime = Date.now() - startTime;
    
    console.log(`⏱️  Temps de chargement total: ${loadTime}ms`);
    
    // Évaluer les métriques de performance
    const metrics = await page.evaluate(() => {
      return {
        // First Contentful Paint
        fcp: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
        // Largest Contentful Paint
        lcp: performance.getEntriesByType('largest-contentful-paint')[0]?.startTime || 0,
        // DOM Content Loaded
        domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
        // Load event
        loadEvent: performance.timing.loadEventEnd - performance.timing.navigationStart
      };
    });
    
    console.log('📊 Métriques de performance:', metrics);
    
    // Vérifications de performance
    expect(loadTime).toBeLessThan(10000); // Moins de 10 secondes
    
    if (metrics.fcp > 0) {
      expect(metrics.fcp).toBeLessThan(3000); // FCP < 3s
      console.log(`✅ First Contentful Paint: ${metrics.fcp.toFixed(0)}ms`);
    }
    
    if (metrics.lcp > 0) {
      expect(metrics.lcp).toBeLessThan(4000); // LCP < 4s
      console.log(`✅ Largest Contentful Paint: ${metrics.lcp.toFixed(0)}ms`);
    }
  });

  test('Test Sécurité - Headers et HTTPS', async () => {
    console.log('🔒 Test de sécurité et headers');
    
    await page.goto('/');
    
    // Vérifier les headers de sécurité via une requête directe
    const response = await page.request.get('http://localhost:3001/');
    const headers = response.headers();
    
    console.log('🛡️  Headers de sécurité:');
    
    const securityHeaders = [
      'x-frame-options',
      'x-content-type-options', 
      'x-xss-protection',
      'strict-transport-security',
      'content-security-policy'
    ];
    
    securityHeaders.forEach(header => {
      if (headers[header]) {
        console.log(`✅ ${header}: ${headers[header]}`);
      } else {
        console.log(`⚠️  ${header}: Non configuré`);
      }
    });
    
    // Vérifier qu'il n'y a pas de failles évidentes
    const pageContent = await page.content();
    expect(pageContent).not.toContain('eval(');
    expect(pageContent).not.toContain('innerHTML');
    
    console.log('✅ Aucune faille évidente détectée');
  });

  test('Test Accessibilité - Standards WCAG', async () => {
    console.log('♿ Test d\'accessibilité WCAG');
    
    await page.goto('/');
    
    // Vérifier les éléments d'accessibilité de base
    const accessibilityChecks = await page.evaluate(() => {
      const issues: string[] = [];
      
      // Images sans alt
      const images = document.querySelectorAll('img');
      images.forEach((img, index) => {
        if (!img.getAttribute('alt')) {
          issues.push(`Image ${index + 1} sans attribut alt`);
        }
      });
      
      // Boutons sans label
      const buttons = document.querySelectorAll('button');
      buttons.forEach((button, index) => {
        if (!button.textContent?.trim() && !button.getAttribute('aria-label')) {
          issues.push(`Bouton ${index + 1} sans label`);
        }
      });
      
      // Inputs sans label
      const inputs = document.querySelectorAll('input');
      inputs.forEach((input, index) => {
        if (!input.getAttribute('aria-label') && !document.querySelector(`label[for="${input.id}"]`)) {
          issues.push(`Input ${index + 1} sans label`);
        }
      });
      
      return issues;
    });
    
    if (accessibilityChecks.length === 0) {
      console.log('✅ Aucun problème d\'accessibilité détecté');
    } else {
      console.log('⚠️  Problèmes d\'accessibilité détectés:');
      accessibilityChecks.forEach(issue => console.log(`   - ${issue}`));
    }
    
    // Le test ne devrait pas échouer pour des problèmes mineurs d'accessibilité
    expect(accessibilityChecks.length).toBeLessThan(10);
  });
}); 