import { test, expect, Page } from '@playwright/test';

// Configuration des timeouts
test.setTimeout(120000); // 2 minutes par test

/**
 * TESTS AUTOMATISÉS COMPLETS - MINDFLOW PRO PHASE 4 SUPABASE
 * Validation automatique de toutes les fonctionnalités
 */

test.describe('🚀 MindFlow Pro - Validation Complète Phase 4 Supabase', () => {
  
  test.beforeEach(async ({ page }) => {
    // Configuration pour chaque test
    await page.goto('http://localhost:3000');
    await page.waitForTimeout(2000);
  });

  test('✅ 1. Validation de la connectivité des pages principales', async ({ page }) => {
    console.log('🔍 Test de connectivité des pages...');
    
    const pages = [
      { url: '/', name: 'Accueil' },
      { url: '/auth/login', name: 'Login' },
      { url: '/auth/register', name: 'Register' },
      { url: '/test-phase4-supabase', name: 'Test Phase 4' },
      { url: '/test-supabase-schema', name: '<PERSON><PERSON><PERSON>' },
      { url: '/test-supabase-verification', name: 'Verification Tables' },
      { url: '/dashboard', name: 'Dashboard' },
      { url: '/journal', name: 'Journal' },
    ];

    for (const pageInfo of pages) {
      console.log(`   Testing: ${pageInfo.name} (${pageInfo.url})`);
      
      await page.goto(`http://localhost:3000${pageInfo.url}`);
      await page.waitForTimeout(1000);
      
      // Vérifier que la page se charge sans erreur 500
      const response = await page.evaluate(() => {
        return {
          status: document.readyState,
          hasError: document.querySelector('.error, [data-testid="error"]') !== null,
          title: document.title
        };
      });
      
      expect(response.status).toBe('complete');
      expect(response.hasError).toBe(false);
      
      console.log(`   ✅ ${pageInfo.name}: OK`);
    }
  });

  test('✅ 2. Validation Configuration Phase 4 Supabase', async ({ page }) => {
    console.log('🔍 Test configuration Phase 4...');
    
    await page.goto('http://localhost:3000/test-phase4-supabase');
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // Vérifier que la page indique Phase 4 ACTIVE
    const statusText = await page.textContent('body');
    expect(statusText).toContain('Phase 4');
    
    // Vérifier les feature flags affichés
    const configElements = await page.$$eval('[class*="text-blue-800"], [class*="text-green-800"]', 
      elements => elements.map(el => el.textContent));
    
    const expectedConfigs = [
      'Auth Supabase: true',
      'DB Supabase: true', 
      'Real-time: true',
      'Mode Dual: false'
    ];
    
    for (const config of expectedConfigs) {
      const found = configElements.some(text => text?.includes(config.split(': ')[1]));
      expect(found).toBe(true);
      console.log(`   ✅ ${config}`);
    }
  });

  test('✅ 3. Test Inscription Utilisateur Automatique', async ({ page }) => {
    console.log('🔍 Test inscription utilisateur...');
    
    await page.goto('http://localhost:3000/auth/register');
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Générer un email unique pour éviter les conflits
    const timestamp = Date.now();
    const testEmail = `test${timestamp}@mindflow.pro`;
    const testPassword = 'TestPassword123!';
    
    // Remplir le formulaire d'inscription
    await page.fill('input[type="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    
    // Chercher le champ de confirmation s'il existe
    const confirmPasswordField = await page.$('input[name="confirmPassword"], input[id="confirm-password"]');
    if (confirmPasswordField) {
      await page.fill('input[name="confirmPassword"], input[id="confirm-password"]', testPassword);
    }
    
    // Chercher les champs nom si ils existent
    const nameField = await page.$('input[name="name"], input[name="firstName"]');
    if (nameField) {
      await page.fill('input[name="name"], input[name="firstName"]', 'Test User');
    }
    
    // Accepter les conditions si nécessaire
    const termsCheckbox = await page.$('input[type="checkbox"]');
    if (termsCheckbox) {
      await page.check('input[type="checkbox"]');
    }
    
    console.log(`   📝 Inscription avec: ${testEmail}`);
    
    // Soumettre le formulaire
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    // Vérifier le résultat - soit redirection vers dashboard, soit message de succès
    const currentUrl = page.url();
    const bodyText = await page.textContent('body');
    
    const success = currentUrl.includes('/dashboard') || 
                   bodyText?.includes('succès') || 
                   bodyText?.includes('Inscription réussie') ||
                   bodyText?.includes('Bienvenue');
    
    if (success) {
      console.log('   ✅ Inscription réussie');
    } else {
      console.log('   ⚠️ Inscription en attente (possiblement email confirmation)');
    }
    
    // Stocker les credentials pour les tests suivants
    page.context().storageState({ path: 'tests/auth-state.json' });
  });

  test('✅ 4. Test Connexion et Authentification', async ({ page }) => {
    console.log('🔍 Test connexion utilisateur...');
    
    await page.goto('http://localhost:3000/auth/login');
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Utiliser des credentials de test
    const testEmail = '<EMAIL>';
    const testPassword = 'TestPassword123!';
    
    await page.fill('input[type="email"]', testEmail);
    await page.fill('input[type="password"]', testPassword);
    
    console.log(`   🔐 Connexion avec: ${testEmail}`);
    
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    // Vérifier la connexion réussie
    const currentUrl = page.url();
    const bodyText = await page.textContent('body');
    
    const authenticated = currentUrl.includes('/dashboard') || 
                         bodyText?.includes('Bienvenue') ||
                         bodyText?.includes('connecté');
    
    if (authenticated) {
      console.log('   ✅ Connexion réussie');
    } else {
      console.log('   ⚠️ Connexion nécessite configuration manuelle');
    }
  });

  test('✅ 5. Validation Fonctionnalités Supabase', async ({ page }) => {
    console.log('🔍 Test fonctionnalités Supabase...');
    
    await page.goto('http://localhost:3000/test-phase4-supabase');
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // Vérifier si utilisateur connecté
    const bodyText = await page.textContent('body');
    const isLoggedIn = bodyText?.includes('Utilisateur connecté') || 
                      bodyText?.includes('ID:') ||
                      !bodyText?.includes('Vous devez être connecté');
    
    if (isLoggedIn) {
      console.log('   👤 Utilisateur connecté, test des fonctionnalités...');
      
      // Test du bouton "Lancer Tests Supabase"
      const testButton = await page.$('button:has-text("Lancer Tests"), button:has-text("🧪")');
      if (testButton) {
        await testButton.click();
        await page.waitForTimeout(5000);
        
        // Vérifier les résultats des tests
        const resultsText = await page.textContent('body');
        const testsPassed = (resultsText?.match(/✅/g) || []).length;
        
        console.log(`   📊 Tests Supabase passés: ${testsPassed}`);
        expect(testsPassed).toBeGreaterThan(0);
      }
      
      // Test du bouton "Créer Données Test"
      const createDataButton = await page.$('button:has-text("Créer Données"), button:has-text("📝")');
      if (createDataButton) {
        await createDataButton.click();
        await page.waitForTimeout(3000);
        
        console.log('   📝 Données de test créées');
      }
    } else {
      console.log('   ⚠️ Utilisateur non connecté - tests limités');
    }
  });

  test('✅ 6. Test Performance et Temps de Réponse', async ({ page }) => {
    console.log('🔍 Test performance...');
    
    const performanceTests = [
      { url: '/auth/login', maxTime: 5000, name: 'Login' },
      { url: '/test-phase4-supabase', maxTime: 8000, name: 'Test Phase 4' },
      { url: '/dashboard', maxTime: 6000, name: 'Dashboard' },
    ];
    
    for (const perfTest of performanceTests) {
      const startTime = Date.now();
      await page.goto(`http://localhost:3000${perfTest.url}`);
      await page.waitForSelector('body', { timeout: perfTest.maxTime });
      const loadTime = Date.now() - startTime;
      
      console.log(`   ⏱️ ${perfTest.name}: ${loadTime}ms`);
      expect(loadTime).toBeLessThan(perfTest.maxTime);
    }
  });

  test('✅ 7. Validation Responsive Design', async ({ page }) => {
    console.log('🔍 Test responsive design...');
    
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' },
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.goto('http://localhost:3000/test-phase4-supabase');
      await page.waitForSelector('h1', { timeout: 5000 });
      
      // Vérifier que le contenu est visible
      const h1Visible = await page.isVisible('h1');
      expect(h1Visible).toBe(true);
      
      console.log(`   📱 ${viewport.name} (${viewport.width}x${viewport.height}): ✅`);
    }
  });

  test('✅ 8. Test Sécurité et Gestion d\'Erreurs', async ({ page }) => {
    console.log('🔍 Test sécurité...');
    
    // Test des pages protégées sans authentification
    const protectedPages = ['/dashboard', '/journal', '/profile'];
    
    for (const protectedPage of protectedPages) {
      await page.goto(`http://localhost:3000${protectedPage}`);
      await page.waitForTimeout(2000);
      
      const currentUrl = page.url();
      const bodyText = await page.textContent('body');
      
      // Vérifier redirection vers login ou message d'erreur approprié
      const isProtected = currentUrl.includes('/auth/login') || 
                         bodyText?.includes('connecter') ||
                         bodyText?.includes('authentification');
      
      console.log(`   🔒 ${protectedPage}: ${isProtected ? 'Protégé' : 'Accessible'}`);
    }
  });

  test('✅ 9. Validation Console Errors', async ({ page }) => {
    console.log('🔍 Test erreurs console...');
    
    const consoleErrors: string[] = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.goto('http://localhost:3000/test-phase4-supabase');
    await page.waitForTimeout(3000);
    
    // Filtrer les erreurs non critiques
    const criticalErrors = consoleErrors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('sw.js') &&
      !error.includes('Console Ninja')
    );
    
    console.log(`   🐛 Erreurs console critiques: ${criticalErrors.length}`);
    
    if (criticalErrors.length > 0) {
      console.log('   Erreurs détectées:', criticalErrors);
    }
    
    // Permettre quelques erreurs non-critiques
    expect(criticalErrors.length).toBeLessThan(3);
  });

  test('✅ 10. Rapport Final de Validation', async ({ page }) => {
    console.log('📊 Génération du rapport final...');
    
    await page.goto('http://localhost:3000/test-phase4-supabase');
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // Créer un screenshot final
    await page.screenshot({ 
      path: 'tests/validation-final-screenshot.png', 
      fullPage: true 
    });
    
    // Vérifier les éléments clés de la Phase 4
    const h1Text = await page.textContent('h1');
    const bodyText = await page.textContent('body');
    
    const phase4Indicators = [
      'Phase 4',
      'Basculement complet',
      'Supabase',
      'DB Supabase: true',
      'Auth Supabase: true',
      'Real-time: true'
    ];
    
    let validatedFeatures = 0;
    for (const indicator of phase4Indicators) {
      if (bodyText?.includes(indicator)) {
        validatedFeatures++;
        console.log(`   ✅ ${indicator}: Détecté`);
      }
    }
    
    const successRate = (validatedFeatures / phase4Indicators.length) * 100;
    console.log(`\n🎯 RAPPORT FINAL:`);
    console.log(`   📊 Taux de validation: ${successRate.toFixed(1)}%`);
    console.log(`   ✅ Fonctionnalités validées: ${validatedFeatures}/${phase4Indicators.length}`);
    
    if (successRate >= 80) {
      console.log(`   🎉 PHASE 4 SUPABASE VALIDÉE AVEC SUCCÈS !`);
    } else {
      console.log(`   ⚠️ Phase 4 partiellement validée - ajustements requis`);
    }
    
    expect(successRate).toBeGreaterThanOrEqual(70);
  });
});

/**
 * TESTS SPÉCIALISÉS SUPABASE
 */
test.describe('🔥 Tests Spécialisés Supabase', () => {
  
  test('✅ Validation directe Supabase API', async ({ page }) => {
    console.log('🔍 Test direct API Supabase...');
    
    // Test de l'API Supabase via JavaScript
    await page.goto('http://localhost:3000');
    
    const supabaseTest = await page.evaluate(async () => {
      try {
        // Simuler un test de connexion Supabase
        const response = await fetch('https://kvdrukmoxetoiojazukf.supabase.co/rest/v1/', {
          headers: {
            'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
          }
        });
        
        return {
          status: response.status,
          ok: response.ok,
          url: response.url
        };
      } catch (error) {
        return {
          error: error.message,
          status: 0
        };
      }
    });
    
    console.log('   🔗 Connexion Supabase:', supabaseTest);
    expect(supabaseTest.status).toBe(200);
  });
}); 