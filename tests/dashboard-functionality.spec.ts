import { test, expect, Page } from '@playwright/test';

/**
 * Tests de validation du Dashboard MindFlow Pro
 * Vérification des composants et interactions
 */

test.describe('Validation Dashboard MindFlow Pro', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    
    // Intercepter les erreurs de console
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Erreur console:', msg.text());
      }
    });
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('Navigation vers le Dashboard', async () => {
    // Tenter d'accéder au dashboard (peut rediriger vers login)
    await page.goto('/dashboard');
    
    // Vérifier si on est redirigé vers login ou si le dashboard se charge
    const currentUrl = page.url();
    console.log('URL actuelle:', currentUrl);
    
    if (currentUrl.includes('/auth/login')) {
      console.log('✅ Redirection vers login détectée (sécurité OK)');
      await expect(page.locator('input[type="email"]')).toBeVisible();
    } else if (currentUrl.includes('/dashboard')) {
      console.log('✅ Dashboard accessible directement');
      await expect(page.locator('h1, h2, h3')).toBeVisible();
    }
    
    await page.screenshot({ path: 'test-results/dashboard-navigation.png' });
  });

  test('Structure du Layout Dashboard', async () => {
    await page.goto('/dashboard');
    
    // Si redirigé vers login, on simule une connexion ou on passe le test
    if (page.url().includes('/auth/login')) {
      console.log('⚠️  Dashboard nécessite une authentification');
      return;
    }
    
    // Vérifier la structure du layout
    const layoutElements = [
      '[data-testid="dashboard-layout"]',
      '[data-testid="sidebar"]', 
      '[data-testid="main-content"]',
      // Fallback si les test-ids ne sont pas présents
      'nav, aside, .sidebar',
      'main, .main-content, .dashboard-content'
    ];
    
    let layoutFound = false;
    for (const selector of layoutElements) {
      try {
        const element = page.locator(selector).first();
        if (await element.isVisible()) {
          console.log(`✅ Élément de layout trouvé: ${selector}`);
          layoutFound = true;
          break;
        }
      } catch (e) {
        // Continue avec le prochain sélecteur
      }
    }
    
    if (!layoutFound) {
      console.log('⚠️  Structure de layout à vérifier manuellement');
    }
    
    await page.screenshot({ path: 'test-results/dashboard-layout.png' });
  });

  test('Composants Dashboard - Chargement', async () => {
    await page.goto('/dashboard');
    
    if (page.url().includes('/auth/login')) {
      console.log('⚠️  Test nécessite une authentification');
      return;
    }
    
    // Attendre le chargement des composants
    await page.waitForTimeout(3000);
    
    // Rechercher les composants du dashboard
    const dashboardComponents = [
      'WelcomeHeader',
      'WellnessStats', 
      'QuickActions',
      'CurrentObjectives',
      'SmartSuggestions',
      'AICoachQuote',
      'RecentActivity'
    ];
    
    for (const component of dashboardComponents) {
      // Rechercher par attribut data-component ou par classe
      const selectors = [
        `[data-component="${component}"]`,
        `.${component.toLowerCase()}`,
        `[class*="${component}"]`
      ];
      
      let componentFound = false;
      for (const selector of selectors) {
        try {
          const element = page.locator(selector).first();
          if (await element.isVisible()) {
            console.log(`✅ Composant ${component} trouvé`);
            componentFound = true;
            break;
          }
        } catch (e) {
          // Continue
        }
      }
      
      if (!componentFound) {
        console.log(`⚠️  Composant ${component} non détecté visuellement`);
      }
    }
    
    await page.screenshot({ path: 'test-results/dashboard-components.png' });
  });

  test('Interactions Dashboard - Actions rapides', async () => {
    await page.goto('/dashboard');
    
    if (page.url().includes('/auth/login')) {
      console.log('⚠️  Test nécessite une authentification');
      return;
    }
    
    await page.waitForTimeout(2000);
    
    // Rechercher les boutons d'action
    const actionButtons = page.locator('button, a[role="button"], .btn, [class*="button"]');
    const buttonCount = await actionButtons.count();
    
    console.log(`🔘 ${buttonCount} boutons/actions détectés sur le dashboard`);
    
    if (buttonCount > 0) {
      // Tester l'interactivité du premier bouton
      const firstButton = actionButtons.first();
      const buttonText = await firstButton.textContent();
      console.log(`🎯 Test d'interaction avec: "${buttonText}"`);
      
      // Vérifier que le bouton est cliquable
      await expect(firstButton).toBeEnabled();
      
      // Clic de test (sans navigation)
      await firstButton.hover();
      console.log('✅ Bouton responsive au hover');
    }
    
    await page.screenshot({ path: 'test-results/dashboard-interactions.png' });
  });

  test('Responsive Design - Mobile Dashboard', async () => {
    // Simuler un appareil mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/dashboard');
    
    if (page.url().includes('/auth/login')) {
      console.log('⚠️  Test mobile nécessite une authentification');
      return;
    }
    
    await page.waitForTimeout(2000);
    
    // Vérifier que le contenu s'adapte
    const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
    console.log(`📱 Largeur du contenu en mobile: ${bodyWidth}px`);
    
    // Le contenu ne devrait pas déborder
    expect(bodyWidth).toBeLessThanOrEqual(375);
    
    await page.screenshot({ path: 'test-results/dashboard-mobile.png' });
  });

  test('Performance Dashboard - Temps de chargement', async () => {
    const startTime = Date.now();
    
    await page.goto('/dashboard');
    
    // Attendre que le contenu principal soit chargé
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    console.log(`⏱️  Dashboard chargé en ${loadTime}ms`);
    
    // Le dashboard devrait se charger en moins de 10 secondes
    expect(loadTime).toBeLessThan(10000);
    
    // Vérifier qu'il n'y a pas de requêtes qui échouent
    const failedRequests: string[] = [];
    page.on('response', response => {
      if (response.status() >= 400) {
        failedRequests.push(`${response.url()} - ${response.status()}`);
      }
    });
    
    await page.waitForTimeout(3000);
    
    if (failedRequests.length > 0) {
      console.log('❌ Requêtes échouées:', failedRequests);
    } else {
      console.log('✅ Aucune requête échouée détectée');
    }
  });
}); 