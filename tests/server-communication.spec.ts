import { test, expect, Page } from '@playwright/test';

/**
 * Tests de validation de la communication Frontend-Backend
 * MindFlow Pro - Plateforme de santé mentale
 */

test.describe('Validation Communication Serveurs MindFlow Pro', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    // Configuration des interceptors de réseau
    await page.route('**/*', route => {
      console.log(`Requête: ${route.request().method()} ${route.request().url()}`);
      route.continue();
    });
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('Validation Backend - Health Check API', async () => {
    // Test direct de l'API backend
    const response = await page.request.get('http://localhost:4000/api/v1/health');
    
    expect(response.status()).toBe(200);
    
    const healthData = await response.json();
    expect(healthData).toHaveProperty('status', 'OK');
    expect(healthData).toHaveProperty('timestamp');
    expect(healthData).toHaveProperty('services');
    
    console.log('✅ Backend Health Check:', healthData);
  });

  test('Validation Frontend - Page d\'accueil accessible', async () => {
    await page.goto('/');
    
    // Vérifier que la page se charge
    await expect(page).toHaveTitle(/MindFlow Pro/);
    
    // Vérifier les éléments clés
    await expect(page.locator('h1')).toBeVisible();
    
    // Prendre une capture d'écran
    await page.screenshot({ path: 'test-results/homepage.png' });
    
    console.log('✅ Frontend - Page d\'accueil chargée');
  });

  test('Validation Frontend - Navigation vers Login', async () => {
    await page.goto('/');
    
    // Naviguer vers la page de connexion
    await page.goto('/auth/login');
    
    // Vérifier les éléments du formulaire de connexion
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    await page.screenshot({ path: 'test-results/login-page.png' });
    
    console.log('✅ Frontend - Page de connexion fonctionnelle');
  });

  test('Communication Frontend-Backend - Tentative de connexion', async () => {
    await page.goto('/auth/login');
    
    // Intercepter les requêtes vers le backend
    let authRequestMade = false;
    await page.route('**/api/v1/auth/**', route => {
      authRequestMade = true;
      console.log('🔗 Requête d\'authentification interceptée:', route.request().url());
      route.continue();
    });
    
    // Remplir le formulaire (avec des données de test)
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword123');
    
    // Soumettre le formulaire
    await page.click('button[type="submit"]');
    
    // Attendre la réponse réseau
    await page.waitForTimeout(2000);
    
    // Vérifier qu'une requête a été faite vers le backend
    if (authRequestMade) {
      console.log('✅ Communication Frontend-Backend établie');
    } else {
      console.log('❌ Aucune communication détectée avec le backend');
    }
    
    await page.screenshot({ path: 'test-results/login-attempt.png' });
  });

  test('Validation des APIs Backend principales', async () => {
    const endpoints = [
      '/api/v1/health',
      '/api/v1/auth/status',
    ];
    
    for (const endpoint of endpoints) {
      const response = await page.request.get(`http://localhost:4000${endpoint}`);
      console.log(`API ${endpoint}: Status ${response.status()}`);
      
      // La plupart des endpoints nécessitent une authentification
      expect([200, 401, 403]).toContain(response.status());
    }
  });

  test('Test de charge - Pages principales', async () => {
    const pages = [
      '/',
      '/auth/login',
      '/auth/register',
    ];
    
    for (const pagePath of pages) {
      const startTime = Date.now();
      await page.goto(pagePath);
      const loadTime = Date.now() - startTime;
      
      console.log(`⏱️  Page ${pagePath} chargée en ${loadTime}ms`);
      expect(loadTime).toBeLessThan(5000); // Moins de 5 secondes
      
      // Vérifier qu'il n'y a pas d'erreurs JavaScript
      const errors: string[] = [];
      page.on('pageerror', error => errors.push(error.toString()));
      
      if (errors.length > 0) {
        console.log('❌ Erreurs JavaScript détectées:', errors);
      }
    }
  });
}); 