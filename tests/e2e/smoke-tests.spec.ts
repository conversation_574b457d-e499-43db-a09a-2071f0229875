import { test, expect } from '@playwright/test';

/**
 * 🚨 SMOKE TESTS - MINDFLOW PRO
 * Tests rapides post-déploiement pour vérifier que l'application fonctionne
 */

const PRODUCTION_URL = 'https://mindflow-pro.vercel.app';

test.describe('🚨 Smoke Tests - Production', () => {
  
  test('✅ Homepage loads correctly', async ({ page }) => {
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Vérifier que la page se charge
    await expect(page).toHaveTitle(/MindFlow/);
    
    // Vérifier qu'il n'y a pas d'erreurs critiques
    const hasError = await page.locator('.error, [data-testid="error"]').count();
    expect(hasError).toBe(0);
  });

  test('✅ Dashboard is accessible', async ({ page }) => {
    await page.goto(`${PRODUCTION_URL}/dashboard`);
    await page.waitForLoadState('networkidle');
    
    // Vérifier que le dashboard se charge
    const dashboardContent = await page.textContent('body');
    expect(dashboardContent).toContain('Dashboard');
  });

  test('✅ ML Analytics loads (Phase 9)', async ({ page }) => {
    await page.goto(`${PRODUCTION_URL}/ml-analytics`);
    await page.waitForLoadState('networkidle');
    
    // Vérifier que la page ML Analytics se charge
    const mlContent = await page.textContent('body');
    expect(mlContent).toMatch(/Analytics|Prédictifs|ML/);
  });

  test('✅ API Health Check', async ({ page }) => {
    const response = await page.request.get(`${PRODUCTION_URL}/api/health/supabase`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('status');
  });

  test('✅ Critical pages are accessible', async ({ page }) => {
    const criticalPages = [
      '/journal',
      '/ai-coach',
      '/analytics',
      '/appointments',
      '/telemedicine',
      '/compliance',
      '/integrations-b2b'
    ];
    
    for (const pagePath of criticalPages) {
      await page.goto(`${PRODUCTION_URL}${pagePath}`);
      await page.waitForTimeout(2000);
      
      // Vérifier que la page ne retourne pas d'erreur 500
      const content = await page.textContent('body');
      expect(content).not.toContain('500');
      expect(content).not.toContain('Internal Server Error');
    }
  });
});
