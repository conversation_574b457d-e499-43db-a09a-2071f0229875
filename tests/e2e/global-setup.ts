import { chromium, FullConfig } from '@playwright/test';

/**
 * 🚀 SETUP GLOBAL - TESTS MINDFLOW PRO
 * Configuration de l'environnement de test avant l'exécution
 */

async function globalSetup(config: FullConfig) {
  console.log('🔧 Initialisation des tests MindFlow Pro...');
  
  // Attendre que les serveurs soient prêts
  await waitForServers();
  
  // Créer le navigateur pour l'authentification
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    console.log('🔐 Configuration de l\'authentification de test...');
    
    // Aller sur la page de connexion
    await page.goto('http://localhost:3000/auth/login');
    await page.waitForLoadState('networkidle');
    
    // Créer un état d'authentification de test
    await page.context().addCookies([
      {
        name: 'mindflow-test-auth',
        value: 'authenticated',
        domain: 'localhost',
        path: '/'
      }
    ]);
    
    // Sauvegarder l'état d'authentification
    await page.context().storageState({ 
      path: 'tests/e2e/auth-state.json' 
    });
    
    console.log('✅ Configuration terminée avec succès');
    
  } catch (error) {
    console.error('❌ Erreur lors du setup:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

/**
 * Attendre que les serveurs soient disponibles
 */
async function waitForServers() {
  const servers = [
    { url: 'http://localhost:3000', name: 'Frontend Next.js' }
  ];
  
  for (const server of servers) {
    console.log(`⏳ Attente du serveur ${server.name}...`);
    
    const maxAttempts = 30;
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      try {
        const response = await fetch(server.url);
        if (response.ok || response.status === 404) {
          console.log(`✅ ${server.name} est disponible`);
          break;
        }
      } catch (error) {
        // Serveur pas encore prêt
      }
      
      attempts++;
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (attempts >= maxAttempts) {
        throw new Error(`Timeout: ${server.name} non disponible après ${maxAttempts * 2}s`);
      }
    }
  }
}

export default globalSetup;
