import { FullConfig } from '@playwright/test';

/**
 * 🧹 TEARDOWN GLOBAL - TESTS MINDFLOW PRO
 * Nettoyage après l'exécution des tests
 */

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Nettoyage après tests MindFlow Pro...');
  
  try {
    // Nettoyer les fichiers temporaires
    const fs = require('fs');
    const path = require('path');
    
    const tempFiles = [
      'tests/e2e/auth-state.json',
      'test-results/temp-data.json'
    ];
    
    for (const file of tempFiles) {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        console.log(`��️ Supprimé: ${file}`);
      }
    }
    
    console.log('✅ Nettoyage terminé avec succès');
    
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
    // Ne pas faire échouer si le nettoyage échoue
  }
}

export default globalTeardown;
