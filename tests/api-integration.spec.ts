import { test, expect } from '@playwright/test';

/**
 * Tests d'intégration API Backend MindFlow Pro
 * Validation des endpoints et communication
 */

test.describe('Intégration API Backend MindFlow Pro', () => {
  const BASE_API_URL = 'http://localhost:4000/api/v1';
  
  test('API Health Check - Validation complète', async ({ request }) => {
    const response = await request.get(`${BASE_API_URL}/health`);
    
    expect(response.status()).toBe(200);
    
    const healthData = await response.json();
    console.log('📊 Health Check Response:', JSON.stringify(healthData, null, 2));
    
    // Vérifications détaillées
    expect(healthData).toHaveProperty('status');
    expect(healthData).toHaveProperty('timestamp');
    expect(healthData.status).toBe('OK');
    
    if (healthData.services) {
      console.log('🔧 Services disponibles:', Object.keys(healthData.services));
    }
  });

  test('API Authentication - Endpoints disponibles', async ({ request }) => {
    const authEndpoints = [
      '/auth/status',
      '/auth/login',
      '/auth/register',
      '/auth/logout',
      '/auth/refresh'
    ];
    
    for (const endpoint of authEndpoints) {
      try {
        const response = await request.get(`${BASE_API_URL}${endpoint}`);
        const status = response.status();
        
        console.log(`🔐 ${endpoint}: ${status}`);
        
        // Ces endpoints peuvent retourner 401 (non autorisé) ou 405 (méthode non autorisée)
        // Ce qui est normal sans authentification
        expect([200, 401, 403, 404, 405]).toContain(status);
        
      } catch (error) {
        console.log(`❌ Erreur sur ${endpoint}:`, error);
      }
    }
  });

  test('API Authentification - Tentative de login', async ({ request }) => {
    const loginData = {
      email: '<EMAIL>',
      password: 'testpassword123'
    };
    
    try {
      const response = await request.post(`${BASE_API_URL}/auth/login`, {
        data: loginData,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const status = response.status();
      console.log(`🔑 Tentative de login: ${status}`);
      
      if (status === 200) {
        const authResponse = await response.json();
        console.log('✅ Login réussi, token reçu');
        expect(authResponse).toHaveProperty('token');
      } else if (status === 401) {
        console.log('🔒 Login échoué - Credentials invalides (normal pour test)');
      } else if (status === 404) {
        console.log('🚫 Endpoint de login non trouvé');
      } else {
        console.log(`⚠️  Réponse inattendue: ${status}`);
      }
      
      // On accepte différents codes de retour car on teste avec des faux credentials
      expect([200, 400, 401, 404, 422]).toContain(status);
      
    } catch (error) {
      console.log('❌ Erreur lors du test de login:', error);
    }
  });

  test('API Dashboard - Endpoints protégés', async ({ request }) => {
    const protectedEndpoints = [
      '/dashboard/stats',
      '/dashboard/activities',
      '/user/profile',
      '/ai-coach/sessions',
      '/journal/entries'
    ];
    
    for (const endpoint of protectedEndpoints) {
      try {
        const response = await request.get(`${BASE_API_URL}${endpoint}`);
        const status = response.status();
        
        console.log(`🛡️  ${endpoint}: ${status}`);
        
        // Ces endpoints devraient retourner 401 sans authentification
        expect([401, 403, 404]).toContain(status);
        
      } catch (error) {
        console.log(`❌ Erreur sur endpoint protégé ${endpoint}`);
      }
    }
  });

  test('API CORS - Configuration', async ({ request }) => {
    const response = await request.options(`${BASE_API_URL}/health`);
    
    const corsHeaders = {
      'access-control-allow-origin': response.headers()['access-control-allow-origin'],
      'access-control-allow-methods': response.headers()['access-control-allow-methods'],
      'access-control-allow-headers': response.headers()['access-control-allow-headers']
    };
    
    console.log('🌐 Headers CORS:', corsHeaders);
    
    // Vérifier que CORS est configuré
    if (corsHeaders['access-control-allow-origin']) {
      console.log('✅ CORS configuré');
    } else {
      console.log('⚠️  CORS peut nécessiter une configuration');
    }
  });

  test('API Rate Limiting - Test de charge léger', async ({ request }) => {
    const requests = [];
    const startTime = Date.now();
    
    // Faire 10 requêtes simultanées pour tester la résistance
    for (let i = 0; i < 10; i++) {
      requests.push(request.get(`${BASE_API_URL}/health`));
    }
    
    const responses = await Promise.all(requests);
    const endTime = Date.now();
    
    console.log(`⚡ 10 requêtes simultanées traitées en ${endTime - startTime}ms`);
    
    // Vérifier que toutes les requêtes ont réussi
    const successfulRequests = responses.filter(r => r.status() === 200).length;
    console.log(`✅ ${successfulRequests}/10 requêtes réussies`);
    
    expect(successfulRequests).toBeGreaterThan(8); // Au moins 80% de réussite
  });

  test('API Content-Type - Validation des headers', async ({ request }) => {
    const response = await request.get(`${BASE_API_URL}/health`);
    
    const contentType = response.headers()['content-type'];
    console.log('📝 Content-Type:', contentType);
    
    // L'API devrait retourner du JSON
    expect(contentType).toContain('application/json');
  });

  test('API Error Handling - Endpoint inexistant', async ({ request }) => {
    const response = await request.get(`${BASE_API_URL}/endpoint-inexistant`);
    
    const status = response.status();
    console.log(`🚫 Endpoint inexistant: ${status}`);
    
    // Devrait retourner 404
    expect(status).toBe(404);
    
    // Vérifier que la réponse d'erreur est en JSON
    try {
      const errorResponse = await response.json();
      console.log('📄 Réponse d\'erreur:', errorResponse);
      expect(errorResponse).toHaveProperty('error');
    } catch (e) {
      console.log('⚠️  Réponse d\'erreur non-JSON');
    }
  });

  test('Backend WebSocket - Disponibilité', async ({ request }) => {
    // Test pour vérifier si le WebSocket est disponible
    try {
      const response = await request.get('http://localhost:4000/socket.io/');
      const status = response.status();
      
      if (status === 200) {
        console.log('🔌 WebSocket endpoint disponible');
      } else {
        console.log(`🔌 WebSocket: ${status} (peut être normal)`);
      }
      
    } catch (error) {
      console.log('🔌 WebSocket non détecté ou non configuré');
    }
  });
}); 