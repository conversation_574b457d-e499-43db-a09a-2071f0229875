import { test, expect } from '@playwright/test';

/**
 * Tests de Migration Supabase
 * Valide l'architecture de migration progressive vers Supabase
 */

test.describe('Migration Supabase - Tests Complets', () => {
  
  test.beforeEach(async ({ page }) => {
    // Configuré pour intercepter les erreurs de console
    const errors: string[] = [];
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Stocker les erreurs dans le contexte du test
    (test.info() as any).errors = errors;
  });

  test('Architecture de base de données - Mode Dual', async ({ page }) => {
    await test.step('1. Navigation vers la page de diagnostic', async () => {
      await page.goto('http://localhost:3000/diagnostic-env');
      await expect(page).toHaveTitle(/MindFlow/);
    });

    await test.step('2. Vérification des variables d\'environnement', async () => {
      // Attendre le chargement complet
      await page.waitForLoadState('networkidle');
      
      // Vérifier que les variables Supabase sont configurées
      const supabaseUrl = await page.textContent('text=NEXT_PUBLIC_SUPABASE_URL');
      expect(supabaseUrl).toContain('kvdrukmoxetoiojazukf.supabase.co');
      
      const supabaseKey = await page.textContent('text=NEXT_PUBLIC_SUPABASE_ANON_KEY');
      expect(supabaseKey).toContain('Configurée');
      
      const dualMode = await page.textContent('text=NEXT_PUBLIC_DUAL_DATABASE_MODE');
      expect(dualMode).toContain('true');
    });

    await test.step('3. Vérification du mode dual activé', async () => {
      const configuredVars = await page.locator('text=VARIABLES CONFIGURÉES').count();
      expect(configuredVars).toBeGreaterThan(0);
      
      const missingVars = await page.locator('text=VARIABLES MANQUANTES').count();
      expect(missingVars).toBeGreaterThan(0);
    });
  });

  test('Intégration progressive simulée', async ({ page }) => {
    await test.step('1. Test de la page test-simple', async () => {
      await page.goto('http://localhost:3000/test-simple');
      await page.waitForLoadState('networkidle');
      
      // Vérifier que la page se charge sans erreur
      const title = await page.textContent('h1');
      expect(title).toContain('Diagnostic Supabase');
    });

    await test.step('2. Vérification des tests automatiques', async () => {
      // Attendre que les tests se lancent automatiquement
      await page.waitForTimeout(3000);
      
      // Vérifier la présence de résultats de tests
      const results = await page.locator('[data-testid="test-result"], .test-result, [style*="margin: 15px"]').count();
      expect(results).toBeGreaterThan(0);
    });

    await test.step('3. Validation des composants sans erreur', async () => {
      // Vérifier qu'aucune erreur critique n'est affichée
      const buildError = await page.locator('text=Build Error').count();
      expect(buildError).toBe(0);
      
      const failedCompile = await page.locator('text=Failed to compile').count();
      expect(failedCompile).toBe(0);
    });
  });

  test('Adaptateurs de base de données', async ({ page }) => {
    await test.step('1. Test de l\'interface DatabaseAdapter', async () => {
      await page.goto('http://localhost:3000/test-simple');
      await page.waitForLoadState('networkidle');
      
      // Attendre que les tests se terminent
      await page.waitForTimeout(5000);
      
      // Vérifier les résultats des tests d'adaptateurs
      const adaptersTest = await page.locator('text=Database Manager').first();
      await expect(adaptersTest).toBeVisible();
    });

    await test.step('2. Vérification de la connectivité Supabase', async () => {
      // Vérifier que le test de connexion Supabase est présent
      const supabaseTest = await page.locator('text=Client Supabase').first();
      await expect(supabaseTest).toBeVisible();
      
      // Vérifier qu'il n'y a pas d'erreur de connexion critique
      const connectionError = await page.locator('text=Erreur import client').count();
      expect(connectionError).toBeLessThanOrEqual(1); // Peut être en erreur en mode test
    });

    await test.step('3. Test des feature flags de migration', async () => {
      // Vérifier que les feature flags sont testés
      const featureFlagsTest = await page.locator('text=Feature Flags').first();
      await expect(featureFlagsTest).toBeVisible();
    });
  });

  test('Validation complète du système', async ({ page }) => {
    await test.step('1. Test de toutes les pages de diagnostic', async () => {
      const pages = [
        '/diagnostic-env',
        '/test-simple'
      ];
      
      for (const pagePath of pages) {
        await page.goto(`http://localhost:3000${pagePath}`);
        await page.waitForLoadState('networkidle');
        
        // Vérifier qu'aucune erreur 404 n'est affichée
        const notFound = await page.locator('text=404').count();
        expect(notFound).toBe(0);
        
        // Vérifier qu'aucune erreur de build n'est affichée
        const buildError = await page.locator('text=Build Error').count();
        expect(buildError).toBe(0);
      }
    });

    await test.step('2. Vérification de la performance', async () => {
      await page.goto('http://localhost:3000/test-simple');
      
      // Mesurer le temps de chargement
      const startTime = Date.now();
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      // Le chargement ne devrait pas prendre plus de 30 secondes
      expect(loadTime).toBeLessThan(30000);
    });

    await test.step('3. Test de la résilience aux erreurs', async () => {
      // Tester une page qui n'existe pas
      await page.goto('http://localhost:3000/page-inexistante');
      await page.waitForLoadState('networkidle');
      
      // Vérifier que la page 404 personnalisée se charge
      const content = await page.content();
      expect(content).not.toContain('Build Error');
    });

    await test.step('4. Validation finale de la configuration', async () => {
      await page.goto('http://localhost:3000/diagnostic-env');
      await page.waitForLoadState('networkidle');
      
      // Prendre une capture d'écran pour documentation
      await page.screenshot({ 
        path: 'test-results/diagnostic-final.png',
        fullPage: true 
      });
      
      // Vérifier que la configuration recommandée est affichée
      const configSection = await page.locator('text=CONFIGURATION RECOMMANDÉE').count();
      expect(configSection).toBe(1);
    });
  });

  test.afterEach(async ({ page }, testInfo) => {
    // Capturer les erreurs console si le test échoue
    if (testInfo.status === 'failed') {
      const errors = (testInfo as any).errors || [];
      if (errors.length > 0) {
        console.log('Erreurs console détectées:', errors);
        testInfo.attach('console-errors.txt', {
          body: errors.join('\n'),
          contentType: 'text/plain'
        });
      }
      
      // Prendre une capture d'écran de l'échec
      await page.screenshot({ 
        path: `test-results/failure-${testInfo.title.replace(/[^a-zA-Z0-9]/g, '-')}.png`,
        fullPage: true 
      });
    }
  });
}); 