#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSTIC COMPLET - MINDFLOW PRO MIGRATION SUPABASE');
console.log('=======================================================\n');

// Couleurs pour le terminal
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = {
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`)
};

// Tests de diagnostic
const diagnostics = {
  // 1. Vérifier la structure des dossiers
  checkFolderStructure() {
    log.info('Test 1: Structure des dossiers');
    
    const requiredPaths = [
      'frontend/src/app',
      'frontend/src/lib',
      'frontend/src/components'
    ];
    
    const conflictPaths = [
      'frontend/src/pages/index.tsx',
      'frontend/src/pages'
    ];
    
    let hasErrors = false;
    
    // Vérifier les dossiers requis
    requiredPaths.forEach(p => {
      if (fs.existsSync(p)) {
        log.success(`Dossier trouvé: ${p}`);
      } else {
        log.error(`Dossier manquant: ${p}`);
        hasErrors = true;
      }
    });
    
    // Vérifier les conflits
    conflictPaths.forEach(p => {
      if (fs.existsSync(p)) {
        log.error(`CONFLIT DÉTECTÉ: ${p} existe et entre en conflit avec App Router`);
        hasErrors = true;
        
        // Suggérer la suppression
        console.log(`   ${colors.yellow}→ Exécutez: rm -rf ${p}${colors.reset}`);
      }
    });
    
    return !hasErrors;
  },
  
  // 2. Vérifier le fichier .env.local
  checkEnvFile() {
    log.info('\nTest 2: Configuration .env.local');
    
    const envPath = 'frontend/.env.local';
    
    if (!fs.existsSync(envPath)) {
      log.error(`Fichier ${envPath} introuvable!`);
      log.warning('Création du fichier avec la configuration par défaut...');
      
      const envContent = `# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUwNDY0ODQsImV4cCI6MjA1MDYyMjQ4NH0.A0HBpJZKqQlUQLGnQK5p3FsHxJKqGZHrUHOEh_Bs2gg

# Feature Flags
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true
NEXT_PUBLIC_MIGRATE_USER_DATA=true
NEXT_PUBLIC_DEBUG_MIGRATION=true

# Development
NODE_ENV=development`;
      
      fs.writeFileSync(envPath, envContent);
      log.success('Fichier .env.local créé');
      return true;
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'NEXT_PUBLIC_DUAL_DATABASE_MODE'
    ];
    
    let missingVars = [];
    requiredVars.forEach(v => {
      if (!envContent.includes(v)) {
        missingVars.push(v);
      }
    });
    
    if (missingVars.length > 0) {
      log.error(`Variables manquantes: ${missingVars.join(', ')}`);
      return false;
    }
    
    log.success('Toutes les variables requises sont présentes');
    return true;
  },
  
  // 3. Vérifier les pages de test
  checkTestPages() {
    log.info('\nTest 3: Pages de test');
    
    const testPages = [
      'frontend/src/app/test-supabase/page.tsx',
      'frontend/src/app/test-simple/page.tsx',
      'frontend/src/app/diagnostic-env/page.tsx'
    ];
    
    let foundPages = 0;
    testPages.forEach(page => {
      if (fs.existsSync(page)) {
        log.success(`Page trouvée: ${page.split('/').pop()}`);
        foundPages++;
      } else {
        log.warning(`Page manquante: ${page.split('/').pop()}`);
      }
    });
    
    return foundPages > 0;
  },
  
  // 4. Vérifier les composants d'erreur
  checkErrorComponents() {
    log.info('\nTest 4: Composants d\'erreur Next.js');
    
    const errorComponents = [
      'frontend/src/app/error.tsx',
      'frontend/src/app/not-found.tsx'
    ];
    
    let hasAllComponents = true;
    errorComponents.forEach(comp => {
      if (fs.existsSync(comp)) {
        log.success(`Composant trouvé: ${comp.split('/').pop()}`);
      } else {
        log.warning(`Composant manquant: ${comp.split('/').pop()}`);
        hasAllComponents = false;
      }
    });
    
    return hasAllComponents;
  },
  
  // 5. Vérifier les dépendances
  checkDependencies() {
    log.info('\nTest 5: Dépendances npm');
    
    const packagePath = 'frontend/package.json';
    if (!fs.existsSync(packagePath)) {
      log.error('package.json introuvable');
      return false;
    }
    
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const requiredDeps = [
      '@supabase/supabase-js',
      '@supabase/ssr',
      'next',
      'react',
      'react-dom'
    ];
    
    const allDeps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };
    
    let missingDeps = [];
    requiredDeps.forEach(dep => {
      if (!allDeps[dep]) {
        missingDeps.push(dep);
      }
    });
    
    if (missingDeps.length > 0) {
      log.error(`Dépendances manquantes: ${missingDeps.join(', ')}`);
      return false;
    }
    
    log.success('Toutes les dépendances requises sont installées');
    return true;
  }
};

// Exécution des diagnostics
console.log('🚀 Démarrage du diagnostic...\n');

const results = {
  folderStructure: diagnostics.checkFolderStructure(),
  envFile: diagnostics.checkEnvFile(),
  testPages: diagnostics.checkTestPages(),
  errorComponents: diagnostics.checkErrorComponents(),
  dependencies: diagnostics.checkDependencies()
};

// Résumé
console.log('\n📊 RÉSUMÉ DU DIAGNOSTIC');
console.log('======================');

const passedTests = Object.values(results).filter(r => r).length;
const totalTests = Object.keys(results).length;

console.log(`\nTests réussis: ${passedTests}/${totalTests}`);

if (passedTests === totalTests) {
  log.success('\n🎉 Tous les tests sont passés! La migration peut continuer.');
} else {
  log.error('\n❌ Des problèmes ont été détectés. Corrigez-les avant de continuer.');
  
  console.log('\n📝 ACTIONS CORRECTIVES:');
  console.log('====================');
  
  if (!results.folderStructure) {
    console.log('\n1. Supprimer les conflits de routing:');
    console.log('   rm -rf frontend/src/pages');
  }
  
  if (!results.envFile) {
    console.log('\n2. Vérifier le fichier .env.local');
  }
  
  if (!results.errorComponents) {
    console.log('\n3. Les composants d\'erreur ont été créés automatiquement');
    console.log('   Redémarrez le serveur Next.js');
  }
  
  console.log('\n4. Après corrections, relancez ce diagnostic:');
  console.log('   node diagnose-mindflow.js');
}

console.log('\n🔗 URLs de test à vérifier après correction:');
console.log('   - http://localhost:3000/test-basic');
console.log('   - http://localhost:3000/test-simple');
console.log('   - http://localhost:3000/diagnostic-env');
console.log('   - http://localhost:3000/test-supabase');

process.exit(passedTests === totalTests ? 0 : 1); 