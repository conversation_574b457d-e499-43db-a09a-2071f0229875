#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const { exec } = require('child_process');

console.log('🚀 AUTOMATISATION COMPLÈTE MINDFLOW PRO - SYSTÈME RENDEZ-VOUS');
console.log('==============================================================');

async function runCompleteAutomation() {
  try {
    const supabase = createClient(
      'https://kvdrukmoxetoiojazukf.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
    );

    console.log('🔍 PHASE 1: Vérification de l\'état actuel...');
    
    // Test initial
    const { data: testData, error: testError } = await supabase
      .from('professionals')
      .select('id')
      .limit(1);

    if (testError && testError.message.includes('does not exist')) {
      console.log('❌ Tables non créées');
      console.log('\n📋 INSTRUCTIONS MANUELLES REQUISES:');
      console.log('1. Allez sur: https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf/sql');
      console.log('2. Copiez le contenu de: supabase-sql-to-execute.sql');
      console.log('3. Collez dans l\'éditeur SQL et cliquez "Run"');
      console.log('4. Relancez: node automation-complete.js');
      
      // Ouvrir automatiquement
      try {
        exec('open https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf/sql');
        console.log('\n🌐 Interface Supabase ouverte automatiquement');
      } catch (e) {}
      
      return;
    }

    console.log('✅ Tables détectées!');
    
    console.log('\n🔍 PHASE 2: Vérification des données...');
    
    const { count: profCount } = await supabase
      .from('professionals')
      .select('*', { count: 'exact', head: true });
    
    const { count: aptCount } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true });

    console.log('Professionnels: ' + (profCount || 0));
    console.log('Rendez-vous: ' + (aptCount || 0));

    if (profCount === 0) {
      console.log('\n📊 PHASE 3: Insertion des données de test...');
      
      const professionals = [
        {
          name: 'Dr. Sophie Martin',
          role: 'Psychologue clinicienne',
          email: '<EMAIL>',
          specialties: ['Thérapie cognitive comportementale', 'Gestion du stress'],
          price_per_session: 85.00,
          location: 'Paris',
          bio: 'Spécialisée en thérapie cognitive comportementale.'
        },
        {
          name: 'Dr. Jean Dupont',
          role: 'Psychiatre',
          email: '<EMAIL>',
          specialties: ['Psychiatrie générale', 'Troubles bipolaires'],
          price_per_session: 120.00,
          location: 'Lyon',
          bio: 'Psychiatre expérimenté.'
        },
        {
          name: 'Marie Leblanc',
          role: 'Thérapeute',
          email: '<EMAIL>',
          specialties: ['Thérapie comportementale', 'Phobies'],
          price_per_session: 75.00,
          location: 'Lyon',
          bio: 'Thérapeute spécialisée.'
        }
      ];

      const { data: insertedProfs, error: profError } = await supabase
        .from('professionals')
        .upsert(professionals, { onConflict: 'email' })
        .select();

      if (profError) throw profError;
      console.log('✅ ' + insertedProfs.length + ' professionnels insérés');

      // Créer rendez-vous
      const appointments = [];
      const today = new Date();

      insertedProfs.forEach((prof, index) => {
        const futureDate = new Date(today);
        futureDate.setDate(today.getDate() + index + 1);

        appointments.push({
          professional_id: prof.id,
          professional_name: prof.name,
          professional_role: prof.role,
          client_id: 'demo-user-' + (index + 1),
          client_name: 'Client Démo ' + (index + 1),
          appointment_date: futureDate.toISOString().split('T')[0],
          appointment_time: '14:00',
          duration_minutes: 60,
          type: 'video',
          status: 'scheduled',
          price: prof.price_per_session,
          currency: 'EUR',
          notes: 'Rendez-vous avec ' + prof.name,
          meeting_link: 'https://meet.mindflow.com/' + prof.id,
          reminder_sent: false
        });
      });

      const { data: insertedApts, error: aptError } = await supabase
        .from('appointments')
        .insert(appointments)
        .select();

      if (aptError) throw aptError;
      console.log('✅ ' + insertedApts.length + ' rendez-vous insérés');
    } else {
      console.log('✅ Données déjà présentes');
    }

    console.log('\n🧪 PHASE 4: Tests de validation...');
    
    // Test du hook React
    console.log('Test hook React...');
    const { data: hookTest, error: hookError } = await supabase
      .from('appointments')
      .select('id, professional_name, appointment_date, status')
      .limit(3);

    if (hookError) {
      console.log('❌ Erreur hook:', hookError.message);
    } else {
      console.log('✅ Hook fonctionnel: ' + hookTest.length + ' rendez-vous');
    }

    console.log('\n🎉 AUTOMATISATION TERMINÉE AVEC SUCCÈS !');
    console.log('===========================================');
    
    console.log('\n📊 RÉSUMÉ:');
    const { count: finalProfCount } = await supabase
      .from('professionals')
      .select('*', { count: 'exact', head: true });
    
    const { count: finalAptCount } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true });

    console.log('👥 Professionnels: ' + (finalProfCount || 0));
    console.log('📅 Rendez-vous: ' + (finalAptCount || 0));
    console.log('🗄️  Base de données: Supabase (Production)');
    console.log('🔗 Hook: useAppointmentsSupabase.ts');
    
    console.log('\n🚀 PROCHAINES ÉTAPES:');
    console.log('1. 🧪 Tester: http://localhost:3001/test-appointments-supabase');
    console.log('2. 🔧 Intégrer dans /appointments');
    console.log('3. 📱 Tester l\'interface complète');
    console.log('4. 🌐 Déployer en production');

    console.log('\n📱 COMMANDES UTILES:');
    console.log('• Test validation: node validate-supabase-setup.js');
    console.log('• Serveur dev: npm run dev (port 3001)');
    console.log('• Dashboard: https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf');

  } catch (error) {
    console.error('\n💥 Erreur d\'automatisation:', error.message);
    console.log('\n🔧 Actions de dépannage:');
    console.log('1. Vérifier la connexion Internet');
    console.log('2. Vérifier les clés Supabase');
    console.log('3. Réessayer l\'automatisation');
  }
}

runCompleteAutomation();
