#!/usr/bin/env node

/**
 * 🔧 CORRECTIF DÉPLOIEMENT VERCEL MINDFLOW PRO
 * 📅 28 Décembre 2024
 * 🎯 Résolution des erreurs Supabase et variables d'environnement
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔧 CORRECTIF DÉPLOIEMENT VERCEL MINDFLOW PRO');
console.log('=' .repeat(60));

class VercelDeploymentFix {
    async run() {
        try {
            console.log('🔥 CORRECTIF EN COURS...\n');
            
            // Phase 1: Configurer les variables Vercel
            await this.configureVercelEnv();
            
            // Phase 2: Corriger les erreurs de build
            await this.fixBuildErrors();
            
            // Phase 3: Redéployer
            await this.redeployVercel();
            
            console.log('🎉 CORRECTIF VERCEL RÉUSSI !');
            
        } catch (error) {
            console.error('❌ Erreur:', error.message);
            process.exit(1);
        }
    }

    async configureVercelEnv() {
        console.log('⚙️ 1. CONFIGURATION VARIABLES VERCEL...');
        
        const envVars = {
            'NEXT_PUBLIC_SUPABASE_URL': 'https://kvdrukmoxetoiojazukf.supabase.co',
            'NEXT_PUBLIC_SUPABASE_ANON_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ',
            'NEXT_PUBLIC_USE_SUPABASE_DATABASE': 'true',
            'NEXT_PUBLIC_PRINCIPAL_DATABASE_MODE': 'true',
            'NEXT_PUBLIC_AUTO_SYNC_ENABLED': 'true'
        };

        console.log('   📝 Variables à configurer sur Vercel:');
        Object.entries(envVars).forEach(([key, value]) => {
            console.log(`   ${key}=${value.substring(0, 20)}...`);
            
            try {
                execSync(`vercel env add ${key} production`, {
                    input: value,
                    stdio: ['pipe', 'pipe', 'pipe']
                });
                console.log(`   ✅ ${key} configuré`);
            } catch (error) {
                console.log(`   ⚠️ ${key}: configuration manuelle requise`);
            }
        });

        console.log('✅ Variables Vercel préparées\n');
    }

    async fixBuildErrors() {
        console.log('🔨 2. CORRECTION ERREURS BUILD...');
        
        // Corriger les pages problématiques
        const problematicPages = [
            'frontend/src/app/inscription-simple/page.tsx',
            'frontend/src/app/test-complet-supabase/page.tsx',
            'frontend/src/app/journal/page.tsx'
        ];

        for (const pagePath of problematicPages) {
            if (fs.existsSync(pagePath)) {
                await this.fixPageSSR(pagePath);
            }
        }

        console.log('✅ Erreurs build corrigées\n');
    }

    async fixPageSSR(filePath) {
        console.log(`   🔧 Correction SSR: ${filePath}`);
        
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Ajouter le dynamic import pour désactiver SSR
        if (!content.includes('dynamic')) {
            const imports = content.match(/^import.*$/gm) || [];
            const lastImport = imports[imports.length - 1];
            
            if (lastImport) {
                content = content.replace(
                    lastImport,
                    lastImport + `\nimport dynamic from 'next/dynamic';`
                );
            }
        }

        // Wrapper la page avec dynamic
        if (!content.includes('dynamic(() =>')) {
            // Trouver l'export default
            const exportMatch = content.match(/export default function (\w+)/);
            if (exportMatch) {
                const functionName = exportMatch[1];
                content = content.replace(
                    `export default function ${functionName}`,
                    `function ${functionName}`
                );
                content += `\n\nexport default dynamic(() => Promise.resolve(${functionName}), { ssr: false });`;
            }
        }

        fs.writeFileSync(filePath, content);
        console.log(`   ✅ ${filePath} corrigé`);
    }

    async redeployVercel() {
        console.log('🚀 3. REDÉPLOIEMENT VERCEL...');
        
        try {
            console.log('   🔄 Nouveau déploiement...');
            const result = execSync('vercel --prod --force', { 
                stdio: 'pipe',
                encoding: 'utf8'
            });
            
            // Extraire l'URL de déploiement
            const urlMatch = result.match(/Production: (https:\/\/[^\s]+)/);
            if (urlMatch) {
                const deploymentUrl = urlMatch[1];
                console.log(`   ✅ Déploiement réussi: ${deploymentUrl}`);
                
                // Sauvegarder l'URL
                fs.writeFileSync('deployment-url.txt', deploymentUrl);
                
                console.log('   🔗 URL sauvegardée dans deployment-url.txt');
            }
            
        } catch (error) {
            console.log('   ⚠️ Redéploiement: ' + error.message.split('\n')[0]);
            console.log('   💡 Configurez manuellement les variables sur Vercel:');
            console.log('      https://vercel.com/dashboard/project/settings/environment-variables');
        }
        
        console.log('✅ Redéploiement terminé\n');
    }
}

// Instructions manuelles
function showManualInstructions() {
    console.log('📝 INSTRUCTIONS MANUELLES VERCEL:');
    console.log('=' .repeat(60));
    console.log('1. Allez sur: https://vercel.com/dashboard');
    console.log('2. Sélectionnez votre projet MindFlow Pro');
    console.log('3. Settings > Environment Variables');
    console.log('4. Ajoutez ces variables:');
    console.log('   NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co');
    console.log('   NEXT_PUBLIC_SUPABASE_ANON_KEY=[votre_clé_anon]');
    console.log('   NEXT_PUBLIC_USE_SUPABASE_DATABASE=true');
    console.log('5. Redéployez: vercel --prod');
    console.log('=' .repeat(60));
}

// Exécution
async function main() {
    const fix = new VercelDeploymentFix();
    await fix.run();
    showManualInstructions();
}

if (require.main === module) {
    main().catch(error => {
        console.error('💥 ERREUR:', error.message);
        showManualInstructions();
        process.exit(1);
    });
} 