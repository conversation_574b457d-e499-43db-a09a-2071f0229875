#!/usr/bin/env node

/**
 * 🔍 VALIDATION PRODUCTION - MINDFLOW PRO
 * Vérification rapide que l'application fonctionne parfaitement
 */

const PRODUCTION_URL = 'https://mindflow-pro.vercel.app';

const colors = {
  green: '\x1b[32m', red: '\x1b[31m', yellow: '\x1b[33m',
  blue: '\x1b[34m', cyan: '\x1b[36m', bold: '\x1b[1m', reset: '\x1b[0m'
};

const log = (msg, color = colors.cyan) => console.log(`${color}${msg}${colors.reset}`);
const success = (msg) => log(`✅ ${msg}`, colors.green);
const error = (msg) => log(`❌ ${msg}`, colors.red);
const info = (msg) => log(`ℹ️ ${msg}`, colors.blue);

async function validateProduction() {
  console.log(`${colors.bold}${colors.cyan}
🔍 VALIDATION PRODUCTION MINDFLOW PRO
===================================
Phase 9 - Analytics Prédictifs ML
${colors.reset}`);

  const tests = [
    {
      name: 'Page d\'accueil',
      url: '/',
      expected: 'MindFlow'
    },
    {
      name: 'Dashboard',
      url: '/dashboard',
      expected: 'Dashboard'
    },
    {
      name: 'ML Analytics (Phase 9)',
      url: '/ml-analytics',
      expected: 'Analytics Prédictifs'
    },
    {
      name: 'Journal',
      url: '/journal',
      expected: 'Journal'
    },
    {
      name: 'IA Coach',
      url: '/ai-coach',
      expected: 'IA'
    },
    {
      name: 'API Supabase',
      url: '/api/health/supabase',
      expected: 'status'
    }
  ];

  let passed = 0;
  let total = tests.length;

  for (const test of tests) {
    try {
      info(`Test: ${test.name}...`);
      
      const response = await fetch(`${PRODUCTION_URL}${test.url}`);
      
      if (response.ok) {
        const content = await response.text();
        
        if (content.includes(test.expected)) {
          success(`${test.name}: OK`);
          passed++;
        } else {
          error(`${test.name}: Contenu inattendu`);
        }
      } else {
        error(`${test.name}: Status ${response.status}`);
      }
    } catch (err) {
      error(`${test.name}: Erreur ${err.message}`);
    }
  }

  console.log(`
${colors.bold}📊 RÉSULTATS VALIDATION PRODUCTION${colors.reset}

✅ Tests réussis: ${passed}/${total}
🎯 Score: ${Math.round((passed/total)*100)}%

${passed === total ? 
  `${colors.green}🎉 PRODUCTION PARFAITEMENT FONCTIONNELLE !${colors.reset}` : 
  `${colors.yellow}⚠️ Quelques vérifications nécessaires${colors.reset}`}

🌐 Application: ${PRODUCTION_URL}
�� ML Analytics: ${PRODUCTION_URL}/ml-analytics
📊 GitHub: https://github.com/Anderson-Archimede/MindFlow-Pro
🗄️ Supabase: https://kvdrukmoxetoiojazukf.supabase.co
`);

  return passed === total;
}

// Exécution
validateProduction().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Erreur:', error);
  process.exit(1);
});
