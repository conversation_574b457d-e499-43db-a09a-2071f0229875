-- =====================================================
-- MIGRATION COMPLÈTE MINDFLOW PRO - PHASE 1 SUPABASE
-- Migration des 4 hooks restants vers Supabase
-- =====================================================

-- Suppression des tables existantes si elles existent (développement uniquement)
DROP TABLE IF EXISTS smart_notifications CASCADE;
DROP TABLE IF EXISTS mood_analytics CASCADE;
DROP TABLE IF EXISTS ai_coaching_sessions CASCADE;
DROP TABLE IF EXISTS journal_entries CASCADE;

-- =====================================================
-- 1. TABLE JOURNAL_ENTRIES (hook useJournalData.ts)
-- =====================================================

CREATE TABLE journal_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(), -- Remplacer par auth.uid() en production
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    entry_type VARCHAR(50) DEFAULT 'daily',
    mood_level INTEGER CHECK (mood_level >= 1 AND mood_level <= 5),
    tags TEXT[] DEFAULT '{}',
    emotions TEXT[] DEFAULT '{}',
    stress_level INTEGER CHECK (stress_level >= 0 AND stress_level <= 10),
    energy_level INTEGER CHECK (energy_level >= 0 AND energy_level <= 10),
    sleep_quality INTEGER CHECK (sleep_quality >= 0 AND sleep_quality <= 10),
    gratitude_notes TEXT,
    goals TEXT,
    challenges TEXT,
    achievements TEXT,
    is_private BOOLEAN DEFAULT false,
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les performances
CREATE INDEX idx_journal_entries_user_id ON journal_entries(user_id);
CREATE INDEX idx_journal_entries_created_at ON journal_entries(created_at DESC);
CREATE INDEX idx_journal_entries_mood_level ON journal_entries(mood_level);
CREATE INDEX idx_journal_entries_entry_type ON journal_entries(entry_type);
CREATE INDEX idx_journal_entries_tags ON journal_entries USING GIN(tags);

-- RLS (Row Level Security)
ALTER TABLE journal_entries ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Journal entries are viewable by everyone" ON journal_entries FOR SELECT USING (true);
CREATE POLICY "Users can manage their journal entries" ON journal_entries FOR ALL USING (true);

-- =====================================================
-- 2. TABLE AI_COACHING_SESSIONS (hook useAICoach.ts)
-- =====================================================

CREATE TABLE ai_coaching_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused')),
    theme VARCHAR(100) NOT NULL,
    goal TEXT NOT NULL,
    messages JSONB DEFAULT '[]',
    mood_analysis JSONB DEFAULT '{}',
    stats JSONB DEFAULT '{}',
    ai_insights JSONB DEFAULT '{}',
    feedback_rating INTEGER CHECK (feedback_rating >= 1 AND feedback_rating <= 5),
    feedback_text TEXT,
    total_messages INTEGER DEFAULT 0,
    duration_minutes INTEGER DEFAULT 0,
    effectiveness_score INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les performances
CREATE INDEX idx_ai_coaching_user_id ON ai_coaching_sessions(user_id);
CREATE INDEX idx_ai_coaching_created_at ON ai_coaching_sessions(created_at DESC);
CREATE INDEX idx_ai_coaching_status ON ai_coaching_sessions(status);
CREATE INDEX idx_ai_coaching_theme ON ai_coaching_sessions(theme);

-- RLS
ALTER TABLE ai_coaching_sessions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "AI coaching sessions are viewable by everyone" ON ai_coaching_sessions FOR SELECT USING (true);
CREATE POLICY "Users can manage their own AI coaching sessions" ON ai_coaching_sessions FOR ALL USING (true);

-- =====================================================
-- 3. TABLE MOOD_ANALYTICS (hook useMoodAnalytics.ts)
-- =====================================================

CREATE TABLE mood_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    date DATE NOT NULL,
    mood DECIMAL(3,1) CHECK (mood >= 1.0 AND mood <= 10.0),
    energy DECIMAL(3,1) CHECK (energy >= 1.0 AND energy <= 10.0),
    stress DECIMAL(3,1) CHECK (stress >= 1.0 AND stress <= 10.0),
    anxiety DECIMAL(3,1) CHECK (anxiety >= 1.0 AND anxiety <= 10.0),
    sleep DECIMAL(3,1) CHECK (sleep >= 1.0 AND sleep <= 10.0),
    factors TEXT[] DEFAULT '{}',
    activities TEXT[] DEFAULT '{}',
    weather VARCHAR(50),
    notes TEXT,
    correlation_data JSONB DEFAULT '{}',
    patterns_detected JSONB DEFAULT '{}',
    ai_insights JSONB DEFAULT '{}',
    wellness_score INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les performances
CREATE INDEX idx_mood_analytics_user_id ON mood_analytics(user_id);
CREATE INDEX idx_mood_analytics_date ON mood_analytics(date DESC);
CREATE INDEX idx_mood_analytics_mood ON mood_analytics(mood);
CREATE INDEX idx_mood_analytics_factors ON mood_analytics USING GIN(factors);
CREATE UNIQUE INDEX idx_mood_analytics_user_date ON mood_analytics(user_id, date);

-- RLS
ALTER TABLE mood_analytics ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Mood analytics are viewable by everyone" ON mood_analytics FOR SELECT USING (true);
CREATE POLICY "Users can manage their own mood analytics" ON mood_analytics FOR ALL USING (true);

-- =====================================================
-- 4. TABLE SMART_NOTIFICATIONS (hook useSmartNotifications.ts)
-- =====================================================

CREATE TABLE smart_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('reminder', 'suggestion', 'achievement', 'warning', 'insight', 'emergency')),
    priority VARCHAR(10) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    action_text VARCHAR(100),
    action_url VARCHAR(500),
    ai_generated BOOLEAN DEFAULT false,
    triggers TEXT[] DEFAULT '{}',
    scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_read BOOLEAN DEFAULT false,
    is_actioned BOOLEAN DEFAULT false,
    metadata JSONB DEFAULT '{}',
    ai_insight JSONB DEFAULT '{}',
    effectiveness_score INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    actioned_at TIMESTAMP WITH TIME ZONE
);

-- Index pour les performances
CREATE INDEX idx_smart_notifications_user_id ON smart_notifications(user_id);
CREATE INDEX idx_smart_notifications_scheduled_for ON smart_notifications(scheduled_for DESC);
CREATE INDEX idx_smart_notifications_type ON smart_notifications(type);
CREATE INDEX idx_smart_notifications_priority ON smart_notifications(priority);
CREATE INDEX idx_smart_notifications_is_read ON smart_notifications(is_read);

-- RLS
ALTER TABLE smart_notifications ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Smart notifications are viewable by everyone" ON smart_notifications FOR SELECT USING (true);
CREATE POLICY "Users can manage their own smart notifications" ON smart_notifications FOR ALL USING (true);

-- =====================================================
-- DONNÉES DE DÉMONSTRATION
-- =====================================================

-- Journal Entries (5 entrées d'exemple)
INSERT INTO journal_entries (title, content, entry_type, mood_level, tags, emotions, stress_level, energy_level, sleep_quality, gratitude_notes, is_favorite) VALUES
('Premier jour de thérapie', 'Aujourd''hui j''ai commencé ma thérapie. C''était intimidant au début, mais mon thérapeute m''a mis à l''aise. Nous avons parlé de mes objectifs et de ce que j''espère accomplir.', 'daily', 4, ARRAY['thérapie', 'nouveau_début', 'espoir'], ARRAY['nerveux', 'hopeful', 'déterminé'], 6, 6, 7, 'Je suis reconnaissant d''avoir enfin pris cette décision importante pour ma santé mentale.', true),
('Méditation matinale réussie', 'J''ai réussi à méditer pendant 15 minutes ce matin sans interruption. Je me sens plus centré et prêt pour la journée.', 'reflection', 5, ARRAY['méditation', 'bien-être', 'routine'], ARRAY['calme', 'centré', 'énergique'], 3, 8, 8, 'Reconnaissant pour ce moment de paix avant une journée chargée.', false),
('Journée stressante au travail', 'Beaucoup de deadlines aujourd''hui. J''ai utilisé les techniques de respiration apprises en thérapie et ça a aidé.', 'stress_tracking', 3, ARRAY['travail', 'stress', 'techniques'], ARRAY['overwhelmed', 'stressé', 'fier'], 8, 4, 6, null, false),
('Sortie entre amis', 'Soirée géniale avec mes amis. J''avais presque oublié à quel point les connexions sociales sont importantes pour mon bien-être.', 'daily', 5, ARRAY['amis', 'social', 'joie'], ARRAY['joyeux', 'connecté', 'reconnaissant'], 2, 9, 7, 'Reconnaissant pour mes amis qui me soutiennent dans les moments difficiles.', true),
('Réflexion sur mes progrès', 'Cela fait un mois que j''ai commencé ce parcours. Je remarque des changements positifs dans ma façon de gérer le stress.', 'reflection', 4, ARRAY['progrès', 'croissance', 'réflexion'], ARRAY['fier', 'optimiste', 'déterminé'], 4, 7, 8, 'Reconnaissant pour tous les petits pas qui mènent aux grands changements.', true);

-- AI Coaching Sessions (3 sessions d'exemple)
INSERT INTO ai_coaching_sessions (theme, goal, messages, mood_analysis, stats, status, total_messages, duration_minutes, effectiveness_score) VALUES
('gestion_stress', 'Apprendre à mieux gérer le stress quotidien', '[
    {"id": "1", "type": "ai", "content": "Bonjour ! Comment vous sentez-vous aujourd''hui ?", "timestamp": "2024-01-15T09:00:00Z"},
    {"id": "2", "type": "user", "content": "Je me sens stressé par mon travail", "timestamp": "2024-01-15T09:01:00Z", "mood": 4},
    {"id": "3", "type": "ai", "content": "Je comprends. Parlons de techniques de gestion du stress.", "timestamp": "2024-01-15T09:02:00Z"}
]', '{"initialMood": 4, "currentMood": 6, "moodTrend": "improving"}', '{"duration": 25, "messageCount": 12, "moodChanges": 2}', 'completed', 12, 25, 85),

('amélioration_humeur', 'Développer des stratégies pour améliorer mon humeur', '[
    {"id": "1", "type": "ai", "content": "Bienvenue dans votre session d''amélioration d''humeur !", "timestamp": "2024-01-16T14:00:00Z"},
    {"id": "2", "type": "user", "content": "J''ai besoin d''aide pour me sentir mieux", "timestamp": "2024-01-16T14:01:00Z", "mood": 3}
]', '{"initialMood": 3, "currentMood": 5, "moodTrend": "improving"}', '{"duration": 18, "messageCount": 8}', 'completed', 8, 18, 78),

('confiance_soi', 'Renforcer ma confiance en moi', '[
    {"id": "1", "type": "ai", "content": "Travaillons ensemble sur votre confiance en vous.", "timestamp": "2024-01-17T16:00:00Z"},
    {"id": "2", "type": "user", "content": "Je doute souvent de mes capacités", "timestamp": "2024-01-17T16:01:00Z", "mood": 4}
]', '{"initialMood": 4, "currentMood": 4, "moodTrend": "stable"}', '{"duration": 15, "messageCount": 6}', 'active', 6, 15, 72);

-- Mood Analytics (7 jours de données)
INSERT INTO mood_analytics (date, mood, energy, stress, anxiety, sleep, factors, activities, weather, wellness_score) VALUES
(CURRENT_DATE - INTERVAL '6 days', 7.2, 6.8, 4.5, 3.2, 8.0, ARRAY['travail', 'exercice'], ARRAY['méditation', 'marche'], 'ensoleillé', 72),
(CURRENT_DATE - INTERVAL '5 days', 6.5, 7.1, 5.8, 4.1, 7.5, ARRAY['stress', 'deadlines'], ARRAY['lecture'], 'nuageux', 65),
(CURRENT_DATE - INTERVAL '4 days', 8.1, 8.5, 3.2, 2.8, 8.5, ARRAY['weekend', 'repos'], ARRAY['sport', 'amis'], 'ensoleillé', 85),
(CURRENT_DATE - INTERVAL '3 days', 7.8, 7.9, 3.5, 2.9, 8.2, ARRAY['social', 'détente'], ARRAY['cinema', 'restaurant'], 'ensoleillé', 82),
(CURRENT_DATE - INTERVAL '2 days', 6.2, 6.0, 6.1, 4.8, 6.8, ARRAY['lundi', 'retour_travail'], ARRAY['méditation'], 'pluvieux', 62),
(CURRENT_DATE - INTERVAL '1 days', 7.0, 7.3, 4.8, 3.5, 7.8, ARRAY['thérapie', 'progrès'], ARRAY['journal', 'respiration'], 'nuageux', 73),
(CURRENT_DATE, 7.5, 7.7, 4.2, 3.1, 8.1, ARRAY['bien-être', 'équilibre'], ARRAY['yoga', 'lecture'], 'ensoleillé', 78);

-- Smart Notifications (5 notifications d'exemple)
INSERT INTO smart_notifications (type, priority, title, message, action_text, action_url, ai_generated, triggers, scheduled_for, is_read, metadata) VALUES
('reminder', 'medium', 'Moment de méditation', 'Il est temps pour votre session de méditation quotidienne de 10 minutes.', 'Commencer', '/meditation', true, ARRAY['scheduled_time', 'routine'], NOW() - INTERVAL '2 hours', true, '{"timeContext": "morning", "moodContext": 6.5}'),

('suggestion', 'low', 'Sortie suggérée', 'Le temps est magnifique ! Une promenade de 15 minutes pourrait améliorer votre humeur.', 'Planifier', '/activities/walk', true, ARRAY['weather', 'mood_boost'], NOW() - INTERVAL '1 hour', false, '{"weatherContext": "sunny", "energyLevel": 6.2}'),

('achievement', 'low', 'Félicitations ! 🎉', 'Vous avez maintenu votre routine de journal pendant 7 jours consécutifs !', 'Voir progrès', '/analytics', false, ARRAY['streak_milestone'], NOW() - INTERVAL '30 minutes', false, '{"streakDays": 7, "category": "journaling"}'),

('insight', 'medium', 'Pattern détecté par l''IA', 'L''IA a remarqué que votre humeur s''améliore après vos séances d''exercice. Continuez !', 'Détails', '/insights/exercise-mood', true, ARRAY['ai_pattern', 'correlation'], NOW() - INTERVAL '15 minutes', false, '{"correlationStrength": 0.78, "pattern": "exercise_mood_boost"}'),

('warning', 'high', 'Niveau de stress élevé', 'Votre niveau de stress est élevé depuis 2 jours. Prenez soin de vous.', 'Techniques anti-stress', '/stress-management', true, ARRAY['stress_threshold', 'duration'], NOW() - INTERVAL '5 minutes', false, '{"stressLevel": 7.2, "duration": "2 days", "urgency": "medium"}');

-- =====================================================
-- FONCTIONS UTILITAIRES
-- =====================================================

-- Fonction pour calculer le score de bien-être
CREATE OR REPLACE FUNCTION calculate_wellness_score(
    mood_val DECIMAL,
    energy_val DECIMAL,
    stress_val DECIMAL,
    anxiety_val DECIMAL,
    sleep_val DECIMAL
) RETURNS INTEGER AS $$
BEGIN
    RETURN CAST(
        (mood_val * 0.3 + energy_val * 0.2 + (11 - stress_val) * 0.2 + 
         (11 - anxiety_val) * 0.15 + sleep_val * 0.15) * 10 AS INTEGER
    );
END;
$$ LANGUAGE plpgsql;

-- Fonction pour mettre à jour le score de bien-être automatiquement
CREATE OR REPLACE FUNCTION update_wellness_score()
RETURNS TRIGGER AS $$
BEGIN
    NEW.wellness_score := calculate_wellness_score(
        NEW.mood, NEW.energy, NEW.stress, NEW.anxiety, NEW.sleep
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour calculer automatiquement le score de bien-être
CREATE TRIGGER trigger_update_wellness_score
    BEFORE INSERT OR UPDATE ON mood_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_wellness_score();

-- =====================================================
-- VUES POUR LES STATISTIQUES
-- =====================================================

-- Vue pour les statistiques de journal
CREATE VIEW journal_stats AS
SELECT 
    user_id,
    COUNT(*) as total_entries,
    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as entries_this_week,
    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as entries_this_month,
    AVG(mood_level) as average_mood,
    COUNT(*) FILTER (WHERE is_favorite = true) as favorite_entries
FROM journal_entries
GROUP BY user_id;

-- Vue pour les statistiques d'humeur
CREATE VIEW mood_trends AS
SELECT 
    user_id,
    date,
    mood,
    LAG(mood) OVER (PARTITION BY user_id ORDER BY date) as previous_mood,
    mood - LAG(mood) OVER (PARTITION BY user_id ORDER BY date) as mood_change
FROM mood_analytics
ORDER BY user_id, date;

COMMIT;

-- =====================================================
-- VALIDATION DES DONNÉES
-- =====================================================

-- Compter les enregistrements créés
SELECT 'journal_entries' as table_name, COUNT(*) as count FROM journal_entries
UNION ALL
SELECT 'ai_coaching_sessions', COUNT(*) FROM ai_coaching_sessions
UNION ALL
SELECT 'mood_analytics', COUNT(*) FROM mood_analytics
UNION ALL
SELECT 'smart_notifications', COUNT(*) FROM smart_notifications;

-- Afficher un échantillon de données
SELECT 'Dernières entrées journal:' as info;
SELECT title, mood_level, created_at FROM journal_entries ORDER BY created_at DESC LIMIT 3;

SELECT 'Sessions IA actives:' as info;
SELECT theme, status, total_messages FROM ai_coaching_sessions WHERE status = 'active';

SELECT 'Statistiques d''humeur récentes:' as info;
SELECT date, mood, energy, wellness_score FROM mood_analytics ORDER BY date DESC LIMIT 3;

SELECT 'Notifications non lues:' as info;
SELECT type, title, priority FROM smart_notifications WHERE is_read = false; 