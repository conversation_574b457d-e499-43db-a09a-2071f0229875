#!/usr/bin/env node

/**
 * 🔍 DIAGNOSTIC COMPLET SUPABASE - MindFlow Pro
 * ============================================
 * Ce script identifie et corrige les problèmes de connectivité Supabase
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSTIC COMPLET SUPABASE - MindFlow Pro');
console.log('='.repeat(60));

// Configuration actuelle (à vérifier)
const CURRENT_CONFIG = {
  url: 'https://kvdrukmoxetoiojazukf.supabase.co',
  anon_key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzODE0ODEsImV4cCI6MjA1MDk1NzQ4MX0.Mu8Wao-8lGO2PkrTHQgPIhzQNHJ9Dtu4bhALCRNq6bw',
  project_id: 'kvdrukmoxetoiojazukf'
};

/**
 * Test 1: Vérification de l'URL Supabase
 */
function testSupabaseURL() {
  return new Promise((resolve) => {
    console.log('\n🌐 TEST 1: Vérification URL Supabase...');
    
    const url = CURRENT_CONFIG.url;
    
    https.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ URL Supabase accessible');
          console.log(`   Status: ${res.statusCode}`);
          resolve({ success: true, url, status: res.statusCode });
        } else {
          console.log('❌ URL Supabase inaccessible');
          console.log(`   Status: ${res.statusCode}`);
          resolve({ success: false, url, status: res.statusCode, error: 'HTTP Error' });
        }
      });
    }).on('error', (err) => {
      console.log('❌ Erreur de connexion à l\'URL Supabase');
      console.log(`   Erreur: ${err.message}`);
      resolve({ success: false, url, error: err.message });
    });
  });
}

/**
 * Test 2: Test API Key avec endpoint /rest/v1/
 */
function testAPIKey() {
  return new Promise((resolve) => {
    console.log('\n🔑 TEST 2: Validation de la clé API...');
    
    const url = `${CURRENT_CONFIG.url}/rest/v1/`;
    const options = {
      method: 'GET',
      headers: {
        'apikey': CURRENT_CONFIG.anon_key,
        'Authorization': `Bearer ${CURRENT_CONFIG.anon_key}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200 || res.statusCode === 404) {
            console.log('✅ Clé API valide');
            console.log(`   Status: ${res.statusCode}`);
            resolve({ success: true, status: res.statusCode, response });
          } else {
            console.log('❌ Clé API invalide');
            console.log(`   Status: ${res.statusCode}`);
            console.log(`   Réponse: ${JSON.stringify(response, null, 2)}`);
            resolve({ success: false, status: res.statusCode, response });
          }
        } catch (err) {
          console.log('❌ Erreur de parsing de la réponse');
          console.log(`   Data: ${data}`);
          resolve({ success: false, error: 'JSON Parse Error', data });
        }
      });
    });

    req.on('error', (err) => {
      console.log('❌ Erreur de requête API');
      console.log(`   Erreur: ${err.message}`);
      resolve({ success: false, error: err.message });
    });

    req.end();
  });
}

/**
 * Test 3: Vérification des variables d'environnement
 */
function testEnvironmentVars() {
  console.log('\n🔧 TEST 3: Variables d\'environnement...');
  
  const frontendEnvPath = path.join(__dirname, 'frontend', '.env.local');
  
  if (fs.existsSync(frontendEnvPath)) {
    console.log('✅ Fichier .env.local existe');
    try {
      const envContent = fs.readFileSync(frontendEnvPath, 'utf8');
      console.log('📄 Contenu du fichier .env.local:');
      console.log(envContent);
      
      // Vérification des variables requises
      const requiredVars = [
        'NEXT_PUBLIC_SUPABASE_URL',
        'NEXT_PUBLIC_SUPABASE_ANON_KEY'
      ];
      
      const missingVars = requiredVars.filter(varName => 
        !envContent.includes(varName)
      );
      
      if (missingVars.length === 0) {
        console.log('✅ Toutes les variables requises sont présentes');
        return { success: true, envExists: true, content: envContent };
      } else {
        console.log('❌ Variables manquantes:', missingVars);
        return { success: false, envExists: true, missingVars };
      }
    } catch (err) {
      console.log('❌ Erreur lecture .env.local:', err.message);
      return { success: false, envExists: true, error: err.message };
    }
  } else {
    console.log('❌ Fichier .env.local manquant');
    return { success: false, envExists: false };
  }
}

/**
 * Test 4: Vérification de la configuration Next.js
 */
function testNextJSConfig() {
  console.log('\n⚙️ TEST 4: Configuration Next.js...');
  
  const nextConfigPath = path.join(__dirname, 'frontend', 'next.config.js');
  
  if (fs.existsSync(nextConfigPath)) {
    console.log('✅ Fichier next.config.js existe');
    try {
      const configContent = fs.readFileSync(nextConfigPath, 'utf8');
      console.log('📄 Configuration actuelle:', configContent.substring(0, 500) + '...');
      return { success: true, configExists: true, content: configContent };
    } catch (err) {
      console.log('❌ Erreur lecture next.config.js:', err.message);
      return { success: false, configExists: true, error: err.message };
    }
  } else {
    console.log('❌ Fichier next.config.js manquant');
    return { success: false, configExists: false };
  }
}

/**
 * Test 5: Validation des pages de test
 */
function testPages() {
  console.log('\n📄 TEST 5: Pages de test...');
  
  const testPages = [
    'frontend/src/app/ultra-simple/page.tsx',
    'frontend/src/app/test-basic/page.tsx',
    'frontend/src/app/test-supabase-simple/page.tsx',
    'frontend/src/app/test-connectivite/page.tsx'
  ];
  
  const results = {};
  
  testPages.forEach(pagePath => {
    const fullPath = path.join(__dirname, pagePath);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${pagePath} existe`);
      results[pagePath] = { exists: true };
    } else {
      console.log(`❌ ${pagePath} manquant`);
      results[pagePath] = { exists: false };
    }
  });
  
  return results;
}

/**
 * Test principal
 */
async function runDiagnostic() {
  const results = {
    timestamp: new Date().toISOString(),
    tests: {}
  };

  try {
    // Test 1: URL Supabase
    results.tests.supabaseURL = await testSupabaseURL();
    
    // Test 2: API Key
    results.tests.apiKey = await testAPIKey();
    
    // Test 3: Variables d'environnement
    results.tests.envVars = testEnvironmentVars();
    
    // Test 4: Configuration Next.js
    results.tests.nextConfig = testNextJSConfig();
    
    // Test 5: Pages de test
    results.tests.pages = testPages();
    
    // Résumé
    console.log('\n📋 RÉSUMÉ DU DIAGNOSTIC');
    console.log('='.repeat(40));
    
    const successes = Object.values(results.tests).filter(test => test.success).length;
    const total = Object.keys(results.tests).length;
    
    console.log(`✅ Tests réussis: ${successes}/${total}`);
    
    if (successes === total) {
      console.log('🎉 Tous les tests sont passés !');
    } else {
      console.log('❌ Certains tests ont échoué. Vérifiez les détails ci-dessus.');
      
      console.log('\n🔧 CORRECTIONS SUGGÉRÉES:');
      
      if (!results.tests.apiKey.success) {
        console.log('1. 🔑 Clé API invalide - Vous devez:');
        console.log('   - Vérifier votre dashboard Supabase');
        console.log('   - Régénérer une nouvelle clé anon si nécessaire');
        console.log('   - Mettre à jour le .env.local');
      }
      
      if (!results.tests.envVars.success) {
        console.log('2. 🔧 Fichier .env.local manquant/incorrect');
        console.log('   - Créer/corriger le fichier avec les bonnes clés');
      }
    }
    
    // Sauvegarde du rapport
    const reportPath = path.join(__dirname, 'diagnostic-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    console.log(`\n📁 Rapport sauvegardé: ${reportPath}`);
    
  } catch (error) {
    console.error('❌ Erreur pendant le diagnostic:', error);
    process.exit(1);
  }
}

// Exécution
if (require.main === module) {
  runDiagnostic();
}

module.exports = { runDiagnostic }; 