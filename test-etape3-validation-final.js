#!/usr/bin/env node

/**
 * Script de validation finale pour l'Étape 3 : IA et Coaching
 * Tests complets des hooks et fonctionnalités développées
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 === VALIDATION FINALE ÉTAPE 3 : IA ET COACHING ===\n');

let score = 0;
let totalTests = 0;
const results = [];

function addResult(test, success, details) {
  totalTests++;
  if (success) score++;
  results.push({
    test,
    success,
    details,
    timestamp: new Date().toISOString()
  });
  console.log(`${success ? '✅' : '❌'} ${test}: ${details}`);
}

// Test 1: Vérification des hooks créés
console.log('📁 Test 1: Vérification des fichiers hooks...');

const hookFiles = [
  'frontend/src/hooks/useAICoach.ts',
  'frontend/src/hooks/useMoodAnalytics.ts',
  'frontend/src/hooks/useSmartNotifications.ts'
];

hookFiles.forEach(hookFile => {
  if (fs.existsSync(hookFile)) {
    const content = fs.readFileSync(hookFile, 'utf8');
    const lines = content.split('\n').length;
    addResult(
      `Hook ${path.basename(hookFile)}`,
      lines > 100,
      `${lines} lignes de code`
    );
  } else {
    addResult(
      `Hook ${path.basename(hookFile)}`,
      false,
      'Fichier manquant'
    );
  }
});

// Test 2: Vérification des pages créées/modifiées
console.log('\n📄 Test 2: Vérification des pages...');

const pageFiles = [
  'frontend/src/app/ai-coach/page.tsx',
  'frontend/src/app/analytics/page.tsx',
  'frontend/src/app/test-ai-coaching/page.tsx',
  'frontend/src/app/test-etape3-simple/page.tsx'
];

pageFiles.forEach(pageFile => {
  if (fs.existsSync(pageFile)) {
    const content = fs.readFileSync(pageFile, 'utf8');
    const hasHookImports = content.includes('useAICoach') || 
                          content.includes('useMoodAnalytics') || 
                          content.includes('useSmartNotifications');
    addResult(
      `Page ${path.basename(path.dirname(pageFile))}`,
      hasHookImports,
      hasHookImports ? 'Intègre les hooks Étape 3' : 'Pas d\'intégration détectée'
    );
  } else {
    addResult(
      `Page ${path.basename(path.dirname(pageFile))}`,
      false,
      'Fichier manquant'
    );
  }
});

// Test 3: Analyse du contenu des hooks
console.log('\n🔍 Test 3: Analyse des fonctionnalités des hooks...');

// Test useAICoach
if (fs.existsSync('frontend/src/hooks/useAICoach.ts')) {
  const aiCoachContent = fs.readFileSync('frontend/src/hooks/useAICoach.ts', 'utf8');
  
  const features = [
    { name: 'Types TypeScript', pattern: /interface.*AI.*\{/ },
    { name: 'Fonction startSession', pattern: /startSession.*=/ },
    { name: 'Fonction sendMessage', pattern: /sendMessage.*=/ },
    { name: 'Analyse sentiment', pattern: /sentiment|emotion/ },
    { name: 'Données simulées', pattern: /generateMock|mock.*data/i }
  ];
  
  features.forEach(feature => {
    const hasFeature = feature.pattern.test(aiCoachContent);
    addResult(
      `useAICoach - ${feature.name}`,
      hasFeature,
      hasFeature ? 'Implémenté' : 'Non trouvé'
    );
  });
}

// Test useMoodAnalytics
if (fs.existsSync('frontend/src/hooks/useMoodAnalytics.ts')) {
  const moodContent = fs.readFileSync('frontend/src/hooks/useMoodAnalytics.ts', 'utf8');
  
  const features = [
    { name: 'Types MoodDataPoint', pattern: /interface.*MoodDataPoint/ },
    { name: 'Calcul tendances', pattern: /calculateTrend|trend/ },
    { name: 'Prédictions IA', pattern: /prediction|predict/ },
    { name: 'Score bien-être', pattern: /wellness.*score|getWellnessScore/ },
    { name: 'Corrélations', pattern: /correlation.*\[\]/ }
  ];
  
  features.forEach(feature => {
    const hasFeature = feature.pattern.test(moodContent);
    addResult(
      `useMoodAnalytics - ${feature.name}`,
      hasFeature,
      hasFeature ? 'Implémenté' : 'Non trouvé'
    );
  });
}

// Test useSmartNotifications
if (fs.existsSync('frontend/src/hooks/useSmartNotifications.ts')) {
  const notifContent = fs.readFileSync('frontend/src/hooks/useSmartNotifications.ts', 'utf8');
  
  const features = [
    { name: 'Types SmartNotification', pattern: /interface.*SmartNotification/ },
    { name: 'Fonction createNotification', pattern: /createNotification.*=/ },
    { name: 'Analytics notifications', pattern: /analytics.*notification/i },
    { name: 'Préférences utilisateur', pattern: /preferences|preference/ },
    { name: 'Règles intelligentes', pattern: /rule.*notification|smart.*rule/i }
  ];
  
  features.forEach(feature => {
    const hasFeature = feature.pattern.test(notifContent);
    addResult(
      `useSmartNotifications - ${feature.name}`,
      hasFeature,
      hasFeature ? 'Implémenté' : 'Non trouvé'
    );
  });
}

// Test 4: Vérification de l'intégration dans les pages
console.log('\n🔗 Test 4: Intégration dans les pages...');

if (fs.existsSync('frontend/src/app/ai-coach/page.tsx')) {
  const aiCoachPage = fs.readFileSync('frontend/src/app/ai-coach/page.tsx', 'utf8');
  
  const integrations = [
    { name: 'Import useAICoach', pattern: /import.*useAICoach/ },
    { name: 'Interface chat', pattern: /chat|message.*input|conversation/ },
    { name: 'Sélection humeur', pattern: /mood.*select|humeur.*select/ },
    { name: 'Actions suggérées', pattern: /action.*suggest|suggestion/ },
    { name: 'Gestion session', pattern: /session.*start|startSession/ }
  ];
  
  integrations.forEach(integration => {
    const hasIntegration = integration.pattern.test(aiCoachPage);
    addResult(
      `Page IA Coach - ${integration.name}`,
      hasIntegration,
      hasIntegration ? 'Intégré' : 'Non trouvé'
    );
  });
}

if (fs.existsSync('frontend/src/app/analytics/page.tsx')) {
  const analyticsPage = fs.readFileSync('frontend/src/app/analytics/page.tsx', 'utf8');
  
  const integrations = [
    { name: 'Import useMoodAnalytics', pattern: /import.*useMoodAnalytics/ },
    { name: 'Score bien-être', pattern: /wellness.*score|score.*bien/ },
    { name: 'Graphiques tendances', pattern: /chart|graph|trend/ },
    { name: 'Prédictions affichées', pattern: /prediction|prédiction/ },
    { name: 'Insights personnalisés', pattern: /insight|recommandation/ }
  ];
  
  integrations.forEach(integration => {
    const hasIntegration = integration.pattern.test(analyticsPage);
    addResult(
      `Page Analytics - ${integration.name}`,
      hasIntegration,
      hasIntegration ? 'Intégré' : 'Non trouvé'
    );
  });
}

// Test 5: Validation TypeScript
console.log('\n📝 Test 5: Validation TypeScript...');

try {
  const tsConfigExists = fs.existsSync('frontend/tsconfig.json');
  addResult(
    'Configuration TypeScript',
    tsConfigExists,
    tsConfigExists ? 'tsconfig.json présent' : 'Configuration manquante'
  );

  // Vérifier les types exportés
  hookFiles.forEach(hookFile => {
    if (fs.existsSync(hookFile)) {
      const content = fs.readFileSync(hookFile, 'utf8');
      const hasExports = content.includes('export interface') || content.includes('export type');
      const hasProperTypes = content.includes(': React.') || content.includes(': string') || content.includes(': number');
      
      addResult(
        `Types TypeScript ${path.basename(hookFile)}`,
        hasExports && hasProperTypes,
        `Exports: ${hasExports}, Types: ${hasProperTypes}`
      );
    }
  });
} catch (error) {
  addResult(
    'Validation TypeScript',
    false,
    `Erreur: ${error.message}`
  );
}

// Test 6: Vérification de la structure des données
console.log('\n📊 Test 6: Structure des données simulées...');

hookFiles.forEach(hookFile => {
  if (fs.existsSync(hookFile)) {
    const content = fs.readFileSync(hookFile, 'utf8');
    
    // Vérifier la présence de données réalistes
    const hasRealisticData = content.includes('Math.random') && 
                           (content.includes('Date') || content.includes('new Date')) &&
                           content.includes('useState');
    
    const hasDataGeneration = content.includes('generate') && 
                            (content.includes('Mock') || content.includes('mock') || content.includes('simulé'));
    
    addResult(
      `Données simulées ${path.basename(hookFile)}`,
      hasRealisticData && hasDataGeneration,
      `Réalistes: ${hasRealisticData}, Génération: ${hasDataGeneration}`
    );
  }
});

// Test 7: Test de cohérence architecturale
console.log('\n🏗️ Test 7: Cohérence architecturale...');

const architectureChecks = [
  {
    name: 'Hooks dans dossier hooks/',
    check: () => hookFiles.every(f => f.includes('/hooks/'))
  },
  {
    name: 'Pages dans dossier app/',
    check: () => pageFiles.every(f => f.includes('/app/'))
  },
  {
    name: 'Nommage cohérent hooks',
    check: () => hookFiles.every(f => path.basename(f).startsWith('use'))
  },
  {
    name: 'Extensions TypeScript',
    check: () => hookFiles.every(f => f.endsWith('.ts')) && pageFiles.every(f => f.endsWith('.tsx'))
  }
];

architectureChecks.forEach(check => {
  try {
    const result = check.check();
    addResult(
      `Architecture - ${check.name}`,
      result,
      result ? 'Conforme' : 'Non conforme'
    );
  } catch (error) {
    addResult(
      `Architecture - ${check.name}`,
      false,
      `Erreur: ${error.message}`
    );
  }
});

// Calcul du score final
console.log('\n' + '='.repeat(60));
console.log('📊 RÉSULTATS FINAUX');
console.log('='.repeat(60));

const percentage = Math.round((score / totalTests) * 100);

console.log(`✅ Tests réussis: ${score}/${totalTests} (${percentage}%)`);
console.log(`❌ Tests échoués: ${totalTests - score}/${totalTests}`);

// Évaluation qualitative
let evaluation = '';
if (percentage >= 90) {
  evaluation = '🏆 EXCELLENT - Étape 3 complètement réussie';
} else if (percentage >= 80) {
  evaluation = '🎉 TRÈS BIEN - Étape 3 largement réussie';
} else if (percentage >= 70) {
  evaluation = '👍 BIEN - Étape 3 majoritairement réussie';
} else if (percentage >= 60) {
  evaluation = '⚠️ MOYEN - Étape 3 partiellement réussie';
} else {
  evaluation = '❌ INSUFFISANT - Étape 3 nécessite des améliorations';
}

console.log(`\n${evaluation}\n`);

// Détails par catégorie
const categories = {
  'Hooks': results.filter(r => r.test.includes('Hook') || r.test.includes('useAI') || r.test.includes('useMood') || r.test.includes('useSmartNotifications')),
  'Pages': results.filter(r => r.test.includes('Page')),
  'Architecture': results.filter(r => r.test.includes('Architecture') || r.test.includes('Types')),
  'Données': results.filter(r => r.test.includes('Données') || r.test.includes('simulées'))
};

Object.entries(categories).forEach(([category, categoryResults]) => {
  if (categoryResults.length > 0) {
    const categoryScore = categoryResults.filter(r => r.success).length;
    const categoryTotal = categoryResults.length;
    const categoryPercentage = Math.round((categoryScore / categoryTotal) * 100);
    
    console.log(`📁 ${category}: ${categoryScore}/${categoryTotal} (${categoryPercentage}%)`);
    
    // Afficher les échecs pour cette catégorie
    const failures = categoryResults.filter(r => !r.success);
    if (failures.length > 0) {
      failures.forEach(failure => {
        console.log(`   ❌ ${failure.test}: ${failure.details}`);
      });
    }
  }
});

// Recommandations
console.log('\n📋 RECOMMANDATIONS:');

if (percentage >= 90) {
  console.log('✅ Étape 3 complète et prête pour la production');
  console.log('✅ Tous les hooks sont fonctionnels et bien intégrés');
  console.log('✅ Architecture solide et maintenable');
  console.log('🚀 Prêt pour l\'intégration Supabase et déploiement');
} else {
  console.log('🔧 Améliorations suggérées:');
  
  if (categories.Hooks.filter(r => r.success).length < categories.Hooks.length) {
    console.log('   - Finaliser l\'implémentation des hooks manquants');
  }
  if (categories.Pages.filter(r => r.success).length < categories.Pages.length) {
    console.log('   - Compléter l\'intégration des hooks dans les pages');
  }
  if (categories.Architecture.filter(r => r.success).length < categories.Architecture.length) {
    console.log('   - Corriger les problèmes d\'architecture identifiés');
  }
}

// Sauvegarde du rapport
const report = {
  timestamp: new Date().toISOString(),
  score: {
    total: score,
    possible: totalTests,
    percentage: percentage
  },
  evaluation: evaluation,
  results: results,
  categories: Object.fromEntries(
    Object.entries(categories).map(([name, results]) => [
      name,
      {
        score: results.filter(r => r.success).length,
        total: results.length,
        percentage: Math.round((results.filter(r => r.success).length / results.length) * 100)
      }
    ])
  )
};

try {
  fs.writeFileSync('rapport-validation-etape3.json', JSON.stringify(report, null, 2));
  console.log('\n💾 Rapport sauvegardé dans: rapport-validation-etape3.json');
} catch (error) {
  console.log(`\n❌ Erreur sauvegarde rapport: ${error.message}`);
}

console.log('\n🎯 Validation Étape 3 terminée !');
console.log(`⏰ Durée: ${new Date().toLocaleTimeString('fr-FR')}`);

// Code de sortie basé sur le score
process.exit(percentage >= 70 ? 0 : 1); 