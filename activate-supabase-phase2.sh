#!/bin/bash

# 🚀 ACTIVATION PHASE 2 SUPABASE - Authentification
# ==================================================

echo "🚀 ACTIVATION PHASE 2 SUPABASE"
echo "==============================="
echo "Authentification Supabase + SQLite (Mode Hybride)"
echo ""

# Navigation vers frontend
cd frontend || { echo "❌ Erreur: Dossier frontend introuvable"; exit 1; }

# 1. Création du fichier .env.local
echo "1. 📄 Création fichier .env.local..."

cat > .env.local << 'EOF'
# =================================================================
# MINDFLOW PRO - CONFIGURATION PHASE 2 SUPABASE
# Activation authentification Supabase + Base SQLite (Mode Hybride)
# =================================================================

# 🔑 SUPABASE - Clés API (Mises à jour Décembre 2024)
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4

# 🚀 PHASE 2 - FEATURE FLAGS (ACTIVATION AUTHENTIFICATION SUPABASE)
NEXT_PUBLIC_USE_SUPABASE_AUTH=true
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true

# Phase 2 spécifique
NEXT_PUBLIC_ENABLE_REAL_TIME=false
NEXT_PUBLIC_MIGRATE_USER_DATA=false
NEXT_PUBLIC_MIGRATE_MOOD_TRACKING=false
NEXT_PUBLIC_MIGRATE_JOURNAL_ENTRIES=false

# 🛠️ CONFIGURATION DÉVELOPPEMENT
NODE_ENV=development
NEXT_PUBLIC_NODE_ENV=development
NEXT_PUBLIC_BACKEND_URL=http://localhost:4000
NEXT_PUBLIC_API_BASE_URL=http://localhost:4000/api/v1

# 📊 MONITORING & DEBUG
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_LOG_LEVEL=debug
EOF

echo "   ✅ .env.local créé avec configuration Phase 2"

# 2. Vérification du fichier
echo ""
echo "2. ⚙️ Vérification configuration..."

if grep -q "NEXT_PUBLIC_USE_SUPABASE_AUTH=true" .env.local; then
    echo "   ✅ Authentification Supabase: ACTIVÉE"
else
    echo "   ❌ Erreur configuration auth"
fi

if grep -q "NEXT_PUBLIC_USE_SUPABASE_DATABASE=false" .env.local; then
    echo "   ✅ Base de données: SQLite (maintenue)"
else
    echo "   ❌ Erreur configuration database"
fi

if grep -q "NEXT_PUBLIC_DUAL_DATABASE_MODE=true" .env.local; then
    echo "   ✅ Mode hybride: ACTIVÉ"
else
    echo "   ❌ Erreur mode dual"
fi

# 3. Test de connectivité basique
echo ""
echo "3. 🔗 Test connectivité Supabase..."

# Création script de test simple
cat > test-supabase-connection.js << 'EOF'
const https = require('https');
const url = 'https://kvdrukmoxetoiojazukf.supabase.co/rest/v1/';

console.log('Test connexion Supabase...');

const options = {
  method: 'GET',
  headers: {
    'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
  }
};

const req = https.request(url, options, (res) => {
  if (res.statusCode === 200) {
    console.log('✅ Connexion Supabase réussie');
  } else {
    console.log('⚠️ Réponse:', res.statusCode);
  }
});

req.on('error', (error) => {
  console.log('❌ Erreur connexion:', error.message);
});

req.end();
EOF

# Exécution du test
node test-supabase-connection.js
rm test-supabase-connection.js

# 4. Instructions finales
echo ""
echo "🎯 PHASE 2 ACTIVÉE !"
echo "==================="
echo ""
echo "✅ Configuration: Authentification Supabase + SQLite"
echo "✅ Mode hybride: FONCTIONNEL"
echo "✅ Fichier .env.local: CRÉÉ"
echo ""
echo "📋 PROCHAINES ÉTAPES:"
echo "1. 🔄 Redémarrer le serveur Next.js:"
echo "   ctrl+c (arrêter serveur actuel)"
echo "   npm run dev (redémarrer)"
echo ""
echo "2. 🧪 Tester les pages d'authentification:"
echo "   http://localhost:3000/auth/login"
echo "   http://localhost:3000/auth/register"
echo "   http://localhost:3000/test-auth"
echo ""
echo "3. 🔍 Vérifier les logs pour d'éventuelles erreurs"
echo ""
echo "⚠️ IMPORTANT:"
echo "Si vous voyez des erreurs AuthContext, c'est normal."
echo "Les composants vont maintenant utiliser l'authentification Supabase."
echo ""
echo "🆘 En cas de problème:"
echo "   Supprimez .env.local et relancez ce script" 