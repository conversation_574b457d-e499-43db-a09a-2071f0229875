
                CREATE TABLE IF NOT EXISTS smart_notifications (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    type TEXT CHECK (type IN ('reminder', 'suggestion', 'achievement', 'warning', 'insight', 'emergency')) DEFAULT 'reminder',
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    priority TEXT CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
                    read_status BOOLEAN DEFAULT FALSE,
                    action_taken BOOLEAN DEFAULT FALSE,
                    scheduled_for TIMESTAMP WITH TIME ZONE,
                    metadata JSONB DEFAULT '{}',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                