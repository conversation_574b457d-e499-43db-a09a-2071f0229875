#!/usr/bin/env node

/**
 * 🧪 TEST D'INSCRIPTION AUTOMATIQUE - MINDFLOW PRO
 * 
 * Ce script va :
 * 1. Tester l'inscription d'un utilisateur
 * 2. <PERSON><PERSON>er des données de test (humeurs, journal)
 * 3. Valider les fonctionnalités Supabase
 * 4. Générer un rapport de test complet
 */

const { createClient } = require('@supabase/supabase-js');

// Configuration Supabase
const SUPABASE_URL = 'https://kvdrukmoxetoiojazukf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ';

// Client Supabase
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

console.log('🚀 DÉBUT DU TEST D\'INSCRIPTION AUTOMATIQUE\n');

/**
 * Génère un email de test unique
 */
function generateTestEmail() {
    const timestamp = Date.now();
    return `test-auto-${timestamp}@mindflow.pro`;
}

/**
 * Test d'inscription d'un nouvel utilisateur
 */
async function testUserRegistration() {
    console.log('1️⃣  Test d\'inscription utilisateur...');
    
    const testUser = {
        email: generateTestEmail(),
        password: 'TestPassword123!',
        fullName: 'Utilisateur Test Auto'
    };
    
    try {
        // Tentative d'inscription
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email: testUser.email,
            password: testUser.password,
            options: {
                data: {
                    full_name: testUser.fullName
                }
            }
        });
        
        if (authError) {
            console.log('   ❌ Erreur inscription:', authError.message);
            return { success: false, error: authError };
        }
        
        console.log('   ✅ Inscription réussie !');
        console.log('   📧 Email:', testUser.email);
        console.log('   🆔 User ID:', authData.user?.id);
        
        return { 
            success: true, 
            user: authData.user, 
            testCredentials: testUser 
        };
        
    } catch (error) {
        console.log('   ❌ Erreur critique inscription:', error.message);
        return { success: false, error };
    }
}

/**
 * Connexion avec l'utilisateur créé
 */
async function testUserLogin(credentials) {
    console.log('\n2️⃣  Test de connexion...');
    
    try {
        const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
            email: credentials.email,
            password: credentials.password
        });
        
        if (loginError) {
            console.log('   ❌ Erreur connexion:', loginError.message);
            return { success: false, error: loginError };
        }
        
        console.log('   ✅ Connexion réussie !');
        console.log('   🔑 Session active:', !!loginData.session);
        
        return { success: true, session: loginData.session };
        
    } catch (error) {
        console.log('   ❌ Erreur critique connexion:', error.message);
        return { success: false, error };
    }
}

/**
 * Création de données de test
 */
async function createTestData(userId) {
    console.log('\n3️⃣  Création de données de test...');
    
    try {
        // Création d'entrées d'humeur
        console.log('   📊 Création des humeurs...');
        
        const moodEntries = [];
        for (let i = 0; i < 3; i++) {
            const mood = {
                user_id: userId,
                mood_score: Math.floor(Math.random() * 10) + 1,
                notes: `Note d'humeur automatique ${i + 1}`,
                tags: ['test', 'automatique']
            };
            
            const { data, error } = await supabase
                .from('mood_entries')
                .insert(mood)
                .select();
                
            if (error) {
                console.log(`     ❌ Erreur humeur ${i + 1}:`, error.message);
            } else {
                console.log(`     ✅ Humeur ${i + 1} créée (score: ${mood.mood_score})`);
                moodEntries.push(data[0]);
            }
        }
        
        // Création d'entrées de journal
        console.log('   📝 Création des entrées de journal...');
        
        const journalEntries = [];
        for (let i = 0; i < 2; i++) {
            const journal = {
                user_id: userId,
                title: `Entrée Journal Test ${i + 1}`,
                content: `Contenu automatique pour le test ${i + 1}.`,
                mood_score: Math.floor(Math.random() * 10) + 1,
                tags: ['test', 'journal'],
                is_private: true
            };
            
            const { data, error } = await supabase
                .from('journal_entries')
                .insert(journal)
                .select();
                
            if (error) {
                console.log(`     ❌ Erreur journal ${i + 1}:`, error.message);
            } else {
                console.log(`     ✅ Journal ${i + 1} créé: "${journal.title}"`);
                journalEntries.push(data[0]);
            }
        }
        
        return {
            success: true,
            moodEntries,
            journalEntries,
            stats: {
                moods: moodEntries.length,
                journals: journalEntries.length
            }
        };
        
    } catch (error) {
        console.log('   ❌ Erreur création données:', error.message);
        return { success: false, error };
    }
}

/**
 * Validation des données créées
 */
async function validateTestData(userId) {
    console.log('\n4️⃣  Validation des données...');
    
    try {
        // Vérification des humeurs
        const { data: moods, error: moodError } = await supabase
            .from('mood_entries')
            .select('*')
            .eq('user_id', userId);
            
        if (moodError) {
            console.log('   ❌ Erreur lecture humeurs:', moodError.message);
        } else {
            console.log(`   ✅ ${moods.length} entrées d'humeur trouvées`);
        }
        
        // Vérification des journaux
        const { data: journals, error: journalError } = await supabase
            .from('journal_entries')
            .select('*')
            .eq('user_id', userId);
            
        if (journalError) {
            console.log('   ❌ Erreur lecture journaux:', journalError.message);
        } else {
            console.log(`   ✅ ${journals.length} entrées de journal trouvées`);
        }
        
        // Calcul des statistiques
        if (moods && moods.length > 0) {
            const avgMood = moods.reduce((sum, m) => sum + m.mood_score, 0) / moods.length;
            console.log(`   📊 Humeur moyenne: ${avgMood.toFixed(1)}/10`);
        }
        
        return {
            success: true,
            validation: {
                moods: moods?.length || 0,
                journals: journals?.length || 0
            }
        };
        
    } catch (error) {
        console.log('   ❌ Erreur validation:', error.message);
        return { success: false, error };
    }
}

/**
 * Nettoyage des données de test (optionnel)
 */
async function cleanupTestData(userId) {
    console.log('\n🧹 Nettoyage des données de test...');
    
    try {
        // Suppression des humeurs
        const { error: moodDeleteError } = await supabase
            .from('mood_entries')
            .delete()
            .eq('user_id', userId);
            
        if (moodDeleteError) {
            console.log('   ⚠️  Erreur suppression humeurs:', moodDeleteError.message);
        } else {
            console.log('   ✅ Humeurs supprimées');
        }
        
        // Suppression des journaux
        const { error: journalDeleteError } = await supabase
            .from('journal_entries')
            .delete()
            .eq('user_id', userId);
            
        if (journalDeleteError) {
            console.log('   ⚠️  Erreur suppression journaux:', journalDeleteError.message);
        } else {
            console.log('   ✅ Journaux supprimés');
        }
        
        return { success: true };
        
    } catch (error) {
        console.log('   ❌ Erreur nettoyage:', error.message);
        return { success: false, error };
    }
}

/**
 * Fonction principale
 */
async function main() {
    const results = {
        registration: null,
        login: null,
        dataCreation: null,
        validation: null,
        cleanup: null
    };
    
    try {
        // Test d'inscription
        const registrationResult = await testUserRegistration();
        results.registration = registrationResult;
        
        if (!registrationResult.success) {
            throw new Error('Échec inscription');
        }
        
        // Test de connexion
        const loginResult = await testUserLogin(registrationResult.testCredentials);
        results.login = loginResult;
        
        if (!loginResult.success) {
            throw new Error('Échec connexion');
        }
        
        const userId = registrationResult.user.id;
        
        // Création de données
        const dataCreationResult = await createTestData(userId);
        results.dataCreation = dataCreationResult;
        
        // Validation
        const validationResult = await validateTestData(userId);
        results.validation = validationResult;
        
        // Option de nettoyage (commentée par défaut pour garder les données)
        // const cleanupResult = await cleanupTestData(userId);
        // results.cleanup = cleanupResult;
        
        // Rapport final
        console.log('\n🎯 RAPPORT FINAL:');
        console.log('==================');
        console.log('✅ Inscription:', results.registration.success ? 'RÉUSSIE' : 'ÉCHEC');
        console.log('✅ Connexion:', results.login.success ? 'RÉUSSIE' : 'ÉCHEC');
        console.log('✅ Données créées:', results.dataCreation.success ? 'RÉUSSIES' : 'ÉCHEC');
        console.log('✅ Validation:', results.validation.success ? 'RÉUSSIE' : 'ÉCHEC');
        
        if (results.validation.success) {
            const v = results.validation.validation;
            console.log(`📊 ${v.moods} humeurs, ${v.journals} journaux créés`);
        }
        
        console.log('\n🚀 MindFlow Pro Phase 4 : VALIDATION COMPLÈTE !');
        console.log('\n📋 Identifiants de test créés:');
        console.log(`   Email: ${registrationResult.testCredentials.email}`);
        console.log(`   Mot de passe: ${registrationResult.testCredentials.password}`);
        console.log('\n📍 Pages à tester:');
        console.log('   • http://localhost:3000/auth/login');
        console.log('   • http://localhost:3000/dashboard');
        console.log('   • http://localhost:3000/test-phase4-supabase');
        
    } catch (error) {
        console.log('\n💥 ERREUR DANS LE TEST:', error.message);
        
        // Affichage des résultats partiels
        console.log('\n📊 Résultats partiels:');
        Object.entries(results).forEach(([test, result]) => {
            if (result !== null) {
                console.log(`   ${test}: ${result.success ? '✅' : '❌'}`);
            }
        });
        
        process.exit(1);
    }
}

// Exécution
if (require.main === module) {
    main();
}

module.exports = { 
    testUserRegistration, 
    testUserLogin, 
    createTestData, 
    validateTestData,
    cleanupTestData 
}; 