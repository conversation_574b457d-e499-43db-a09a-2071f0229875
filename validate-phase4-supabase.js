#!/usr/bin/env node

/**
 * Script de validation Phase 4 - Basculement complet Supabase
 * MindFlow Pro - 27 Décembre 2024
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 VALIDATION PHASE 4 - BASCULEMENT COMPLET SUPABASE');
console.log('='.repeat(60));

// Couleurs pour l'affichage
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = (message, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`);
};

const success = (message) => log(`✅ ${message}`, colors.green);
const error = (message) => log(`❌ ${message}`, colors.red);
const warning = (message) => log(`⚠️  ${message}`, colors.yellow);
const info = (message) => log(`ℹ️  ${message}`, colors.blue);

let validationResults = {
  configPhase4: false,
  servicesSupabase: false,
  hooksReact: false,
  testPages: false,
  envConfiguration: false,
  schemaSupabase: false
};

// 1. Validation configuration .env.local
function validateEnvironmentConfig() {
  info('1. Validation configuration environnement...');
  
  const envPath = path.join(__dirname, 'frontend', '.env.local');
  
  if (!fs.existsSync(envPath)) {
    error('Fichier .env.local manquant');
    return false;
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  const requiredVars = [
    'NEXT_PUBLIC_USE_SUPABASE_AUTH=true',
    'NEXT_PUBLIC_USE_SUPABASE_DATABASE=true',
    'NEXT_PUBLIC_ENABLE_REAL_TIME=true',
    'NEXT_PUBLIC_DUAL_DATABASE_MODE=false',
    'NEXT_PUBLIC_MIGRATE_MOOD_TRACKING=true',
    'NEXT_PUBLIC_MIGRATE_JOURNAL_ENTRIES=true'
  ];
  
  let allValid = true;
  requiredVars.forEach(varConfig => {
    if (envContent.includes(varConfig)) {
      success(`  ${varConfig}`);
    } else {
      error(`  Manquant: ${varConfig}`);
      allValid = false;
    }
  });
  
  if (envContent.includes('kvdrukmoxetoiojazukf.supabase.co')) {
    success('  URL Supabase configurée');
  } else {
    error('  URL Supabase manquante');
    allValid = false;
  }
  
  return allValid;
}

// 2. Validation services Supabase
function validateSupabaseServices() {
  info('2. Validation services Supabase...');
  
  const services = [
    'frontend/src/services/auth-supabase.ts',
    'frontend/src/services/data-supabase.ts',
    'frontend/src/lib/supabase/browser-client.ts'
  ];
  
  let allExist = true;
  services.forEach(servicePath => {
    if (fs.existsSync(servicePath)) {
      success(`  ${servicePath}`);
    } else {
      error(`  Manquant: ${servicePath}`);
      allExist = false;
    }
  });
  
  return allExist;
}

// 3. Validation hooks React
function validateReactHooks() {
  info('3. Validation hooks React...');
  
  const hookPath = 'frontend/src/hooks/useSupabaseData.ts';
  
  if (fs.existsSync(hookPath)) {
    const hookContent = fs.readFileSync(hookPath, 'utf8');
    
    const requiredHooks = [
      'useSupabaseData',
      'useMoodEntries',
      'useJournalEntries',
      'useRealtimeSubscriptions'
    ];
    
    let allValid = true;
    requiredHooks.forEach(hookName => {
      if (hookContent.includes(`export const ${hookName}`)) {
        success(`  Hook ${hookName} présent`);
      } else {
        error(`  Hook ${hookName} manquant`);
        allValid = false;
      }
    });
    
    return allValid;
  } else {
    error(`  Fichier hooks manquant: ${hookPath}`);
    return false;
  }
}

// 4. Validation pages de test
function validateTestPages() {
  info('4. Validation pages de test...');
  
  const testPages = [
    'frontend/src/app/test-phase4-supabase/page.tsx',
    'frontend/src/app/test-supabase-schema/page.tsx'
  ];
  
  let allExist = true;
  testPages.forEach(pagePath => {
    if (fs.existsSync(pagePath)) {
      success(`  ${pagePath}`);
    } else {
      error(`  Manquant: ${pagePath}`);
      allExist = false;
    }
  });
  
  return allExist;
}

// 5. Validation intégration feature flags
function validateFeatureFlags() {
  info('5. Validation feature flags...');
  
  const featureFlagsPath = 'frontend/src/lib/config/feature-flags.ts';
  
  if (fs.existsSync(featureFlagsPath)) {
    const content = fs.readFileSync(featureFlagsPath, 'utf8');
    
    const requiredFlags = [
      'USE_SUPABASE_AUTH',
      'USE_SUPABASE_DATABASE',
      'ENABLE_REAL_TIME',
      'DUAL_DATABASE_MODE'
    ];
    
    let allValid = true;
    requiredFlags.forEach(flag => {
      if (content.includes(flag)) {
        success(`  Feature flag ${flag} présent`);
      } else {
        error(`  Feature flag ${flag} manquant`);
        allValid = false;
      }
    });
    
    return allValid;
  } else {
    error(`  Fichier feature flags manquant`);
    return false;
  }
}

// 6. Validation schéma SQL
function validateSupabaseSchema() {
  info('6. Validation schéma Supabase...');
  
  const schemaPath = 'supabase-schema.sql';
  
  if (fs.existsSync(schemaPath)) {
    const content = fs.readFileSync(schemaPath, 'utf8');
    
    const requiredTables = [
      'create table public.users',
      'create table public.mood_entries',
      'create table public.journal_entries'
    ];
    
    const requiredPolicies = [
      'create policy "Users can view own',
      'enable row level security'
    ];
    
    let allValid = true;
    
    requiredTables.forEach(table => {
      if (content.includes(table)) {
        success(`  Table: ${table.split(' ')[3]}`);
      } else {
        error(`  Table manquante: ${table}`);
        allValid = false;
      }
    });
    
    requiredPolicies.forEach(policy => {
      if (content.includes(policy)) {
        success(`  Sécurité RLS configurée`);
      } else {
        error(`  Politique sécurité manquante`);
        allValid = false;
      }
    });
    
    return allValid;
  } else {
    error(`  Fichier schéma SQL manquant`);
    return false;
  }
}

// Exécution des validations
async function runValidation() {
  try {
    validationResults.envConfiguration = validateEnvironmentConfig();
    validationResults.servicesSupabase = validateSupabaseServices();
    validationResults.hooksReact = validateReactHooks();
    validationResults.testPages = validateTestPages();
    validationResults.configPhase4 = validateFeatureFlags();
    validationResults.schemaSupabase = validateSupabaseSchema();
    
    // Résultats finaux
    console.log('\n' + '='.repeat(60));
    log('📊 RÉSULTATS VALIDATION PHASE 4', colors.bold);
    console.log('='.repeat(60));
    
    const totalChecks = Object.keys(validationResults).length;
    const passedChecks = Object.values(validationResults).filter(Boolean).length;
    const successRate = Math.round((passedChecks / totalChecks) * 100);
    
    Object.entries(validationResults).forEach(([key, passed]) => {
      const message = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      if (passed) {
        success(message);
      } else {
        error(message);
      }
    });
    
    console.log('\n' + '-'.repeat(60));
    
    if (successRate === 100) {
      success(`🎉 PHASE 4 VALIDÉE À 100% (${passedChecks}/${totalChecks})`);
      success('✅ Basculement complet Supabase OPÉRATIONNEL !');
      
      console.log('\n' + colors.green + colors.bold);
      console.log('🚀 PROCHAINES ÉTAPES :');
      console.log('1. Exécuter le schéma SQL dans Supabase Dashboard');
      console.log('2. Tester : http://localhost:3000/test-phase4-supabase');
      console.log('3. Valider l\'inscription/connexion utilisateur');
      console.log('4. Créer des données de test');
      console.log('5. Vérifier les temps réel (real-time)');
      console.log(colors.reset);
      
    } else if (successRate >= 80) {
      warning(`⚠️ PHASE 4 PARTIELLEMENT VALIDÉE (${passedChecks}/${totalChecks}) - ${successRate}%`);
      warning('Quelques ajustements nécessaires');
      
    } else {
      error(`❌ PHASE 4 ÉCHOUE (${passedChecks}/${totalChecks}) - ${successRate}%`);
      error('Corrections importantes requises');
    }
    
    console.log('\n' + '='.repeat(60));
    
  } catch (error) {
    error(`Erreur durant la validation: ${error.message}`);
    process.exit(1);
  }
}

// Lancement du script
if (require.main === module) {
  runValidation();
}

module.exports = { runValidation, validationResults }; 