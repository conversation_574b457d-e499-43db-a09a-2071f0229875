#!/bin/bash

# 🚀 ACTIVATION PHASE 2 SUPABASE - FINAL
# =======================================

echo "🚀 ACTIVATION PHASE 2 SUPABASE - AUTHENTIFICATION"
echo "=================================================="
echo "✅ Serveur Next.js opérationnel détecté"
echo "🎯 Activation authentification Supabase + SQLite"
echo ""

# Navigation vers frontend
cd frontend || { echo "❌ Erreur: Dossier frontend introuvable"; exit 1; }

# Création du fichier .env.local avec configuration Phase 2
echo "📄 Création fichier .env.local avec configuration Phase 2..."

cat > .env.local << 'EOF'
# =================================================================
# MINDFLOW PRO - CONFIGURATION PHASE 2 SUPABASE
# Activation authentification Supabase + Base SQLite (Mode Hybride)
# =================================================================

# 🔑 SUPABASE - Clés API (Mises à jour Décembre 2024)
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4

# 🚀 PHASE 2 - FEATURE FLAGS (ACTIVATION AUTHENTIFICATION SUPABASE)
NEXT_PUBLIC_USE_SUPABASE_AUTH=true          # ← ACTIVATION !
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false     # ← SQLite maintenue 
NEXT_PUBLIC_DUAL_DATABASE_MODE=true         # ← Mode hybride
NEXT_PUBLIC_MIGRATION_PHASE=2               # ← Phase 2 active

# 🔧 CONFIGURATION BACKEND (maintenue)
NEXT_PUBLIC_API_BASE_URL=http://localhost:4000/api/v1
NEXT_PUBLIC_BACKEND_URL=http://localhost:4000

# 📊 MONITORING & DEBUG
NEXT_PUBLIC_LOG_LEVEL=debug
NEXT_PUBLIC_PERFORMANCE_MONITORING=true

# ⚡ OPTIMISATIONS DÉVELOPPEMENT
NEXT_PUBLIC_NODE_ENV=development
FAST_REFRESH=true
EOF

echo "✅ Fichier .env.local créé avec succès !"
echo ""

# Vérification de la création
if [ -f ".env.local" ]; then
    echo "🔍 Vérification configuration:"
    echo "   ✅ .env.local existe"
    
    # Vérification des variables critiques
    if grep -q "NEXT_PUBLIC_USE_SUPABASE_AUTH=true" .env.local; then
        echo "   ✅ Authentification Supabase ACTIVÉE"
    fi
    
    if grep -q "NEXT_PUBLIC_USE_SUPABASE_DATABASE=false" .env.local; then
        echo "   ✅ Base de données SQLite MAINTENUE"
    fi
    
    if grep -q "NEXT_PUBLIC_DUAL_DATABASE_MODE=true" .env.local; then
        echo "   ✅ Mode hybride ACTIVÉ"
    fi
else
    echo "❌ Erreur: Impossible de créer .env.local"
    exit 1
fi

echo ""
echo "🎉 PHASE 2 SUPABASE ACTIVÉE AVEC SUCCÈS !"
echo "========================================"
echo ""
echo "📋 Configuration active:"
echo "   🔐 Authentification: Supabase ✅"
echo "   🗄️  Base de données: SQLite ✅" 
echo "   🔄 Mode: Hybride ✅"
echo ""
echo "🔄 Le serveur Next.js va redémarrer automatiquement"
echo "   pour prendre en compte la nouvelle configuration."
echo ""
echo "🧪 Pages de test disponibles:"
echo "   • http://localhost:3001/auth/login"
echo "   • http://localhost:3001/auth/register"
echo "   • http://localhost:3001/test-auth"
echo ""
echo "✨ L'authentification Supabase est maintenant active !" 