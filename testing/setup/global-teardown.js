/**
 * Global Teardown for MindFlow Pro E2E Tests
 * Cleans up after test execution
 */

async function globalTeardown(config) {
  console.log('🧹 Starting MindFlow Pro E2E Test Cleanup...');
  
  try {
    // Clean up test data
    await cleanupTestData();
    
    // Generate test summary
    await generateTestSummary();
    
    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
  }
}

/**
 * Clean up test data from database
 */
async function cleanupTestData() {
  console.log('🗑️ Cleaning up test data...');
  
  try {
    // Note: In a real implementation, you would clean up test-specific data
    // For now, we'll just log the cleanup process
    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.warn('⚠️ Test data cleanup failed:', error.message);
  }
}

/**
 * Generate test execution summary
 */
async function generateTestSummary() {
  console.log('📊 Generating test summary...');
  
  const fs = require('fs').promises;
  const path = require('path');
  
  try {
    const resultsPath = path.join(__dirname, '../test-results/results.json');
    
    // Check if results file exists
    try {
      await fs.access(resultsPath);
      
      const results = JSON.parse(await fs.readFile(resultsPath, 'utf8'));
      
      const summary = {
        timestamp: new Date().toISOString(),
        totalTests: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
        environment: {
          frontend: 'http://localhost:3000',
          backend: 'http://localhost:4000',
          node: process.version,
          platform: process.platform
        }
      };
      
      // Write summary to file
      const summaryPath = path.join(__dirname, '../test-results/summary.json');
      await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2));
      
      console.log('📋 Test Summary:');
      console.log(`   Total Tests: ${summary.totalTests}`);
      console.log(`   Passed: ${summary.passed}`);
      console.log(`   Failed: ${summary.failed}`);
      console.log(`   Skipped: ${summary.skipped}`);
      console.log(`   Duration: ${Math.round(summary.duration / 1000)}s`);
      
    } catch (error) {
      console.log('📋 Test results file not found - tests may not have completed');
    }
    
  } catch (error) {
    console.warn('⚠️ Failed to generate test summary:', error.message);
  }
}

module.exports = globalTeardown;
