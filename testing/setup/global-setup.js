/**
 * Global Setup for MindFlow Pro E2E Tests
 * Prepares the test environment before running tests
 */

const { chromium } = require('@playwright/test');
const { setupTestEnvironment } = require('../scripts/setup-test-environment');

async function globalSetup(config) {
  console.log('🚀 Starting MindFlow Pro E2E Test Setup...');
  
  // Wait for servers to be ready
  await waitForServers();
  
  // Setup test environment
  await setupTestEnvironment();
  
  // Create a browser instance for authentication
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Pre-authenticate test users and store auth state
    await authenticateTestUsers(page);
    
    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

/**
 * Wait for both frontend and backend servers to be ready
 */
async function waitForServers() {
  console.log('⏳ Waiting for servers to be ready...');
  
  const maxAttempts = 30;
  const delay = 2000;
  
  // Wait for backend
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch('http://localhost:4000/api/v1/health');
      if (response.ok) {
        console.log('✅ Backend server is ready');
        break;
      }
    } catch (error) {
      if (i === maxAttempts - 1) {
        throw new Error('Backend server failed to start');
      }
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  // Wait for frontend
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch('http://localhost:3000');
      if (response.ok) {
        console.log('✅ Frontend server is ready');
        break;
      }
    } catch (error) {
      if (i === maxAttempts - 1) {
        throw new Error('Frontend server failed to start');
      }
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

/**
 * Pre-authenticate test users to speed up tests
 */
async function authenticateTestUsers(page) {
  console.log('🔐 Pre-authenticating test users...');
  
  const testUsers = [
    { email: '<EMAIL>', password: 'Password123', name: 'root' },
    { email: '<EMAIL>', password: 'AdminPass123', name: 'admin' },
    { email: '<EMAIL>', password: 'UserPass123', name: 'user' },
    { email: '<EMAIL>', password: 'ProfessionalPass123', name: 'professional' }
  ];
  
  for (const user of testUsers) {
    try {
      // Navigate to login page
      await page.goto('http://localhost:3000/login');
      await page.waitForLoadState('networkidle');
      
      // Fill login form
      await page.fill('input[type="email"]', user.email);
      await page.fill('input[type="password"]', user.password);
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Wait for successful login
      await page.waitForURL(/\/(dashboard|journal)/, { timeout: 10000 });
      
      // Store authentication state
      await page.context().storageState({ 
        path: `testing/auth-states/${user.name}-auth.json` 
      });
      
      console.log(`✅ Pre-authenticated user: ${user.email}`);
      
    } catch (error) {
      console.warn(`⚠️ Failed to pre-authenticate ${user.email}:`, error.message);
    }
  }
}

/**
 * Verify test data integrity
 */
async function verifyTestData() {
  console.log('🔍 Verifying test data...');
  
  try {
    // Check if test users exist
    const response = await fetch('http://localhost:4000/api/v1/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'UserPass123'
      })
    });
    
    if (response.ok) {
      console.log('✅ Test user authentication verified');
    } else {
      console.warn('⚠️ Test user authentication failed');
    }
    
  } catch (error) {
    console.warn('⚠️ Test data verification failed:', error.message);
  }
}

module.exports = globalSetup;
