#!/bin/bash

# Couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Aller au répertoire du script
cd "$(dirname "$0")"
TEST_DIR="$(pwd)"
ROOT_DIR="$(dirname "$TEST_DIR")"

echo -e "${BLUE}=== MindFlow Pro - Tests complets ===${NC}"
echo -e "${YELLOW}Répertoire de test: ${TEST_DIR}${NC}"
echo -e "${YELLOW}Répertoire racine: ${ROOT_DIR}${NC}"

# Fonction pour afficher les résultats
function print_result {
  if [ $1 -eq 0 ]; then
    echo -e "${GREEN}✅ $2 réussi${NC}"
  else
    echo -e "${RED}❌ $2 échoué${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
  fi
}

# Initialiser le compteur d'échecs
FAILED_TESTS=0

# 1. Vérifier si les serveurs sont en cours d'exécution
echo -e "\n${PURPLE}[1/5] Vérification des serveurs...${NC}"
node check-servers.js
SERVER_STATUS=$?
print_result $SERVER_STATUS "Test des serveurs"

# Si les serveurs ne sont pas en cours d'exécution, essayer de les démarrer
if [ $SERVER_STATUS -ne 0 ]; then
  echo -e "${YELLOW}Tentative de démarrage des serveurs...${NC}"
  node manage-servers.js start
  sleep 5
  node check-servers.js
  SERVER_RESTART=$?
  print_result $SERVER_RESTART "Redémarrage des serveurs"
fi

# 2. Corriger les problèmes courants
echo -e "\n${PURPLE}[2/5] Correction des problèmes courants...${NC}"
node fix-common-issues.js
print_result $? "Correction des problèmes"

# 3. Tester les endpoints backend
echo -e "\n${PURPLE}[3/5] Test des endpoints backend...${NC}"
echo -e "${YELLOW}Test du endpoint health...${NC}"
curl -s http://localhost:4000/api/v1/health | grep -q "success\|healthy"
print_result $? "Endpoint health"

echo -e "${YELLOW}Test du endpoint test...${NC}"
curl -s http://localhost:4000/api/v1/test | grep -q "message\|Test"
print_result $? "Endpoint test"

echo -e "${YELLOW}Test du endpoint status...${NC}"
curl -s http://localhost:4000/api/v1/status | grep -q "server\|timestamp\|environment\|uptime"
print_result $? "Endpoint status"

# 4. Tester l'authentification
echo -e "\n${PURPLE}[4/5] Test de l'authentification...${NC}"
echo -e "${YELLOW}Test de login...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"password123"}' http://localhost:4000/api/v1/auth/login)
echo "$LOGIN_RESPONSE" | grep -q "token"
print_result $? "Login"

# Extraire le token pour les tests suivants
TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -n "$TOKEN" ]; then
  echo -e "${GREEN}✅ Token obtenu avec succès${NC}"
  
  # Test d'un endpoint protégé
  echo -e "${YELLOW}Test d'un endpoint protégé...${NC}"
  curl -s -H "Authorization: Bearer $TOKEN" http://localhost:4000/api/v1/status | grep -q "server\|timestamp\|environment"
  print_result $? "Endpoint protégé"
else
  echo -e "${RED}❌ Impossible d'obtenir un token${NC}"
  FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# 5. Vérifier le frontend
echo -e "\n${PURPLE}[5/5] Test du frontend...${NC}"
echo -e "${YELLOW}Vérification de l'accessibilité du frontend...${NC}"
curl -s -I http://localhost:5179 | grep -q "200\|304\|404"
print_result $? "Accessibilité du frontend"

# Résumé final
echo -e "\n${BLUE}=== Résumé des tests ===${NC}"
if [ $FAILED_TESTS -eq 0 ]; then
  echo -e "${GREEN}✅ Tous les tests ont réussi!${NC}"
  exit 0
else
  echo -e "${RED}❌ $FAILED_TESTS test(s) ont échoué.${NC}"
  echo -e "${YELLOW}Consultez les messages d'erreur ci-dessus pour plus de détails.${NC}"
  exit 1
fi 