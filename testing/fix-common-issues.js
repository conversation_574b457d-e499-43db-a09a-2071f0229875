#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const BACKEND_DIR = path.join(ROOT_DIR, 'backend');
const FRONTEND_DIR = path.join(ROOT_DIR, 'frontend');

// Couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Fonction pour logger avec couleurs
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Vérifier si un fichier existe
async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

// Créer un fichier de déclaration de types Express
async function fixExpressTypes() {
  const typesDir = path.join(BACKEND_DIR, 'src', 'types', 'express');
  const indexDtsPath = path.join(typesDir, 'index.d.ts');
  
  log('🔍 Vérification des types Express...', 'blue');
  
  try {
    // Créer le répertoire si nécessaire
    await fs.mkdir(typesDir, { recursive: true });
    
    // Vérifier si le fichier existe déjà
    const exists = await fileExists(indexDtsPath);
    
    if (!exists) {
      log('⚠️ Fichier de déclaration de types Express manquant. Création en cours...', 'yellow');
      
      const content = `import { Request as ExpressRequest } from 'express';

declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role?: string;
      };
    }
  }
}

export {};`;
      
      await fs.writeFile(indexDtsPath, content);
      log('✅ Fichier de déclaration de types Express créé avec succès!', 'green');
    } else {
      log('✅ Fichier de déclaration de types Express existe déjà.', 'green');
    }
  } catch (error) {
    log(`❌ Erreur lors de la création des types Express: ${error.message}`, 'red');
  }
}

// Corriger l'import de Payment dans app.ts
async function fixPaymentImport() {
  const appTsPath = path.join(BACKEND_DIR, 'src', 'app.ts');
  
  log('🔍 Vérification de l\'import de Payment dans app.ts...', 'blue');
  
  try {
    if (await fileExists(appTsPath)) {
      let content = await fs.readFile(appTsPath, 'utf8');
      
      // Vérifier si l'import problématique existe
      if (content.includes('import Payment from \'./models/Payment\'')) {
        log('⚠️ Import problématique de Payment détecté. Correction en cours...', 'yellow');
        
        // Corriger l'import
        content = content.replace(
          'import Payment from \'./models/Payment\';',
          'import { Payment } from \'./models/Payment\';'
        );
        
        await fs.writeFile(appTsPath, content);
        log('✅ Import de Payment corrigé avec succès!', 'green');
      } else {
        log('✅ Aucun problème d\'import de Payment détecté.', 'green');
      }
    } else {
      log('⚠️ Le fichier app.ts n\'existe pas.', 'yellow');
    }
  } catch (error) {
    log(`❌ Erreur lors de la correction de l'import de Payment: ${error.message}`, 'red');
  }
}

// Corriger les routes utilisateur
async function fixUserRoutes() {
  const userRoutesPath = path.join(BACKEND_DIR, 'src', 'routes', 'user.routes.ts');
  
  log('🔍 Vérification des routes utilisateur...', 'blue');
  
  try {
    if (await fileExists(userRoutesPath)) {
      let content = await fs.readFile(userRoutesPath, 'utf8');
      
      // Corriger l'import du middleware d'authentification
      if (content.includes('import { authenticate } from \'../middlewares/auth\'')) {
        log('✅ Import du middleware d\'authentification est correct.', 'green');
      } else if (content.includes('import { authenticate } from \'../middleware/auth\'')) {
        log('✅ Import du middleware d\'authentification est correct.', 'green');
      } else {
        log('⚠️ Import problématique du middleware d\'authentification. Correction en cours...', 'yellow');
        
        // Corriger l'import
        content = content.replace(
          /import.*from ['"]\.\.\/middlewares\/auth['"]/,
          'import { authenticate } from \'../middleware/auth\''
        );
        
        await fs.writeFile(userRoutesPath, content);
        log('✅ Import du middleware d\'authentification corrigé!', 'green');
      }
      
      // Corriger l'appel à la méthode deleteProfile
      if (content.includes('deleteProfile')) {
        log('⚠️ Méthode deleteProfile non définie détectée. Correction en cours...', 'yellow');
        
        // Corriger l'appel de méthode
        content = content.replace(
          /UserController\.deleteProfile/g,
          'UserController.getProfile /* TODO: Implémenter deleteProfile */'
        );
        
        await fs.writeFile(userRoutesPath, content);
        log('✅ Appel à deleteProfile corrigé temporairement!', 'green');
      } else {
        log('✅ Aucun problème avec deleteProfile détecté.', 'green');
      }
    } else {
      log('⚠️ Le fichier user.routes.ts n\'existe pas.', 'yellow');
    }
  } catch (error) {
    log(`❌ Erreur lors de la correction des routes utilisateur: ${error.message}`, 'red');
  }
}

// Créer un fichier .env.local pour le frontend
async function createFrontendEnv() {
  const envPath = path.join(FRONTEND_DIR, '.env.local');
  
  log('🔍 Vérification du fichier .env.local pour le frontend...', 'blue');
  
  try {
    if (!await fileExists(envPath)) {
      log('⚠️ Fichier .env.local manquant. Création en cours...', 'yellow');
      
      const content = `NEXT_PUBLIC_API_URL=http://localhost:4000/api/v1
NEXT_PUBLIC_WS_URL=ws://localhost:4000`;
      
      await fs.writeFile(envPath, content);
      log('✅ Fichier .env.local créé avec succès!', 'green');
    } else {
      log('✅ Le fichier .env.local existe déjà.', 'green');
    }
  } catch (error) {
    log(`❌ Erreur lors de la création du fichier .env.local: ${error.message}`, 'red');
  }
}

// Fonction principale
async function main() {
  log('🔧 Démarrage de la correction automatique des problèmes courants...', 'magenta');
  
  // Corriger les problèmes backend
  await fixExpressTypes();
  await fixPaymentImport();
  await fixUserRoutes();
  
  // Corriger les problèmes frontend
  await createFrontendEnv();
  
  log('✅ Correction automatique terminée!', 'green');
  log('📋 Exécutez maintenant les tests avec: ./test-servers.sh', 'cyan');
}

// Exécuter le script
main().catch(error => {
  log(`❌ Erreur non gérée: ${error.message}`, 'red');
  process.exit(1);
}); 