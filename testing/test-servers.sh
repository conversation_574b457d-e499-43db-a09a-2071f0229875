#!/bin/bash

# Couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Aller dans le répertoire du script
cd "$(dirname "$0")"

echo -e "${BLUE}=== MindFlow Pro - Test automatisé des serveurs ===${NC}"

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js n'est pas installé. Veuillez l'installer avant de continuer.${NC}"
    exit 1
fi

# Vérifier si Playwright est installé
if ! npx playwright --version &> /dev/null; then
    echo -e "${YELLOW}Playwright n'est pas installé ou accessible. Installation en cours...${NC}"
    npm install -D @playwright/test
    npx playwright install chromium
fi

# Exécuter le script de test
echo -e "${PURPLE}Démarrage des tests automatisés...${NC}"
node run-server-tests.js

# Vérifier le code de sortie
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Tests terminés avec succès!${NC}"
    exit 0
else
    echo -e "${RED}❌ Des erreurs se sont produites pendant les tests.${NC}"
    exit 1
fi 