# MindFlow Pro - Outils de Test et Automatisation

Ce répertoire contient des outils pour tester, diagnostiquer et gérer automatiquement les serveurs frontend et backend de MindFlow Pro.

## Scripts disponibles

### 1. Installation et configuration

```bash
# Installer et configurer le projet
./setup-project.sh
```

Ce script:
- Installe toutes les dépendances nécessaires
- Configure les fichiers d'environnement
- Prépare les fichiers de types
- Rend tous les scripts exécutables

### 2. Gestion des serveurs

```bash
# Vérifier l'état des serveurs
node manage-servers.js status

# Démarrer les serveurs
node manage-servers.js start

# Arrêter les serveurs
node manage-servers.js stop

# Redémarrer les serveurs
node manage-servers.js restart
```

### 3. Diagnostic et correction automatique

```bash
# Vérifier rapidement si les serveurs sont en ligne
node check-servers.js

# Corriger les problèmes courants
node fix-common-issues.js

# Exécuter les tests automatisés
./test-servers.sh
```

## Résolution des problèmes courants

### Le serveur backend ne démarre pas

1. Vérifiez que tous les ports nécessaires sont disponibles:
   ```bash
   lsof -i:4000
   ```

2. Exécutez le script de correction automatique:
   ```bash
   node fix-common-issues.js
   ```

3. Démarrez le serveur minimal pour tester:
   ```bash
   cd ../backend && npx ts-node minimal-server.ts
   ```

### Le serveur frontend ne démarre pas

1. Vérifiez que le fichier `.env.local` existe dans le répertoire frontend:
   ```bash
   cat ../frontend/.env.local
   ```

2. Vérifiez que les ports nécessaires sont disponibles:
   ```bash
   lsof -i:5179
   ```

3. Démarrez le frontend manuellement:
   ```bash
   cd ../frontend && npm run dev
   ```

## Ports utilisés

- Backend: `http://localhost:4000`
- Frontend: `http://localhost:5179`

## Utilisateurs de test

- Email: `<EMAIL>` / Mot de passe: `password123`
- Email: `<EMAIL>` / Mot de passe: `admin123` 