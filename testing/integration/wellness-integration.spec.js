/**
 * Wellness Modules Integration Tests
 * Tests journal creation integration with wellness modules and therapeutic paths
 */

const { test, expect } = require('@playwright/test');

const BASE_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:4000/api/v1';

test.describe('Journal Creation - Wellness Modules Integration', () => {
  
  test.beforeEach(async ({ page }) => {
    // Login as regular user
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'UserPass123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(dashboard|journal)/);
  });
  
  test('should reference wellness modules in journal entries', async ({ page }) => {
    // Navigate to journal creation
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Create entry referencing wellness module
    await page.fill('input[placeholder*="title"]', 'Mindfulness Practice Reflection');
    await page.fill('textarea[placeholder*="content"]', 'Completed the Introduction to Mindfulness module today. The breathing exercises were particularly helpful for managing my anxiety.');
    
    // Add relevant tags
    await page.click('button:has-text("#mindfulness")');
    await page.click('button:has-text("#meditation")');
    
    // Add custom tag for module reference
    await page.fill('input[placeholder*="custom tag"]', 'wellness-module');
    await page.press('input[placeholder*="custom tag"]', 'Enter');
    
    // Submit entry
    await page.click('button[type="submit"]');
    
    // Verify success
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
  });
  
  test('should track mood changes related to wellness activities', async ({ page }) => {
    // Navigate to journal creation
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Create entry tracking mood improvement
    await page.fill('input[placeholder*="title"]', 'Post-Meditation Mood Check');
    await page.fill('textarea[placeholder*="content"]', 'After completing the 10-minute guided meditation from the wellness module, I feel much calmer and more centered.');
    
    // Select improved mood
    await page.click('button:has-text("Great")'); // Mood level 4-5
    
    // Add emotions
    await page.click('button:has-text("calm")');
    await page.click('button:has-text("peaceful")');
    
    // Lower stress level
    await page.fill('input[placeholder="0"][max="10"]:nth(0)', '2'); // Stress level
    
    // Higher energy level
    await page.fill('input[placeholder="0"][max="10"]:nth(1)', '7'); // Energy level
    
    // Add gratitude related to wellness
    await page.fill('textarea[placeholder*="grateful"]', 'Grateful for access to guided meditation resources and wellness tools.');
    
    // Submit entry
    await page.click('button[type="submit"]');
    
    // Verify success
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
  });
  
  test('should integrate with therapeutic path progress', async ({ page }) => {
    // Navigate to journal creation
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Create entry documenting therapeutic path progress
    await page.fill('input[placeholder*="title"]', 'Anxiety Management Path - Week 2');
    await page.fill('textarea[placeholder*="content"]', 'Continuing with the anxiety management therapeutic path. The cognitive restructuring exercises from this week have been challenging but helpful.');
    
    // Select entry type as goals
    await page.selectOption('select', 'goals');
    
    // Add relevant emotions
    await page.click('button:has-text("hopeful")');
    await page.click('button:has-text("determined")');
    
    // Add goals related to therapeutic path
    await page.fill('textarea[placeholder*="goals"]', 'Complete all exercises in the anxiety management path this week. Practice daily mindfulness for 10 minutes.');
    
    // Add challenges
    await page.fill('textarea[placeholder*="challenges"]', 'Finding time for daily practice. Some exercises trigger initial anxiety before helping.');
    
    // Add achievements
    await page.fill('textarea[placeholder*="accomplished"]', 'Successfully completed 5 out of 7 daily mindfulness sessions this week.');
    
    // Add therapeutic path tag
    await page.fill('input[placeholder*="custom tag"]', 'therapeutic-path');
    await page.press('input[placeholder*="custom tag"]', 'Enter');
    
    // Submit entry
    await page.click('button[type="submit"]');
    
    // Verify success
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
  });
  
});

test.describe('Journal Creation - Module Progress Tracking', () => {
  
  test.beforeEach(async ({ page }) => {
    // Login as regular user
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'UserPass123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(dashboard|journal)/);
  });
  
  test('should document module completion in journal', async ({ page }) => {
    // Navigate to journal creation
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Create entry documenting module completion
    await page.fill('input[placeholder*="title"]', 'Completed Sleep Hygiene Module');
    await page.fill('textarea[placeholder*="content"]', 'Just finished the Sleep Hygiene Fundamentals module. Learned about the importance of consistent sleep schedules and bedroom environment.');
    
    // Select gratitude entry type
    await page.selectOption('select', 'gratitude');
    
    // Add positive emotions
    await page.click('button:has-text("accomplished")');
    await page.click('button:has-text("motivated")');
    
    // Add gratitude for learning
    await page.fill('textarea[placeholder*="grateful"]', 'Grateful for the comprehensive sleep education and practical tips provided in the module.');
    
    // Add goals for implementation
    await page.fill('textarea[placeholder*="goals"]', 'Implement the sleep hygiene recommendations starting tonight. Create a consistent bedtime routine.');
    
    // Add achievements
    await page.fill('textarea[placeholder*="accomplished"]', 'Completed all 8 lessons in the Sleep Hygiene Fundamentals module with 100% quiz scores.');
    
    // Add module-related tags
    await page.click('button:has-text("#sleep")');
    await page.click('button:has-text("#health")');
    
    // Submit entry
    await page.click('button[type="submit"]');
    
    // Verify success
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
  });
  
  test('should track skill application from modules', async ({ page }) => {
    // Navigate to journal creation
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Create entry about applying learned skills
    await page.fill('input[placeholder*="title"]', 'Applied Breathing Technique from Module');
    await page.fill('textarea[placeholder*="content"]', 'Used the 4-7-8 breathing technique I learned in the anxiety management module during a stressful meeting today. It really helped me stay calm and focused.');
    
    // Select mood entry type
    await page.selectOption('select', 'mood');
    
    // Indicate improved mood
    await page.click('button:has-text("Good")');
    
    // Add relevant emotions
    await page.click('button:has-text("calm")');
    await page.click('button:has-text("confident")');
    
    // Lower stress level (technique worked)
    await page.fill('input[placeholder="0"][max="10"]:nth(0)', '3');
    
    // Add achievements
    await page.fill('textarea[placeholder*="accomplished"]', 'Successfully applied breathing technique in real-world stressful situation.');
    
    // Add relevant tags
    await page.click('button:has-text("#stress")');
    await page.click('button:has-text("#mindfulness")');
    
    // Submit entry
    await page.click('button[type="submit"]');
    
    // Verify success
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
  });
  
});

test.describe('Journal Creation - Wellness Data Integration', () => {
  
  test('should correlate journal mood with wellness activity completion', async ({ page, request }) => {
    // Login
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'UserPass123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(dashboard|journal)/);
    
    // First, create a journal entry with low mood
    await page.goto(`${BASE_URL}/journal/new`);
    await page.fill('input[placeholder*="title"]', 'Feeling Stressed Before Module');
    await page.fill('textarea[placeholder*="content"]', 'Having a difficult day with high stress levels. About to start a mindfulness module.');
    await page.click('button:has-text("Poor")'); // Low mood
    await page.fill('input[placeholder="0"][max="10"]:nth(0)', '8'); // High stress
    await page.click('button[type="submit"]');
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
    
    // Simulate completing a wellness module (this would normally be done through the wellness interface)
    // For testing purposes, we'll create another journal entry showing improvement
    
    await page.goto(`${BASE_URL}/journal/new`);
    await page.fill('input[placeholder*="title"]', 'Feeling Better After Mindfulness');
    await page.fill('textarea[placeholder*="content"]', 'Completed the mindfulness module and feel much more centered and calm.');
    await page.click('button:has-text("Good")'); // Improved mood
    await page.fill('input[placeholder="0"][max="10"]:nth(0)', '3'); // Lower stress
    await page.click('button[type="submit"]');
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
  });
  
});
