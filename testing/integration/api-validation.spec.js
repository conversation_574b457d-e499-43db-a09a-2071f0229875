/**
 * API Integration Validation Tests
 * Tests frontend-backend API integration for journal creation
 */

const { test, expect } = require('@playwright/test');

const BASE_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:4000/api/v1';

// Helper function to get auth token
async function getAuthToken(email, password) {
  const response = await fetch(`${API_URL}/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  });
  
  if (!response.ok) {
    throw new Error(`Login failed: ${response.status}`);
  }
  
  const data = await response.json();
  return data.data.tokens.accessToken;
}

test.describe('Journal Creation - API Integration Validation', () => {
  
  test('should validate API request/response structure', async ({ page, request }) => {
    // Get auth token
    const token = await getAuthToken('<EMAIL>', 'UserPass123');
    
    // Test direct API call with correct structure
    const journalData = {
      title: 'API Validation Test Entry',
      content: 'Testing API request/response structure validation',
      entryType: 'daily',
      moodLevel: 3,
      tags: ['api-test', 'validation'],
      emotions: ['focused', 'determined'],
      stressLevel: 4,
      energyLevel: 6,
      sleepQuality: 7,
      gratitudeNotes: 'Grateful for robust API testing',
      goals: 'Ensure API validation is comprehensive',
      challenges: 'Testing all edge cases thoroughly',
      achievements: 'Implemented comprehensive API validation',
      isPrivate: false
    };
    
    const response = await request.post(`${API_URL}/journal`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: journalData
    });
    
    expect(response.status()).toBe(201);
    
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
    expect(responseData.message).toContain('created successfully');
    expect(responseData.data.entry).toBeDefined();
    expect(responseData.data.entry.title).toBe(journalData.title);
    expect(responseData.data.entry.content).toBe(journalData.content);
  });
  
  test('should handle API validation errors correctly', async ({ page, request }) => {
    const token = await getAuthToken('<EMAIL>', 'UserPass123');
    
    // Test with invalid data
    const invalidData = {
      title: '', // Empty title should fail
      content: '', // Empty content should fail
      moodLevel: 15, // Invalid mood level
      stressLevel: -5, // Invalid stress level
      energyLevel: 20 // Invalid energy level
    };
    
    const response = await request.post(`${API_URL}/journal`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: invalidData
    });
    
    expect(response.status()).toBe(400);
    
    const responseData = await response.json();
    expect(responseData.success).toBe(false);
    expect(responseData.message).toContain('required');
  });
  
  test('should validate authentication for API calls', async ({ page, request }) => {
    // Test without authentication token
    const journalData = {
      title: 'Unauthorized Test',
      content: 'This should fail without auth token'
    };
    
    const response = await request.post(`${API_URL}/journal`, {
      headers: { 'Content-Type': 'application/json' },
      data: journalData
    });
    
    expect(response.status()).toBe(401);
    
    const responseData = await response.json();
    expect(responseData.success).toBe(false);
  });
  
  test('should validate token expiration handling', async ({ page, request }) => {
    // Test with expired/invalid token
    const journalData = {
      title: 'Expired Token Test',
      content: 'Testing expired token handling'
    };
    
    const response = await request.post(`${API_URL}/journal`, {
      headers: {
        'Authorization': 'Bearer invalid-expired-token',
        'Content-Type': 'application/json'
      },
      data: journalData
    });
    
    expect(response.status()).toBe(401);
    
    const responseData = await response.json();
    expect(responseData.success).toBe(false);
  });
  
});

test.describe('Journal Creation - Frontend API Integration', () => {
  
  test('should handle API responses correctly in frontend', async ({ page }) => {
    // Login
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'UserPass123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(dashboard|journal)/);
    
    // Intercept API call to verify request structure
    let apiRequest = null;
    await page.route('**/api/v1/journal', route => {
      apiRequest = route.request();
      route.continue();
    });
    
    // Create journal entry through frontend
    await page.goto(`${BASE_URL}/journal/new`);
    await page.fill('input[placeholder*="title"]', 'Frontend API Integration Test');
    await page.fill('textarea[placeholder*="content"]', 'Testing frontend to API integration');
    await page.selectOption('select', 'daily');
    await page.click('button:has-text("Good")');
    await page.click('button:has-text("happy")');
    await page.click('button:has-text("#testing")');
    await page.fill('input[placeholder="0"][max="10"]:nth(0)', '3');
    await page.fill('input[placeholder="0"][max="10"]:nth(1)', '7');
    await page.fill('input[placeholder="0"][max="10"]:nth(2)', '8');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Verify API call was made with correct structure
    expect(apiRequest).not.toBeNull();
    expect(apiRequest.method()).toBe('POST');
    expect(apiRequest.url()).toContain('/api/v1/journal');
    
    const requestData = JSON.parse(apiRequest.postData());
    expect(requestData.title).toBe('Frontend API Integration Test');
    expect(requestData.content).toBe('Testing frontend to API integration');
    expect(requestData.entryType).toBe('daily');
    expect(requestData.emotions).toContain('happy');
    expect(requestData.tags).toContain('testing');
    
    // Verify success response handling
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
  });
  
  test('should handle API errors gracefully in frontend', async ({ page }) => {
    // Login
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'UserPass123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(dashboard|journal)/);
    
    // Mock API error response
    await page.route('**/api/v1/journal', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          message: 'Database connection error'
        })
      });
    });
    
    // Create journal entry
    await page.goto(`${BASE_URL}/journal/new`);
    await page.fill('input[placeholder*="title"]', 'Error Handling Test');
    await page.fill('textarea[placeholder*="content"]', 'Testing error handling');
    await page.click('button[type="submit"]');
    
    // Verify error is displayed to user
    await expect(page.locator('text=Database connection error')).toBeVisible();
    
    // Form should remain visible for retry
    await expect(page.locator('form')).toBeVisible();
    await expect(page.locator('input[placeholder*="title"]')).toHaveValue('Error Handling Test');
  });
  
  test('should handle network timeouts appropriately', async ({ page }) => {
    // Login
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'UserPass123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(dashboard|journal)/);
    
    // Mock slow API response
    await page.route('**/api/v1/journal', route => {
      // Delay response to simulate timeout
      setTimeout(() => {
        route.fulfill({
          status: 408,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            message: 'Request timeout'
          })
        });
      }, 5000);
    });
    
    // Create journal entry
    await page.goto(`${BASE_URL}/journal/new`);
    await page.fill('input[placeholder*="title"]', 'Timeout Test');
    await page.fill('textarea[placeholder*="content"]', 'Testing timeout handling');
    await page.click('button[type="submit"]');
    
    // Should show loading state
    await expect(page.locator('button:has-text("Saving...")')).toBeVisible();
    
    // Eventually should show timeout error
    await expect(page.locator('text=Request timeout')).toBeVisible({ timeout: 10000 });
  });
  
});

test.describe('Journal Creation - Data Validation Integration', () => {
  
  test('should validate data consistency between frontend and backend', async ({ page, request }) => {
    const token = await getAuthToken('<EMAIL>', 'UserPass123');
    
    // Create entry through frontend
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'UserPass123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(dashboard|journal)/);
    
    await page.goto(`${BASE_URL}/journal/new`);
    await page.fill('input[placeholder*="title"]', 'Data Consistency Test');
    await page.fill('textarea[placeholder*="content"]', 'Testing data consistency between frontend and backend');
    await page.selectOption('select', 'gratitude');
    await page.click('button:has-text("Great")');
    await page.click('button:has-text("grateful")');
    await page.click('button:has-text("content")');
    await page.click('button:has-text("#gratitude")');
    await page.fill('textarea[placeholder*="grateful"]', 'Grateful for comprehensive testing');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
    
    // Retrieve entry through API to verify data consistency
    const entriesResponse = await request.get(`${API_URL}/journal`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    expect(entriesResponse.status()).toBe(200);
    
    const entriesData = await entriesResponse.json();
    const createdEntry = entriesData.data.entries.find(entry => 
      entry.title === 'Data Consistency Test'
    );
    
    expect(createdEntry).toBeDefined();
    expect(createdEntry.content).toBe('Testing data consistency between frontend and backend');
    expect(createdEntry.entryType).toBe('gratitude');
    expect(createdEntry.moodLevel).toBe(5); // "Great" should map to 5
    expect(createdEntry.emotions).toContain('grateful');
    expect(createdEntry.emotions).toContain('content');
    expect(createdEntry.tags).toContain('gratitude');
    expect(createdEntry.gratitudeNotes).toBe('Grateful for comprehensive testing');
  });
  
});
