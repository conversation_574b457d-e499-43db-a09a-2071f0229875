/**
 * Authentication System Integration Tests
 * Tests journal creation integration with AuthContext2 and session management
 */

const { test, expect } = require('@playwright/test');

const BASE_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:4000/api/v1';

test.describe('Journal Creation - Authentication Integration', () => {
  
  test('should maintain authentication state across journal operations', async ({ page }) => {
    // Login as regular user
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'UserPass123');
    await page.click('button[type="submit"]');
    
    // Verify login successful
    await page.waitForURL(/\/(dashboard|journal)/);
    
    // Navigate to journal creation
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Verify user context is available
    await expect(page.locator('text=Test User')).toBeVisible();
    
    // Create journal entry
    await page.fill('input[placeholder*="title"]', 'Auth Integration Test');
    await page.fill('textarea[placeholder*="content"]', 'Testing authentication integration');
    await page.click('button[type="submit"]');
    
    // Verify success and maintained session
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
    
    // Navigate to another page and back
    await page.goto(`${BASE_URL}/dashboard`);
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Should still be authenticated
    await expect(page.locator('form')).toBeVisible();
  });
  
  test('should handle token expiration during journal creation', async ({ page }) => {
    // Login first
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'UserPass123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(dashboard|journal)/);
    
    // Navigate to journal creation
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Fill form
    await page.fill('input[placeholder*="title"]', 'Token Expiration Test');
    await page.fill('textarea[placeholder*="content"]', 'Testing token expiration handling');
    
    // Mock expired token response
    await page.route('**/api/v1/journal', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          message: 'Token expired'
        })
      });
    });
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should redirect to login or show auth error
    await expect(page.locator('text=Token expired')).toBeVisible();
  });
  
  test('should prevent unauthorized access to journal creation', async ({ page }) => {
    // Try to access journal creation without authentication
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Should redirect to login or show error
    await page.waitForURL(/\/login/);
    await expect(page.locator('h1, h2')).toContainText(/login|sign in/i);
  });
  
  test('should handle different user roles correctly', async ({ page }) => {
    const userRoles = [
      { email: '<EMAIL>', password: 'UserPass123', role: 'user' },
      { email: '<EMAIL>', password: 'ProfessionalPass123', role: 'professional' },
      { email: '<EMAIL>', password: 'AdminPass123', role: 'admin' }
    ];
    
    for (const user of userRoles) {
      // Login as different user
      await page.goto(`${BASE_URL}/login`);
      await page.fill('input[type="email"]', user.email);
      await page.fill('input[type="password"]', user.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/\/(dashboard|journal)/);
      
      // Navigate to journal creation
      await page.goto(`${BASE_URL}/journal/new`);
      
      // All roles should be able to create journal entries
      await expect(page.locator('form')).toBeVisible();
      
      // Create entry
      await page.fill('input[placeholder*="title"]', `${user.role} Role Test`);
      await page.fill('textarea[placeholder*="content"]', `Testing journal creation as ${user.role}`);
      await page.click('button[type="submit"]');
      
      // Verify success
      await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
      
      // Logout
      await page.goto(`${BASE_URL}/logout`);
    }
  });
  
});

test.describe('Journal Creation - Session Management', () => {
  
  test('should preserve form data during session refresh', async ({ page }) => {
    // Login
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'UserPass123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(dashboard|journal)/);
    
    // Navigate to journal creation
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Fill form partially
    await page.fill('input[placeholder*="title"]', 'Session Test Entry');
    await page.fill('textarea[placeholder*="content"]', 'Testing session management');
    
    // Refresh page
    await page.reload();
    
    // Check if session is maintained
    await expect(page.locator('form')).toBeVisible();
    
    // Note: Form data preservation would need to be implemented
    // This test documents the expected behavior
  });
  
  test('should handle concurrent sessions correctly', async ({ browser }) => {
    // Create two browser contexts (simulating different devices)
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    // Login on both contexts with same user
    for (const page of [page1, page2]) {
      await page.goto(`${BASE_URL}/login`);
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'UserPass123');
      await page.click('button[type="submit"]');
      await page.waitForURL(/\/(dashboard|journal)/);
    }
    
    // Create journal entries from both sessions
    await page1.goto(`${BASE_URL}/journal/new`);
    await page1.fill('input[placeholder*="title"]', 'Session 1 Entry');
    await page1.fill('textarea[placeholder*="content"]', 'From session 1');
    
    await page2.goto(`${BASE_URL}/journal/new`);
    await page2.fill('input[placeholder*="title"]', 'Session 2 Entry');
    await page2.fill('textarea[placeholder*="content"]', 'From session 2');
    
    // Submit both entries
    await page1.click('button[type="submit"]');
    await page2.click('button[type="submit"]');
    
    // Both should succeed
    await expect(page1.locator('text=Journal entry created successfully')).toBeVisible();
    await expect(page2.locator('text=Journal entry created successfully')).toBeVisible();
    
    await context1.close();
    await context2.close();
  });
  
});
