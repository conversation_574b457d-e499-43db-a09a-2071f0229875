-- MindFlow Pro Test User Setup Script
-- This script creates test users with different permission levels for comprehensive testing

-- Test Admin User
INSERT OR IGNORE INTO users (
    id, email, password, firstName, lastName, status, userType, 
    isEmailVerified, roleId, createdAt, updatedAt
) VALUES (
    'test-admin-001', 
    '<EMAIL>',
    '$2b$10$rQJ8vQZ9QZ9QZ9QZ9QZ9QOuKqGqGqGqGqGqGqGqGqGqGqGqGqGqGqG', -- AdminPass123
    'Test',
    'Admin',
    'active',
    'individual',
    1,
    (SELECT id FROM roles WHERE name = 'admin'),
    datetime('now'),
    datetime('now')
);

-- Test Regular User
INSERT OR IGNORE INTO users (
    id, email, password, firstName, lastName, status, userType, 
    isEmailVerified, roleId, createdAt, updatedAt
) VALUES (
    'test-user-001', 
    '<EMAIL>',
    '$2b$10$rQJ8vQZ9QZ9QZ9QZ9QZ9QOuKqGqGqGqGqGqGqGqGqGqGqGqGqGqGqG', -- UserPass123
    'Test',
    'User',
    'active',
    'individual',
    1,
    (SELECT id FROM roles WHERE name = 'user'),
    datetime('now'),
    datetime('now')
);

-- Test Professional User 2
INSERT OR IGNORE INTO users (
    id, email, password, firstName, lastName, status, userType, 
    isEmailVerified, roleId, createdAt, updatedAt
) VALUES (
    'test-prof-001', 
    '<EMAIL>',
    '$2b$10$rQJ8vQZ9QZ9QZ9QZ9QZ9QOuKqGqGqGqGqGqGqGqGqGqGqGqGqGqGqG', -- ProfessionalPass123
    'Test',
    'Professional',
    'active',
    'individual',
    1,
    (SELECT id FROM roles WHERE name = 'professional'),
    datetime('now'),
    datetime('now')
);

-- Create professional profile for test professional
INSERT OR IGNORE INTO professional_profiles (
    id, userId, licenseNumber, specializations, yearsOfExperience,
    education, certifications, bio, isVerified, verificationStatus,
    consultationFee, availableHours, timeZone, createdAt, updatedAt
) VALUES (
    'test-prof-profile-001',
    'test-prof-001',
    'TEST-LIC-001',
    '["Anxiety Disorders", "Depression", "Stress Management"]',
    5,
    '["PhD in Clinical Psychology - Test University"]',
    '["Licensed Clinical Psychologist", "Cognitive Behavioral Therapy Certified"]',
    'Test professional for automated testing and quality assurance.',
    1,
    'verified',
    150.00,
    '{"monday": ["09:00", "17:00"], "tuesday": ["09:00", "17:00"], "wednesday": ["09:00", "17:00"]}',
    'America/New_York',
    datetime('now'),
    datetime('now')
);

-- Test User with Limited Permissions
INSERT OR IGNORE INTO users (
    id, email, password, firstName, lastName, status, userType, 
    isEmailVerified, roleId, createdAt, updatedAt
) VALUES (
    'test-limited-001', 
    '<EMAIL>',
    '$2b$10$rQJ8vQZ9QZ9QZ9QZ9QZ9QOuKqGqGqGqGqGqGqGqGqGqGqGqGqGqGqG', -- LimitedPass123
    'Limited',
    'User',
    'active',
    'individual',
    1,
    (SELECT id FROM roles WHERE name = 'user'),
    datetime('now'),
    datetime('now')
);

-- Sample Journal Entries for Testing
INSERT OR IGNORE INTO journal_entries (
    id, userId, title, content, entryType, moodLevel, tags, emotions,
    stressLevel, energyLevel, sleepQuality, gratitudeNotes, goals,
    challenges, achievements, isPrivate, isFavorite, createdAt, updatedAt
) VALUES 
(
    'test-entry-001',
    'test-user-001',
    'Test Entry - Minimal Data',
    'This is a minimal test journal entry with only required fields.',
    'daily',
    3,
    '[]',
    '[]',
    0,
    0,
    0,
    NULL,
    NULL,
    NULL,
    NULL,
    0,
    0,
    datetime('now', '-1 day'),
    datetime('now', '-1 day')
),
(
    'test-entry-002',
    'test-user-001',
    'Test Entry - Complete Data',
    'This is a comprehensive test journal entry with all fields populated for testing purposes.',
    'gratitude',
    4,
    '["test", "automation", "quality"]',
    '["happy", "confident", "motivated"]',
    3,
    7,
    8,
    'Grateful for automated testing capabilities and quality assurance processes.',
    'Complete comprehensive testing of journal functionality.',
    'Ensuring all edge cases are covered in testing scenarios.',
    'Successfully implemented automated test suite.',
    0,
    1,
    datetime('now', '-2 hours'),
    datetime('now', '-2 hours')
),
(
    'test-entry-003',
    'test-user-001',
    'Test Entry - Private',
    'This is a private test journal entry to verify privacy controls.',
    'mood',
    2,
    '["private", "testing"]',
    '["anxious", "focused"]',
    6,
    4,
    5,
    NULL,
    'Test privacy functionality thoroughly.',
    'Ensuring private entries remain secure.',
    NULL,
    1,
    0,
    datetime('now', '-1 hour'),
    datetime('now', '-1 hour')
);

-- Test Wellness Module Progress for Integration Testing
INSERT OR IGNORE INTO user_module_progress (
    id, userId, moduleId, status, progressPercentage, timeSpentMinutes,
    lastAccessedAt, completedAt, notes, createdAt, updatedAt
) VALUES (
    'test-progress-001',
    'test-user-001',
    (SELECT id FROM wellness_modules WHERE title = 'Introduction to Mindfulness' LIMIT 1),
    'completed',
    100,
    25,
    datetime('now', '-1 day'),
    datetime('now', '-1 day'),
    'Completed for testing integration with journal entries.',
    datetime('now', '-2 days'),
    datetime('now', '-1 day')
);

-- Test Notifications for Integration Testing
INSERT OR IGNORE INTO notifications (
    id, userId, type, title, message, isRead, priority,
    metadata, expiresAt, createdAt, updatedAt
) VALUES (
    'test-notification-001',
    'test-user-001',
    'journal_reminder',
    'Daily Journal Reminder',
    'Don''t forget to write in your journal today!',
    0,
    'medium',
    '{"source": "automated_testing"}',
    datetime('now', '+7 days'),
    datetime('now'),
    datetime('now')
);
