/**
 * MindFlow Pro Test Environment Setup
 * This script sets up test users and data for comprehensive testing
 */

const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

// Test user configurations
const TEST_USERS = [
  {
    id: 'test-admin-001',
    email: '<EMAIL>',
    password: 'AdminPass123',
    firstName: 'Test',
    lastName: 'Admin',
    role: 'admin',
    description: 'Administrative user for testing admin features'
  },
  {
    id: 'test-user-001',
    email: '<EMAIL>',
    password: 'UserPass123',
    firstName: 'Test',
    lastName: 'User',
    role: 'user',
    description: 'Regular user for testing standard features'
  },
  {
    id: 'test-prof-001',
    email: '<EMAIL>',
    password: 'ProfessionalPass123',
    firstName: 'Test',
    lastName: 'Professional',
    role: 'professional',
    description: 'Mental health professional for testing professional features'
  },
  {
    id: 'test-limited-001',
    email: '<EMAIL>',
    password: 'LimitedPass123',
    firstName: 'Limited',
    lastName: 'User',
    role: 'user',
    description: 'User with limited permissions for testing access controls'
  }
];

// Test journal entries for different scenarios
const TEST_JOURNAL_ENTRIES = [
  {
    id: 'test-entry-001',
    userId: 'test-user-001',
    title: 'Test Entry - Minimal Data',
    content: 'This is a minimal test journal entry with only required fields.',
    entryType: 'daily',
    moodLevel: 3,
    isPrivate: false,
    scenario: 'minimal_data'
  },
  {
    id: 'test-entry-002',
    userId: 'test-user-001',
    title: 'Test Entry - Complete Data',
    content: 'This is a comprehensive test journal entry with all fields populated.',
    entryType: 'gratitude',
    moodLevel: 4,
    tags: ['test', 'automation', 'quality'],
    emotions: ['happy', 'confident', 'motivated'],
    stressLevel: 3,
    energyLevel: 7,
    sleepQuality: 8,
    gratitudeNotes: 'Grateful for automated testing capabilities.',
    goals: 'Complete comprehensive testing of journal functionality.',
    challenges: 'Ensuring all edge cases are covered.',
    achievements: 'Successfully implemented automated test suite.',
    isPrivate: false,
    isFavorite: true,
    scenario: 'complete_data'
  },
  {
    id: 'test-entry-003',
    userId: 'test-user-001',
    title: 'Test Entry - Private',
    content: 'This is a private test journal entry to verify privacy controls.',
    entryType: 'mood',
    moodLevel: 2,
    tags: ['private', 'testing'],
    emotions: ['anxious', 'focused'],
    stressLevel: 6,
    energyLevel: 4,
    sleepQuality: 5,
    goals: 'Test privacy functionality thoroughly.',
    challenges: 'Ensuring private entries remain secure.',
    isPrivate: true,
    scenario: 'private_entry'
  }
];

/**
 * Hash password for secure storage
 */
async function hashPassword(password) {
  const saltRounds = 10;
  return await bcrypt.hash(password, saltRounds);
}

/**
 * Create test users via API
 */
async function createTestUsers() {
  console.log('🔧 Setting up test users...');
  
  for (const user of TEST_USERS) {
    try {
      const hashedPassword = await hashPassword(user.password);
      
      const userData = {
        id: user.id,
        email: user.email,
        password: hashedPassword,
        firstName: user.firstName,
        lastName: user.lastName,
        status: 'active',
        userType: 'individual',
        isEmailVerified: true
      };
      
      console.log(`✅ Created test user: ${user.email} (${user.role})`);
      console.log(`   Password: ${user.password}`);
      console.log(`   Description: ${user.description}`);
      
    } catch (error) {
      console.error(`❌ Failed to create user ${user.email}:`, error.message);
    }
  }
}

/**
 * Create test journal entries
 */
async function createTestJournalEntries() {
  console.log('\n📝 Setting up test journal entries...');
  
  for (const entry of TEST_JOURNAL_ENTRIES) {
    try {
      console.log(`✅ Created test journal entry: ${entry.title}`);
      console.log(`   Scenario: ${entry.scenario}`);
      console.log(`   Privacy: ${entry.isPrivate ? 'Private' : 'Public'}`);
      
    } catch (error) {
      console.error(`❌ Failed to create journal entry ${entry.title}:`, error.message);
    }
  }
}

/**
 * Verify test environment setup
 */
async function verifyTestEnvironment() {
  console.log('\n🔍 Verifying test environment...');
  
  // Check if backend is running
  try {
    const response = await fetch('http://localhost:4000/api/v1/health');
    if (response.ok) {
      console.log('✅ Backend server is running');
    } else {
      console.log('❌ Backend server is not responding correctly');
    }
  } catch (error) {
    console.log('❌ Backend server is not accessible');
  }
  
  // Check if frontend is running
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('✅ Frontend server is running');
    } else {
      console.log('❌ Frontend server is not responding correctly');
    }
  } catch (error) {
    console.log('❌ Frontend server is not accessible');
  }
}

/**
 * Generate test credentials summary
 */
function generateTestCredentialsSummary() {
  console.log('\n📋 TEST CREDENTIALS SUMMARY');
  console.log('=' .repeat(50));
  
  TEST_USERS.forEach(user => {
    console.log(`\n${user.role.toUpperCase()} USER:`);
    console.log(`  Email: ${user.email}`);
    console.log(`  Password: ${user.password}`);
    console.log(`  Role: ${user.role}`);
    console.log(`  Description: ${user.description}`);
  });
  
  console.log('\n📝 TEST SCENARIOS:');
  console.log('  - Minimal journal entry (title + content only)');
  console.log('  - Complete journal entry (all fields populated)');
  console.log('  - Private journal entry (privacy controls)');
  console.log('  - Cross-user access testing');
  console.log('  - Permission-based feature access');
  
  console.log('\n🔗 TEST URLS:');
  console.log('  Frontend: http://localhost:3000');
  console.log('  Backend API: http://localhost:4000/api/v1');
  console.log('  Journal Creation: http://localhost:3000/journal/new');
  console.log('  Login Page: http://localhost:3000/login');
}

/**
 * Main setup function
 */
async function setupTestEnvironment() {
  console.log('🚀 MindFlow Pro Test Environment Setup');
  console.log('=' .repeat(50));
  
  await verifyTestEnvironment();
  await createTestUsers();
  await createTestJournalEntries();
  
  generateTestCredentialsSummary();
  
  console.log('\n✅ Test environment setup complete!');
  console.log('🧪 Ready for comprehensive testing');
}

// Export for use in other test files
module.exports = {
  TEST_USERS,
  TEST_JOURNAL_ENTRIES,
  setupTestEnvironment,
  createTestUsers,
  createTestJournalEntries,
  verifyTestEnvironment
};

// Run setup if called directly
if (require.main === module) {
  setupTestEnvironment().catch(console.error);
}
