#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const BACKEND_DIR = path.join(ROOT_DIR, 'backend');
const FRONTEND_DIR = path.join(ROOT_DIR, 'frontend');
const BACKEND_SCRIPT = path.join(BACKEND_DIR, 'enhanced-server.js');
const BACKEND_URL = 'http://localhost:4000';
const FRONTEND_URL = 'http://localhost:5179';
const MAX_RETRIES = 5;
const RETRY_DELAY = 3000; // 3 seconds

// Utilitaires
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Fonction pour vérifier si un serveur est déjà en cours d'exécution sur un port
async function isPortInUse(port) {
  return new Promise((resolve) => {
    const cmd = process.platform === 'win32' 
      ? `netstat -ano | find "LISTENING" | find ":${port}"` 
      : `lsof -i:${port} | grep LISTEN`;
    
    exec(cmd, (error, stdout, stderr) => {
      resolve(!!stdout);
    });
  });
}

// Fonction pour tester si un serveur est en ligne
async function testServerHealth(url, retries = MAX_RETRIES) {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        console.log(`✅ Serveur sur ${url} est en ligne`);
        return true;
      }
    } catch (error) {
      console.log(`❌ Tentative ${i + 1}/${retries}: Serveur sur ${url} n'est pas accessible`);
    }
    
    if (i < retries - 1) {
      console.log(`⏳ Attente de ${RETRY_DELAY/1000} secondes avant la prochaine tentative...`);
      await sleep(RETRY_DELAY);
    }
  }
  
  return false;
}

// Fonction pour démarrer le serveur backend
async function startBackendServer() {
  console.log('🚀 Démarrage du serveur backend...');
  
  // Vérifier si le fichier existe
  if (!fs.existsSync(BACKEND_SCRIPT)) {
    console.error(`❌ Le fichier ${BACKEND_SCRIPT} n'existe pas!`);
    return null;
  }
  
  // Vérifier si le port est déjà utilisé
  const port = 4000;
  const isPortBusy = await isPortInUse(port);
  
  if (isPortBusy) {
    console.log(`⚠️ Le port ${port} est déjà utilisé. Le serveur backend est peut-être déjà en cours d'exécution.`);
    return null;
  }
  
  // Démarrer le serveur
  const server = spawn('node', [BACKEND_SCRIPT], {
    cwd: BACKEND_DIR,
    stdio: 'pipe',
    detached: true
  });
  
  server.stdout.on('data', (data) => {
    console.log(`📤 Backend: ${data.toString().trim()}`);
  });
  
  server.stderr.on('data', (data) => {
    console.error(`❌ Backend Error: ${data.toString().trim()}`);
  });
  
  server.on('error', (err) => {
    console.error(`❌ Erreur lors du démarrage du serveur backend: ${err.message}`);
  });
  
  // Attendre que le serveur soit prêt
  let isReady = false;
  for (let i = 0; i < MAX_RETRIES && !isReady; i++) {
    await sleep(RETRY_DELAY);
    isReady = await testServerHealth(`${BACKEND_URL}/api/v1/health`);
  }
  
  if (!isReady) {
    console.error('❌ Le serveur backend n\'a pas pu démarrer correctement.');
    if (server) {
      process.kill(-server.pid);
    }
    return null;
  }
  
  return server;
}

// Fonction pour démarrer le serveur frontend
async function startFrontendServer() {
  console.log('🚀 Démarrage du serveur frontend...');
  
  // Vérifier si le port est déjà utilisé
  const port = 5179;
  const isPortBusy = await isPortInUse(port);
  
  if (isPortBusy) {
    console.log(`⚠️ Le port ${port} est déjà utilisé. Le serveur frontend est peut-être déjà en cours d'exécution.`);
    return null;
  }
  
  // Démarrer le serveur
  const server = spawn('npm', ['run', 'dev'], {
    cwd: FRONTEND_DIR,
    stdio: 'pipe',
    detached: true,
    env: { ...process.env, PORT: port.toString() }
  });
  
  server.stdout.on('data', (data) => {
    console.log(`📤 Frontend: ${data.toString().trim()}`);
  });
  
  server.stderr.on('data', (data) => {
    console.error(`❌ Frontend Error: ${data.toString().trim()}`);
  });
  
  server.on('error', (err) => {
    console.error(`❌ Erreur lors du démarrage du serveur frontend: ${err.message}`);
  });
  
  // Attendre que le serveur soit prêt
  let isReady = false;
  for (let i = 0; i < MAX_RETRIES && !isReady; i++) {
    await sleep(RETRY_DELAY);
    isReady = await testServerHealth(FRONTEND_URL);
  }
  
  if (!isReady) {
    console.error('❌ Le serveur frontend n\'a pas pu démarrer correctement.');
    if (server) {
      process.kill(-server.pid);
    }
    return null;
  }
  
  return server;
}

// Fonction pour exécuter les tests Playwright
async function runPlaywrightTests() {
  console.log('🧪 Exécution des tests Playwright...');
  
  const testProcess = spawn('npx', ['playwright', 'test', 'server-health-check.spec.js'], {
    cwd: __dirname,
    stdio: 'inherit'
  });
  
  return new Promise((resolve) => {
    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Tous les tests ont réussi!');
        resolve(true);
      } else {
        console.error(`❌ Des tests ont échoué avec le code de sortie ${code}`);
        resolve(false);
      }
    });
  });
}

// Fonction principale
async function main() {
  console.log('🔍 Vérification des serveurs existants...');
  
  // Vérifier si les serveurs sont déjà en cours d'exécution
  const backendAlreadyRunning = await testServerHealth(`${BACKEND_URL}/api/v1/health`, 1);
  const frontendAlreadyRunning = await testServerHealth(FRONTEND_URL, 1);
  
  let backendServer = null;
  let frontendServer = null;
  
  // Démarrer le serveur backend si nécessaire
  if (!backendAlreadyRunning) {
    backendServer = await startBackendServer();
    if (!backendServer) {
      console.error('❌ Impossible de démarrer le serveur backend. Arrêt des tests.');
      process.exit(1);
    }
  }
  
  // Démarrer le serveur frontend si nécessaire
  if (!frontendAlreadyRunning) {
    frontendServer = await startFrontendServer();
    if (!frontendServer) {
      console.error('❌ Impossible de démarrer le serveur frontend. Arrêt des tests.');
      if (backendServer) {
        process.kill(-backendServer.pid);
      }
      process.exit(1);
    }
  }
  
  // Exécuter les tests
  const testsPassed = await runPlaywrightTests();
  
  // Arrêter les serveurs si nous les avons démarrés
  if (backendServer) {
    console.log('🛑 Arrêt du serveur backend...');
    process.kill(-backendServer.pid);
  }
  
  if (frontendServer) {
    console.log('🛑 Arrêt du serveur frontend...');
    process.kill(-frontendServer.pid);
  }
  
  // Sortir avec le code approprié
  process.exit(testsPassed ? 0 : 1);
}

// Exécuter le script
main().catch(error => {
  console.error(`❌ Erreur non gérée: ${error.message}`);
  process.exit(1);
}); 