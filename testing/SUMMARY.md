# MindFlow Pro - Automatisation et Tests

## Résumé des Outils Développés

Nous avons créé un ensemble complet d'outils pour automatiser le déploiement, la gestion et les tests de l'application MindFlow Pro. Ces outils permettent de diagnostiquer et résoudre automatiquement les problèmes courants, assurant ainsi un fonctionnement fiable des serveurs backend et frontend.

### 1. Scripts de Diagnostic et Correction

- **`check-servers.js`** : Vérifie rapidement si les serveurs backend et frontend sont en cours d'exécution et répondent correctement.
- **`fix-common-issues.js`** : Détecte et corrige automatiquement les problèmes courants dans le code source, comme les erreurs de types, les imports problématiques, et les configurations manquantes.

### 2. Gestion des Serveurs

- **`manage-servers.js`** : Interface de ligne de commande pour démarrer, arrêter, redémarrer et vérifier l'état des serveurs.
- Commandes disponibles:
  - `node manage-servers.js start` : D<PERSON>marrer les serveurs
  - `node manage-servers.js stop` : Arrêter les serveurs
  - `node manage-servers.js restart` : Redémarrer les serveurs
  - `node manage-servers.js status` : Vérifier l'état des serveurs

### 3. Scripts de Test

- **`test-all.sh`** : Script de test complet qui vérifie tous les aspects du système:
  - Vérification des serveurs
  - Correction des problèmes courants
  - Test des endpoints backend
  - Test de l'authentification
  - Vérification du frontend
- **`test-servers.sh`** : Script pour tester spécifiquement les serveurs avec Playwright.

### 4. Installation et Configuration

- **`setup-project.sh`** : Script d'installation qui configure l'environnement de développement:
  - Installation des dépendances
  - Configuration des fichiers d'environnement
  - Création des fichiers de types
  - Configuration des scripts

## Architecture

Les outils sont organisés selon une architecture modulaire:

1. **Diagnostic** : Détection des problèmes dans l'environnement et le code source.
2. **Correction** : Application automatique des corrections pour les problèmes détectés.
3. **Gestion** : Contrôle des serveurs et de leur état.
4. **Test** : Validation du bon fonctionnement de l'application.

## Utilisation

Pour commencer à utiliser ces outils:

1. Exécuter le script d'installation:
   ```bash
   ./setup-project.sh
   ```

2. Démarrer les serveurs:
   ```bash
   node manage-servers.js start
   ```

3. Exécuter les tests complets:
   ```bash
   ./test-all.sh
   ```

## Résolution de Problèmes

Si vous rencontrez des problèmes:

1. Vérifiez l'état des serveurs:
   ```bash
   node manage-servers.js status
   ```

2. Exécutez l'outil de correction automatique:
   ```bash
   node fix-common-issues.js
   ```

3. Redémarrez les serveurs:
   ```bash
   node manage-servers.js restart
   ```

4. Vérifiez les logs pour plus de détails sur les erreurs. 