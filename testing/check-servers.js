#!/usr/bin/env node

const http = require('http');
const https = require('https');

// Configuration
const BACKEND_URL = 'http://localhost:4000/api/v1/health';
const FRONTEND_URL = 'http://localhost:5179/';  // Ajout du slash à la fin
const RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 2000; // 2 seconds

// Couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Fonction pour logger avec couleurs
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Fonction utilitaire pour attendre avec délai
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Fonction pour faire une requête HTTP
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    
    const req = client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data
        });
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    // Ajouter un timeout
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Timeout de la requête après 5 secondes'));
    });
    
    req.end();
  });
}

// Fonction pour tester un endpoint avec retry
async function testEndpointWithRetry(url, attempts = RETRY_ATTEMPTS) {
  for (let i = 0; i < attempts; i++) {
    try {
      log(`🔍 Tentative ${i + 1}/${attempts}: Test de ${url}...`, 'blue');
      const response = await makeRequest(url);
      
      // Pour le frontend, même un 404 est considéré comme un succès car cela signifie que le serveur est en cours d'exécution
      if ((url.includes('localhost:5179') && response.statusCode === 404) || 
          (response.statusCode >= 200 && response.statusCode < 300)) {
        log(`✅ Endpoint ${url} est accessible (status: ${response.statusCode})`, 'green');
        return { success: true, data: response.data };
      } else {
        log(`❌ Tentative ${i + 1}/${attempts}: Endpoint ${url} a retourné ${response.statusCode}`, 'red');
      }
    } catch (error) {
      log(`❌ Tentative ${i + 1}/${attempts}: Erreur en accédant à ${url} - ${error.message}`, 'red');
    }
    
    if (i < attempts - 1) {
      log(`⏳ Attente de ${RETRY_DELAY/1000} secondes avant la prochaine tentative...`, 'yellow');
      await wait(RETRY_DELAY);
    }
  }
  
  return { success: false };
}

// Fonction principale
async function main() {
  let backendOk = false;
  let frontendOk = false;
  
  log('🔍 Vérification des serveurs...', 'magenta');
  
  // Tester le backend
  log('🔍 Test du backend...', 'blue');
  const backendResult = await testEndpointWithRetry(BACKEND_URL);
  backendOk = backendResult.success;
  
  if (backendOk) {
    try {
      // Afficher les détails de la réponse
      const data = JSON.parse(backendResult.data);
      log('📊 Détails du serveur backend:', 'cyan');
      log(`  - Version: ${data.version || 'N/A'}`, 'cyan');
      log(`  - Environnement: ${data.environment || 'N/A'}`, 'cyan');
      log(`  - Status: ${data.status || 'N/A'}`, 'cyan');
      log(`  - Timestamp: ${data.timestamp || 'N/A'}`, 'cyan');
    } catch (error) {
      log(`⚠️ Impossible de parser les détails du serveur: ${error.message}`, 'yellow');
    }
  }
  
  // Tester le frontend
  log('🔍 Test du frontend...', 'blue');
  const frontendResult = await testEndpointWithRetry(FRONTEND_URL);
  frontendOk = frontendResult.success;
  
  // Afficher le résultat final
  log('\n📋 Résultat final:', 'magenta');
  log(`  - Backend: ${backendOk ? '✅ En ligne' : '❌ Hors ligne'}`, backendOk ? 'green' : 'red');
  log(`  - Frontend: ${frontendOk ? '✅ En ligne' : '❌ Hors ligne'}`, frontendOk ? 'green' : 'red');
  
  // Suggérer des actions si nécessaire
  if (!backendOk) {
    log('\n⚙️ Pour démarrer le backend, exécutez:', 'yellow');
    log('  cd backend && node enhanced-server.js', 'yellow');
  }
  
  if (!frontendOk) {
    log('\n⚙️ Pour démarrer le frontend, exécutez:', 'yellow');
    log('  cd frontend && npm run dev', 'yellow');
  }
  
  // Sortir avec le code approprié
  process.exit(backendOk && frontendOk ? 0 : 1);
}

// Exécuter le script
main().catch(error => {
  log(`❌ Erreur non gérée: ${error.message}`, 'red');
  process.exit(1);
}); 