#!/bin/bash

# Couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Aller au répertoire racine du projet
cd "$(dirname "$0")/.."
ROOT_DIR="$(pwd)"

echo -e "${BLUE}=== MindFlow Pro - Script d'installation ===${NC}"
echo -e "${YELLOW}Répertoire racine: ${ROOT_DIR}${NC}"

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js n'est pas installé. Veuillez l'installer avant de continuer.${NC}"
    exit 1
fi

# Vérifier la version de Node.js
NODE_VERSION=$(node -v | cut -d 'v' -f 2)
echo -e "${YELLOW}Version de Node.js détectée: ${NODE_VERSION}${NC}"

# Installer les dépendances backend
echo -e "\n${PURPLE}Installation des dépendances backend...${NC}"
cd "${ROOT_DIR}/backend"
npm install

# Installer les dépendances frontend
echo -e "\n${PURPLE}Installation des dépendances frontend...${NC}"
cd "${ROOT_DIR}/frontend"
npm install

# Installer les dépendances de test
echo -e "\n${PURPLE}Installation des dépendances de test...${NC}"
cd "${ROOT_DIR}/testing"
npm install -D @playwright/test
npx playwright install chromium

# Créer le fichier .env.local pour le frontend si nécessaire
echo -e "\n${PURPLE}Configuration du frontend...${NC}"
cd "${ROOT_DIR}/frontend"
if [ ! -f .env.local ]; then
    echo -e "${YELLOW}Création du fichier .env.local...${NC}"
    cat > .env.local << EOL
NEXT_PUBLIC_API_URL=http://localhost:4000/api/v1
NEXT_PUBLIC_WS_URL=ws://localhost:4000
EOL
    echo -e "${GREEN}Fichier .env.local créé avec succès!${NC}"
else
    echo -e "${GREEN}Le fichier .env.local existe déjà.${NC}"
fi

# Créer le fichier de types Express si nécessaire
echo -e "\n${PURPLE}Configuration du backend...${NC}"
cd "${ROOT_DIR}/backend"
TYPES_DIR="src/types/express"
mkdir -p "$TYPES_DIR"
if [ ! -f "${TYPES_DIR}/index.d.ts" ]; then
    echo -e "${YELLOW}Création du fichier de types Express...${NC}"
    cat > "${TYPES_DIR}/index.d.ts" << EOL
import { Request as ExpressRequest } from 'express';

declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role?: string;
      };
    }
  }
}

export {};
EOL
    echo -e "${GREEN}Fichier de types Express créé avec succès!${NC}"
else
    echo -e "${GREEN}Le fichier de types Express existe déjà.${NC}"
fi

# Rendre les scripts exécutables
echo -e "\n${PURPLE}Configuration des scripts...${NC}"
cd "${ROOT_DIR}/testing"
chmod +x check-servers.js
chmod +x fix-common-issues.js
chmod +x manage-servers.js
chmod +x test-servers.sh

echo -e "\n${GREEN}Installation terminée avec succès!${NC}"
echo -e "${YELLOW}Vous pouvez maintenant démarrer les serveurs avec:${NC}"
echo -e "${BLUE}cd ${ROOT_DIR}/testing && node manage-servers.js start${NC}"
echo -e "${YELLOW}Ou vérifier l'état des serveurs avec:${NC}"
echo -e "${BLUE}cd ${ROOT_DIR}/testing && node manage-servers.js status${NC}" 