/**
 * MindFlow Pro Journal Creation - End-to-End Tests
 * Comprehensive E2E testing for journal creation functionality
 */

const { test, expect } = require('@playwright/test');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:4000/api/v1';

// Test user credentials
const TEST_USERS = {
  admin: {
    email: '<EMAIL>',
    password: 'AdminPass123'
  },
  user: {
    email: '<EMAIL>',
    password: 'UserPass123'
  },
  professional: {
    email: '<EMAIL>',
    password: 'ProfessionalPass123'
  },
  root: {
    email: '<EMAIL>',
    password: 'Password123'
  }
};

// Test data
const TEST_JOURNAL_ENTRY = {
  title: 'E2E Test Journal Entry',
  content: 'This is a comprehensive test journal entry created through automated E2E testing to verify the complete workflow from login to successful submission.',
  entryType: 'daily',
  moodLevel: '4',
  emotions: ['happy', 'confident'],
  tags: ['testing', 'automation'],
  stressLevel: '3',
  energyLevel: '7',
  sleepQuality: '8',
  gratitudeNotes: 'Grateful for automated testing capabilities',
  goals: 'Complete E2E testing successfully',
  challenges: 'Ensuring comprehensive test coverage',
  achievements: 'Implemented robust testing framework'
};

/**
 * Helper function to login user
 */
async function loginUser(page, userType = 'user') {
  const user = TEST_USERS[userType];
  
  await page.goto(`${BASE_URL}/login`);
  await page.waitForLoadState('networkidle');
  
  // Fill login form
  await page.fill('input[type="email"]', user.email);
  await page.fill('input[type="password"]', user.password);
  
  // Submit login form
  await page.click('button[type="submit"]');
  
  // Wait for successful login (redirect or dashboard)
  await page.waitForURL(/\/(dashboard|journal)/);
  
  return user;
}

/**
 * Helper function to navigate to journal creation page
 */
async function navigateToJournalCreation(page) {
  await page.goto(`${BASE_URL}/journal/new`);
  await page.waitForLoadState('networkidle');
  
  // Verify we're on the journal creation page
  await expect(page.locator('h1')).toContainText('New Journal Entry');
}

test.describe('Journal Creation - Authentication Flow', () => {
  
  test('should redirect unauthenticated users to login', async ({ page }) => {
    await page.goto(`${BASE_URL}/journal/new`);
    
    // Should redirect to login page or show authentication error
    await page.waitForURL(/\/login/);
    await expect(page.locator('h1, h2')).toContainText(/login|sign in/i);
  });
  
  test('should allow authenticated users to access journal creation', async ({ page }) => {
    await loginUser(page, 'user');
    await navigateToJournalCreation(page);
    
    // Verify journal creation form is visible
    await expect(page.locator('form')).toBeVisible();
    await expect(page.locator('input[placeholder*="title"]')).toBeVisible();
    await expect(page.locator('textarea[placeholder*="content"]')).toBeVisible();
  });
  
});

test.describe('Journal Creation - Form Validation', () => {
  
  test.beforeEach(async ({ page }) => {
    await loginUser(page, 'user');
    await navigateToJournalCreation(page);
  });
  
  test('should show validation errors for empty required fields', async ({ page }) => {
    // Try to submit empty form
    await page.click('button[type="submit"]');
    
    // Check for validation messages
    await expect(page.locator('text=Title and content are required')).toBeVisible();
  });
  
  test('should validate title character limit', async ({ page }) => {
    const longTitle = 'A'.repeat(201); // Exceeds 200 character limit
    
    await page.fill('input[placeholder*="title"]', longTitle);
    await page.fill('textarea[placeholder*="content"]', 'Test content');
    
    // Check character count indicator
    await expect(page.locator('text=201/200')).toBeVisible();
  });
  
  test('should validate mood level selection', async ({ page }) => {
    await page.fill('input[placeholder*="title"]', 'Test Title');
    await page.fill('textarea[placeholder*="content"]', 'Test content');
    
    // Select mood level
    await page.click('button:has-text("Good")');
    
    // Verify mood level is selected
    await expect(page.locator('button:has-text("Good")')).toHaveClass(/border-blue-500/);
  });
  
  test('should validate numeric field ranges', async ({ page }) => {
    await page.fill('input[placeholder*="title"]', 'Test Title');
    await page.fill('textarea[placeholder*="content"]', 'Test content');
    
    // Test stress level validation
    await page.fill('input[placeholder="0"][max="10"]', '15');
    await page.blur();
    
    // Should be clamped to max value
    await expect(page.locator('input[placeholder="0"][max="10"]')).toHaveValue('10');
  });
  
});

test.describe('Journal Creation - Form Functionality', () => {
  
  test.beforeEach(async ({ page }) => {
    await loginUser(page, 'user');
    await navigateToJournalCreation(page);
  });
  
  test('should add and remove custom emotions', async ({ page }) => {
    // Add custom emotion
    await page.fill('input[placeholder*="custom emotion"]', 'excited');
    await page.click('button:has-text("Add"):near(input[placeholder*="custom emotion"])');
    
    // Verify emotion was added
    await expect(page.locator('button:has-text("excited")')).toBeVisible();
    
    // Remove emotion
    await page.click('button:has-text("excited")');
    
    // Verify emotion was removed (should not have selected styling)
    await expect(page.locator('button:has-text("excited")')).not.toHaveClass(/bg-blue-100/);
  });
  
  test('should add and remove custom tags', async ({ page }) => {
    // Add custom tag
    await page.fill('input[placeholder*="custom tag"]', 'automation');
    await page.click('button:has-text("Add"):near(input[placeholder*="custom tag"])');
    
    // Verify tag was added
    await expect(page.locator('button:has-text("#automation")')).toBeVisible();
    
    // Remove tag
    await page.click('button:has-text("#automation")');
    
    // Verify tag was removed
    await expect(page.locator('button:has-text("#automation")')).not.toHaveClass(/bg-green-100/);
  });
  
  test('should handle keyboard navigation for custom inputs', async ({ page }) => {
    // Test Enter key for custom emotion
    await page.fill('input[placeholder*="custom emotion"]', 'peaceful');
    await page.press('input[placeholder*="custom emotion"]', 'Enter');
    
    // Verify emotion was added
    await expect(page.locator('button:has-text("peaceful")')).toBeVisible();
    
    // Test Enter key for custom tag
    await page.fill('input[placeholder*="custom tag"]', 'mindfulness');
    await page.press('input[placeholder*="custom tag"]', 'Enter');
    
    // Verify tag was added
    await expect(page.locator('button:has-text("#mindfulness")')).toBeVisible();
  });
  
});

test.describe('Journal Creation - Complete Workflow', () => {
  
  test('should successfully create a minimal journal entry', async ({ page }) => {
    await loginUser(page, 'user');
    await navigateToJournalCreation(page);
    
    // Fill minimal required fields
    await page.fill('input[placeholder*="title"]', 'Minimal Test Entry');
    await page.fill('textarea[placeholder*="content"]', 'This is a minimal test entry with only required fields.');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for success notification
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
    
    // Should redirect to journal list or dashboard
    await page.waitForURL(/\/(journal|dashboard)/);
  });
  
  test('should successfully create a complete journal entry', async ({ page }) => {
    await loginUser(page, 'user');
    await navigateToJournalCreation(page);
    
    // Fill all form fields
    await page.fill('input[placeholder*="title"]', TEST_JOURNAL_ENTRY.title);
    await page.fill('textarea[placeholder*="content"]', TEST_JOURNAL_ENTRY.content);
    
    // Select entry type
    await page.selectOption('select', TEST_JOURNAL_ENTRY.entryType);
    
    // Select mood level
    await page.click(`button:has-text("${TEST_JOURNAL_ENTRY.moodLevel}")`);
    
    // Add emotions
    for (const emotion of TEST_JOURNAL_ENTRY.emotions) {
      await page.click(`button:has-text("${emotion}")`);
    }
    
    // Add tags
    for (const tag of TEST_JOURNAL_ENTRY.tags) {
      await page.click(`button:has-text("#${tag}")`);
    }
    
    // Fill numeric fields
    await page.fill('input[placeholder="0"][max="10"]:nth(0)', TEST_JOURNAL_ENTRY.stressLevel);
    await page.fill('input[placeholder="0"][max="10"]:nth(1)', TEST_JOURNAL_ENTRY.energyLevel);
    await page.fill('input[placeholder="0"][max="10"]:nth(2)', TEST_JOURNAL_ENTRY.sleepQuality);
    
    // Fill additional reflection fields
    await page.fill('textarea[placeholder*="grateful"]', TEST_JOURNAL_ENTRY.gratitudeNotes);
    await page.fill('textarea[placeholder*="goals"]', TEST_JOURNAL_ENTRY.goals);
    await page.fill('textarea[placeholder*="challenges"]', TEST_JOURNAL_ENTRY.challenges);
    await page.fill('textarea[placeholder*="accomplished"]', TEST_JOURNAL_ENTRY.achievements);
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for success notification
    await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
    
    // Verify redirect
    await page.waitForURL(/\/(journal|dashboard)/);
  });
  
  test('should handle form submission errors gracefully', async ({ page }) => {
    await loginUser(page, 'user');
    await navigateToJournalCreation(page);
    
    // Fill form with valid data
    await page.fill('input[placeholder*="title"]', 'Error Test Entry');
    await page.fill('textarea[placeholder*="content"]', 'Testing error handling');
    
    // Mock network error by intercepting the API call
    await page.route('**/api/v1/journal', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          message: 'Internal server error'
        })
      });
    });
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for error notification
    await expect(page.locator('text=Internal server error')).toBeVisible();
    
    // Form should still be visible for retry
    await expect(page.locator('form')).toBeVisible();
  });
  
});

test.describe('Journal Creation - User Experience', () => {
  
  test('should show loading state during submission', async ({ page }) => {
    await loginUser(page, 'user');
    await navigateToJournalCreation(page);
    
    // Fill form
    await page.fill('input[placeholder*="title"]', 'Loading Test Entry');
    await page.fill('textarea[placeholder*="content"]', 'Testing loading state');
    
    // Slow down the network to see loading state
    await page.route('**/api/v1/journal', route => {
      setTimeout(() => {
        route.continue();
      }, 2000);
    });
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Check for loading state
    await expect(page.locator('button:has-text("Saving...")')).toBeVisible();
    
    // Button should be disabled during submission
    await expect(page.locator('button[type="submit"]')).toBeDisabled();
  });
  
  test('should handle cancel button correctly', async ({ page }) => {
    await loginUser(page, 'user');
    await navigateToJournalCreation(page);
    
    // Fill some form data
    await page.fill('input[placeholder*="title"]', 'Cancel Test');
    await page.fill('textarea[placeholder*="content"]', 'This should be cancelled');
    
    // Click cancel button
    await page.click('button:has-text("Cancel")');
    
    // Should redirect to journal list
    await page.waitForURL(/\/journal$/);
  });
  
  test('should display professional disclaimer', async ({ page }) => {
    await loginUser(page, 'user');
    await navigateToJournalCreation(page);
    
    // Verify professional disclaimer is visible
    await expect(page.locator('text=Professional Disclaimer')).toBeVisible();
    await expect(page.locator('text=mental health crisis')).toBeVisible();
  });
  
});

test.describe('Journal Creation - Cross-Browser Compatibility', () => {
  
  ['chromium', 'firefox', 'webkit'].forEach(browserName => {
    test(`should work correctly in ${browserName}`, async ({ page }) => {
      await loginUser(page, 'user');
      await navigateToJournalCreation(page);
      
      // Basic functionality test
      await page.fill('input[placeholder*="title"]', `${browserName} Test Entry`);
      await page.fill('textarea[placeholder*="content"]', `Testing in ${browserName} browser`);
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Verify success
      await expect(page.locator('text=Journal entry created successfully')).toBeVisible();
    });
  });
  
});
