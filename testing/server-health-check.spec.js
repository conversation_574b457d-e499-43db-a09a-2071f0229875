// @ts-check
const { test, expect } = require('@playwright/test');

const BACKEND_URL = 'http://localhost:4000';
const FRONTEND_URL = 'http://localhost:5179';
const RETRY_ATTEMPTS = 5;
const RETRY_DELAY = 2000; // 2 seconds

// Fonction utilitaire pour attendre avec délai
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Fonction pour tester un endpoint avec retry
async function testEndpointWithRetry(page, url, expectedStatus = 200, attempts = RETRY_ATTEMPTS) {
  for (let i = 0; i < attempts; i++) {
    try {
      const response = await page.goto(url);
      if (response && response.status() === expectedStatus) {
        console.log(`✅ Endpoint ${url} est accessible (status: ${response.status()})`);
        return true;
      } else {
        console.log(`❌ Tentative ${i + 1}/${attempts}: Endpoint ${url} a retourné ${response ? response.status() : 'aucune réponse'}`);
      }
    } catch (error) {
      console.log(`❌ Tentative ${i + 1}/${attempts}: Erreur en accédant à ${url} - ${error.message}`);
    }
    
    if (i < attempts - 1) {
      console.log(`⏳ Attente de ${RETRY_DELAY/1000} secondes avant la prochaine tentative...`);
      await wait(RETRY_DELAY);
    }
  }
  return false;
}

test.describe('Vérification de la santé du serveur', () => {
  test('Le backend est en ligne et répond correctement', async ({ page }) => {
    // Test du endpoint health
    const healthUrl = `${BACKEND_URL}/api/v1/health`;
    const healthEndpointWorks = await testEndpointWithRetry(page, healthUrl);
    expect(healthEndpointWorks).toBeTruthy();

    // Vérifier que la réponse est au format JSON et contient les bonnes données
    const responseText = await page.content();
    expect(responseText).toContain('success');
    expect(responseText).toContain('healthy');
  });

  test('Le backend endpoint de test répond correctement', async ({ page }) => {
    const testUrl = `${BACKEND_URL}/api/v1/test`;
    const testEndpointWorks = await testEndpointWithRetry(page, testUrl);
    expect(testEndpointWorks).toBeTruthy();
  });

  test('Le backend endpoint de status répond correctement', async ({ page }) => {
    const statusUrl = `${BACKEND_URL}/api/v1/status`;
    const statusEndpointWorks = await testEndpointWithRetry(page, statusUrl);
    expect(statusEndpointWorks).toBeTruthy();
  });

  test('Le frontend est accessible', async ({ page }) => {
    const frontendWorks = await testEndpointWithRetry(page, FRONTEND_URL);
    expect(frontendWorks).toBeTruthy();
  });
});

// Test de diagnostic pour démarrer les serveurs si nécessaire
test.describe('Diagnostic et réparation automatique', () => {
  test('Démarrer les serveurs si nécessaire', async ({ page }) => {
    // Vérifier si le backend est en ligne
    const backendHealth = await testEndpointWithRetry(page, `${BACKEND_URL}/api/v1/health`, 200, 1);
    
    if (!backendHealth) {
      console.log('🔄 Le backend n\'est pas accessible. Tentative de démarrage...');
      
      // Démarrer le serveur backend (simulé ici - dans un vrai scénario, vous utiliseriez child_process)
      console.log('📋 Commande à exécuter: cd backend && node enhanced-server.js');
      console.log('⚠️ Cette simulation ne démarre pas réellement le serveur.');
      
      // Attendre un peu pour simuler le démarrage
      await wait(2000);
      
      // Vérifier à nouveau
      const backendHealthRetry = await testEndpointWithRetry(page, `${BACKEND_URL}/api/v1/health`, 200, 1);
      if (backendHealthRetry) {
        console.log('✅ Le backend a été démarré avec succès!');
      } else {
        console.log('❌ Échec du démarrage du backend. Intervention manuelle requise.');
      }
    }
    
    // Vérifier si le frontend est en ligne
    const frontendHealth = await testEndpointWithRetry(page, FRONTEND_URL, 200, 1);
    
    if (!frontendHealth) {
      console.log('🔄 Le frontend n\'est pas accessible. Tentative de démarrage...');
      
      // Démarrer le serveur frontend (simulé ici)
      console.log('📋 Commande à exécuter: cd frontend && npm run dev');
      console.log('⚠️ Cette simulation ne démarre pas réellement le serveur.');
      
      // Attendre un peu pour simuler le démarrage
      await wait(2000);
      
      // Vérifier à nouveau
      const frontendHealthRetry = await testEndpointWithRetry(page, FRONTEND_URL, 200, 1);
      if (frontendHealthRetry) {
        console.log('✅ Le frontend a été démarré avec succès!');
      } else {
        console.log('❌ Échec du démarrage du frontend. Intervention manuelle requise.');
      }
    }
  });
}); 