#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const BACKEND_DIR = path.join(ROOT_DIR, 'backend');
const FRONTEND_DIR = path.join(ROOT_DIR, 'frontend');
const BACKEND_SCRIPT = path.join(BACKEND_DIR, 'enhanced-server.js');
const BACKEND_PORT = 4000;
const FRONTEND_PORT = 5179;
const PID_FILE = path.join(__dirname, '.server-pids.json');

// Couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Fonction pour logger avec couleurs
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Fonction utilitaire pour attendre avec délai
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Fonction pour vérifier si un port est en cours d'utilisation
function isPortInUse(port) {
  return new Promise((resolve) => {
    const server = http.createServer();
    
    server.once('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        resolve(true);
      } else {
        resolve(false);
      }
    });
    
    server.once('listening', () => {
      server.close();
      resolve(false);
    });
    
    server.listen(port);
  });
}

// Fonction pour tester si un serveur est en ligne
async function testServer(url) {
  try {
    const response = await new Promise((resolve, reject) => {
      const req = http.get(url, (res) => {
        resolve(res);
      });
      
      req.on('error', (err) => {
        reject(err);
      });
      
      req.setTimeout(5000, () => {
        req.destroy();
        reject(new Error('Timeout'));
      });
    });
    
    return response.statusCode >= 200 && response.statusCode < 500;
  } catch (error) {
    return false;
  }
}

// Fonction pour sauvegarder les PIDs des serveurs
function savePids(pids) {
  fs.writeFileSync(PID_FILE, JSON.stringify(pids, null, 2));
}

// Fonction pour charger les PIDs des serveurs
function loadPids() {
  try {
    if (fs.existsSync(PID_FILE)) {
      return JSON.parse(fs.readFileSync(PID_FILE, 'utf8'));
    }
  } catch (error) {
    log(`⚠️ Erreur lors du chargement des PIDs: ${error.message}`, 'yellow');
  }
  
  return { backend: null, frontend: null };
}

// Fonction pour démarrer le serveur backend
async function startBackend() {
  log('🚀 Démarrage du serveur backend...', 'blue');
  
  // Vérifier si le port est déjà utilisé
  const portInUse = await isPortInUse(BACKEND_PORT);
  if (portInUse) {
    log(`⚠️ Le port ${BACKEND_PORT} est déjà utilisé. Le serveur backend est peut-être déjà en cours d'exécution.`, 'yellow');
    
    // Vérifier si le serveur est fonctionnel
    const isRunning = await testServer(`http://localhost:${BACKEND_PORT}/api/v1/health`);
    if (isRunning) {
      log("✅ Le serveur backend est déjà en cours d'exécution et répond correctement.", 'green');
      return null;
    } else {
      log('❌ Le port est utilisé mais le serveur ne répond pas correctement.', 'red');
      return null;
    }
  }
  
  // Vérifier si le fichier existe
  if (!fs.existsSync(BACKEND_SCRIPT)) {
    log(`❌ Le fichier ${BACKEND_SCRIPT} n'existe pas!`, 'red');
    return null;
  }
  
  // Démarrer le serveur
  const server = spawn('node', [BACKEND_SCRIPT], {
    cwd: BACKEND_DIR,
    stdio: 'pipe',
    detached: true
  });
  
  server.stdout.on('data', (data) => {
    log(`📤 Backend: ${data.toString().trim()}`, 'cyan');
  });
  
  server.stderr.on('data', (data) => {
    log(`❌ Backend Error: ${data.toString().trim()}`, 'red');
  });
  
  server.on('error', (err) => {
    log(`❌ Erreur lors du démarrage du serveur backend: ${err.message}`, 'red');
  });
  
  // Attendre que le serveur soit prêt
  let isReady = false;
  for (let i = 0; i < 10 && !isReady; i++) {
    await wait(1000);
    isReady = await testServer(`http://localhost:${BACKEND_PORT}/api/v1/health`);
    if (isReady) break;
    log(`⏳ Attente du démarrage du serveur backend (${i + 1}/10)...`, 'yellow');
  }
  
  if (!isReady) {
    log('❌ Le serveur backend n\'a pas pu démarrer correctement.', 'red');
    try {
      process.kill(-server.pid);
    } catch (error) {
      // Ignorer les erreurs
    }
    return null;
  }
  
  log('✅ Serveur backend démarré avec succès!', 'green');
  return server.pid;
}

// Fonction pour démarrer le serveur frontend
async function startFrontend() {
  log('🚀 Démarrage du serveur frontend...', 'blue');
  
  // Vérifier si le port est déjà utilisé
  const portInUse = await isPortInUse(FRONTEND_PORT);
  if (portInUse) {
    log(`⚠️ Le port ${FRONTEND_PORT} est déjà utilisé. Le serveur frontend est peut-être déjà en cours d'exécution.`, 'yellow');
    
    // Vérifier si le serveur est fonctionnel
    const isRunning = await testServer(`http://localhost:${FRONTEND_PORT}`);
    if (isRunning) {
      log("✅ Le serveur frontend est déjà en cours d'exécution et répond correctement.", 'green');
      return null;
    } else {
      log('❌ Le port est utilisé mais le serveur ne répond pas correctement.', 'red');
      return null;
    }
  }
  
  // Démarrer le serveur
  const server = spawn('npm', ['run', 'dev'], {
    cwd: FRONTEND_DIR,
    stdio: 'pipe',
    detached: true,
    env: { ...process.env, PORT: FRONTEND_PORT.toString() }
  });
  
  server.stdout.on('data', (data) => {
    const output = data.toString().trim();
    log(`📤 Frontend: ${output}`, 'cyan');
    
    // Détecter quand le serveur est prêt
    if (output.includes(`http://localhost:${FRONTEND_PORT}`)) {
      log('✅ Serveur frontend démarré avec succès!', 'green');
    }
  });
  
  server.stderr.on('data', (data) => {
    log(`❌ Frontend Error: ${data.toString().trim()}`, 'red');
  });
  
  server.on('error', (err) => {
    log(`❌ Erreur lors du démarrage du serveur frontend: ${err.message}`, 'red');
  });
  
  // Attendre que le serveur soit prêt
  let isReady = false;
  for (let i = 0; i < 30 && !isReady; i++) {
    await wait(1000);
    isReady = await testServer(`http://localhost:${FRONTEND_PORT}`);
    if (isReady) break;
    log(`⏳ Attente du démarrage du serveur frontend (${i + 1}/30)...`, 'yellow');
  }
  
  if (!isReady) {
    log('❌ Le serveur frontend n\'a pas pu démarrer correctement.', 'red');
    try {
      process.kill(-server.pid);
    } catch (error) {
      // Ignorer les erreurs
    }
    return null;
  }
  
  log('✅ Serveur frontend démarré avec succès!', 'green');
  return server.pid;
}

// Fonction pour arrêter les serveurs
async function stopServers() {
  log('🛑 Arrêt des serveurs...', 'blue');
  
  const pids = loadPids();
  
  // Arrêter le backend
  if (pids.backend) {
    try {
      log(`🛑 Arrêt du serveur backend (PID: ${pids.backend})...`, 'yellow');
      process.kill(-pids.backend);
      log('✅ Serveur backend arrêté avec succès!', 'green');
    } catch (error) {
      log(`⚠️ Erreur lors de l'arrêt du serveur backend: ${error.message}`, 'yellow');
    }
  } else {
    log('ℹ️ Aucun PID de serveur backend enregistré.', 'blue');
  }
  
  // Arrêter le frontend
  if (pids.frontend) {
    try {
      log(`🛑 Arrêt du serveur frontend (PID: ${pids.frontend})...`, 'yellow');
      process.kill(-pids.frontend);
      log('✅ Serveur frontend arrêté avec succès!', 'green');
    } catch (error) {
      log(`⚠️ Erreur lors de l'arrêt du serveur frontend: ${error.message}`, 'yellow');
    }
  } else {
    log('ℹ️ Aucun PID de serveur frontend enregistré.', 'blue');
  }
  
  // Réinitialiser les PIDs
  savePids({ backend: null, frontend: null });
  
  // Vérifier si les ports sont toujours utilisés
  const backendPortInUse = await isPortInUse(BACKEND_PORT);
  const frontendPortInUse = await isPortInUse(FRONTEND_PORT);
  
  if (backendPortInUse || frontendPortInUse) {
    log('⚠️ Certains ports sont toujours utilisés. Tentative de libération forcée...', 'yellow');
    
    // Tuer tous les processus sur ces ports
    if (process.platform === 'win32') {
      if (backendPortInUse) {
        exec(`FOR /F "tokens=5" %P IN ('netstat -ano ^| find ":${BACKEND_PORT}" ^| find "LISTENING"') DO taskkill /F /PID %P`);
      }
      if (frontendPortInUse) {
        exec(`FOR /F "tokens=5" %P IN ('netstat -ano ^| find ":${FRONTEND_PORT}" ^| find "LISTENING"') DO taskkill /F /PID %P`);
      }
    } else {
      if (backendPortInUse) {
        exec(`lsof -ti:${BACKEND_PORT} | xargs kill -9`);
      }
      if (frontendPortInUse) {
        exec(`lsof -ti:${FRONTEND_PORT} | xargs kill -9`);
      }
    }
    
    await wait(2000);
    
    // Vérifier à nouveau
    const backendStillInUse = await isPortInUse(BACKEND_PORT);
    const frontendStillInUse = await isPortInUse(FRONTEND_PORT);
    
    if (backendStillInUse || frontendStillInUse) {
      log('❌ Impossible de libérer tous les ports. Veuillez vérifier manuellement.', 'red');
    } else {
      log('✅ Tous les ports ont été libérés avec succès!', 'green');
    }
  }
}

// Fonction pour vérifier l'état des serveurs
async function checkServers() {
  log('🔍 Vérification de l\'état des serveurs...', 'blue');
  
  // Vérifier le backend
  const backendRunning = await testServer(`http://localhost:${BACKEND_PORT}/api/v1/health`);
  log(`Backend: ${backendRunning ? '✅ En ligne' : '❌ Hors ligne'}`, backendRunning ? 'green' : 'red');
  
  // Vérifier le frontend
  const frontendRunning = await testServer(`http://localhost:${FRONTEND_PORT}`);
  log(`Frontend: ${frontendRunning ? '✅ En ligne' : '❌ Hors ligne'}`, frontendRunning ? 'green' : 'red');
  
  return { backendRunning, frontendRunning };
}

// Fonction principale
async function main() {
  // Analyser les arguments
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command || command === 'help') {
    log('📋 Utilisation:', 'cyan');
    log('  node manage-servers.js start     # Démarrer les serveurs', 'cyan');
    log('  node manage-servers.js stop      # Arrêter les serveurs', 'cyan');
    log('  node manage-servers.js restart   # Redémarrer les serveurs', 'cyan');
    log('  node manage-servers.js status    # Vérifier l\'état des serveurs', 'cyan');
    process.exit(0);
  }
  
  if (command === 'start') {
    // Démarrer les serveurs
    log('🚀 Démarrage des serveurs...', 'magenta');
    
    // Démarrer le backend
    const backendPid = await startBackend();
    
    // Démarrer le frontend
    const frontendPid = await startFrontend();
    
    // Sauvegarder les PIDs
    savePids({
      backend: backendPid,
      frontend: frontendPid
    });
    
    log('✅ Opération terminée!', 'green');
  } else if (command === 'stop') {
    // Arrêter les serveurs
    await stopServers();
    log('✅ Opération terminée!', 'green');
  } else if (command === 'restart') {
    // Redémarrer les serveurs
    log('🔄 Redémarrage des serveurs...', 'magenta');
    
    // Arrêter les serveurs
    await stopServers();
    
    // Attendre un peu
    await wait(2000);
    
    // Démarrer les serveurs
    const backendPid = await startBackend();
    const frontendPid = await startFrontend();
    
    // Sauvegarder les PIDs
    savePids({
      backend: backendPid,
      frontend: frontendPid
    });
    
    log('✅ Opération terminée!', 'green');
  } else if (command === 'status') {
    // Vérifier l'état des serveurs
    await checkServers();
  } else {
    log(`❌ Commande inconnue: ${command}`, 'red');
    process.exit(1);
  }
}

// Exécuter le script
main().catch(error => {
  log(`❌ Erreur non gérée: ${error.message}`, 'red');
  process.exit(1);
}); 