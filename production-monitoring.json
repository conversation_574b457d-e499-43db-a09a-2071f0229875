{"vercel_analytics": {"enabled": true, "features": ["page_views", "unique_visitors", "bounce_rate", "session_duration"]}, "speed_insights": {"enabled": true, "metrics": ["first_contentful_paint", "largest_contentful_paint", "first_input_delay", "cumulative_layout_shift"]}, "error_tracking": {"enabled": true, "alerts": true}, "custom_monitoring": {"supabase_health": "/api/health/supabase", "response_times": true, "user_flows": ["dashboard_access", "appointment_booking", "journal_creation", "ai_chat_interaction"]}}