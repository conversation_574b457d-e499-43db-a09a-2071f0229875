#!/usr/bin/env node

/**
 * 🧪 VALIDATION SCHEMA SUPABASE
 * 📅 Script de validation automatique
 */

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://kvdrukmoxetoiojazukf.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function validerSchema() {
    console.log('🧪 VALIDATION DU SCHÉMA SUPABASE');
    console.log('=' .repeat(50));

    const tables = [
        'users',
        'professionals', 
        'appointments',
        'journal_entries',
        'ai_coach_sessions',
        'mood_analytics',
        'smart_notifications'
    ];

    let tablesValides = 0;

    for (const table of tables) {
        try {
            const { data, error } = await supabase
                .from(table)
                .select('*')
                .limit(1);

            if (error) {
                console.log('❌ Table ' + table + ': ' + error.message);
            } else {
                console.log('✅ Table ' + table + ': OK');
                tablesValides++;
            }
        } catch (err) {
            console.log('❌ Table ' + table + ': Erreur de connexion');
        }
    }

    console.log('\n📊 RÉSULTAT:');
    console.log('   Tables validées: ' + tablesValides + '/' + tables.length);
    
    if (tablesValides === tables.length) {
        console.log('🎉 SCHÉMA APPLIQUÉ AVEC SUCCÈS !');
        console.log('✅ Architecture centralisée opérationnelle');
    } else {
        console.log('⚠️  Certaines tables manquent');
        console.log('🔧 Vérifier l'application du schéma en Supabase');
    }
}

validerSchema().catch(console.error);
