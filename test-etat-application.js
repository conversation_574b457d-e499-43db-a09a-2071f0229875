#!/usr/bin/env node

const http = require('http');
const fs = require('fs');

console.log('🔍 TEST DE L\'ÉTAT D\'AVANCEMENT - MINDFLOW PRO (Phase 8)\n');

const BASE_URL = 'http://localhost:3000';
const PAGES = [
  '/',
  '/dashboard',
  '/appointments',
  '/professionals',
  '/ai-coach',
  '/analytics',
  '/compliance',
  '/integrations-b2b',
  '/telemedicine-advanced',
  '/monitoring-realtime'
];

function testUrl(url) {
  return new Promise((resolve) => {
    const req = http.get(url, (res) => {
      resolve({
        url,
        status: res.statusCode,
        success: res.statusCode >= 200 && res.statusCode < 400
      });
    });

    req.on('error', () => {
      resolve({ url, status: 'ERROR', success: false });
    });

    req.setTimeout(3000, () => {
      req.destroy();
      resolve({ url, status: 'TIMEOUT', success: false });
    });
  });
}

async function runTests() {
  let successful = 0;
  let total = 0;
  
  for (const page of PAGES) {
    const result = await testUrl(`${BASE_URL}${page}`);
    total++;
    
    const icon = result.success ? '✅' : '❌';
    const status = result.success ? 'OK' : result.status;
    const isNew = page === '/monitoring-realtime' ? ' 🆕' : '';
    
    console.log(`${icon} ${page.padEnd(25)} ${status}${isNew}`);
    
    if (result.success) successful++;
  }
  
  const score = ((successful / total) * 100).toFixed(1);
  console.log(`\n📊 Score: ${score}% (${successful}/${total})`);
  
  if (score >= 90) {
    console.log('🎉 EXCELLENT! Prêt pour Phase 9');
  } else if (score >= 80) {
    console.log('👍 BON ÉTAT - Quelques ajustements mineurs');
  }
  
  return { successful, total, score };
}

runTests().catch(console.error); 