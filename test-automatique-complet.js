#!/usr/bin/env node

/**
 * 🚀 TEST AUTOMATIQUE COMPLET - MINDFLOW PRO
 * Script autonome qui teste toutes les fonctionnalités automatiquement
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
const TIMEOUT = 30000;

// Couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = (msg, color = colors.reset) => console.log(`${color}${msg}${colors.reset}`);
const success = (msg) => log(`✅ ${msg}`, colors.green);
const error = (msg) => log(`❌ ${msg}`, colors.red);
const info = (msg) => log(`ℹ️  ${msg}`, colors.blue);
const warning = (msg) => log(`⚠️  ${msg}`, colors.yellow);

// Pages à tester
const PAGES_TO_TEST = [
  { url: '/', name: 'Accueil' },
  { url: '/auth/login', name: 'Connexion' },
  { url: '/auth/register', name: 'Inscription' },
  { url: '/test-phase4-supabase', name: 'Test Phase 4' },
  { url: '/test-direct-supabase', name: 'Test Direct' },
  { url: '/test-complet-supabase', name: 'Test Complet' },
  { url: '/inscription-simple', name: 'Inscription Simple' },
  { url: '/dashboard', name: 'Dashboard' }
];

// Résultats des tests
const results = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

async function testPage(page, pageInfo) {
  try {
    info(`Test de ${pageInfo.name} (${pageInfo.url})...`);
    
    const response = await page.goto(`${BASE_URL}${pageInfo.url}`, {
      waitUntil: 'domcontentloaded',
      timeout: TIMEOUT
    });

    if (response && response.status() === 200) {
      success(`${pageInfo.name} - Page accessible (HTTP 200)`);
      
      // Vérifications spécifiques par page
      if (pageInfo.url === '/test-phase4-supabase') {
        await testPhase4Features(page);
      } else if (pageInfo.url === '/inscription-simple') {
        await testInscriptionSimple(page);
      } else if (pageInfo.url === '/test-direct-supabase') {
        await testDirectSupabase(page);
      }
      
      results.passed++;
    } else {
      error(`${pageInfo.name} - Erreur HTTP ${response?.status() || 'inconnu'}`);
      results.failed++;
      results.errors.push(`${pageInfo.name}: HTTP ${response?.status()}`);
    }
  } catch (err) {
    error(`${pageInfo.name} - Erreur: ${err.message}`);
    results.failed++;
    results.errors.push(`${pageInfo.name}: ${err.message}`);
  }
  
  results.total++;
}

async function testPhase4Features(page) {
  try {
    // Attendre que la page soit chargée
    await page.waitForSelector('h1', { timeout: 5000 });
    
    // Vérifier le titre
    const title = await page.textContent('h1');
    if (title.includes('Phase 4')) {
      success('Phase 4 - Titre correct');
    }
    
    // Chercher les indicateurs de configuration
    const configText = await page.textContent('body');
    if (configText.includes('✅') && configText.includes('Supabase')) {
      success('Phase 4 - Configuration Supabase détectée');
    }
  } catch (err) {
    warning(`Phase 4 - Tests spécifiques échoués: ${err.message}`);
  }
}

async function testInscriptionSimple(page) {
  try {
    // Vérifier la présence du formulaire
    const form = await page.locator('form').count();
    if (form > 0) {
      success('Inscription Simple - Formulaire trouvé');
      
      // Vérifier les champs
      const emailField = await page.locator('input[type="email"]').count();
      const passwordField = await page.locator('input[type="password"]').count();
      const submitButton = await page.locator('button[type="submit"]').count();
      
      if (emailField > 0 && passwordField > 0 && submitButton > 0) {
        success('Inscription Simple - Tous les champs présents');
      }
    }
  } catch (err) {
    warning(`Inscription Simple - Tests échoués: ${err.message}`);
  }
}

async function testDirectSupabase(page) {
  try {
    // Cliquer sur le bouton de vérification de configuration
    const configButton = await page.locator('button:has-text("Vérifier Configuration")');
    if (await configButton.count() > 0) {
      await configButton.click();
      await page.waitForTimeout(2000);
      
      // Vérifier les résultats
      const bodyText = await page.textContent('body');
      if (bodyText.includes('✅ Configurée')) {
        success('Test Direct - Configuration Supabase vérifiée');
      }
    }
  } catch (err) {
    warning(`Test Direct - Tests échoués: ${err.message}`);
  }
}

async function runAllTests() {
  log('\n🚀 DÉMARRAGE DES TESTS AUTOMATIQUES MINDFLOW PRO\n', colors.blue);
  
  let browser;
  let page;
  
  try {
    // Lancer le navigateur
    browser = await chromium.launch({
      headless: false, // Mode visible pour debug
      slowMo: 100 // Ralentir pour voir les actions
    });
    
    page = await browser.newPage();
    
    // Tester toutes les pages
    for (const pageInfo of PAGES_TO_TEST) {
      await testPage(page, pageInfo);
      await page.waitForTimeout(1000); // Pause entre les tests
    }
    
    // Test d'inscription automatique
    log('\n📝 TEST D\'INSCRIPTION AUTOMATIQUE', colors.blue);
    await testAutoRegistration(page);
    
  } catch (err) {
    error(`Erreur globale: ${err.message}`);
  } finally {
    if (browser) {
      await browser.close();
    }
    
    // Afficher le rapport final
    displayFinalReport();
  }
}

async function testAutoRegistration(page) {
  try {
    // Aller sur la page d'inscription simple
    await page.goto(`${BASE_URL}/inscription-simple`, { waitUntil: 'domcontentloaded' });
    
    // Attendre le formulaire
    await page.waitForSelector('form', { timeout: 5000 });
    
    // Remplir le formulaire avec des données de test
    const timestamp = Date.now();
    const testEmail = `test${timestamp}@mindflow.pro`;
    
    await page.fill('input[name="name"]', 'Test Automatique');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', 'TestPassword123!');
    
    // Soumettre le formulaire
    await page.click('button[type="submit"]');
    
    // Attendre la réponse
    await page.waitForTimeout(3000);
    
    // Vérifier le résultat
    const pageContent = await page.textContent('body');
    if (pageContent.includes('✅') && pageContent.includes('réussie')) {
      success(`Inscription automatique réussie avec ${testEmail}`);
      results.passed++;
    } else if (pageContent.includes('❌')) {
      const errorMsg = pageContent.match(/❌[^✅]*/)?.[0] || 'Erreur inconnue';
      error(`Inscription échouée: ${errorMsg}`);
      results.failed++;
    }
    
    results.total++;
    
  } catch (err) {
    error(`Test inscription automatique échoué: ${err.message}`);
    results.failed++;
    results.total++;
  }
}

function displayFinalReport() {
  const successRate = results.total > 0 ? ((results.passed / results.total) * 100).toFixed(1) : 0;
  
  log('\n' + '='.repeat(60), colors.blue);
  log('📊 RAPPORT FINAL DES TESTS AUTOMATIQUES', colors.blue);
  log('='.repeat(60), colors.blue);
  
  log(`\nTests exécutés: ${results.total}`);
  success(`Tests réussis: ${results.passed}`);
  if (results.failed > 0) {
    error(`Tests échoués: ${results.failed}`);
  }
  
  log(`\nTaux de réussite: ${successRate}%`);
  
  if (results.errors.length > 0) {
    log('\n❌ Erreurs détectées:', colors.red);
    results.errors.forEach(err => log(`  - ${err}`, colors.red));
  }
  
  // Créer un fichier de rapport
  const report = {
    date: new Date().toISOString(),
    results: results,
    successRate: successRate,
    baseUrl: BASE_URL
  };
  
  fs.writeFileSync('test-report.json', JSON.stringify(report, null, 2));
  
  log('\n✅ Rapport sauvegardé dans test-report.json', colors.green);
  
  // Message final
  if (successRate >= 80) {
    log('\n🎉 TESTS GLOBALEMENT RÉUSSIS !', colors.green);
  } else if (successRate >= 60) {
    log('\n⚠️  TESTS PARTIELLEMENT RÉUSSIS', colors.yellow);
  } else {
    log('\n❌ TESTS ÉCHOUÉS - ACTION REQUISE', colors.red);
  }
}

// Vérifier Playwright
try {
  require('playwright');
} catch (err) {
  error('Playwright n\'est pas installé !');
  log('Installation en cours...');
  require('child_process').execSync('npm install -D playwright', { stdio: 'inherit' });
}

// Lancer les tests
runAllTests().catch(err => {
  error(`Erreur fatale: ${err.message}`);
  process.exit(1);
}); 