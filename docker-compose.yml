version: '3.8'

services:
  postgres:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: mindflow
      POSTGRES_PASSWORD: supersecret
      POSTGRES_DB: mindflow_db
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  redis:
    image: redis:7
    restart: always
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    restart: always
    env_file: ./backend/.env
    depends_on:
      - postgres
      - redis
    ports:
      - "4000:4000"
    volumes:
      - ./backend/logs:/app/logs

  frontend:
    build: ./frontend
    restart: always
    env_file: ./frontend/.env
    depends_on:
      - backend
    ports:
      - "5173:5173"

  nginx:
    image: nginx:1.25
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend

volumes:
  pgdata: 