
                CREATE TABLE IF NOT EXISTS mood_analytics (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    date DATE NOT NULL,
                    mood_score INTEGER CHECK (mood_score >= 1 AND mood_score <= 10),
                    energy_level INTEGER CHECK (energy_level >= 1 AND energy_level <= 10),
                    stress_level INTEGER CHECK (stress_level >= 1 AND stress_level <= 10),
                    sleep_hours DECIMAL(3,1),
                    exercise_minutes INTEGER DEFAULT 0,
                    factors JSONB DEFAULT '{}',
                    notes TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                