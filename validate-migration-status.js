#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 Validation de l\'état de la migration MindFlow Pro vers Supabase\n');

// Couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

function logSuccess(message) {
  console.log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logError(message) {
  console.log(`${colors.red}❌ ${message}${colors.reset}`);
}

function logWarning(message) {
  console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

function logInfo(message) {
  console.log(`${colors.blue}ℹ️  ${message}${colors.reset}`);
}

function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  if (exists) {
    const stats = fs.statSync(filePath);
    const size = stats.size;
    if (size > 0) {
      logSuccess(`${description} - ${size} bytes`);
      return true;
    } else {
      logWarning(`${description} - FICHIER VIDE`);
      return false;
    }
  } else {
    logError(`${description} - MANQUANT`);
    return false;
  }
}

function checkDirectoryExists(dirPath, description) {
  const exists = fs.existsSync(dirPath);
  if (exists) {
    const files = fs.readdirSync(dirPath);
    logSuccess(`${description} - ${files.length} fichiers`);
    return true;
  } else {
    logError(`${description} - MANQUANT`);
    return false;
  }
}

async function validateMigrationStatus() {
  let score = 0;
  let total = 0;

  console.log(`${colors.bright}📋 1. ARCHITECTURE SUPABASE${colors.reset}`);
  
  // Configuration Supabase
  total++;
  if (checkFileExists('frontend/src/lib/supabase/client.ts', 'Client Supabase')) score++;
  
  total++;
  if (checkFileExists('frontend/src/lib/database/supabase-adapter.ts', 'Adaptateur Supabase')) score++;
  
  total++;
  if (checkFileExists('frontend/src/lib/database/sqlite-adapter.ts', 'Adaptateur SQLite')) score++;
  
  total++;
  if (checkFileExists('frontend/src/lib/database/index.ts', 'Interface DatabaseAdapter')) score++;
  
  total++;
  if (checkFileExists('frontend/src/lib/migration/data-migrator.ts', 'Service de migration')) score++;
  
  total++;
  if (checkFileExists('frontend/src/lib/config/feature-flags.ts', 'Feature flags')) score++;

  console.log(`\n${colors.bright}📋 2. COMPOSANTS UI ET LAYOUT${colors.reset}`);
  
  // Composants UI
  total++;
  if (checkFileExists('frontend/src/components/ui/dropdown-menu.tsx', 'Dropdown Menu')) score++;
  
  total++;
  if (checkFileExists('frontend/src/components/ui/avatar.tsx', 'Avatar')) score++;
  
  total++;
  if (checkFileExists('frontend/src/components/ui/card.tsx', 'Card')) score++;
  
  total++;
  if (checkFileExists('frontend/src/components/ui/button.tsx', 'Button')) score++;
  
  total++;
  if (checkFileExists('frontend/src/components/ui/badge.tsx', 'Badge')) score++;

  // Layout
  total++;
  if (checkFileExists('frontend/src/components/Layout/DashboardLayout.tsx', 'DashboardLayout')) score++;
  
  total++;
  if (checkFileExists('frontend/src/components/Layout/TopBar.tsx', 'TopBar')) score++;
  
  total++;
  if (checkFileExists('frontend/src/components/Layout/NavigationSidebar.tsx', 'NavigationSidebar')) score++;

  console.log(`\n${colors.bright}📋 3. STORES ET PROVIDERS${colors.reset}`);
  
  // Stores
  total++;
  if (checkFileExists('frontend/src/stores/authStore.ts', 'Auth Store')) score++;
  
  total++;
  if (checkFileExists('frontend/src/stores/dashboardStore.ts', 'Dashboard Store')) score++;

  // Providers
  total++;
  if (checkFileExists('frontend/src/components/providers/AuthProvider.tsx', 'Auth Provider')) score++;
  
  total++;
  if (checkFileExists('frontend/src/components/providers/ThemeProvider.tsx', 'Theme Provider')) score++;
  
  total++;
  if (checkFileExists('frontend/src/components/providers/Providers.tsx', 'Root Providers')) score++;

  console.log(`\n${colors.bright}📋 4. MIGRATION ET CONFIGURATION${colors.reset}`);
  
  // Scripts de migration
  total++;
  if (checkFileExists('setup-supabase.js', 'Script setup Supabase')) score++;
  
  total++;
  if (checkFileExists('supabase-schema.sql', 'Schéma Supabase')) score++;
  
  total++;
  if (checkFileExists('PROCHAINES_ETAPES_SUPABASE.md', 'Guide des prochaines étapes')) score++;

  // Configuration Next.js
  total++;
  if (checkFileExists('frontend/package.json', 'Package.json')) score++;
  
  total++;
  if (checkFileExists('frontend/next.config.js', 'Next.js config')) score++;
  
  total++;
  if (checkFileExists('frontend/tailwind.config.js', 'Tailwind config')) score++;

  console.log(`\n${colors.bright}📋 5. TESTS ET VALIDATION${colors.reset}`);
  
  // Tests
  total++;
  if (checkFileExists('tests/supabase-migration.spec.ts', 'Tests Supabase')) score++;
  
  total++;
  if (checkDirectoryExists('frontend/tests', 'Répertoire tests frontend')) score++;

  // Variables d'environnement
  total++;
  if (checkFileExists('frontend/.env.local.example', 'Exemple .env.local')) score++;

  console.log(`\n${colors.bright}📊 RÉSULTAT DE LA VALIDATION${colors.reset}`);
  
  const percentage = Math.round((score / total) * 100);
  
  if (percentage >= 90) {
    logSuccess(`Score: ${score}/${total} (${percentage}%) - EXCELLENT! Migration prête`);
  } else if (percentage >= 75) {
    logInfo(`Score: ${score}/${total} (${percentage}%) - BIEN - Migration possible avec corrections mineures`);
  } else if (percentage >= 60) {
    logWarning(`Score: ${score}/${total} (${percentage}%) - MOYEN - Corrections nécessaires`);
  } else {
    logError(`Score: ${score}/${total} (${percentage}%) - INSUFFISANT - Corrections majeures requises`);
  }

  console.log(`\n${colors.bright}📋 PROCHAINES ACTIONS RECOMMANDÉES${colors.reset}`);
  
  if (percentage >= 75) {
    logInfo('1. Créer le projet Supabase sur https://supabase.com');
    logInfo('2. Configurer les variables dans frontend/.env.local');
    logInfo('3. Exécuter: node setup-supabase.js');
    logInfo('4. Lancer les tests: npm run test');
  } else {
    logWarning('1. Corriger les fichiers manquants ou vides');
    logWarning('2. Installer les dépendances manquantes');
    logWarning('3. Re-valider avec ce script');
  }

  return { score, total, percentage };
}

// Vérifier les variables d'environnement
function checkEnvironmentVariables() {
  console.log(`\n${colors.bright}📋 6. VARIABLES D'ENVIRONNEMENT${colors.reset}`);
  
  const envFile = 'frontend/.env.local';
  if (fs.existsSync(envFile)) {
    const content = fs.readFileSync(envFile, 'utf8');
    
    const requiredVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY'
    ];
    
    requiredVars.forEach(varName => {
      if (content.includes(varName)) {
        logSuccess(`${varName} configuré`);
      } else {
        logWarning(`${varName} manquant`);
      }
    });
  } else {
    logWarning('.env.local non trouvé - À configurer après création du projet Supabase');
  }
}

// Exécution du script
if (require.main === module) {
  validateMigrationStatus()
    .then(({ score, total, percentage }) => {
      checkEnvironmentVariables();
      
      console.log(`\n${colors.bright}🎯 MIGRATION MINDFLOW PRO - STATUS: ${percentage}% PRÊT${colors.reset}\n`);
      
      if (percentage >= 75) {
        console.log(`${colors.green}🚀 Vous pouvez procéder à la migration Supabase!${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️  Corrections nécessaires avant migration${colors.reset}`);
      }
    })
    .catch(error => {
      logError(`Erreur lors de la validation: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { validateMigrationStatus }; 