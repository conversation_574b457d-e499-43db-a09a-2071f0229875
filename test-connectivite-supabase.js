#!/usr/bin/env node

/**
 * Test de Connectivité Supabase - MindFlow Pro
 * ===========================================
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

console.log('🚀 TEST DE CONNECTIVITÉ SUPABASE - MindFlow Pro');
console.log('='.repeat(60));

// Configuration Supabase
const SUPABASE_CONFIG = {
  url: 'https://kvdrukmoxetoiojazukf.supabase.co',
  anon_key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzODE0ODEsImV4cCI6MjA1MDk1NzQ4MX0.Mu8Wao-8lGO2PkrTHQgPIhzQNHJ9Dtu4bhALCRNq6bw',
  project_id: 'kvdrukmoxetoiojazukf'
};

/**
 * Test 1: Connectivité de base à Supabase
 */
function testBasicConnectivity() {
  return new Promise((resolve) => {
    console.log('\n📡 TEST 1: Connectivité de base Supabase');
    console.log('   URL:', SUPABASE_CONFIG.url);
    
    const options = {
      hostname: 'kvdrukmoxetoiojazukf.supabase.co',
      port: 443,
      path: '/rest/v1/',
      method: 'GET',
      headers: {
        'apikey': SUPABASE_CONFIG.anon_key,
        'Authorization': `Bearer ${SUPABASE_CONFIG.anon_key}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('   ✅ Connexion réussie !');
          console.log('   📊 Status Code:', res.statusCode);
          console.log('   📏 Réponse:', data.length + ' caractères');
          resolve({ success: true, statusCode: res.statusCode, data });
        } else {
          console.log('   ⚠️  Connexion avec erreur');
          console.log('   📊 Status Code:', res.statusCode);
          console.log('   🔍 Réponse:', data.substring(0, 200) + '...');
          resolve({ success: false, statusCode: res.statusCode, data });
        }
      });
    });

    req.on('error', (error) => {
      console.log('   ❌ Erreur de connexion:', error.message);
      resolve({ success: false, error: error.message });
    });

    req.on('timeout', () => {
      console.log('   ⏰ Timeout de connexion (10s)');
      req.destroy();
      resolve({ success: false, error: 'Timeout' });
    });

    req.end();
  });
}

/**
 * Test 2: Test API REST avec les tables
 */
function testRestAPI() {
  return new Promise((resolve) => {
    console.log('\n🔌 TEST 2: API REST - Table users');
    
    const options = {
      hostname: 'kvdrukmoxetoiojazukf.supabase.co',
      port: 443,
      path: '/rest/v1/users?select=count',
      method: 'GET',
      headers: {
        'apikey': SUPABASE_CONFIG.anon_key,
        'Authorization': `Bearer ${SUPABASE_CONFIG.anon_key}`,
        'Content-Type': 'application/json',
        'Prefer': 'count=exact'
      },
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          console.log('   ✅ API REST fonctionnelle !');
          console.log('   📊 Status Code:', res.statusCode);
          console.log('   📈 Données:', parsed);
          resolve({ success: true, statusCode: res.statusCode, data: parsed });
        } catch (parseError) {
          console.log('   ⚠️  Réponse non-JSON reçue');
          console.log('   📊 Status Code:', res.statusCode);
          console.log('   🔍 Réponse brute:', data.substring(0, 200));
          resolve({ success: false, statusCode: res.statusCode, data, parseError: parseError.message });
        }
      });
    });

    req.on('error', (error) => {
      console.log('   ❌ Erreur API REST:', error.message);
      resolve({ success: false, error: error.message });
    });

    req.on('timeout', () => {
      console.log('   ⏰ Timeout API REST (10s)');
      req.destroy();
      resolve({ success: false, error: 'Timeout' });
    });

    req.end();
  });
}

/**
 * Test 3: Vérification de la structure projet
 */
function testProjectStructure() {
  console.log('\n📁 TEST 3: Structure du projet');
  
  const criticalFiles = [
    { path: 'frontend/src/app/page.tsx', name: 'Page d\'accueil' },
    { path: 'frontend/src/app/ultra-simple/page.tsx', name: 'Page ultra-simple' },
    { path: 'frontend/src/app/test-supabase/page.tsx', name: 'Page test Supabase' },
    { path: 'frontend/src/lib/supabase/client.ts', name: 'Client Supabase' },
    { path: 'frontend/src/lib/database/index.ts', name: 'Database Manager' },
    { path: 'frontend/src/lib/config/feature-flags.ts', name: 'Feature Flags' },
    { path: 'backend/database.sqlite', name: 'Base SQLite' },
    { path: 'supabase-schema.sql', name: 'Schéma Supabase' }
  ];
  
  const results = {};
  
  criticalFiles.forEach(({ path: filePath, name }) => {
    const fullPath = path.join(__dirname, filePath);
    const exists = fs.existsSync(fullPath);
    console.log(`   ${exists ? '✅' : '❌'} ${name} (${filePath})`);
    results[filePath] = { exists, name };
    
    if (exists && filePath.endsWith('.sqlite')) {
      const stats = fs.statSync(fullPath);
      console.log(`      📊 Taille: ${(stats.size / 1024).toFixed(1)} KB`);
      results[filePath].size = stats.size;
    }
  });
  
  return results;
}

/**
 * Test 4: Création fichier de configuration manquant
 */
function createEnvConfig() {
  console.log('\n⚙️  TEST 4: Configuration environnement');
  
  const envPath = path.join(__dirname, 'frontend', '.env.local');
  const envExists = fs.existsSync(envPath);
  
  console.log(`   ${envExists ? '✅' : '❌'} Fichier .env.local existe`);
  
  if (!envExists) {
    const envContent = `# Configuration Supabase pour MindFlow Pro
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzODE0ODEsImV4cCI6MjA1MDk1NzQ4MX0.Mu8Wao-8lGO2PkrTHQgPIhzQNHJ9Dtu4bhALCRNq6bw

# Configuration migration progressive
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true
NEXT_PUBLIC_MIGRATE_USER_DATA=true
NEXT_PUBLIC_DEBUG_MIGRATION=true

# Environnement
NODE_ENV=development
`;
    
    try {
      fs.writeFileSync(envPath, envContent);
      console.log('   ✅ Fichier .env.local créé avec succès !');
      return { created: true };
    } catch (error) {
      console.log('   ❌ Erreur création .env.local:', error.message);
      return { created: false, error: error.message };
    }
  }
  
  // Lire le contenu existant
  try {
    const content = fs.readFileSync(envPath, 'utf8');
    const hasSupabaseUrl = content.includes('NEXT_PUBLIC_SUPABASE_URL');
    const hasSupabaseKey = content.includes('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    const hasDualMode = content.includes('NEXT_PUBLIC_DUAL_DATABASE_MODE');
    
    console.log(`   ${hasSupabaseUrl ? '✅' : '❌'} Variable SUPABASE_URL`);
    console.log(`   ${hasSupabaseKey ? '✅' : '❌'} Variable SUPABASE_ANON_KEY`);
    console.log(`   ${hasDualMode ? '✅' : '❌'} Variable DUAL_DATABASE_MODE`);
    
    return { 
      exists: true, 
      configured: hasSupabaseUrl && hasSupabaseKey,
      variables: { hasSupabaseUrl, hasSupabaseKey, hasDualMode }
    };
  } catch (error) {
    console.log('   ❌ Erreur lecture .env.local:', error.message);
    return { exists: true, configured: false, error: error.message };
  }
}

/**
 * Résumé final et recommandations
 */
function generateSummary(results) {
  console.log('\n📋 RÉSUMÉ DES TESTS');
  console.log('='.repeat(40));
  
  const { connectivity, restApi, structure, envConfig } = results;
  
  console.log('\n🔍 Résultats:');
  console.log(`   Connectivité Supabase: ${connectivity.success ? '✅ OK' : '❌ ÉCHEC'}`);
  console.log(`   API REST Supabase: ${restApi.success ? '✅ OK' : '❌ ÉCHEC'}`);
  console.log(`   Structure projet: ✅ Vérifié`);
  console.log(`   Configuration env: ${envConfig.configured ? '✅ OK' : '⚠️  PARTIEL'}`);
  
  console.log('\n🎯 Étapes suivantes:');
  
  if (connectivity.success && restApi.success) {
    console.log('   ✅ La connectivité Supabase fonctionne !');
    console.log('   🚀 Vous pouvez procéder aux tests d\'interface web:');
    console.log('      → http://localhost:3000/test-supabase-simple');
    console.log('      → http://localhost:3000/test-supabase');
    console.log('      → http://localhost:3000/ultra-simple');
  } else {
    console.log('   ⚠️  Problème de connectivité détecté');
    console.log('   🔧 Vérifiez:');
    console.log('      → URL Supabase : ' + SUPABASE_CONFIG.url);
    console.log('      → Clé API valide');
    console.log('      → Schéma de base de données déployé');
  }
  
  if (!envConfig.configured) {
    console.log('   ⚙️  Configuration d\'environnement à compléter');
    console.log('   📝 Fichier: frontend/.env.local');
  }
  
  return results;
}

/**
 * Fonction principale
 */
async function main() {
  try {
    console.log('⏰ Début des tests:', new Date().toLocaleString());
    
    // Exécution séquentielle des tests
    const connectivity = await testBasicConnectivity();
    const restApi = await testRestAPI();
    const structure = testProjectStructure();
    const envConfig = createEnvConfig();
    
    const results = {
      connectivity,
      restApi,
      structure,
      envConfig,
      timestamp: new Date().toISOString()
    };
    
    // Génération du résumé
    generateSummary(results);
    
    console.log('\n📊 Test terminé:', new Date().toLocaleString());
    console.log('='.repeat(60));
    
    return results;
    
  } catch (error) {
    console.error('\n❌ ERREUR FATALE:', error.message);
    console.error('   Stack:', error.stack);
    process.exit(1);
  }
}

// Exécution si appelé directement
if (require.main === module) {
  main();
}

module.exports = { main, testBasicConnectivity, testRestAPI, testProjectStructure }; 