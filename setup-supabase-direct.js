#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

// Configuration Supabase
const SUPABASE_URL = 'https://kvdrukmoxetoiojazukf.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4';

console.log('🚀 SETUP SUPABASE DIRECT - MINDFLOW PRO');
console.log('======================================');

async function setupSupabase() {
  try {
    console.log('📡 Connexion à Supabase...');
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
    
    console.log('👥 Insertion des professionnels...');
    
    // Données des professionnels
    const professionals = [
      {
        name: 'Dr. <PERSON>',
        role: 'Psychologue clinicienne',
        email: '<EMAIL>',
        specialties: ['Thérapie cognitive comportementale', 'Gestion du stress', 'Anxiété'],
        price_per_session: 85.00,
        location: '15 rue de la Paix, 75001 Paris',
        bio: 'Spécialisée en thérapie cognitive comportementale avec plus de 10 ans d\'expérience.'
      },
      {
        name: 'Dr. Jean Dupont',
        role: 'Psychiatre',
        email: '<EMAIL>',
        specialties: ['Psychiatrie générale', 'Troubles bipolaires', 'Dépression'],
        price_per_session: 120.00,
        location: '42 avenue Victor Hugo, 69003 Lyon',
        bio: 'Psychiatre expérimenté spécialisé dans les troubles de l\'humeur.'
      },
      {
        name: 'Marie Leblanc',
        role: 'Thérapeute comportementale',
        email: '<EMAIL>',
        specialties: ['Thérapie comportementale', 'Phobies', 'Addictions'],
        price_per_session: 75.00,
        location: '8 place Bellecour, 69002 Lyon',
        bio: 'Thérapeute spécialisée dans les troubles comportementaux.'
      },
      {
        name: 'Dr. Ahmed Benali',
        role: 'Psychothérapeute',
        email: '<EMAIL>',
        specialties: ['Psychothérapie humaniste', 'Thérapie de couple', 'Traumatismes'],
        price_per_session: 90.00,
        location: '25 cours Mirabeau, 13100 Aix-en-Provence',
        bio: 'Psychothérapeute avec une approche humaniste centrée sur la personne.'
      }
    ];

    // Tenter d'insérer les professionnels
    const { data: insertedProfessionals, error: profError } = await supabase
      .from('professionals')
      .upsert(professionals, { onConflict: 'email' })
      .select();

    if (profError) {
      console.log('❌ Erreur professionnels:', profError.message);
      if (profError.message.includes('does not exist')) {
        console.log('🔧 Les tables doivent être créées manuellement dans Supabase');
        console.log('📋 Copiez-collez le script SQL suivant dans l\'éditeur Supabase:');
        console.log('🔗 https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf/sql');
        console.log('\n--- SCRIPT SQL À COPIER ---');
        console.log(`
-- Création table professionals
CREATE TABLE IF NOT EXISTS professionals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    specialties TEXT[] DEFAULT '{}',
    price_per_session DECIMAL(10,2) DEFAULT 0,
    location TEXT,
    bio TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Création table appointments
CREATE TABLE IF NOT EXISTS appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    professional_id UUID REFERENCES professionals(id) ON DELETE CASCADE,
    professional_name TEXT NOT NULL,
    professional_role TEXT,
    client_id TEXT NOT NULL,
    client_name TEXT,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    type TEXT DEFAULT 'video',
    status TEXT DEFAULT 'scheduled',
    price DECIMAL(10,2),
    currency TEXT DEFAULT 'EUR',
    notes TEXT,
    meeting_link TEXT,
    reminder_sent BOOLEAN DEFAULT FALSE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_professional ON appointments(professional_id);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);

-- RLS
ALTER TABLE professionals ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public access" ON professionals FOR ALL USING (true);
CREATE POLICY "Public access" ON appointments FOR ALL USING (true);
        `);
        console.log('--- FIN DU SCRIPT SQL ---\n');
        console.log('⚡ Après avoir exécuté le SQL, relancez ce script avec: node setup-supabase-direct.js');
        return;
      }
      throw profError;
    }

    console.log(`✅ ${insertedProfessionals.length} professionnels insérés`);

    console.log('📅 Insertion des rendez-vous...');

    // Créer des rendez-vous
    const appointments = [];
    const today = new Date();

    insertedProfessionals.forEach((prof, index) => {
      const futureDate = new Date(today);
      futureDate.setDate(today.getDate() + index + 1);

      const pastDate = new Date(today);
      pastDate.setDate(today.getDate() - (index + 1));

      // Rendez-vous futur
      appointments.push({
        professional_id: prof.id,
        professional_name: prof.name,
        professional_role: prof.role,
        client_id: `demo-user-${index + 1}`,
        client_name: `Client Démo ${index + 1}`,
        appointment_date: futureDate.toISOString().split('T')[0],
        appointment_time: '14:00',
        duration_minutes: 60,
        type: 'video',
        status: 'scheduled',
        price: prof.price_per_session,
        currency: 'EUR',
        notes: `Rendez-vous programmé avec ${prof.name}`,
        meeting_link: `https://meet.mindflow.com/session-${prof.id}`,
        reminder_sent: false
      });

      // Rendez-vous passé
      appointments.push({
        professional_id: prof.id,
        professional_name: prof.name,
        professional_role: prof.role,
        client_id: `demo-user-${index + 10}`,
        client_name: `Client Expérience ${index + 10}`,
        appointment_date: pastDate.toISOString().split('T')[0],
        appointment_time: '10:00',
        duration_minutes: 60,
        type: index % 2 === 0 ? 'in-person' : 'video',
        status: index === 0 ? 'completed' : 'cancelled',
        price: prof.price_per_session,
        currency: 'EUR',
        notes: index === 0 ? 'Séance terminée avec succès' : 'Annulé par le client',
        rating: index === 0 ? 5 : null,
        feedback: index === 0 ? 'Excellente séance!' : null,
        reminder_sent: true
      });
    });

    const { data: insertedAppointments, error: apptError } = await supabase
      .from('appointments')
      .insert(appointments)
      .select();

    if (apptError) {
      console.log('❌ Erreur rendez-vous:', apptError.message);
      throw apptError;
    }

    console.log(`✅ ${insertedAppointments.length} rendez-vous insérés`);

    // Validation
    console.log('\n🔍 Validation...');
    const { data: finalCheck, error: checkError } = await supabase
      .from('appointments')
      .select(`
        id,
        appointment_date,
        appointment_time,
        status,
        professional_name,
        client_name
      `)
      .limit(5);

    if (checkError) {
      console.log('❌ Erreur de validation:', checkError.message);
    } else {
      console.log(`✅ Validation réussie: ${finalCheck.length} rendez-vous trouvés`);
      console.log('\n📊 Aperçu des données:');
      finalCheck.forEach(apt => {
        console.log(`  📅 ${apt.appointment_date} ${apt.appointment_time} - ${apt.professional_name} avec ${apt.client_name} (${apt.status})`);
      });
    }

    console.log('\n🎉 SETUP TERMINÉ AVEC SUCCÈS !');
    console.log('🌐 Testez sur: http://localhost:3001/test-appointments-supabase');

  } catch (error) {
    console.error('💥 Erreur:', error.message);
    process.exit(1);
  }
}

setupSupabase(); 