#!/usr/bin/env node

/**
 * 🔐 RÉCUPÉRATION DES SECRETS VERCEL - MINDFLOW PRO
 * Script pour obtenir les IDs nécessaires à la configuration GitHub Actions
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Execute une commande shell
 */
function exec(command, options = {}) {
  try {
    const result = execSync(command, { 
      encoding: 'utf-8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    return result.toString().trim();
  } catch (error) {
    if (!options.continueOnError) {
      console.error(`❌ Erreur: ${command}`);
      console.error(error.message);
    }
    return null;
  }
}

/**
 * Vérifie si Vercel CLI est installé
 */
function checkVercelCLI() {
  console.log('🔍 Vérification de Vercel CLI...');
  
  const vercelVersion = exec('vercel --version', { silent: true, continueOnError: true });
  if (!vercelVersion) {
    console.log('❌ Vercel CLI non installé');
    console.log('📦 Installation automatique...');
    exec('npm install -g vercel@latest');
    console.log('✅ Vercel CLI installé');
  } else {
    console.log(`✅ Vercel CLI détecté: ${vercelVersion}`);
  }
}

/**
 * Vérifie l'authentification Vercel
 */
function checkVercelAuth() {
  console.log('🔐 Vérification de l\'authentification Vercel...');
  
  const whoami = exec('vercel whoami', { silent: true, continueOnError: true });
  if (!whoami) {
    console.log('❌ Non authentifié sur Vercel');
    console.log('🔑 Connectez-vous avec: vercel login');
    process.exit(1);
  } else {
    console.log(`✅ Authentifié en tant que: ${whoami}`);
    return whoami;
  }
}

/**
 * Configure le projet Vercel
 */
function setupVercelProject() {
  console.log('🔗 Configuration du projet Vercel...');
  
  const frontendPath = path.join(__dirname, 'frontend');
  if (!fs.existsSync(frontendPath)) {
    console.error('❌ Dossier frontend/ non trouvé');
    process.exit(1);
  }
  
  process.chdir(frontendPath);
  
  // Vérifier si déjà lié
  const vercelDir = path.join(frontendPath, '.vercel');
  if (!fs.existsSync(vercelDir)) {
    console.log('🔗 Liaison du projet...');
    exec('vercel link --yes');
  }
  
  console.log('✅ Projet configuré');
}

/**
 * Récupère les secrets Vercel
 */
function getVercelSecrets() {
  console.log('📋 Récupération des secrets Vercel...');
  
  const secrets = {};
  
  // 1. Token Vercel (à récupérer manuellement)
  console.log('\n🔑 VERCEL_TOKEN:');
  console.log('   Allez sur: https://vercel.com/account/tokens');
  console.log('   Créez un nouveau token avec les permissions "Full Access"');
  console.log('   ⚠️  Gardez ce token secret et sécurisé!');
  
  // 2. Project ID et Org ID
  try {
    const projectJsonPath = path.join(process.cwd(), '.vercel', 'project.json');
    
    if (fs.existsSync(projectJsonPath)) {
      const projectData = JSON.parse(fs.readFileSync(projectJsonPath, 'utf-8'));
      
      secrets.VERCEL_PROJECT_ID = projectData.projectId;
      secrets.VERCEL_ORG_ID = projectData.orgId;
      
      console.log('\n📊 SECRETS RÉCUPÉRÉS:');
      console.log(`   VERCEL_PROJECT_ID: ${secrets.VERCEL_PROJECT_ID}`);
      console.log(`   VERCEL_ORG_ID: ${secrets.VERCEL_ORG_ID}`);
      
    } else {
      console.error('❌ Fichier .vercel/project.json non trouvé');
      console.log('🔗 Exécutez: vercel link');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Erreur lecture project.json:', error.message);
    process.exit(1);
  }
  
  return secrets;
}

/**
 * Génère les instructions GitHub
 */
function generateGitHubInstructions(secrets) {
  console.log('\n🚀 CONFIGURATION GITHUB SECRETS:');
  console.log('   1. Allez sur: https://github.com/Anderson-Archimede/MindFlow-Pro/settings/secrets/actions');
  console.log('   2. Cliquez sur "New repository secret"');
  console.log('   3. Ajoutez ces secrets:');
  
  console.log('\n📝 SECRETS À AJOUTER:');
  console.log('┌─────────────────────┬─────────────────────────────────────────────┐');
  console.log('│ Nom du Secret       │ Valeur                                      │');
  console.log('├─────────────────────┼─────────────────────────────────────────────┤');
  console.log(`│ VERCEL_TOKEN        │ [Token depuis vercel.com/account/tokens]   │`);
  console.log(`│ VERCEL_PROJECT_ID   │ ${secrets.VERCEL_PROJECT_ID}                │`);
  console.log(`│ VERCEL_ORG_ID       │ ${secrets.VERCEL_ORG_ID}                    │`);
  console.log('└─────────────────────┴─────────────────────────────────────────────┘');
}

/**
 * Teste la configuration
 */
function testConfiguration() {
  console.log('\n🧪 TEST DE CONFIGURATION:');
  
  // Test de build local
  try {
    console.log('🏗️ Test de build...');
    exec('npm run build', { silent: true });
    console.log('✅ Build réussi');
  } catch (error) {
    console.log('⚠️ Erreur de build (normal si pas encore configuré)');
  }
  
  // Test des projets Vercel
  try {
    console.log('📋 Projets Vercel disponibles:');
    const projects = exec('vercel projects list', { silent: true });
    if (projects.includes('mindflow-pro')) {
      console.log('✅ Projet MindFlow Pro trouvé');
    }
  } catch (error) {
    console.log('⚠️ Impossible de lister les projets');
  }
}

/**
 * Sauvegarde les secrets dans un fichier local (pour référence)
 */
function saveSecretsReference(secrets) {
  const secretsRef = {
    ...secrets,
    VERCEL_TOKEN: '[À récupérer sur vercel.com/account/tokens]',
    instructions: 'Ajoutez ces secrets dans GitHub: Settings > Secrets and variables > Actions',
    github_url: 'https://github.com/Anderson-Archimede/MindFlow-Pro/settings/secrets/actions',
    vercel_tokens_url: 'https://vercel.com/account/tokens'
  };
  
  fs.writeFileSync('vercel-secrets-reference.json', JSON.stringify(secretsRef, null, 2));
  console.log('\n💾 Référence sauvegardée: vercel-secrets-reference.json');
}

/**
 * Fonction principale
 */
function main() {
  console.log('🔐 CONFIGURATION DES SECRETS VERCEL - MINDFLOW PRO\n');
  
  try {
    // 1. Vérifications préliminaires
    checkVercelCLI();
    const user = checkVercelAuth();
    
    // 2. Configuration du projet
    setupVercelProject();
    
    // 3. Récupération des secrets
    const secrets = getVercelSecrets();
    
    // 4. Instructions GitHub
    generateGitHubInstructions(secrets);
    
    // 5. Tests
    testConfiguration();
    
    // 6. Sauvegarde référence
    saveSecretsReference(secrets);
    
    console.log('\n🎉 CONFIGURATION TERMINÉE!');
    console.log('\n📋 PROCHAINES ÉTAPES:');
    console.log('   1. Récupérez votre VERCEL_TOKEN sur https://vercel.com/account/tokens');
    console.log('   2. Ajoutez les 3 secrets dans GitHub Actions');
    console.log('   3. Faites un push pour tester le CI/CD');
    
  } catch (error) {
    console.error('\n❌ ERREUR:', error.message);
    process.exit(1);
  }
}

// Aide
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🔐 CONFIGURATION DES SECRETS VERCEL - MINDFLOW PRO

Ce script vous aide à configurer les secrets nécessaires pour le CI/CD GitHub Actions.

Usage:
  node setup-vercel-secrets.js

Prérequis:
  - Compte Vercel actif
  - Vercel CLI installé (sera installé automatiquement)
  - Projet déjà déployé sur Vercel

Secrets générés:
  - VERCEL_PROJECT_ID  (automatique)
  - VERCEL_ORG_ID      (automatique)  
  - VERCEL_TOKEN       (manuel - voir instructions)

URLs utiles:
  - Tokens Vercel: https://vercel.com/account/tokens
  - GitHub Secrets: https://github.com/Anderson-Archimede/MindFlow-Pro/settings/secrets/actions
`);
  process.exit(0);
}

// Exécution
main(); 