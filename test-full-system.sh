#!/bin/bash

echo "🧪 Test Complet du Système MindFlow Pro"
echo "======================================="

# Couleurs pour les résultats
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Fonction pour tester un endpoint
test_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    echo -n "🔍 Test: $description... "
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url")
    status_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*$//')
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (Status: $status_code)"
        if [ "$expected_status" = "200" ]; then
            echo "   📄 Réponse: $(echo "$body" | head -c 100)..."
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        echo "   📄 Réponse: $body"
    fi
    echo
}

echo "🚀 Test des Services Backend"
echo "-----------------------------"

# Test du health check
test_endpoint "http://localhost:4000/api/v1/health" "200" "Backend Health Check"

# Test endpoints publics
test_endpoint "http://localhost:4000/api/v1/ai-coach/crisis-resources" "200" "Ressources de crise (public)"
test_endpoint "http://localhost:4000/api/v1/ai-coach/wellness-tips" "200" "Conseils bien-être (public)"

# Test endpoints protégés (doivent retourner 401)
test_endpoint "http://localhost:4000/api/v1/users/profile" "401" "Profil utilisateur (protégé)"
test_endpoint "http://localhost:4000/api/v1/ai-coach/interactions" "401" "Interactions IA (protégé)"
test_endpoint "http://localhost:4000/api/v1/journal" "401" "Journal (protégé)"

echo "🌐 Test du Frontend"
echo "-------------------"

# Vérifier que le frontend répond
frontend_status=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:5173/")
echo -n "🔍 Test: Frontend accessible... "

if [ "$frontend_status" = "200" ] || [ "$frontend_status" = "404" ]; then
    echo -e "${GREEN}✅ PASS${NC} (Status: $frontend_status)"
    echo "   📄 Frontend accessible sur http://localhost:5173"
else
    echo -e "${RED}❌ FAIL${NC} (Status: $frontend_status)"
    echo "   📄 Frontend non accessible"
fi

echo
echo "📊 Résumé des Services"
echo "----------------------"
echo "🟢 Backend API: http://localhost:4000"
echo "🟢 Frontend:    http://localhost:5173"
echo "📚 Documentation API: http://localhost:4000/api/v1/health"

echo
echo "🔧 Commandes utiles:"
echo "--------------------"
echo "• Backend logs:   cd backend && npm run dev"
echo "• Frontend logs:  cd frontend && npm run dev"  
echo "• Test API:       ./test-endpoints.sh"
echo "• Arrêter tout:   pkill -f 'nodemon|vite'"

echo
echo "✨ Test terminé! Les services sont opérationnels." 