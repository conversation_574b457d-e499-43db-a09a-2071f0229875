-- =====================================================
-- MINDFLOW PRO - MIGRATION COMPLÈTE PHASE 1 (ULTRA-FIX)
-- Correction définitive des erreurs de colonnes manquantes
-- Script avec suppression/recréation complète des tables
-- =====================================================

-- SUPPRESSION COMPLÈTE DES TABLES EXISTANTES (pour éviter les conflits)
DROP TABLE IF EXISTS journal_entries CASCADE;
DROP TABLE IF EXISTS ai_coaching_sessions CASCADE;
DROP TABLE IF EXISTS mood_analytics CASCADE;
DROP TABLE IF EXISTS smart_notifications CASCADE;

-- 1. TABLE JOURNAL_ENTRIES (useJournalData.ts - 384 lignes)
CREATE TABLE journal_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    entry_type VARCHAR(50) DEFAULT 'daily',
    mood_level INTEGER CHECK (mood_level >= 1 AND mood_level <= 5),
    tags TEXT[] DEFAULT '{}',
    emotions TEXT[] DEFAULT '{}',
    stress_level INTEGER CHECK (stress_level >= 0 AND stress_level <= 10),
    energy_level INTEGER CHECK (energy_level >= 0 AND energy_level <= 10),
    sleep_quality INTEGER CHECK (sleep_quality >= 0 AND sleep_quality <= 10),
    gratitude_notes TEXT,
    goals TEXT,
    challenges TEXT,
    achievements TEXT,
    is_private BOOLEAN DEFAULT false,
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. TABLE AI_COACHING_SESSIONS (useAICoach.ts - 507 lignes)
CREATE TABLE ai_coaching_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused')),
    theme VARCHAR(100) NOT NULL,
    goal TEXT NOT NULL,
    messages JSONB DEFAULT '[]',
    mood_analysis JSONB DEFAULT '{}',
    stats JSONB DEFAULT '{}',
    ai_insights JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. TABLE MOOD_ANALYTICS (useMoodAnalytics.ts - 439 lignes)  
CREATE TABLE mood_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    date DATE NOT NULL,
    mood DECIMAL(3,1) CHECK (mood >= 1.0 AND mood <= 10.0),
    energy DECIMAL(3,1) CHECK (energy >= 1.0 AND energy <= 10.0),
    stress DECIMAL(3,1) CHECK (stress >= 1.0 AND stress <= 10.0),
    anxiety DECIMAL(3,1) CHECK (anxiety >= 1.0 AND anxiety <= 10.0),
    sleep DECIMAL(3,1) CHECK (sleep >= 1.0 AND sleep <= 10.0),
    factors TEXT[] DEFAULT '{}',
    activities TEXT[] DEFAULT '{}',
    weather VARCHAR(50),
    notes TEXT,
    wellness_score INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. TABLE SMART_NOTIFICATIONS (useSmartNotifications.ts - 508 lignes)
CREATE TABLE smart_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('reminder', 'suggestion', 'achievement', 'warning', 'insight', 'emergency')),
    priority VARCHAR(10) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    action_text VARCHAR(100),
    action_url VARCHAR(500),
    ai_generated BOOLEAN DEFAULT false,
    triggers TEXT[] DEFAULT '{}',
    scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_read BOOLEAN DEFAULT false,
    is_actioned BOOLEAN DEFAULT false,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- INDEX POUR PERFORMANCES
CREATE INDEX idx_journal_entries_user_created ON journal_entries(user_id, created_at DESC);
CREATE INDEX idx_ai_coaching_user_status ON ai_coaching_sessions(user_id, status);
CREATE INDEX idx_mood_analytics_user_date ON mood_analytics(user_id, date DESC);
CREATE INDEX idx_smart_notifications_user_read ON smart_notifications(user_id, is_read);

-- RLS (Row Level Security) - RÉACTIVATION
ALTER TABLE journal_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_coaching_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE mood_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE smart_notifications ENABLE ROW LEVEL SECURITY;

-- Politiques pour développement (accès public complet)
CREATE POLICY "Allow all for development" ON journal_entries FOR ALL USING (true);
CREATE POLICY "Allow all for development" ON ai_coaching_sessions FOR ALL USING (true);  
CREATE POLICY "Allow all for development" ON mood_analytics FOR ALL USING (true);
CREATE POLICY "Allow all for development" ON smart_notifications FOR ALL USING (true);

-- DONNÉES DE DÉMONSTRATION COMPLÈTES
INSERT INTO journal_entries (title, content, entry_type, mood_level, tags, emotions, stress_level, energy_level, sleep_quality, is_favorite) VALUES
('Premier jour de thérapie', 'Aujourd''hui j''ai commencé ma thérapie. C''était intimidant mais prometteur.', 'daily', 4, ARRAY['thérapie', 'nouveau'], ARRAY['nerveux', 'hopeful'], 6, 6, 7, true),
('Méditation matinale', 'Session de méditation très apaisante de 15 minutes.', 'reflection', 5, ARRAY['méditation', 'bien-être'], ARRAY['calme', 'centré'], 3, 8, 8, false),
('Journée stressante', 'Beaucoup de deadlines, mais j''ai utilisé les techniques de respiration.', 'stress_tracking', 3, ARRAY['travail', 'stress'], ARRAY['overwhelmed', 'fier'], 8, 4, 6, false),
('Sortie entre amis', 'Soirée géniale qui m''a rappelé l''importance des connexions sociales.', 'daily', 5, ARRAY['amis', 'social'], ARRAY['joyeux', 'connecté'], 2, 9, 7, true),
('Réflexion sur mes progrès', 'Un mois de parcours, je remarque des changements positifs.', 'reflection', 4, ARRAY['progrès', 'croissance'], ARRAY['fier', 'optimiste'], 4, 7, 8, true);

INSERT INTO ai_coaching_sessions (theme, goal, messages, mood_analysis, stats, status) VALUES
('gestion_stress', 'Apprendre à mieux gérer le stress quotidien', 
'[{"id": "1", "type": "ai", "content": "Comment vous sentez-vous aujourd''hui ?"}]',
'{"initialMood": 4, "currentMood": 6, "moodTrend": "improving"}',
'{"duration": 25, "messageCount": 12}', 'completed'),

('amélioration_humeur', 'Développer des stratégies pour améliorer mon humeur',
'[{"id": "1", "type": "ai", "content": "Bienvenue dans votre session d''amélioration d''humeur !"}]',
'{"initialMood": 3, "currentMood": 5, "moodTrend": "improving"}',
'{"duration": 18, "messageCount": 8}', 'completed'),

('confiance_soi', 'Renforcer ma confiance en moi',
'[{"id": "1", "type": "ai", "content": "Travaillons sur votre confiance en vous."}]',
'{"initialMood": 4, "currentMood": 4, "moodTrend": "stable"}',
'{"duration": 15, "messageCount": 6}', 'active');

INSERT INTO mood_analytics (date, mood, energy, stress, anxiety, sleep, factors, activities, weather, wellness_score) VALUES
(CURRENT_DATE - INTERVAL '6 days', 7.2, 6.8, 4.5, 3.2, 8.0, ARRAY['travail', 'exercice'], ARRAY['méditation', 'marche'], 'ensoleillé', 72),
(CURRENT_DATE - INTERVAL '5 days', 6.5, 7.1, 5.8, 4.1, 7.5, ARRAY['stress', 'deadlines'], ARRAY['lecture'], 'nuageux', 65),
(CURRENT_DATE - INTERVAL '4 days', 8.1, 8.5, 3.2, 2.8, 8.5, ARRAY['weekend', 'repos'], ARRAY['sport', 'amis'], 'ensoleillé', 85),
(CURRENT_DATE - INTERVAL '3 days', 7.8, 7.9, 3.5, 2.9, 8.2, ARRAY['social', 'détente'], ARRAY['cinema'], 'ensoleillé', 82),
(CURRENT_DATE - INTERVAL '2 days', 6.2, 6.0, 6.1, 4.8, 6.8, ARRAY['lundi', 'travail'], ARRAY['méditation'], 'pluvieux', 62),
(CURRENT_DATE - INTERVAL '1 days', 7.0, 7.3, 4.8, 3.5, 7.8, ARRAY['thérapie'], ARRAY['journal'], 'nuageux', 73),
(CURRENT_DATE, 7.5, 7.7, 4.2, 3.1, 8.1, ARRAY['bien-être'], ARRAY['yoga', 'lecture'], 'ensoleillé', 78);

INSERT INTO smart_notifications (type, priority, title, message, action_text, action_url, ai_generated, triggers, is_read) VALUES
('reminder', 'medium', 'Moment de méditation', 'Il est temps pour votre session de méditation quotidienne.', 'Commencer', '/meditation', true, ARRAY['scheduled_time'], true),
('suggestion', 'low', 'Sortie suggérée', 'Une promenade pourrait améliorer votre humeur.', 'Planifier', '/activities', true, ARRAY['weather', 'mood'], false),
('achievement', 'low', 'Félicitations ! 🎉', 'Vous avez maintenu votre routine de journal pendant 7 jours !', 'Voir progrès', '/analytics', false, ARRAY['streak'], false),
('insight', 'medium', 'Pattern détecté', 'Votre humeur s''améliore après l''exercice. Continuez !', 'Détails', '/insights', true, ARRAY['ai_pattern'], false),
('warning', 'high', 'Niveau de stress élevé', 'Votre stress est élevé depuis 2 jours. Prenez soin de vous.', 'Techniques', '/stress', true, ARRAY['stress_threshold'], false);

-- VALIDATION FINALE
SELECT 'MIGRATION ULTRA-FIX TERMINÉE - Tables recréées avec succès !' as status;
SELECT 'journal_entries' as table_name, COUNT(*) as records FROM journal_entries
UNION ALL SELECT 'ai_coaching_sessions', COUNT(*) FROM ai_coaching_sessions  
UNION ALL SELECT 'mood_analytics', COUNT(*) FROM mood_analytics
UNION ALL SELECT 'smart_notifications', COUNT(*) FROM smart_notifications;

-- VÉRIFICATION DES COLONNES CRÉÉES
SELECT 'Vérification des colonnes journal_entries:' as info;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'journal_entries' 
ORDER BY ordinal_position; 