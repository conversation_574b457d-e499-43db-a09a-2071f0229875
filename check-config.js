const fs = require('fs');

console.log('Vérification Migration Supabase');
console.log('===============================');

// Vérifier .env.local
const envPath = 'frontend/.env.local';
if (fs.existsSync(envPath)) {
  console.log('✅ .env.local existe');
  const content = fs.readFileSync(envPath, 'utf8');
  const hasSupabaseUrl = content.includes('NEXT_PUBLIC_SUPABASE_URL');
  const hasDualMode = content.includes('DUAL_DATABASE_MODE');
  console.log(`✅ Supabase URL: ${hasSupabaseUrl}`);
  console.log(`✅ Mode Dual: ${hasDualMode}`);
} else {
  console.log('❌ .env.local manquant');
}

// Vérifier composants
const files = [
  'frontend/src/lib/supabase/client.ts',
  'frontend/src/lib/database/index.ts',
  'frontend/src/app/test-supabase/page.tsx'
];

console.log('\nComposants:');
files.forEach(f => {
  console.log(`${fs.existsSync(f) ? '✅' : '❌'} ${f}`);
});

console.log('\nPour tester: http://localhost:3001/test-supabase'); 