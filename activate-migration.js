#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

console.log('🔄 Activation Migration Progressive MindFlow Pro\n');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

const envPath = path.join(__dirname, 'frontend', '.env.local');

function readEnvFile() {
  try {
    return fs.readFileSync(envPath, 'utf8');
  } catch (error) {
    console.log('❌ Fichier .env.local introuvable. Exécutez d\'abord setup-supabase.js');
    process.exit(1);
  }
}

function updateEnvFile(content) {
  fs.writeFileSync(envPath, content);
}

function updateEnvVar(content, varName, value) {
  const regex = new RegExp(`^${varName}=.*$`, 'm');
  if (content.match(regex)) {
    return content.replace(regex, `${varName}=${value}`);
  } else {
    return content + `\n${varName}=${value}`;
  }
}

async function activateMigration() {
  try {
    console.log('📋 Étapes de Migration Progressive:\n');
    console.log('1. Mode Dual Database (SQLite + Supabase)');
    console.log('2. Migration des utilisateurs');
    console.log('3. Migration du suivi d\'humeur');
    console.log('4. Migration des entrées journal');
    console.log('5. Basculement complet vers Supabase');
    console.log('6. Activation temps réel\n');

    const step = await question('🎯 Quelle étape voulez-vous activer? (1-6): ');

    let envContent = readEnvFile();

    switch (step) {
      case '1':
        console.log('\n🔄 Activation du mode Dual Database...');
        envContent = updateEnvVar(envContent, 'NEXT_PUBLIC_DUAL_DATABASE_MODE', 'true');
        console.log('✅ Mode Dual activé - SQLite et Supabase en parallèle');
        break;

      case '2':
        console.log('\n👥 Activation migration des utilisateurs...');
        envContent = updateEnvVar(envContent, 'NEXT_PUBLIC_DUAL_DATABASE_MODE', 'true');
        envContent = updateEnvVar(envContent, 'NEXT_PUBLIC_MIGRATE_USER_DATA', 'true');
        console.log('✅ Migration utilisateurs activée');
        break;

      case '3':
        console.log('\n😊 Activation migration suivi d\'humeur...');
        envContent = updateEnvVar(envContent, 'NEXT_PUBLIC_MIGRATE_MOOD_TRACKING', 'true');
        console.log('✅ Migration mood tracking activée');
        break;

      case '4':
        console.log('\n📝 Activation migration journal...');
        envContent = updateEnvVar(envContent, 'NEXT_PUBLIC_MIGRATE_JOURNAL_ENTRIES', 'true');
        console.log('✅ Migration journal activée');
        break;

      case '5':
        console.log('\n🚀 Basculement complet vers Supabase...');
        envContent = updateEnvVar(envContent, 'NEXT_PUBLIC_USE_SUPABASE_DATABASE', 'true');
        envContent = updateEnvVar(envContent, 'NEXT_PUBLIC_USE_SUPABASE_AUTH', 'true');
        console.log('✅ Supabase activé comme DB principale');
        break;

      case '6':
        console.log('\n⚡ Activation temps réel...');
        envContent = updateEnvVar(envContent, 'NEXT_PUBLIC_ENABLE_REAL_TIME', 'true');
        console.log('✅ Fonctionnalités temps réel activées');
        break;

      default:
        console.log('❌ Étape invalide. Choisissez entre 1-6.');
        return;
    }

    updateEnvFile(envContent);
    
    console.log('\n✅ Configuration mise à jour !');
    console.log('🎯 Redémarrez : npm run dev');

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    rl.close();
  }
}

activateMigration();
