
                CREATE TABLE IF NOT EXISTS appointments (
                    id TEXT PRIMARY KEY,
                    professional_id TEXT NOT NULL,
                    client_name TEXT NOT NULL,
                    client_email TEXT NOT NULL,
                    appointment_date TIMESTAMP WITH TIME ZONE NOT NULL,
                    duration_minutes INTEGER DEFAULT 60,
                    type TEXT CHECK (type IN ('video', 'in-person', 'chat')) DEFAULT 'video',
                    status TEXT CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no-show')) DEFAULT 'scheduled',
                    notes TEXT,
                    price DECIMAL(10,2) DEFAULT 0.00,
                    currency TEXT DEFAULT 'EUR',
                    meeting_link TEXT,
                    cancellation_reason TEXT,
                    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
                    feedback TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                