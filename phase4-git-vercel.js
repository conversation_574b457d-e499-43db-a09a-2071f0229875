#!/usr/bin/env node

/**
 * 🚀 PHASE 4: PUSH GIT + VERCEL
 * 
 * Automatisation du déploiement Git et Vercel
 */

const fs = require('fs');
const { execSync } = require('child_process');

console.log('\n🌐 PHASE 4: PUSH GIT + VERCEL');
console.log('===============================\n');

class GitVercelDeployment {
    constructor() {
        this.deploymentSteps = [
            'Préparation du commit',
            'Push vers GitHub',
            'Configuration Vercel',
            'Déploiement production',
            'Validation post-déploiement',
            'Monitoring production'
        ];
        this.currentStep = 0;
    }

    log(message) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] ${message}`);
    }

    async executeStep(stepName, action) {
        this.currentStep++;
        console.log(`\n📋 Étape ${this.currentStep}/${this.deploymentSteps.length}: ${stepName}`);
        console.log('-'.repeat(50));
        
        try {
            await action();
            console.log(`✅ ${stepName} - TERMINÉ`);
            return true;
        } catch (error) {
            console.log(`❌ ${stepName} - ERREUR: ${error.message}`);
            return false;
        }
    }

    // Étape 1: Préparation du commit
    async prepareCommit() {
        this.log('📝 Préparation du commit Git...');
        
        // Vérification du statut Git
        try {
            const status = execSync('git status --porcelain', { encoding: 'utf8' });
            if (status.trim()) {
                console.log('📄 Fichiers modifiés détectés:');
                console.log(status);
            } else {
                console.log('✅ Aucun fichier modifié détecté');
            }
        } catch (error) {
            throw new Error('Erreur lors de la vérification du statut Git');
        }

        // Création du fichier de déploiement
        const deploymentInfo = {
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            features: [
                'Dashboard principal avec statistiques',
                'Système de journal personnel',
                'IA Coach avec analyse de sentiment',
                'Analytics d\'humeur avec prédictions',
                'Notifications intelligentes',
                'Système de rendez-vous Supabase',
                'Interface responsive moderne'
            ],
            technologies: [
                'Next.js 14',
                'React 18',
                'TypeScript',
                'Tailwind CSS',
                'Supabase',
                'Vercel'
            ],
            status: 'Production Ready'
        };

        fs.writeFileSync('deployment-info.json', JSON.stringify(deploymentInfo, null, 2));
        
        this.log('📋 Informations de déploiement créées');
    }

    // Étape 2: Push vers GitHub
    async pushToGitHub() {
        this.log('📤 Push vers GitHub...');
        
        try {
            // Ajout de tous les fichiers
            execSync('git add .', { stdio: 'inherit' });
            this.log('✅ Fichiers ajoutés au staging');
            
            // Commit avec message automatique
            const commitMessage = `🚀 MindFlow Pro - Déploiement production complet

✨ Fonctionnalités:
- Dashboard principal avec statistiques de bien-être
- Système de journal personnel avec CRUD complet
- IA Coach avec analyse de sentiment en français
- Analytics d'humeur avec prédictions IA
- Notifications intelligentes personnalisées
- Système de rendez-vous intégré Supabase
- Interface responsive moderne

🛠️ Technologies:
- Next.js 14 + React 18 + TypeScript
- Tailwind CSS + shadcn/ui
- Supabase (Auth + Database + Realtime)
- Architecture full-stack scalable

📊 Status: Production Ready
⏰ Déployé le: ${new Date().toLocaleDateString('fr-FR')}

#MindFlowPro #NextJS #Supabase #SantéMentale`;

            execSync(`git commit -m "${commitMessage}"`, { stdio: 'inherit' });
            this.log('✅ Commit créé avec succès');
            
            // Push vers origin main
            execSync('git push origin main', { stdio: 'inherit' });
            this.log('✅ Code pushé vers GitHub');
            
            // Affichage du lien GitHub
            try {
                const remoteUrl = execSync('git remote get-url origin', { encoding: 'utf8' }).trim();
                console.log(`\n🔗 Repository GitHub: ${remoteUrl}`);
            } catch (error) {
                console.log('⚠️  Impossible de récupérer l\'URL du repository');
            }
            
        } catch (error) {
            throw new Error(`Erreur Git: ${error.message}`);
        }
    }

    // Étape 3: Configuration Vercel
    async configureVercel() {
        this.log('⚙️ Configuration Vercel...');
        
        // Création du fichier vercel.json optimisé
        const vercelConfig = {
            "version": 2,
            "name": "mindflow-pro",
            "builds": [
                {
                    "src": "frontend/package.json",
                    "use": "@vercel/next"
                }
            ],
            "routes": [
                {
                    "src": "/(.*)",
                    "dest": "/frontend/$1"
                }
            ],
            "env": {
                "NEXT_PUBLIC_APP_NAME": "MindFlow Pro",
                "NEXT_PUBLIC_APP_VERSION": "1.0.0"
            },
            "functions": {
                "frontend/src/app/api/**/*.ts": {
                    "maxDuration": 10
                }
            },
            "headers": [
                {
                    "source": "/(.*)",
                    "headers": [
                        {
                            "key": "X-Frame-Options",
                            "value": "DENY"
                        },
                        {
                            "key": "X-Content-Type-Options",
                            "value": "nosniff"
                        },
                        {
                            "key": "Referrer-Policy",
                            "value": "origin-when-cross-origin"
                        }
                    ]
                }
            ]
        };

        fs.writeFileSync('vercel.json', JSON.stringify(vercelConfig, null, 2));
        
        // Variables d'environnement à configurer sur Vercel
        const envVars = {
            "NEXT_PUBLIC_SUPABASE_URL": "https://kvdrukmoxetoiojazukf.supabase.co",
            "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ",
            "NEXT_PUBLIC_USE_SUPABASE_DATABASE": "true",
            "NEXT_PUBLIC_USE_SUPABASE_AUTH": "false",
            "NEXT_PUBLIC_DUAL_DATABASE_MODE": "true"
        };

        fs.writeFileSync('vercel-env-vars.json', JSON.stringify(envVars, null, 2));
        
        console.log('\n🔧 Configuration Vercel créée:');
        console.log('✅ vercel.json - Configuration de déploiement');
        console.log('✅ vercel-env-vars.json - Variables d\'environnement');
        
        console.log('\n📋 ACTIONS MANUELLES REQUISES:');
        console.log('1. Connecter le repository GitHub à Vercel');
        console.log('2. Configurer les variables d\'environnement dans Vercel Dashboard');
        console.log('3. Définir le répertoire racine: ./frontend');
        console.log('4. Définir la commande de build: npm run build');
    }

    // Étape 4: Instructions de déploiement
    async deploymentInstructions() {
        this.log('🚀 Instructions de déploiement Vercel...');
        
        const deploymentGuide = `
# 🚀 GUIDE DE DÉPLOIEMENT VERCEL - MINDFLOW PRO

## 1. Connexion du Repository

1. Aller sur https://vercel.com/dashboard
2. Cliquer sur "Add New" > "Project"
3. Sélectionner le repository GitHub "MindFlow-Pro"
4. Configurer les paramètres:
   - Framework: Next.js
   - Root Directory: ./frontend
   - Build Command: npm run build
   - Output Directory: .next

## 2. Variables d'environnement

Ajouter ces variables dans Vercel Dashboard > Settings > Environment Variables:

\`\`\`
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
NEXT_PUBLIC_USE_SUPABASE_DATABASE=true
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true
\`\`\`

## 3. Déploiement

1. Cliquer sur "Deploy"
2. Attendre la fin du build (2-5 minutes)
3. Vérifier le déploiement sur l'URL fournie

## 4. Domaine personnalisé (optionnel)

1. Aller dans Settings > Domains
2. Ajouter votre domaine personnalisé
3. Configurer les DNS selon les instructions

## 5. Monitoring

- Analytics: Activé automatiquement
- Speed Insights: Activé automatiquement
- Functions: Monitoring des API routes

## 6. URLs importantes

- Dashboard Vercel: https://vercel.com/dashboard
- Repository GitHub: Voir la sortie Git
- Application déployée: Voir après déploiement

---

🎉 MindFlow Pro sera accessible publiquement après le déploiement !
`;

        fs.writeFileSync('DEPLOYMENT_GUIDE_VERCEL.md', deploymentGuide);
        
        console.log('📄 Guide de déploiement créé: DEPLOYMENT_GUIDE_VERCEL.md');
    }

    // Étape 5: Validation post-déploiement
    async postDeploymentValidation() {
        this.log('🧪 Préparation de la validation post-déploiement...');
        
        const validationScript = `
#!/usr/bin/env node

/**
 * Script de validation post-déploiement
 */

const https = require('https');

async function validateDeployment(url) {
    console.log('🔍 Validation du déploiement:', url);
    
    const checks = [
        { path: '/', name: 'Page d\\'accueil' },
        { path: '/dashboard', name: 'Dashboard' },
        { path: '/appointments', name: 'Rendez-vous' },
        { path: '/journal', name: 'Journal' },
        { path: '/ai-coach', name: 'IA Coach' },
        { path: '/analytics', name: 'Analytics' }
    ];
    
    for (const check of checks) {
        try {
            const response = await fetch(url + check.path);
            const status = response.status === 200 ? '✅' : '❌';
            console.log(\`\${status} \${check.name}: \${response.status}\`);
        } catch (error) {
            console.log(\`❌ \${check.name}: Erreur de connexion\`);
        }
    }
}

// Usage: node validate-deployment.js https://your-app.vercel.app
const url = process.argv[2];
if (url) {
    validateDeployment(url);
} else {
    console.log('Usage: node validate-deployment.js <URL>');
}
`;

        fs.writeFileSync('validate-deployment.js', validationScript);
        
        console.log('🔧 Script de validation créé: validate-deployment.js');
        console.log('   Usage après déploiement: node validate-deployment.js <VERCEL_URL>');
    }

    // Étape 6: Monitoring production
    async setupProductionMonitoring() {
        this.log('📊 Configuration du monitoring production...');
        
        const monitoringConfig = {
            vercel_analytics: {
                enabled: true,
                features: [
                    'page_views',
                    'unique_visitors',
                    'bounce_rate',
                    'session_duration'
                ]
            },
            speed_insights: {
                enabled: true,
                metrics: [
                    'first_contentful_paint',
                    'largest_contentful_paint',
                    'first_input_delay',
                    'cumulative_layout_shift'
                ]
            },
            error_tracking: {
                enabled: true,
                alerts: true
            },
            custom_monitoring: {
                supabase_health: '/api/health/supabase',
                response_times: true,
                user_flows: [
                    'dashboard_access',
                    'appointment_booking',
                    'journal_creation',
                    'ai_chat_interaction'
                ]
            }
        };

        fs.writeFileSync('production-monitoring.json', JSON.stringify(monitoringConfig, null, 2));
        
        console.log('📊 Configuration monitoring créée: production-monitoring.json');
        
        console.log('\n📈 MONITORING AUTOMATIQUE INCLUS:');
        console.log('✅ Vercel Analytics - Trafic et comportement utilisateur');
        console.log('✅ Speed Insights - Performance et Core Web Vitals');
        console.log('✅ Function Logs - Monitoring des API routes');
        console.log('✅ Error Tracking - Détection automatique d\'erreurs');
    }

    // Méthode principale d'exécution
    async execute() {
        console.log('🚀 Démarrage du déploiement Phase 4...\n');
        
        const steps = [
            { name: this.deploymentSteps[0], action: () => this.prepareCommit() },
            { name: this.deploymentSteps[1], action: () => this.pushToGitHub() },
            { name: this.deploymentSteps[2], action: () => this.configureVercel() },
            { name: this.deploymentSteps[3], action: () => this.deploymentInstructions() },
            { name: this.deploymentSteps[4], action: () => this.postDeploymentValidation() },
            { name: this.deploymentSteps[5], action: () => this.setupProductionMonitoring() }
        ];

        let successCount = 0;
        
        for (const step of steps) {
            const success = await this.executeStep(step.name, step.action);
            if (success) {
                successCount++;
            } else {
                console.log('\n⚠️  Arrêt du déploiement après erreur');
                break;
            }
        }

        console.log('\n📊 RÉSULTAT PHASE 4:');
        console.log(`✅ ${successCount}/${steps.length} étapes réussies`);
        
        if (successCount === steps.length) {
            console.log('\n�� PHASE 4 AUTOMATISÉE AVEC SUCCÈS !');
            console.log('');
            console.log('📋 PROCHAINES ACTIONS MANUELLES:');
            console.log('1. Connecter le repository sur Vercel Dashboard');
            console.log('2. Configurer les variables d\'environnement');
            console.log('3. Lancer le déploiement');
            console.log('4. Valider avec validate-deployment.js');
            console.log('');
            console.log('📄 GUIDES CRÉÉS:');
            console.log('✅ DEPLOYMENT_GUIDE_VERCEL.md');
            console.log('✅ vercel.json (configuration)');
            console.log('✅ validate-deployment.js (validation)');
            console.log('✅ production-monitoring.json (monitoring)');
            console.log('');
            console.log('🌐 APRÈS DÉPLOIEMENT:');
            console.log('   MindFlow Pro sera accessible publiquement !');
        } else {
            console.log('\n❌ Phase 4 incomplète. Corrigez les erreurs et relancez.');
        }
    }
}

// Exécution
if (require.main === module) {
    const deployment = new GitVercelDeployment();
    deployment.execute().catch(console.error);
}

module.exports = GitVercelDeployment;
