import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 4000;

// Middleware basique
app.use(cors());
app.use(express.json());

// Routes de test
app.get('/', (req, res) => {
  res.json({ 
    message: 'MindFlow Pro API - Version minimale',
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/v1/health', (req, res) => {
  res.json({ 
    success: true,
    status: 'healthy',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    database: 'sqlite ready',
    api: 'operational'
  });
});

app.get('/api/v1/test', (req, res) => {
  res.json({
    message: 'Test endpoint OK',
    environment: process.env.NODE_ENV || 'development'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Serveur minimal MindFlow Pro lancé sur http://localhost:${PORT}`);
  console.log(`📚 Health check: http://localhost:${PORT}/api/v1/health`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/v1/test`);
}); 