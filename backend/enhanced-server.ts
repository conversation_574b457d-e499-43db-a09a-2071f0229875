import express from 'express';
import cors from 'cors';

const app = express();
const PORT = process.env.PORT || 4000;

// Configuration CORS permissive pour développement
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Middleware basique
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Trust proxy for correct IP detection
app.set('trust proxy', 1);

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Base de données mock pour démonstration
const mockUsers = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Demo',
    lastName: 'User',
    role: { name: 'user', permissions: ['user:read'] },
    status: 'active',
    createdAt: new Date().toISOString()
  }
];

// Sessions mock
const mockSessions: { [token: string]: any } = {};

// Utilitaires
function generateToken() {
  return 'demo-token-' + Math.random().toString(36).substr(2, 9);
}

function authenticateToken(req: any, res: express.Response, next: express.NextFunction) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Token requis' });
  }

  const session = mockSessions[token];
  if (!session) {
    return res.status(403).json({ success: false, message: 'Token invalide' });
  }

  req.user = session.user;
  next();
}

// Routes de base
app.get('/', (req, res) => {
  res.json({
    message: 'MindFlow Pro API - Serveur Amélioré avec Auth',
    status: 'running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    endpoints: {
      health: '/api/v1/health',
      auth: { login: 'POST /api/v1/auth/login', register: 'POST /api/v1/auth/register' }
    }
  });
});

app.get('/api/v1/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    database: 'mock',
    features: { basic: true, auth: true, mock: true }
  });
});

app.get('/api/v1/test', (req, res) => {
  res.json({
    message: 'Test endpoint OK',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString()
  });
});

// Routes d'authentification
app.post('/api/v1/auth/login', (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email et mot de passe requis'
    });
  }

  const user = mockUsers.find(u => u.email === email && u.password === password);
  
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Identifiants invalides'
    });
  }

  const token = generateToken();
  mockSessions[token] = {
    user: {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      status: user.status
    }
  };

  res.json({
    success: true,
    message: 'Connexion réussie',
    data: { user: mockSessions[token].user, token, expiresIn: '24h' }
  });
});

app.get('/api/v1/auth/me', authenticateToken, (req: any, res) => {
  res.json({
    success: true,
    message: 'Informations utilisateur récupérées',
    data: { user: req.user }
  });
});

// Démarrage du serveur
const server = app.listen(PORT, () => {
  console.log(`🌟 Serveur MindFlow Pro Enhanced démarré sur http://localhost:${PORT}`);
  console.log(`📚 Health check: http://localhost:${PORT}/api/v1/health`);
  console.log(`🔐 Login demo: POST ${PORT}/api/v1/auth/login`);
  console.log(`   Credentials: <EMAIL> / password123`);
});

export default app;
