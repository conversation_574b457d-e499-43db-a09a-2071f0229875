module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  moduleFileExtensions: ['ts', 'js', 'json'],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  transformIgnorePatterns: [
    '/node_modules/(?!axios|supertest)/',
  ],
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json',
      useESM: false,
    },
  },
  coverageDirectory: 'coverage',
  collectCoverageFrom: ['src/**/*.{ts,js}', '!src/**/*.d.ts'],
  testMatch: [
    "<rootDir>/tests/**/*.test.ts",
    "<rootDir>/tests/**/*.integration.test.ts",
    "<rootDir>/tests/**/*.unit.test.ts"
  ],
  testPathIgnorePatterns: [
    "/node_modules/",
    "<rootDir>/../frontend/",
    "<rootDir>/../testing/",
    "<rootDir>/../test-results/"
  ],
}; 