import { io as Client } from 'socket.io-client';
import { createServer } from 'http';
import { Server } from 'socket.io';
import app from '../../src/app';

describe('WebSocket généralisé', () => {
  let server, io, client;
  beforeAll((done) => {
    server = createServer(app);
    io = new Server(server);
    server.listen(() => {
      const port = server.address().port;
      client = Client(`http://localhost:${port}`);
      done();
    });
  });
  afterAll(() => {
    client.close();
    io.close();
    server.close();
  });
  it('rejoint et quitte une room', (done) => {
    client.emit('joinRoom', 'test-room');
    client.emit('leaveRoom', 'test-room');
    done();
  });
  it('reçoit un broadcast', (done) => {
    client.on('system-alert', (data) => {
      expect(data.message).toBe('Alerte système');
      done();
    });
    io.emit('system-alert', { message: 'Alerte système' });
  });
  it('reçoit un événement IA', (done) => {
    client.on('ia-message', (data) => {
      expect(data.text).toBe('Réponse IA');
      done();
    });
    io.emit('ia-message', { text: 'Réponse IA' });
  });
  it('reçoit une alerte monitoring', (done) => {
    client.on('monitoring-alert', (data) => {
      expect(data.level).toBe('critical');
      done();
    });
    io.emit('monitoring-alert', { level: 'critical' });
  });
}); 