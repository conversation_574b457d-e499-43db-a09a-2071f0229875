import request from 'supertest';
import app from '../src/app';

describe('MentalHealthData API', () => {
  let token = '';
  let entryId = '';

  beforeAll(async () => {
    await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'MH',
        lastName: 'User',
      });
    const res = await request(app)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
      });
    token = res.body.accessToken;
  });

  it('creates a mental health data entry', async () => {
    const res = await request(app)
      .post('/api/v1/mental-health-data')
      .set('Authorization', `Bearer ${token}`)
      .send({
        type: 'journal',
        data: { text: 'Ceci est un test.' },
      });
    expect(res.status).toBe(201);
    expect(res.body.success).toBe(true);
    entryId = res.body.entry.id;
  });

  it('gets all mental health data entries', async () => {
    const res = await request(app)
      .get('/api/v1/mental-health-data')
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(Array.isArray(res.body.entries)).toBe(true);
  });

  it('updates a mental health data entry', async () => {
    const res = await request(app)
      .put(`/api/v1/mental-health-data/${entryId}`)
      .set('Authorization', `Bearer ${token}`)
      .send({ data: { text: 'Mise à jour.' } });
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(res.body.entry.data.text).toBe('Mise à jour.');
  });

  it('deletes a mental health data entry', async () => {
    const res = await request(app)
      .delete(`/api/v1/mental-health-data/${entryId}`)
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
  });
}); 