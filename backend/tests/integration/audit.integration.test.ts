import request from 'supertest';
import app from '../../src/app';
import { logger } from '../../src/utils/auditTrail';

describe('AuditTrail middleware', () => {
  let token: string;
  const logs: any[] = [];

  beforeAll(async () => {
    await request(app).post('/api/v1/auth/register').send({
      email: '<EMAIL>', password: 'Password123!', firstName: 'Audit', lastName: 'User',
    });
    const res = await request(app).post('/api/v1/auth/login').send({
      email: '<EMAIL>', password: 'Password123!'
    });
    token = res.body.data.tokens.accessToken;
    jest.spyOn(logger, 'info').mockImplementation((log) => logs.push(log));
  });

  it('log l’action sur une route sensible', async () => {
    await request(app)
      .post('/api/v1/journal')
      .set('Authorization', `Bearer ${token}`)
      .send({ title: 'Test', content: 'Contenu' });
    expect(logs.some(l => l.action === 'journal-create')).toBe(true);
  });
}); 