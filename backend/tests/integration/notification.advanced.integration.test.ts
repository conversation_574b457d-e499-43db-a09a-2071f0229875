import request from 'supertest';
import app from '../../src/app';

describe('Notifications avancées', () => {
  let token: string;
  let notifId: string;

  beforeAll(async () => {
    await request(app).post('/api/v1/auth/register').send({
      email: '<EMAIL>', password: 'Password123!', firstName: 'Notif', lastName: 'Adv',
    });
    const res = await request(app).post('/api/v1/auth/login').send({
      email: '<EMAIL>', password: 'Password123!'
    });
    token = res.body.data.tokens.accessToken;
  });

  it('met à jour les préférences', async () => {
    const res = await request(app)
      .put('/api/v1/notifications/preferences')
      .set('Authorization', `Bearer ${token}`)
      .send({ types: ['alert'], categories: ['system'] });
    expect(res.status).toBe(200);
    expect(res.body.preferences.types).toContain('alert');
  });

  it('ignore une notification non préférée', async () => {
    const res = await request(app)
      .post('/api/v1/notifications')
      .set('Authorization', `Bearer ${token}`)
      .send({ content: 'Non préférée', type: 'info', category: 'user' });
    expect(res.body.notification).toBeNull();
  });

  it('crée une notification préférée', async () => {
    const res = await request(app)
      .post('/api/v1/notifications')
      .set('Authorization', `Bearer ${token}`)
      .send({ content: 'Alerte système', type: 'alert', category: 'system' });
    expect(res.body.notification).toBeDefined();
    notifId = res.body.notification.id;
  });

  it('marque toutes les notifications comme lues', async () => {
    const res = await request(app)
      .patch('/api/v1/notifications/mark-all-read')
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
  });

  it('supprime toutes les notifications', async () => {
    const res = await request(app)
      .delete('/api/v1/notifications/delete-all')
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
  });
}); 