import request from 'supertest';
import app from '../../src/app';

describe('Contrôle d’accès par permissions', () => {
  let tokenAdmin: string;
  let tokenUser: string;

  beforeAll(async () => {
    // Créer un admin et un user avec des rôles différents
    await request(app).post('/api/v1/auth/register').send({
      email: '<EMAIL>', password: 'Password123!', firstName: 'Admin', lastName: 'Test',
    });
    await request(app).post('/api/v1/auth/register').send({
      email: '<EMAIL>', password: 'Password123!', firstName: 'User', lastName: 'Test',
    });
    const resAdmin = await request(app).post('/api/v1/auth/login').send({
      email: '<EMAIL>', password: 'Password123!'
    });
    const resUser = await request(app).post('/api/v1/auth/login').send({
      email: '<EMAIL>', password: 'Password123!'
    });
    tokenAdmin = resAdmin.body.data.tokens.accessToken;
    tokenUser = resUser.body.data.tokens.accessToken;
  });

  it('autorise l’accès avec la bonne permission', async () => {
    const res = await request(app)
      .get('/api/v1/users/profile')
      .set('Authorization', `Bearer ${tokenAdmin}`);
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
  });

  it('refuse l’accès sans la permission', async () => {
    const res = await request(app)
      .delete('/api/v1/users/profile')
      .set('Authorization', `Bearer ${tokenUser}`);
    expect(res.status).toBe(403);
    expect(res.body.success).toBe(false);
    expect(res.body.message).toMatch(/Permission refusée/);
  });
}); 