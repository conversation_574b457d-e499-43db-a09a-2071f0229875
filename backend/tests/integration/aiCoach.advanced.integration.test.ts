import request from 'supertest';
import app from '../../src/app';

describe('IA Coach avancé', () => {
  let token: string;
  let sessionId: string;

  beforeAll(async () => {
    await request(app).post('/api/v1/auth/register').send({
      email: '<EMAIL>', password: 'Password123!', firstName: 'IA', lastName: 'User',
    });
    const res = await request(app).post('/api/v1/auth/login').send({
      email: '<EMAIL>', password: 'Password123!'
    });
    token = res.body.data.tokens.accessToken;
  });

  it('crée une session IA', async () => {
    const res = await request(app)
      .post('/api/v1/ai-coach/sessions')
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
    expect(res.body.session).toBeDefined();
    sessionId = res.body.session.id;
  });

  it('termine la session avec feedback', async () => {
    const res = await request(app)
      .post('/api/v1/ai-coach/sessions/end')
      .set('Authorization', `Bearer ${token}`)
      .send({ sessionId, feedback: 'Très utile', rating: 5, stats: { duration: 120 } });
    expect(res.status).toBe(200);
    expect(res.body.session.feedback).toBe('Très utile');
    expect(res.body.session.rating).toBe(5);
  });

  it('récupère les stats IA', async () => {
    const res = await request(app)
      .get('/api/v1/ai-coach/sessions/stats')
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
    expect(res.body.stats.count).toBeGreaterThan(0);
  });
}); 