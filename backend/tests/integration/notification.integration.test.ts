import request from 'supertest';
import app from '../../src/app';

describe('Notifications API', () => {
  let token: string;
  let notifId: string;

  beforeAll(async () => {
    await request(app).post('/api/v1/auth/register').send({
      email: '<EMAIL>', password: 'Password123!', firstName: 'Notif', lastName: 'User',
    });
    const res = await request(app).post('/api/v1/auth/login').send({
      email: '<EMAIL>', password: 'Password123!'
    });
    token = res.body.data.tokens.accessToken;
  });

  it('crée une notification', async () => {
    const res = await request(app)
      .post('/api/v1/notifications')
      .set('Authorization', `Bearer ${token}`)
      .send({ content: 'Test notification', type: 'info' });
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    notifId = res.body.notification.id;
  });

  it('récupère les notifications', async () => {
    const res = await request(app)
      .get('/api/v1/notifications')
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
    expect(res.body.notifications.length).toBeGreaterThan(0);
  });

  it('marque une notification comme lue', async () => {
    const res = await request(app)
      .patch(`/api/v1/notifications/${notifId}/read`)
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
    expect(res.body.notification.isRead).toBe(true);
  });

  it('supprime une notification', async () => {
    const res = await request(app)
      .delete(`/api/v1/notifications/${notifId}`)
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
  });
}); 