import request from 'supertest';
import app from '../../src/app';

describe('Modules bien-être enrichis', () => {
  let token: string;
  let moduleId = 'test-module-1';

  beforeAll(async () => {
    await request(app).post('/api/v1/auth/register').send({
      email: '<EMAIL>', password: 'Password123!', firstName: 'Well', lastName: 'User',
    });
    const res = await request(app).post('/api/v1/auth/login').send({
      email: '<EMAIL>', password: 'Password123!'
    });
    token = res.body.data.tokens.accessToken;
  });

  it('met à jour la progression', async () => {
    const res = await request(app)
      .patch(`/api/v1/wellness/${moduleId}/progress`)
      .set('Authorization', `Bearer ${token}`)
      .send({ progress: 50, stats: { timeSpent: 30 } });
    expect(res.status).toBe(200);
    expect(res.body.progress.progress).toBe(50);
  });

  it('ajoute un feedback', async () => {
    const res = await request(app)
      .post(`/api/v1/wellness/${moduleId}/feedback`)
      .set('Authorization', `Bearer ${token}`)
      .send({ feedback: 'Très utile' });
    expect(res.status).toBe(200);
    expect(res.body.progress.feedback).toBe('Très utile');
  });

  it('récupère les stats du module', async () => {
    const res = await request(app)
      .get(`/api/v1/wellness/${moduleId}/stats`);
    expect(res.status).toBe(200);
    expect(res.body.stats.count).toBeGreaterThan(0);
  });
}); 