import { requirePermission } from '../../src/middleware/permissions';

describe('requirePermission middleware', () => {
  it('laisse passer si la permission est présente', () => {
    const req = { user: { permissions: ['admin'] } } as any;
    const res = {} as any;
    const next = jest.fn();
    requirePermission('admin')(req, res, next);
    expect(next).toHaveBeenCalled();
  });

  it('bloque si la permission est absente', () => {
    const req = { user: { permissions: ['user'] } } as any;
    const res = { status: jest.fn().mockReturnThis(), json: jest.fn() } as any;
    const next = jest.fn();
    requirePermission('admin')(req, res, next);
    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith({ success: false, message: 'Permission refusée' });
  });
}); 