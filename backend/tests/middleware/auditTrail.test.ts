import { auditTrail } from '../../src/middleware/auditTrail';

describe('auditTrail middleware', () => {
  it('log l’action avec les bonnes infos', () => {
    const logger = { info: jest.fn() };
    const req = { user: { id: '1' }, method: 'POST', originalUrl: '/test' } as any;
    const res = {} as any;
    const next = jest.fn();
    jest.spyOn(require('../../src/utils/auditTrail'), 'logger', 'get').mockReturnValue(logger);
    auditTrail('test-action')(req, res, next);
    expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
      action: 'test-action',
      userId: '1',
      method: 'POST',
      url: '/test',
    }));
    expect(next).toHaveBeenCalled();
  });
}); 