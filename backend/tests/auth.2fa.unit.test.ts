import speakeasy from 'speakeasy';

describe('2FA TOTP', () => {
  it('génère et valide un code TOTP', () => {
    const secret = speakeasy.generateSecret();
    const token = speakeasy.totp({ secret: secret.base32, encoding: 'base32' });
    const verified = speakeasy.totp.verify({
      secret: secret.base32,
      encoding: 'base32',
      token,
    });
    expect(verified).toBe(true);
  });

  it('rejette un code TOTP invalide', () => {
    const secret = speakeasy.generateSecret();
    const token = '000000';
    const verified = speakeasy.totp.verify({
      secret: secret.base32,
      encoding: 'base32',
      token,
    });
    expect(verified).toBe(false);
  });
}); 