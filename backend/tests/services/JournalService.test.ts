import { JournalService } from '../../src/services/JournalService';

describe('JournalService', () => {
  const service = new JournalService();

  it('doit être instanciable', () => {
    expect(service).toBeInstanceOf(JournalService);
  });

  // Décommenter si les méthodes existent :
  // it('doit avoir une méthode findAll', () => {
  //   expect(typeof service.findAll).toBe('function');
  // });
  // it('create doit retourner une promesse', () => {
  //   const result = service.create({ userId: '1', title: 'Test', content: 'Contenu' });
  //   expect(result).toBeInstanceOf(Promise);
  // });
}); 