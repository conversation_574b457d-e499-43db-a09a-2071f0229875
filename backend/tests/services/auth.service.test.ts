import { AuthService } from '../../src/services/AuthService';

describe('AuthService', () => {
  const authService = new AuthService();
  const password = 'Password123!';
  let hash: string;

  it('hashPassword doit hasher le mot de passe', async () => {
    hash = await authService.hashPassword(password);
    expect(hash).toBeDefined();
    expect(hash).not.toBe(password);
  });

  it('comparePassword doit valider le mot de passe', async () => {
    const isValid = await authService.comparePassword(password, hash);
    expect(isValid).toBe(true);
  });

  // Pour register/login, il faudrait mocker la DB, ici on vérifie juste la structure
  it('register doit échouer si email déjà utilisé (mock)', async () => {
    // À compléter avec un mock de la DB
    expect(true).toBe(true);
  });
}); 