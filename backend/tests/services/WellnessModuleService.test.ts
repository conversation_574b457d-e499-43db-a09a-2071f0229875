import { WellnessModuleService } from '../../src/services/WellnessModuleService';

// <PERSON><PERSON> de <PERSON> couche DB
const mockDb = {
  create: jest.fn(),
  find: jest.fn(),
  delete: jest.fn(),
};

describe('WellnessModuleService', () => {
  let service: WellnessModuleService;

  beforeEach(() => {
    service = new WellnessModuleService();
    // @ts-ignore
    service.db = mockDb;
  });

  it('doit créer un module bien-être', async () => {
    mockDb.create.mockResolvedValue({ id: '1', title: 'Module' });
    const result = await service.create({ title: 'Module' });
    expect(result).toEqual({ id: '1', title: 'Module' });
    expect(mockDb.create).toHaveBeenCalledWith({ title: 'Module' });
  });

  it('doit récupérer les modules', async () => {
    mockDb.find.mockResolvedValue([{ id: '1', title: 'Module' }]);
    const result = await service.findAll();
    expect(result).toEqual([{ id: '1', title: 'Module' }]);
    expect(mockDb.find).toHaveBeenCalled();
  });

  it('doit supprimer un module', async () => {
    mockDb.delete.mockResolvedValue(true);
    const result = await service.delete('1');
    expect(result).toBe(true);
    expect(mockDb.delete).toHaveBeenCalledWith('1');
  });
}); 