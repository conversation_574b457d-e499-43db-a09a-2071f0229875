import { ProfessionalService } from '../../src/services/ProfessionalService';

// <PERSON><PERSON> de <PERSON>e DB
const mockDb = {
  create: jest.fn(),
  find: jest.fn(),
  delete: jest.fn(),
};

describe('ProfessionalService', () => {
  let service: ProfessionalService;

  beforeEach(() => {
    service = new ProfessionalService();
    // @ts-ignore
    service.db = mockDb;
  });

  it('doit créer un professionnel', async () => {
    mockDb.create.mockResolvedValue({ id: '1', name: 'Pro' });
    const result = await service.create({ name: 'Pro' });
    expect(result).toEqual({ id: '1', name: 'Pro' });
    expect(mockDb.create).toHaveBeenCalledWith({ name: 'Pro' });
  });

  it('doit récupérer les professionnels', async () => {
    mockDb.find.mockResolvedValue([{ id: '1', name: 'Pro' }]);
    const result = await service.findAll();
    expect(result).toEqual([{ id: '1', name: 'Pro' }]);
    expect(mockDb.find).toHaveBeenCalled();
  });

  it('doit supprimer un professionnel', async () => {
    mockDb.delete.mockResolvedValue(true);
    const result = await service.delete('1');
    expect(result).toBe(true);
    expect(mockDb.delete).toHaveBeenCalledWith('1');
  });
}); 