import request from 'supertest';
import app from '../src/app';

describe('2FA Auth API', () => {
  let token = '';

  beforeAll(async () => {
    await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: '2FA',
        lastName: 'User',
      });
    const res = await request(app)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
      });
    token = res.body.accessToken;
  });

  it('active le 2FA et retourne un QR code', async () => {
    const res = await request(app)
      .post('/api/v1/auth/enable-2fa')
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(res.body.qr).toBeDefined();
    expect(res.body.otpauthUrl).toBeDefined();
  });

  it('désactive le 2FA', async () => {
    const res = await request(app)
      .post('/api/v1/auth/disable-2fa')
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
  });
}); 