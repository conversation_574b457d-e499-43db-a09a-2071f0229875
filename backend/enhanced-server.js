const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 4000;

// Configuration CORS permissive pour développement
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Middleware basique
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Trust proxy for correct IP detection
app.set('trust proxy', 1);

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Base de données mock pour démonstration
const mockUsers = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Demo',
    lastName: 'User',
    role: { name: 'user', permissions: ['user:read'] },
    status: 'active',
    createdAt: new Date().toISOString()
  },
  {
    id: 'admin-1',
    email: '<EMAIL>',
    password: 'admin123',
    firstName: 'Admin',
    lastName: 'User',
    role: { name: 'admin', permissions: ['user:read', 'user:write', 'admin:all'] },
    status: 'active',
    createdAt: new Date().toISOString()
  }
];

// Sessions mock
const mockSessions = {};

// Utilitaires
function generateToken() {
  return 'demo-token-' + Math.random().toString(36).substr(2, 9);
}

function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Token requis' });
  }

  const session = mockSessions[token];
  if (!session) {
    return res.status(403).json({ success: false, message: 'Token invalide' });
  }

  req.user = session.user;
  next();
}

// Routes de base
app.get('/', (req, res) => {
  res.json({
    message: 'MindFlow Pro API - Serveur Amélioré avec Auth',
    status: 'running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    features: {
      authentication: true,
      userManagement: true,
      mockData: true
    },
    endpoints: {
      health: '/api/v1/health',
      test: '/api/v1/test',
      auth: {
        login: 'POST /api/v1/auth/login',
        register: 'POST /api/v1/auth/register',
        me: 'GET /api/v1/auth/me',
        logout: 'POST /api/v1/auth/logout'
      }
    }
  });
});

app.get('/api/v1/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    database: 'mock',
    features: {
      basic: true,
      auth: true,
      mock: true
    }
  });
});

app.get('/api/v1/test', (req, res) => {
  res.json({
    message: 'Test endpoint OK',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString(),
    requestInfo: {
      method: req.method,
      path: req.path,
      ip: req.ip,
      userAgent: req.headers['user-agent']
    }
  });
});

// Routes d'authentification
app.post('/api/v1/auth/login', (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email et mot de passe requis'
    });
  }

  const user = mockUsers.find(u => u.email === email && u.password === password);
  
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Identifiants invalides'
    });
  }

  const token = generateToken();
  const refreshToken = generateToken();

  // Stocker la session
  mockSessions[token] = {
    user: {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      status: user.status
    },
    createdAt: new Date().toISOString()
  };

  res.json({
    success: true,
    message: 'Connexion réussie',
    data: {
      user: mockSessions[token].user,
      token,
      refreshToken,
      expiresIn: '24h'
    }
  });
});

app.post('/api/v1/auth/register', (req, res) => {
  const { email, password, firstName, lastName } = req.body;

  if (!email || !password || !firstName || !lastName) {
    return res.status(400).json({
      success: false,
      message: 'Tous les champs sont requis'
    });
  }

  // Vérifier si l'utilisateur existe déjà
  const existingUser = mockUsers.find(u => u.email === email);
  if (existingUser) {
    return res.status(409).json({
      success: false,
      message: 'Un utilisateur avec cet email existe déjà'
    });
  }

  // Créer nouvel utilisateur
  const newUser = {
    id: 'user-' + Math.random().toString(36).substr(2, 9),
    email,
    password,
    firstName,
    lastName,
    role: { name: 'user', permissions: ['user:read'] },
    status: 'active',
    createdAt: new Date().toISOString()
  };

  mockUsers.push(newUser);

  res.status(201).json({
    success: true,
    message: 'Utilisateur créé avec succès',
    data: {
      user: {
        id: newUser.id,
        email: newUser.email,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        role: newUser.role,
        status: newUser.status
      }
    }
  });
});

app.get('/api/v1/auth/me', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Informations utilisateur récupérées',
    data: {
      user: req.user
    }
  });
});

app.post('/api/v1/auth/logout', authenticateToken, (req, res) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (token && mockSessions[token]) {
    delete mockSessions[token];
  }
  
  res.json({
    success: true,
    message: 'Déconnexion réussie'
  });
});

// Status endpoint
app.get('/api/v1/status', (req, res) => {
  res.json({
    server: 'MindFlow Pro Enhanced Server',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    activeSessions: Object.keys(mockSessions).length,
    registeredUsers: mockUsers.length,
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString()
  });
});

// Error handler global
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint non trouvé',
    path: req.path,
    method: req.method,
    availableEndpoints: [
      'GET /',
      'GET /api/v1/health',
      'GET /api/v1/test',
      'GET /api/v1/status',
      'POST /api/v1/auth/login',
      'POST /api/v1/auth/register',
      'GET /api/v1/auth/me',
      'POST /api/v1/auth/logout'
    ],
    timestamp: new Date().toISOString()
  });
});

// Démarrage du serveur
const server = app.listen(PORT, () => {
  console.log(`🌟 Serveur MindFlow Pro Enhanced démarré sur http://localhost:${PORT}`);
  console.log(`📚 Health check: http://localhost:${PORT}/api/v1/health`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/v1/test`);
  console.log(`📊 Status: http://localhost:${PORT}/api/v1/status`);
  console.log(`🔐 Login demo: POST ${PORT}/api/v1/auth/login`);
  console.log(`   Credentials: <EMAIL> / password123`);
  console.log(`   Credentials: <EMAIL> / admin123`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Arrêt gracieux du serveur...');
  server.close(() => {
    console.log('✅ Serveur arrêté');
    process.exit(0);
  });
});

module.exports = app;
