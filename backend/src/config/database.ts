import { DataSource } from 'typeorm';
import dotenv from 'dotenv';
import * as bcrypt from 'bcrypt';

import { User } from '../models/User';
import { Role } from '../models/Role';
import { Permission } from '../models/Permission';
import { RolePermission } from '../models/RolePermission';
import { JournalEntry } from '../models/JournalEntry';
import { AICoachInteraction } from '../models/AICoachInteraction';
import { WellnessModule } from '../models/WellnessModule';
import { UserModuleProgress } from '../models/UserModuleProgress';
import { TherapeuticPath } from '../models/TherapeuticPath';
import { PathModuleLink } from '../models/PathModuleLink';
import { UserPathEnrollment } from '../models/UserPathEnrollment';
import { ProfessionalProfile } from '../models/ProfessionalProfile';
import { Appointment } from '../models/Appointment';
import { Notification } from '../models/Notification';
import { BiometricDataEntry } from '../models/BiometricDataEntry';
import { Payment } from '../models/Payment';

dotenv.config();

const isDevelopment = process.env.NODE_ENV === 'development';
const databaseType = process.env.DATABASE_TYPE || (isDevelopment ? 'sqlite' : 'postgres');

const commonEntities = [
    User,
    Role,
    Permission,
    RolePermission,
    JournalEntry,
    AICoachInteraction,
    WellnessModule,
    UserModuleProgress,
    TherapeuticPath,
    PathModuleLink,
    UserPathEnrollment,
    ProfessionalProfile,
    Appointment,
    Notification,
    BiometricDataEntry,
    Payment,
];

let AppDataSource: DataSource;

if (databaseType === 'sqlite') {
  AppDataSource = new DataSource({
    type: 'sqlite',
    database: process.env.DATABASE_PATH || './data/database.sqlite',
    entities: commonEntities,
  synchronize: true,
  logging: false,
});
  console.log('🟢 [DB] Utilisation de SQLite (développement local)');
} else {
  AppDataSource = new DataSource({
    type: 'postgres',
    host: process.env.POSTGRES_HOST,
    port: Number(process.env.POSTGRES_PORT) || 5432,
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
    entities: commonEntities,
    synchronize: false, // migrations recommandées en prod
    logging: false,
  });
  console.log('🟢 [DB] Utilisation de PostgreSQL (production)');
}

export { AppDataSource };

export const initializeDatabase = async (): Promise<void> => {
  let retries = 5;
  while (retries) {
  try {
    await AppDataSource.initialize();
      console.log('✅ Connexion à la base de données établie avec succès');
      return;
  } catch (error) {
      console.error('❌ Erreur lors de la connexion à la base de données:', error);
      retries -= 1;
      if (!retries) throw error;
      console.log(`Nouvelle tentative dans 3s... (${retries} essais restants)`);
      await new Promise(res => setTimeout(res, 3000));
    }
  }
};

const seedInitialData = async (): Promise<void> => {
  try {
    const roleRepository = AppDataSource.getRepository(Role);
    const permissionRepository = AppDataSource.getRepository(Permission);
    const userRepository = AppDataSource.getRepository(User);

    // Create permissions if they don't exist
    const permissions = [
      // User Management
      { name: 'read_users', description: 'Read user data', category: 'user_management' },
      { name: 'write_users', description: 'Create and update users', category: 'user_management' },
      { name: 'delete_users', description: 'Delete users', category: 'user_management' },
      { name: 'manage_roles', description: 'Manage roles and permissions', category: 'user_management' },
      { name: 'access_admin', description: 'Access admin panel', category: 'admin' },

      // Journal Management
      { name: 'read_own_journal', description: 'Read own journal entries', category: 'journal' },
      { name: 'write_own_journal', description: 'Create and update own journal entries', category: 'journal' },
      { name: 'delete_own_journal', description: 'Delete own journal entries', category: 'journal' },
      { name: 'read_all_journals', description: 'Read all journal entries (admin)', category: 'journal' },

      // AI Coach
      { name: 'use_ai_coach', description: 'Interact with AI coach', category: 'ai_coach' },
      { name: 'view_ai_interactions', description: 'View AI interaction history', category: 'ai_coach' },
      { name: 'manage_ai_coach', description: 'Manage AI coach settings (admin)', category: 'ai_coach' },

      // Wellness Modules
      { name: 'access_wellness_modules', description: 'Access wellness modules', category: 'wellness' },
      { name: 'track_module_progress', description: 'Track module progress', category: 'wellness' },
      { name: 'manage_wellness_modules', description: 'Manage wellness modules (admin)', category: 'wellness' },

      // Therapeutic Paths
      { name: 'enroll_therapeutic_paths', description: 'Enroll in therapeutic paths', category: 'therapeutic' },
      { name: 'track_path_progress', description: 'Track therapeutic path progress', category: 'therapeutic' },
      { name: 'manage_therapeutic_paths', description: 'Manage therapeutic paths (admin)', category: 'therapeutic' },

      // Professional Network
      { name: 'view_professionals', description: 'View professional directory', category: 'professional' },
      { name: 'book_appointments', description: 'Book appointments with professionals', category: 'professional' },
      { name: 'manage_professional_profile', description: 'Manage professional profile', category: 'professional' },
      { name: 'verify_professionals', description: 'Verify professional credentials (admin)', category: 'professional' },

      // Appointments
      { name: 'manage_own_appointments', description: 'Manage own appointments', category: 'appointments' },
      { name: 'manage_client_appointments', description: 'Manage client appointments (professional)', category: 'appointments' },
      { name: 'view_all_appointments', description: 'View all appointments (admin)', category: 'appointments' },

      // Biometric Data
      { name: 'track_biometric_data', description: 'Track biometric data', category: 'biometric' },
      { name: 'share_biometric_data', description: 'Share biometric data with professionals', category: 'biometric' },
      { name: 'view_client_biometric_data', description: 'View client biometric data (professional)', category: 'biometric' },

      // Notifications
      { name: 'receive_notifications', description: 'Receive notifications', category: 'notifications' },
      { name: 'manage_notification_settings', description: 'Manage notification preferences', category: 'notifications' },
      { name: 'send_system_notifications', description: 'Send system notifications (admin)', category: 'notifications' },
    ];

    for (const permData of permissions) {
      const existingPerm = await permissionRepository.findOne({
        where: { name: permData.name }
      });
      if (!existingPerm) {
        const permission = permissionRepository.create(permData);
        await permissionRepository.save(permission);
      }
    }

    // Create roles if they don't exist
    const roles = [
      { name: 'root', description: 'Super administrator with all permissions' },
      { name: 'admin', description: 'Administrator with most permissions' },
      { name: 'professional', description: 'Mental health professional with client management permissions' },
      { name: 'user', description: 'Regular user with basic permissions' },
    ];

    for (const roleData of roles) {
      const existingRole = await roleRepository.findOne({
        where: { name: roleData.name }
      });
      if (!existingRole) {
        const role = roleRepository.create(roleData);
        await roleRepository.save(role);
      }
    }

    // Create root user if it doesn't exist
    const rootUser = await userRepository.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!rootUser) {
      const rootRole = await roleRepository.findOne({ where: { name: 'root' } });

      if (rootRole) {
        const hashedPassword = await bcrypt.hash('Password123', 12);
        const user = userRepository.create({
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: 'Root',
          lastName: 'Admin',
          role: rootRole,
          isEmailVerified: true,
        });
        await userRepository.save(user);
        console.log('✅ Root user created: <EMAIL> / Password123');
      }
    }

    // Create sample wellness modules
    await createSampleWellnessModules();

    // Create sample professional profiles
    await createSampleProfessionalProfiles();

    console.log('✅ Initial data seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding initial data:', error);
  }
};

const createSampleWellnessModules = async (): Promise<void> => {
  const wellnessModuleRepository = AppDataSource.getRepository(WellnessModule);
  const { ModuleType, DifficultyLevel, ModuleStatus } = require('../models/WellnessModule');

  const sampleModules = [
    {
      title: 'Introduction to Mindfulness',
      description: 'Learn the basics of mindfulness meditation and how to incorporate it into your daily life.',
      moduleType: ModuleType.MINDFULNESS,
      difficultyLevel: DifficultyLevel.BEGINNER,
      status: ModuleStatus.PUBLISHED,
      estimatedDurationMinutes: 30,
      content: {
        sections: [
          {
            id: 'intro-1',
            title: 'What is Mindfulness?',
            content: 'Mindfulness is the practice of being fully present and engaged in the current moment...',
            type: 'text' as const,
            duration: 10,
          },
          {
            id: 'intro-2',
            title: 'Basic Breathing Exercise',
            content: 'Let\'s start with a simple breathing exercise to center yourself...',
            type: 'exercise' as const,
            duration: 15,
          },
          {
            id: 'intro-3',
            title: 'Daily Mindfulness Tips',
            content: 'Here are some practical ways to bring mindfulness into your everyday activities...',
            type: 'text' as const,
            duration: 5,
          },
        ],
      },
      learningObjectives: [
        'Understand the concept of mindfulness',
        'Practice basic breathing techniques',
        'Learn to apply mindfulness in daily life',
      ],
      tags: ['mindfulness', 'meditation', 'breathing', 'beginner'],
      isActive: true,
      isFeatured: true,
    },
    {
      title: 'Managing Anxiety',
      description: 'Practical strategies and techniques for managing anxiety and reducing stress.',
      moduleType: ModuleType.ANXIETY_RELIEF,
      difficultyLevel: DifficultyLevel.INTERMEDIATE,
      status: ModuleStatus.PUBLISHED,
      estimatedDurationMinutes: 45,
      content: {
        sections: [
          {
            id: 'anxiety-1',
            title: 'Understanding Anxiety',
            content: 'Anxiety is a normal human emotion, but when it becomes overwhelming...',
            type: 'text' as const,
            duration: 15,
          },
          {
            id: 'anxiety-2',
            title: 'Grounding Techniques',
            content: 'The 5-4-3-2-1 technique: Name 5 things you can see, 4 things you can touch...',
            type: 'exercise' as const,
            duration: 20,
          },
          {
            id: 'anxiety-3',
            title: 'Building Your Anxiety Toolkit',
            content: 'Create a personalized set of tools and strategies for managing anxiety...',
            type: 'text' as const,
            duration: 10,
          },
        ],
      },
      learningObjectives: [
        'Recognize anxiety symptoms and triggers',
        'Practice grounding and calming techniques',
        'Develop a personal anxiety management plan',
      ],
      tags: ['anxiety', 'stress', 'coping', 'grounding'],
      isActive: true,
      isFeatured: true,
    },
    {
      title: 'Sleep Hygiene Fundamentals',
      description: 'Improve your sleep quality with evidence-based sleep hygiene practices.',
      moduleType: ModuleType.SLEEP_HYGIENE,
      difficultyLevel: DifficultyLevel.BEGINNER,
      status: ModuleStatus.PUBLISHED,
      estimatedDurationMinutes: 25,
      content: {
        sections: [
          {
            id: 'sleep-1',
            title: 'The Science of Sleep',
            content: 'Understanding sleep cycles and why quality sleep is crucial for mental health...',
            type: 'text' as const,
            duration: 10,
          },
          {
            id: 'sleep-2',
            title: 'Creating a Sleep-Friendly Environment',
            content: 'Tips for optimizing your bedroom for better sleep...',
            type: 'text' as const,
            duration: 10,
          },
          {
            id: 'sleep-3',
            title: 'Bedtime Routine Exercise',
            content: 'Design your personalized bedtime routine for better sleep...',
            type: 'exercise' as const,
            duration: 5,
          },
        ],
      },
      learningObjectives: [
        'Understand the importance of sleep for mental health',
        'Learn sleep hygiene best practices',
        'Create a personalized bedtime routine',
      ],
      tags: ['sleep', 'hygiene', 'routine', 'health'],
      isActive: true,
      isFeatured: false,
    },
  ];

  for (const moduleData of sampleModules) {
    const existingModule = await wellnessModuleRepository.findOne({
      where: { title: moduleData.title },
    });

    if (!existingModule) {
      const module = wellnessModuleRepository.create(moduleData);
      await wellnessModuleRepository.save(module);
      console.log(`✅ Created wellness module: ${moduleData.title}`);
    }
  }
};

const createSampleProfessionalProfiles = async (): Promise<void> => {
  const userRepository = AppDataSource.getRepository(User);
  const professionalRepository = AppDataSource.getRepository(ProfessionalProfile);
  const roleRepository = AppDataSource.getRepository(Role);
  const { ProfessionalType, VerificationStatus } = require('../models/ProfessionalProfile');

  // Get or create professional role
  let professionalRole = await roleRepository.findOne({ where: { name: 'professional' } });
  if (!professionalRole) {
    professionalRole = roleRepository.create({
      name: 'professional',
      description: 'Mental health professional',
    });
    await roleRepository.save(professionalRole);
    console.log('✅ Created professional role');
  }

  const sampleProfessionals = [
    {
      user: {
        email: '<EMAIL>',
        firstName: 'Dr. Sarah',
        lastName: 'Johnson',
        password: 'Professional123!',
        roleId: professionalRole.id,
      },
      profile: {
        professionalType: ProfessionalType.PSYCHOLOGIST,
        licenseNumber: 'PSY12345',
        licenseExpiryDate: new Date('2025-12-31'),
        licensingBoard: 'California Board of Psychology',
        licensingState: 'California',
        verificationStatus: VerificationStatus.VERIFIED,
        specializations: ['Anxiety Disorders', 'Depression', 'Cognitive Behavioral Therapy'],
        treatmentApproaches: ['CBT', 'Mindfulness-Based Therapy', 'Solution-Focused Therapy'],
        languagesSpoken: ['English', 'Spanish'],
        yearsOfExperience: 8,
        professionalBio: 'Dr. Sarah Johnson is a licensed psychologist with over 8 years of experience specializing in anxiety and depression treatment. She uses evidence-based approaches including CBT and mindfulness techniques to help clients achieve their mental health goals.',
        education: [
          {
            degree: 'Ph.D. in Clinical Psychology',
            institution: 'Stanford University',
            year: 2015,
            field: 'Clinical Psychology',
          },
          {
            degree: 'M.A. in Psychology',
            institution: 'UCLA',
            year: 2012,
            field: 'Psychology',
          },
        ],
        certifications: [
          {
            name: 'Licensed Psychologist',
            issuingOrganization: 'California Board of Psychology',
            issueDate: new Date('2016-01-15'),
            expiryDate: new Date('2025-12-31'),
            credentialId: 'PSY12345',
          },
        ],
        availability: {
          timezone: 'America/Los_Angeles',
          schedule: {
            monday: [{ startTime: '09:00', endTime: '17:00' }],
            tuesday: [{ startTime: '09:00', endTime: '17:00' }],
            wednesday: [{ startTime: '09:00', endTime: '17:00' }],
            thursday: [{ startTime: '09:00', endTime: '17:00' }],
            friday: [{ startTime: '09:00', endTime: '15:00' }],
          },
          exceptions: [],
        },
        consultationTypes: [
          { type: 'video' as const, duration: 50, price: 150, currency: 'USD' },
          { type: 'phone' as const, duration: 50, price: 130, currency: 'USD' },
          { type: 'in_person' as const, duration: 50, price: 180, currency: 'USD' },
        ],
        hourlyRate: 150,
        currency: 'USD',
        acceptingNewClients: true,
        maxClientsPerWeek: 25,
        insuranceAccepted: ['Blue Cross Blue Shield', 'Aetna', 'Cigna', 'UnitedHealth'],
        averageRating: 4.8,
        totalReviews: 47,
        totalAppointments: 156,
      },
    },
    {
      user: {
        email: '<EMAIL>',
        firstName: 'Dr. Michael',
        lastName: 'Chen',
        password: 'Professional123!',
        roleId: professionalRole.id,
      },
      profile: {
        professionalType: ProfessionalType.PSYCHIATRIST,
        licenseNumber: 'MD67890',
        licenseExpiryDate: new Date('2026-06-30'),
        licensingBoard: 'Medical Board of California',
        licensingState: 'California',
        verificationStatus: VerificationStatus.VERIFIED,
        specializations: ['ADHD', 'Bipolar Disorder', 'Medication Management', 'Adult Psychiatry'],
        treatmentApproaches: ['Medication Management', 'Psychopharmacology', 'Integrative Psychiatry'],
        languagesSpoken: ['English', 'Mandarin', 'Cantonese'],
        yearsOfExperience: 12,
        professionalBio: 'Dr. Michael Chen is a board-certified psychiatrist with 12 years of experience in adult psychiatry and medication management. He specializes in treating ADHD, bipolar disorder, and complex psychiatric conditions using evidence-based medication strategies.',
        education: [
          {
            degree: 'M.D.',
            institution: 'Harvard Medical School',
            year: 2011,
            field: 'Medicine',
          },
          {
            degree: 'Residency in Psychiatry',
            institution: 'Massachusetts General Hospital',
            year: 2015,
            field: 'Psychiatry',
          },
        ],
        certifications: [
          {
            name: 'Board Certified Psychiatrist',
            issuingOrganization: 'American Board of Psychiatry and Neurology',
            issueDate: new Date('2015-07-01'),
            expiryDate: new Date('2025-07-01'),
            credentialId: 'ABPN12345',
          },
        ],
        availability: {
          timezone: 'America/Los_Angeles',
          schedule: {
            monday: [{ startTime: '08:00', endTime: '16:00' }],
            tuesday: [{ startTime: '08:00', endTime: '16:00' }],
            wednesday: [{ startTime: '08:00', endTime: '16:00' }],
            thursday: [{ startTime: '08:00', endTime: '16:00' }],
            friday: [{ startTime: '08:00', endTime: '14:00' }],
          },
          exceptions: [],
        },
        consultationTypes: [
          { type: 'video' as const, duration: 30, price: 200, currency: 'USD' },
          { type: 'phone' as const, duration: 30, price: 180, currency: 'USD' },
          { type: 'in_person' as const, duration: 45, price: 250, currency: 'USD' },
        ],
        hourlyRate: 200,
        currency: 'USD',
        acceptingNewClients: true,
        maxClientsPerWeek: 20,
        insuranceAccepted: ['Blue Cross Blue Shield', 'Kaiser Permanente', 'Anthem'],
        averageRating: 4.9,
        totalReviews: 63,
        totalAppointments: 234,
      },
    },
    {
      user: {
        email: '<EMAIL>',
        firstName: 'Lisa',
        lastName: 'Rodriguez',
        password: 'Professional123!',
        roleId: professionalRole.id,
      },
      profile: {
        professionalType: ProfessionalType.THERAPIST,
        licenseNumber: 'LMFT54321',
        licenseExpiryDate: new Date('2025-09-30'),
        licensingBoard: 'California Board of Behavioral Sciences',
        licensingState: 'California',
        verificationStatus: VerificationStatus.VERIFIED,
        specializations: ['Family Therapy', 'Couples Counseling', 'Relationship Issues', 'Communication Skills'],
        treatmentApproaches: ['Emotionally Focused Therapy', 'Gottman Method', 'Family Systems Therapy'],
        languagesSpoken: ['English', 'Spanish'],
        yearsOfExperience: 6,
        professionalBio: 'Lisa Rodriguez is a Licensed Marriage and Family Therapist with 6 years of experience helping couples and families improve their relationships. She specializes in communication skills, conflict resolution, and building stronger emotional connections.',
        education: [
          {
            degree: 'M.A. in Marriage and Family Therapy',
            institution: 'Alliant International University',
            year: 2017,
            field: 'Marriage and Family Therapy',
          },
        ],
        certifications: [
          {
            name: 'Licensed Marriage and Family Therapist',
            issuingOrganization: 'California Board of Behavioral Sciences',
            issueDate: new Date('2018-03-15'),
            expiryDate: new Date('2025-09-30'),
            credentialId: 'LMFT54321',
          },
        ],
        availability: {
          timezone: 'America/Los_Angeles',
          schedule: {
            monday: [{ startTime: '10:00', endTime: '18:00' }],
            tuesday: [{ startTime: '10:00', endTime: '18:00' }],
            wednesday: [{ startTime: '10:00', endTime: '18:00' }],
            thursday: [{ startTime: '10:00', endTime: '18:00' }],
            saturday: [{ startTime: '09:00', endTime: '15:00' }],
          },
          exceptions: [],
        },
        consultationTypes: [
          { type: 'video' as const, duration: 50, price: 120, currency: 'USD' },
          { type: 'in_person' as const, duration: 50, price: 140, currency: 'USD' },
        ],
        hourlyRate: 120,
        currency: 'USD',
        acceptingNewClients: true,
        maxClientsPerWeek: 30,
        insuranceAccepted: ['Blue Cross Blue Shield', 'Aetna', 'Psychology Today'],
        averageRating: 4.7,
        totalReviews: 29,
        totalAppointments: 89,
      },
    },
  ];

  for (const professionalData of sampleProfessionals) {
    const existingUser = await userRepository.findOne({
      where: { email: professionalData.user.email },
    });

    if (!existingUser) {
      // Create user
      const hashedPassword = await bcrypt.hash(professionalData.user.password, 10);
      const user = userRepository.create({
        ...professionalData.user,
        password: hashedPassword,
        isEmailVerified: true,
      });
      const savedUser = await userRepository.save(user);

      // Create professional profile
      const profile = professionalRepository.create({
        ...professionalData.profile,
        userId: savedUser.id,
        verifiedAt: new Date(),
        verifiedBy: 'system',
      });
      await professionalRepository.save(profile);

      console.log(`✅ Created professional: ${professionalData.user.email}`);
    }
  }
};
