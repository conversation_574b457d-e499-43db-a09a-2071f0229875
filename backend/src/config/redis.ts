import { createClient } from 'redis';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Client Redis pour cache, sessions, rate limiting, etc.
 */
const redisUrl = process.env.REDIS_URL || `redis://${process.env.REDIS_HOST || 'localhost'}:${process.env.REDIS_PORT || '6379'}`;

export const redisClient = createClient({
  url: redisUrl,
});

redisClient.on('error', (err) => {
  console.error('Erreur Redis (non bloquant en dev):', err);
});

// Make Redis optional in development
if (process.env.NODE_ENV === 'development') {
  redisClient.on('error', () => {
    // Silence Redis errors in development
  });
} 