import { AppDataSource } from '../config/database';
import { MentalHealthData, DataType } from '../models/MentalHealthData';

const dataRepo = AppDataSource.getRepository(MentalHealthData);

export class MentalHealthDataService {
  /** Créer une entrée de santé mentale */
  static async create(userId: string, type: DataType, data: any) {
    const entry = dataRepo.create({ userId, type, data });
    await dataRepo.save(entry);
    return entry;
  }

  /** Récupérer toutes les entrées d'un utilisateur (filtrage possible) */
  static async getAll(userId: string, type?: DataType) {
    const where: any = { userId };
    if (type) where.type = type;
    return dataRepo.find({ where, order: { createdAt: 'DESC' } });
  }

  /** Récupérer une entrée par ID */
  static async getById(userId: string, id: string) {
    const entry = await dataRepo.findOneBy({ id, userId });
    if (!entry) throw new Error('Entrée non trouvée');
    return entry;
  }

  /** Mettre à jour une entrée */
  static async update(userId: string, id: string, data: any) {
    await dataRepo.update({ id, userId }, { data });
    return this.getById(userId, id);
  }

  /** Supprimer une entrée */
  static async remove(userId: string, id: string) {
    await dataRepo.delete({ id, userId });
  }
} 