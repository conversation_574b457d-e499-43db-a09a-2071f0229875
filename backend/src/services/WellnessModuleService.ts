import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { WellnessModule, ModuleType, DifficultyLevel, ModuleStatus } from '../models/WellnessModule';
import { UserModuleProgress, ProgressStatus } from '../models/UserModuleProgress';
import { User } from '../models/User';
import { ValidationError, NotFoundError } from '../utils/errors';
import { websocketServiceInstance } from './websocketInstance';

export interface CreateModuleDto {
  title: string;
  description: string;
  moduleType: ModuleType;
  difficultyLevel?: DifficultyLevel;
  estimatedDurationMinutes: number;
  content: {
    sections: Array<{
      id: string;
      title: string;
      content: string;
      type: 'text' | 'video' | 'audio' | 'exercise' | 'quiz';
      duration?: number;
      resources?: string[];
    }>;
  };
  learningObjectives?: string[];
  prerequisites?: string[];
  tags?: string[];
  resources?: Array<{
    title: string;
    url: string;
    type: 'article' | 'video' | 'podcast' | 'book' | 'app';
  }>;
  thumbnailUrl?: string;
  videoUrl?: string;
  audioUrl?: string;
  authorNotes?: string;
  requiresSupervision?: boolean;
}

export interface ModuleFilters {
  moduleType?: ModuleType;
  difficultyLevel?: DifficultyLevel;
  tags?: string[];
  isFeatured?: boolean;
  requiresSupervision?: boolean;
}

export class WellnessModuleService {
  private moduleRepository: Repository<WellnessModule>;
  private progressRepository: Repository<UserModuleProgress>;
  private userRepository: Repository<User>;

  constructor() {
    this.moduleRepository = AppDataSource.getRepository(WellnessModule);
    this.progressRepository = AppDataSource.getRepository(UserModuleProgress);
    this.userRepository = AppDataSource.getRepository(User);
  }

  async createModule(data: CreateModuleDto): Promise<WellnessModule> {
    // Validate input
    if (!data.title || data.title.trim().length === 0) {
      throw new ValidationError('Title is required');
    }

    if (!data.description || data.description.trim().length === 0) {
      throw new ValidationError('Description is required');
    }

    if (!data.content || !data.content.sections || data.content.sections.length === 0) {
      throw new ValidationError('Module must have at least one section');
    }

    if (data.estimatedDurationMinutes <= 0) {
      throw new ValidationError('Estimated duration must be greater than 0');
    }

    const module = this.moduleRepository.create({
      title: data.title.trim(),
      description: data.description.trim(),
      moduleType: data.moduleType || ModuleType.MINDFULNESS,
      difficultyLevel: data.difficultyLevel || DifficultyLevel.BEGINNER,
      estimatedDurationMinutes: data.estimatedDurationMinutes,
      content: data.content,
      learningObjectives: data.learningObjectives || [],
      prerequisites: data.prerequisites || [],
      tags: data.tags || [],
      resources: data.resources || [],
      thumbnailUrl: data.thumbnailUrl,
      videoUrl: data.videoUrl,
      audioUrl: data.audioUrl,
      authorNotes: data.authorNotes?.trim(),
      requiresSupervision: data.requiresSupervision || false,
      status: ModuleStatus.DRAFT,
      isActive: true,
      isFeatured: false,
    });

    return await this.moduleRepository.save(module);
  }

  async getModules(
    filters: ModuleFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{ modules: WellnessModule[]; total: number; page: number; totalPages: number }> {
    const queryBuilder = this.moduleRepository
      .createQueryBuilder('module')
      .where('module.isActive = :isActive', { isActive: true })
      .andWhere('module.status = :status', { status: ModuleStatus.PUBLISHED });

    // Apply filters
    if (filters.moduleType) {
      queryBuilder.andWhere('module.moduleType = :moduleType', { moduleType: filters.moduleType });
    }

    if (filters.difficultyLevel) {
      queryBuilder.andWhere('module.difficultyLevel = :difficultyLevel', { difficultyLevel: filters.difficultyLevel });
    }

    if (filters.isFeatured !== undefined) {
      queryBuilder.andWhere('module.isFeatured = :isFeatured', { isFeatured: filters.isFeatured });
    }

    if (filters.requiresSupervision !== undefined) {
      queryBuilder.andWhere('module.requiresSupervision = :requiresSupervision', { 
        requiresSupervision: filters.requiresSupervision 
      });
    }

    if (filters.tags && filters.tags.length > 0) {
      queryBuilder.andWhere('JSON_EXTRACT(module.tags, "$") LIKE :tags', { 
        tags: `%${filters.tags.join('%')}%` 
      });
    }

    // Order by featured first, then by average rating, then by creation date
    queryBuilder.orderBy('module.isFeatured', 'DESC')
               .addOrderBy('module.averageRating', 'DESC')
               .addOrderBy('module.createdAt', 'DESC');

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const modules = await queryBuilder.getMany();

    return {
      modules,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getModuleById(moduleId: string): Promise<WellnessModule> {
    const module = await this.moduleRepository.findOne({
      where: { 
        id: moduleId, 
        isActive: true,
        status: ModuleStatus.PUBLISHED 
      },
    });

    if (!module) {
      throw new NotFoundError('Wellness module not found');
    }

    return module;
  }

  async startModule(userId: string, moduleId: string): Promise<UserModuleProgress> {
    // Validate user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Validate module exists
    const module = await this.getModuleById(moduleId);

    // Check if user already has progress for this module
    let progress = await this.progressRepository.findOne({
      where: { userId, moduleId },
    });

    if (progress) {
      // If already started, just return existing progress
      if (progress.status !== ProgressStatus.NOT_STARTED) {
        return progress;
      }
    } else {
      // Create new progress record
      progress = this.progressRepository.create({
        userId,
        moduleId,
        status: ProgressStatus.NOT_STARTED,
        progressPercentage: 0,
        completedSections: [],
        sectionProgress: {},
        totalTimeSpentMinutes: 0,
      });
    }

    // Start the module
    progress.status = ProgressStatus.IN_PROGRESS;
    progress.startedAt = new Date();
    progress.lastAccessedAt = new Date();

    return await this.progressRepository.save(progress);
  }

  async updateProgress(
    userId: string, 
    moduleId: string, 
    sectionId: string, 
    timeSpentMinutes: number = 0,
    notes?: string
  ): Promise<UserModuleProgress> {
    const progress = await this.progressRepository.findOne({
      where: { userId, moduleId },
      relations: ['module'],
    });

    if (!progress) {
      throw new NotFoundError('Module progress not found');
    }

    // Update section progress
    const sectionProgress = progress.sectionProgress || {};
    sectionProgress[sectionId] = {
      completed: true,
      timeSpent: (sectionProgress[sectionId]?.timeSpent || 0) + timeSpentMinutes,
      lastAccessed: new Date(),
      notes: notes || sectionProgress[sectionId]?.notes,
    };

    // Add to completed sections if not already there
    const completedSections = progress.completedSections || [];
    if (!completedSections.includes(sectionId)) {
      completedSections.push(sectionId);
    }

    // Update total time spent
    progress.totalTimeSpentMinutes += timeSpentMinutes;
    progress.sectionProgress = sectionProgress;
    progress.completedSections = completedSections;
    progress.lastAccessedAt = new Date();

    // Update progress percentage
    progress.updateProgress();

    await this.progressRepository.save(progress);
    if (websocketServiceInstance) {
      websocketServiceInstance.emitToUser(userId, 'wellness-progress', { moduleId, progressPercentage: progress.progressPercentage });
    }
    return progress;
  }

  async getUserProgress(
    userId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ progress: UserModuleProgress[]; total: number; page: number; totalPages: number }> {
    const queryBuilder = this.progressRepository
      .createQueryBuilder('progress')
      .leftJoinAndSelect('progress.module', 'module')
      .where('progress.userId = :userId', { userId })
      .orderBy('progress.lastAccessedAt', 'DESC');

    const total = await queryBuilder.getCount();
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const progress = await queryBuilder.getMany();

    return {
      progress,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async rateModule(
    userId: string, 
    moduleId: string, 
    rating: number, 
    feedback?: string
  ): Promise<UserModuleProgress> {
    if (rating < 1 || rating > 5) {
      throw new ValidationError('Rating must be between 1 and 5');
    }

    const progress = await this.progressRepository.findOne({
      where: { userId, moduleId },
      relations: ['module'],
    });

    if (!progress) {
      throw new NotFoundError('Module progress not found');
    }

    // Update user rating
    progress.userRating = rating;
    if (feedback) {
      progress.userFeedback = feedback.trim();
    }

    await this.progressRepository.save(progress);

    // Update module's average rating
    const allRatings = await this.progressRepository
      .createQueryBuilder('progress')
      .where('progress.moduleId = :moduleId', { moduleId })
      .andWhere('progress.userRating IS NOT NULL')
      .getMany();

    if (allRatings.length > 0) {
      const totalRating = allRatings.reduce((sum, p) => sum + (p.userRating || 0), 0);
      const averageRating = totalRating / allRatings.length;

      await this.moduleRepository.update(moduleId, {
        averageRating: Math.round(averageRating * 100) / 100,
        ratingCount: allRatings.length,
      });
    }

    return progress;
  }

  async getModuleStats(): Promise<{
    totalModules: number;
    modulesByType: Array<{ type: string; count: number }>;
    modulesByDifficulty: Array<{ difficulty: string; count: number }>;
    averageRating: number;
    totalCompletions: number;
  }> {
    const [
      totalModules,
      allModules,
      allProgress
    ] = await Promise.all([
      this.moduleRepository.count({ 
        where: { 
          isActive: true, 
          status: ModuleStatus.PUBLISHED 
        } 
      }),
      this.moduleRepository.find({ 
        where: { 
          isActive: true, 
          status: ModuleStatus.PUBLISHED 
        } 
      }),
      this.progressRepository.find({ 
        where: { 
          status: ProgressStatus.COMPLETED 
        } 
      })
    ]);

    // Count by type
    const typeCount: Record<string, number> = {};
    allModules.forEach(module => {
      typeCount[module.moduleType] = (typeCount[module.moduleType] || 0) + 1;
    });

    // Count by difficulty
    const difficultyCount: Record<string, number> = {};
    allModules.forEach(module => {
      difficultyCount[module.difficultyLevel] = (difficultyCount[module.difficultyLevel] || 0) + 1;
    });

    // Calculate average rating
    const modulesWithRatings = allModules.filter(m => m.ratingCount > 0);
    const averageRating = modulesWithRatings.length > 0
      ? modulesWithRatings.reduce((sum, m) => sum + m.averageRating, 0) / modulesWithRatings.length
      : 0;

    return {
      totalModules,
      modulesByType: Object.entries(typeCount).map(([type, count]) => ({ type, count })),
      modulesByDifficulty: Object.entries(difficultyCount).map(([difficulty, count]) => ({ difficulty, count })),
      averageRating: Math.round(averageRating * 100) / 100,
      totalCompletions: allProgress.length,
    };
  }

  static async updateProgress(userId: string, moduleId: string, progress: number, stats?: any) {
    const repo = AppDataSource.getRepository(UserModuleProgress);
    let record = await repo.findOneBy({ userId, moduleId });
    if (!record) {
      record = repo.create({ userId, moduleId, progress, stats });
    } else {
      record.progress = progress;
      record.stats = stats;
      if (progress >= 100) record.completedAt = new Date();
    }
    await repo.save(record);
    if (websocketServiceInstance) {
      websocketServiceInstance.emitToUser(userId, 'wellness-progress', { moduleId, progress });
    }
    return record;
  }

  static async addFeedback(userId: string, moduleId: string, feedback: string) {
    const repo = AppDataSource.getRepository(UserModuleProgress);
    let record = await repo.findOneBy({ userId, moduleId });
    if (!record) {
      record = repo.create({ userId, moduleId, progress: 0 });
    }
    record.feedback = feedback;
    await repo.save(record);
    if (websocketServiceInstance) {
      websocketServiceInstance.emitToUser(userId, 'wellness-feedback', { moduleId, feedback });
    }
    return record;
  }

  static async getStats(moduleId: string) {
    const repo = AppDataSource.getRepository(UserModuleProgress);
    const progresses = await repo.find({ where: { moduleId } });
    const count = progresses.length;
    const avgProgress = progresses.reduce((acc, p) => acc + (p.progress || 0), 0) / (count || 1);
    const feedbacks = progresses.map(p => p.feedback).filter(Boolean);
    return { count, avgProgress, feedbacks };
  }
}
