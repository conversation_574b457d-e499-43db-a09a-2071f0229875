import Stripe from 'stripe';
import Payment from '../models/Payment';

// Remplacer par la clé réelle dans la config
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

class StripeService {
  static async createPaymentIntent(userId: number, amount: number, type: string) {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount * 100, // Stripe utilise des centimes
      currency: 'usd',
      metadata: { userId: userId.toString(), type },
    });
    return paymentIntent;
  }

  static async handleWebhook(event: Stripe.Event) {
    // Traitement des webhooks Stripe
    console.log('Stripe webhook reçu:', event.type);
  }

  static async createSubscription(userId: number, priceId: string) {
    // Logique pour créer un abonnement
    return { userId, priceId };
  }
}

export default StripeService; 