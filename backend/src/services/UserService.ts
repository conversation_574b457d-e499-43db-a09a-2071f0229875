import { AppDataSource } from '../config/database';
import { User } from '../models/User';

const userRepo = AppDataSource.getRepository(User);

export class UserService {
  /** Récupérer le profil utilisateur par ID */
  static async getProfile(userId: string) {
    const user = await userRepo.findOneBy({ id: userId });
    if (!user) throw new Error('Utilisateur non trouvé');
    return user;
  }

  /** Mettre à jour le profil utilisateur */
  static async updateProfile(userId: string, data: Partial<User>) {
    await userRepo.update(userId, data);
    return this.getProfile(userId);
  }

  /** Supprimer le profil utilisateur */
  static async deleteProfile(userId: string) {
    const user = await userRepo.findOneBy({ id: userId });
    if (!user) throw new Error('Utilisateur non trouvé');
    await userRepo.remove(user);
    return { success: true, message: 'Profil supprimé avec succès' };
  }
} 