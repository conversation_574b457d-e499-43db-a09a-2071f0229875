import { Server as SocketIOServer, Socket } from 'socket.io';
import { Server } from 'http';
import jwt from 'jsonwebtoken';
import { User } from '../models/User';
import { AppDataSource } from '../config/database';
import { Repository } from 'typeorm';

// WebSocket Event Types
export interface WebSocketEvents {
  // Authentication
  authenticate: (token: string) => void;
  authenticated: (user: Partial<User>) => void;
  authentication_error: (error: string) => void;

  // Appointment Events
  appointment_reminder: (data: AppointmentReminderData) => void;
  appointment_status_update: (data: AppointmentStatusData) => void;
  professional_availability_change: (data: ProfessionalAvailabilityData) => void;
  appointment_booking_confirmation: (data: AppointmentBookingData) => void;

  // Crisis Alerts
  crisis_alert: (data: CrisisAlertData) => void;
  emergency_contact_notification: (data: EmergencyContactData) => void;
  crisis_resource_recommendation: (data: CrisisResourceData) => void;

  // Messaging
  new_message: (data: MessageData) => void;
  message_read: (data: MessageReadData) => void;
  typing_indicator: (data: TypingIndicatorData) => void;
  unread_count_update: (data: UnreadCountData) => void;

  // Real-time Updates
  dashboard_update: (data: DashboardUpdateData) => void;
  mood_tracking_update: (data: MoodTrackingData) => void;
  wellness_progress_update: (data: WellnessProgressData) => void;

  // Connection Events
  connect: () => void;
  disconnect: (reason: string) => void;
  error: (error: Error) => void;

  // Custom events
  ia_message: (data: any) => void;
  monitoring_alert: (data: any) => void;
  system_alert: (data: any) => void;

  // Rooms
  joinRoom: (room: string) => void;
  leaveRoom: (room: string) => void;
  broadcast: (event: string, data: any) => void;
  emitToRole: (role: string, event: string, data: any) => void;
  emitToType: (type: string, event: string, data: any) => void;
}

// Data Interfaces
export interface AppointmentReminderData {
  appointmentId: string;
  professionalName: string;
  scheduledAt: string;
  reminderType: '15min' | '1hour' | '24hour';
  appointmentType: string;
  mode: 'video' | 'phone' | 'chat' | 'in_person';
}

export interface AppointmentStatusData {
  appointmentId: string;
  status: 'confirmed' | 'cancelled' | 'rescheduled' | 'completed';
  newScheduledAt?: string;
  reason?: string;
  professionalName: string;
}

export interface ProfessionalAvailabilityData {
  professionalId: string;
  professionalName: string;
  isOnline: boolean;
  availableSlots?: string[];
  lastSeen?: string;
}

export interface AppointmentBookingData {
  appointmentId: string;
  professionalName: string;
  scheduledAt: string;
  appointmentType: string;
  mode: string;
  confirmationNumber: string;
}

export interface CrisisAlertData {
  alertId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  resources: Array<{
    type: 'hotline' | 'chat' | 'emergency';
    name: string;
    contact: string;
    available24h: boolean;
  }>;
  location?: {
    latitude: number;
    longitude: number;
    city: string;
    state: string;
  };
}

export interface EmergencyContactData {
  contactId: string;
  contactName: string;
  contactType: 'professional' | 'emergency' | 'family';
  message: string;
  urgency: 'immediate' | 'urgent' | 'normal';
}

export interface CrisisResourceData {
  resourceId: string;
  title: string;
  description: string;
  type: 'article' | 'video' | 'exercise' | 'contact';
  url?: string;
  priority: number;
}

export interface MessageData {
  messageId: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderType: 'user' | 'professional' | 'system';
  content: string;
  timestamp: string;
  messageType: 'text' | 'image' | 'file' | 'system';
}

export interface MessageReadData {
  messageId: string;
  conversationId: string;
  readBy: string;
  readAt: string;
}

export interface TypingIndicatorData {
  conversationId: string;
  userId: string;
  userName: string;
  isTyping: boolean;
}

export interface UnreadCountData {
  conversationId: string;
  unreadCount: number;
  totalUnreadCount: number;
}

export interface DashboardUpdateData {
  type: 'stats' | 'activity' | 'appointments' | 'mood';
  data: any;
  timestamp: string;
}

export interface MoodTrackingData {
  entryId: string;
  mood: number;
  energy: number;
  stress: number;
  anxiety: number;
  timestamp: string;
}

export interface WellnessProgressData {
  moduleId: string;
  moduleName: string;
  progress: number;
  completed: boolean;
  timestamp: string;
}

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
  userType?: 'client' | 'professional' | 'admin';
}

interface UserSession {
  socketId: string;
  userId: string;
  userRole: string;
  userType: 'client' | 'professional' | 'admin';
  joinedAt: Date;
  lastActivity: Date;
  isOnline: boolean;
}

export class WebSocketService {
  private io: SocketIOServer;
  private userSessions: Map<string, UserSession> = new Map();
  private userRepository: Repository<User>;
  private professionalRooms: Map<string, Set<string>> = new Map(); // professionalId -> Set of socket IDs
  private appointmentRooms: Map<string, Set<string>> = new Map(); // appointmentId -> Set of socket IDs

  constructor(server: Server) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: ['http://localhost:3000', 'http://localhost:5173'],
        methods: ['GET', 'POST'],
        credentials: true,
      },
      pingTimeout: 60000,
      pingInterval: 25000,
    });

    this.userRepository = AppDataSource.getRepository(User);
    this.setupEventHandlers();
    this.setupHeartbeat();
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log(`🔌 New socket connection: ${socket.id}`);

      // Authentication handler
      socket.on('authenticate', async (token: string) => {
        try {
          const decoded = jwt.verify(token, process.env.JWT_SECRET || 'secret') as any;
          const user = await this.userRepository.findOne({
            where: { id: decoded.userId },
            relations: ['role']
          });

          if (!user) {
            socket.emit('authentication_error', 'User not found');
            return;
          }

          socket.userId = user.id;
          socket.userRole = user.role.name;
          socket.userType = user.userType as 'client' | 'professional' | 'admin';

          // Store user session
          const userSession: UserSession = {
            socketId: socket.id,
            userId: user.id,
            userRole: user.role.name,
            userType: socket.userType,
            joinedAt: new Date(),
            lastActivity: new Date(),
            isOnline: true
          };

          this.userSessions.set(socket.id, userSession);

          // Join user-specific room
          socket.join(`user_${user.id}`);

          // Join role-specific rooms
          socket.join(`role_${user.role.name}`);
          if (socket.userType === 'professional') {
            socket.join('professionals');
          }

          socket.emit('authenticated', {
            userId: user.id,
            role: user.role.name,
            userType: socket.userType
          });

          // Notify others about professional availability
          if (socket.userType === 'professional') {
            this.broadcastProfessionalAvailability(user.id, true);
          }

          // Send pending notifications
          this.sendPendingNotifications(user.id);

          console.log(`✅ User authenticated: ${user.id} (${user.role.name})`);
        } catch (error) {
          console.error('❌ Authentication error:', error);
          socket.emit('authentication_error', 'Invalid token');
        }
      });

      // Professional-specific events
      socket.on('join_professional_room', (professionalId: string) => {
        if (socket.userType === 'professional' && socket.userId === professionalId) {
          if (!this.professionalRooms.has(professionalId)) {
            this.professionalRooms.set(professionalId, new Set());
          }
          this.professionalRooms.get(professionalId)!.add(socket.id);
          socket.join(`professional_${professionalId}`);
          console.log(`🏥 Professional ${professionalId} joined their room`);
        }
      });

      // Appointment room events
      socket.on('join_appointment_room', (appointmentId: string) => {
        if (socket.userId) {
          if (!this.appointmentRooms.has(appointmentId)) {
            this.appointmentRooms.set(appointmentId, new Set());
          }
          this.appointmentRooms.get(appointmentId)!.add(socket.id);
          socket.join(`appointment_${appointmentId}`);
          console.log(`📅 User ${socket.userId} joined appointment room ${appointmentId}`);
        }
      });

      // Real-time messaging
      socket.on('send_message', (data: {
        recipientId: string;
        message: string;
        type: 'text' | 'appointment_request' | 'appointment_update';
        appointmentId?: string;
      }) => {
        if (socket.userId) {
          const messageData = {
            senderId: socket.userId,
            senderType: socket.userType,
            message: data.message,
            type: data.type,
            appointmentId: data.appointmentId,
            timestamp: new Date().toISOString()
          };

          // Send to recipient
          this.io.to(`user_${data.recipientId}`).emit('new_message', messageData);

          // Send confirmation to sender
          socket.emit('message_sent', { messageId: Date.now().toString() });

          console.log(`💬 Message sent from ${socket.userId} to ${data.recipientId}`);
        }
      });

      // Professional availability updates
      socket.on('update_availability', (availability: {
        isAvailable: boolean;
        availableSlots?: string[];
        status: 'online' | 'busy' | 'away' | 'offline';
      }) => {
        if (socket.userType === 'professional' && socket.userId) {
          // Update professional availability
          this.broadcastProfessionalAvailability(socket.userId, availability.isAvailable, availability);
          console.log(`🟢 Professional ${socket.userId} updated availability: ${availability.status}`);
        }
      });

      // Crisis alert handling
      socket.on('crisis_alert', (data: {
        severity: 'low' | 'medium' | 'high' | 'critical';
        message: string;
        location?: { latitude: number; longitude: number; };
      }) => {
        if (socket.userId) {
          // Send to all professionals and admin
          this.io.to('professionals').to('role_admin').emit('crisis_alert', {
            userId: socket.userId,
            severity: data.severity,
            message: data.message,
            location: data.location,
            timestamp: new Date().toISOString(),
            alertId: `crisis_${Date.now()}`
          });

          // Log crisis alert
          console.log(`🚨 CRISIS ALERT from user ${socket.userId}: ${data.severity} - ${data.message}`);
        }
      });

      // Heart rate and wellness monitoring
      socket.on('wellness_update', (data: {
        moodLevel: number;
        stressLevel: number;
        energyLevel: number;
        heartRate?: number;
        bloodPressure?: { systolic: number; diastolic: number; };
      }) => {
        if (socket.userId) {
          // Store wellness data and notify connected professionals
          const wellnessData = {
            userId: socket.userId,
            ...data,
            timestamp: new Date().toISOString()
          };

          // Emit to user's connected professionals
          this.notifyUserProfessionals(socket.userId, 'wellness_update', wellnessData);

          console.log(`💝 Wellness update from user ${socket.userId}`);
        }
      });

      // Appointment reminders
      socket.on('appointment_reminder', (appointmentId: string) => {
        if (socket.userType === 'professional' && socket.userId) {
          this.io.to(`appointment_${appointmentId}`).emit('appointment_reminder', {
            appointmentId,
            reminderTime: new Date().toISOString(),
            professionalId: socket.userId
          });
        }
      });

      // Activity tracking
      socket.on('activity', () => {
        const session = this.userSessions.get(socket.id);
        if (session) {
          session.lastActivity = new Date();
        }
      });

      // Disconnect handler
      socket.on('disconnect', () => {
        const session = this.userSessions.get(socket.id);
        if (session) {
          console.log(`🔌 User disconnected: ${session.userId}`);

          // Remove from professional rooms
          if (session.userType === 'professional') {
            this.professionalRooms.forEach((sockets, professionalId) => {
              sockets.delete(socket.id);
              if (sockets.size === 0) {
                this.professionalRooms.delete(professionalId);
              }
            });

            // Notify about professional going offline
            this.broadcastProfessionalAvailability(session.userId, false);
          }

          // Remove from appointment rooms
          this.appointmentRooms.forEach((sockets, appointmentId) => {
            sockets.delete(socket.id);
            if (sockets.size === 0) {
              this.appointmentRooms.delete(appointmentId);
            }
          });

          this.userSessions.delete(socket.id);
        }

        console.log(`🔌 Socket disconnected: ${socket.id}`);
      });
    });
  }

  // Public methods for external services
  public emitToUser(userId: string, event: string, data: any): void {
    this.io.to(`user_${userId}`).emit(event, data);
  }

  public emitToProfessionals(event: string, data: any): void {
    this.io.to('professionals').emit(event, data);
  }

  public emitToAppointment(appointmentId: string, event: string, data: any): void {
    this.io.to(`appointment_${appointmentId}`).emit(event, data);
  }

  public emitToRole(role: string, event: string, data: any): void {
    this.io.to(`role_${role}`).emit(event, data);
  }

  public sendNotification(userId: string, notification: {
    type: 'appointment' | 'message' | 'reminder' | 'emergency' | 'system';
    title: string;
    message: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    actionUrl?: string;
    appointmentId?: string;
  }): void {
    this.emitToUser(userId, 'notification', {
      ...notification,
      id: `notif_${Date.now()}`,
      timestamp: new Date().toISOString(),
      read: false
    });
  }

  public broadcastSystemAnnouncement(announcement: {
    title: string;
    message: string;
    type: 'maintenance' | 'feature' | 'security' | 'general';
    targetRoles?: string[];
  }): void {
    const announcementData = {
      ...announcement,
      id: `announcement_${Date.now()}`,
      timestamp: new Date().toISOString()
    };

    if (announcement.targetRoles && announcement.targetRoles.length > 0) {
      announcement.targetRoles.forEach(role => {
        this.emitToRole(role, 'system_announcement', announcementData);
      });
    } else {
      this.io.emit('system_announcement', announcementData);
    }
  }

  // Private helper methods
  private broadcastProfessionalAvailability(professionalId: string, isAvailable: boolean, additionalData?: any): void {
    const availabilityData = {
      professionalId,
      isAvailable,
      timestamp: new Date().toISOString(),
      ...additionalData
    };

    // Notify all users about professional availability change
    this.io.emit('professional_availability_change', availabilityData);
  }

  private notifyUserProfessionals(userId: string, event: string, data: any): void {
    // This would typically query the database for connected professionals
    // For now, we'll emit to all professionals
    this.emitToProfessionals(event, { userId, ...data });
  }

  private async sendPendingNotifications(userId: string): Promise<void> {
    // This would typically fetch pending notifications from database
    // For now, we'll send a welcome notification
    this.sendNotification(userId, {
      type: 'system',
      title: 'Bienvenue sur MindFlow Pro',
      message: 'Vous êtes maintenant connecté et pouvez recevoir des notifications en temps réel.',
      priority: 'low'
    });
  }

  private setupHeartbeat(): void {
    setInterval(() => {
      const now = new Date();
      const timeoutThreshold = 5 * 60 * 1000; // 5 minutes

      this.userSessions.forEach((session, socketId) => {
        const timeSinceLastActivity = now.getTime() - session.lastActivity.getTime();
        
        if (timeSinceLastActivity > timeoutThreshold) {
          session.isOnline = false;
          // Optionally disconnect inactive users
          const socket = this.io.sockets.sockets.get(socketId);
          if (socket) {
            socket.emit('session_timeout');
            socket.disconnect(true);
          }
        }
      });
    }, 60000); // Check every minute
  }

  public getOnlineUsers(): { total: number; professionals: number; clients: number; } {
    let professionals = 0;
    let clients = 0;

    this.userSessions.forEach(session => {
      if (session.isOnline) {
        if (session.userType === 'professional') {
          professionals++;
        } else if (session.userType === 'client') {
          clients++;
        }
      }
    });

    return {
      total: professionals + clients,
      professionals,
      clients
    };
  }

  public isProfessionalOnline(professionalId: string): boolean {
    for (const session of this.userSessions.values()) {
      if (session.userId === professionalId && session.userType === 'professional' && session.isOnline) {
        return true;
      }
    }
    return false;
  }

  // Méthodes manquantes pour le notificationService
  public isUserConnected(userId: string): boolean {
    for (const session of this.userSessions.values()) {
      if (session.userId === userId && session.isOnline) {
        return true;
      }
    }
    return false;
  }

  public getConnectedUsers(): UserSession[] {
    return Array.from(this.userSessions.values()).filter(session => session.isOnline);
  }
}

let websocketServiceInstance: WebSocketService | null = null;

export const initializeWebSocketService = (server: Server): WebSocketService => {
  if (!websocketServiceInstance) {
    websocketServiceInstance = new WebSocketService(server);
    console.log('🔌 WebSocket service initialized');
  }
  return websocketServiceInstance;
};

export const getWebSocketService = (): WebSocketService | null => {
  return websocketServiceInstance;
};

export default { initializeWebSocketService, getWebSocketService };
