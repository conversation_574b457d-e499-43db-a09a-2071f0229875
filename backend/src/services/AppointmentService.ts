import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { Appointment, AppointmentType, AppointmentStatus, AppointmentMode } from '../models/Appointment';
import { ProfessionalProfile } from '../models/ProfessionalProfile';
import { User } from '../models/User';
import { ValidationError, NotFoundError, ConflictError } from '../utils/errors';

export interface CreateAppointmentDto {
  professionalId: string;
  appointmentType: AppointmentType;
  mode: AppointmentMode;
  scheduledAt: Date;
  durationMinutes?: number;
  clientNotes?: string;
  preSessionAssessment?: {
    moodLevel: number;
    stressLevel: number;
    anxietyLevel: number;
    concerns: string[];
    goals: string[];
  };
  isEmergency?: boolean;
}

export interface UpdateAppointmentDto {
  status?: AppointmentStatus;
  actualStartTime?: Date;
  actualEndTime?: Date;
  professionalNotes?: string;
  sessionSummary?: string;
  postSessionAssessment?: {
    moodLevel: number;
    stressLevel: number;
    satisfactionLevel: number;
    helpfulnessRating: number;
    feedback: string;
  };
  actionItems?: Array<{
    description: string;
    dueDate?: Date;
    completed: boolean;
    completedAt?: Date;
  }>;
  followUpPlan?: {
    nextAppointmentRecommended: boolean;
    recommendedTimeframe: string;
    specificGoals: string[];
    homeworkAssignments: string[];
  };
  cost?: number;
  clientRating?: number;
  clientFeedback?: string;
}

export interface AppointmentFilters {
  status?: AppointmentStatus;
  appointmentType?: AppointmentType;
  mode?: AppointmentMode;
  dateFrom?: Date;
  dateTo?: Date;
  professionalId?: string;
  clientId?: string;
  isEmergency?: boolean;
}

export class AppointmentService {
  private appointmentRepository: Repository<Appointment>;
  private professionalRepository: Repository<ProfessionalProfile>;
  private userRepository: Repository<User>;

  constructor() {
    this.appointmentRepository = AppDataSource.getRepository(Appointment);
    this.professionalRepository = AppDataSource.getRepository(ProfessionalProfile);
    this.userRepository = AppDataSource.getRepository(User);
  }

  async createAppointment(clientId: string, data: CreateAppointmentDto): Promise<Appointment> {
    // Validate client exists
    const client = await this.userRepository.findOne({ where: { id: clientId } });
    if (!client) {
      throw new NotFoundError('Client not found');
    }

    // Validate professional exists and is accepting clients
    const professional = await this.professionalRepository.findOne({
      where: { id: data.professionalId, isActive: true, acceptingNewClients: true },
      relations: ['user'],
    });

    if (!professional) {
      throw new NotFoundError('Professional not found or not accepting new clients');
    }

    // Validate appointment time is in the future
    if (data.scheduledAt <= new Date()) {
      throw new ValidationError('Appointment must be scheduled for a future date and time');
    }

    // Check for conflicts with existing appointments
    const conflictingAppointment = await this.checkForConflicts(
      data.professionalId,
      data.scheduledAt,
      data.durationMinutes || 60
    );

    if (conflictingAppointment) {
      throw new ConflictError('Professional is not available at the requested time');
    }

    // Validate professional availability
    const isAvailable = await this.checkProfessionalAvailability(
      data.professionalId,
      data.scheduledAt,
      data.durationMinutes || 60
    );

    if (!isAvailable) {
      throw new ValidationError('Professional is not available at the requested time');
    }

    // Create appointment
    const appointment = this.appointmentRepository.create({
      clientId,
      professionalId: data.professionalId,
      appointmentType: data.appointmentType,
      status: AppointmentStatus.SCHEDULED,
      mode: data.mode,
      scheduledAt: data.scheduledAt,
      durationMinutes: data.durationMinutes || 60,
      clientNotes: data.clientNotes?.trim(),
      preSessionAssessment: data.preSessionAssessment,
      isEmergency: data.isEmergency || false,
      actionItems: [],
      reschedulingHistory: [],
      reminders: [],
      currency: professional.currency,
      metadata: {},
    });

    // Calculate cost based on professional's rates
    if (professional.consultationTypes && professional.consultationTypes.length > 0) {
      const consultationType = professional.consultationTypes.find(ct => ct.type === data.mode);
      if (consultationType) {
        appointment.cost = consultationType.price;
      }
    } else if (professional.hourlyRate) {
      appointment.cost = (professional.hourlyRate * (data.durationMinutes || 60)) / 60;
    }

    const savedAppointment = await this.appointmentRepository.save(appointment);

    // Update professional's total appointments count
    professional.totalAppointments += 1;
    await this.professionalRepository.save(professional);

    return savedAppointment;
  }

  async getAppointments(
    filters: AppointmentFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{ appointments: Appointment[]; total: number; page: number; totalPages: number }> {
    const queryBuilder = this.appointmentRepository
      .createQueryBuilder('appointment')
      .leftJoinAndSelect('appointment.client', 'client')
      .leftJoinAndSelect('appointment.professional', 'professional')
      .leftJoinAndSelect('professional.user', 'professionalUser');

    // Apply filters
    if (filters.status) {
      queryBuilder.andWhere('appointment.status = :status', { status: filters.status });
    }

    if (filters.appointmentType) {
      queryBuilder.andWhere('appointment.appointmentType = :appointmentType', { 
        appointmentType: filters.appointmentType 
      });
    }

    if (filters.mode) {
      queryBuilder.andWhere('appointment.mode = :mode', { mode: filters.mode });
    }

    if (filters.professionalId) {
      queryBuilder.andWhere('appointment.professionalId = :professionalId', { 
        professionalId: filters.professionalId 
      });
    }

    if (filters.clientId) {
      queryBuilder.andWhere('appointment.clientId = :clientId', { clientId: filters.clientId });
    }

    if (filters.isEmergency !== undefined) {
      queryBuilder.andWhere('appointment.isEmergency = :isEmergency', { 
        isEmergency: filters.isEmergency 
      });
    }

    if (filters.dateFrom) {
      queryBuilder.andWhere('appointment.scheduledAt >= :dateFrom', { dateFrom: filters.dateFrom });
    }

    if (filters.dateTo) {
      queryBuilder.andWhere('appointment.scheduledAt <= :dateTo', { dateTo: filters.dateTo });
    }

    // Order by scheduled time
    queryBuilder.orderBy('appointment.scheduledAt', 'ASC');

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const appointments = await queryBuilder.getMany();

    return {
      appointments,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getAppointmentById(appointmentId: string): Promise<Appointment> {
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: ['client', 'professional', 'professional.user'],
    });

    if (!appointment) {
      throw new NotFoundError('Appointment not found');
    }

    return appointment;
  }

  async updateAppointment(appointmentId: string, data: UpdateAppointmentDto): Promise<Appointment> {
    const appointment = await this.getAppointmentById(appointmentId);

    // Update fields
    Object.keys(data).forEach(key => {
      if (data[key as keyof UpdateAppointmentDto] !== undefined) {
        (appointment as any)[key] = data[key as keyof UpdateAppointmentDto];
      }
    });

    return await this.appointmentRepository.save(appointment);
  }

  async cancelAppointment(
    appointmentId: string, 
    cancelledBy: string, 
    reason: string
  ): Promise<Appointment> {
    const appointment = await this.getAppointmentById(appointmentId);

    if (appointment.status === AppointmentStatus.COMPLETED ||
        appointment.status === AppointmentStatus.CANCELLED_BY_CLIENT ||
        appointment.status === AppointmentStatus.CANCELLED_BY_PROFESSIONAL) {
      throw new ValidationError('Cannot cancel appointment with current status');
    }

    // Determine cancellation status based on who is cancelling
    const professional = await this.professionalRepository.findOne({ 
      where: { userId: cancelledBy } 
    });

    appointment.status = professional 
      ? AppointmentStatus.CANCELLED_BY_PROFESSIONAL 
      : AppointmentStatus.CANCELLED_BY_CLIENT;
    
    appointment.cancellationReason = reason.trim();
    appointment.cancelledAt = new Date();
    appointment.cancelledBy = cancelledBy;

    return await this.appointmentRepository.save(appointment);
  }

  async rescheduleAppointment(
    appointmentId: string, 
    newScheduledAt: Date, 
    rescheduledBy: string,
    reason: string
  ): Promise<Appointment> {
    const appointment = await this.getAppointmentById(appointmentId);

    if (appointment.status !== AppointmentStatus.SCHEDULED && 
        appointment.status !== AppointmentStatus.CONFIRMED) {
      throw new ValidationError('Cannot reschedule appointment with current status');
    }

    // Validate new time is in the future
    if (newScheduledAt <= new Date()) {
      throw new ValidationError('New appointment time must be in the future');
    }

    // Check for conflicts
    const conflictingAppointment = await this.checkForConflicts(
      appointment.professionalId,
      newScheduledAt,
      appointment.durationMinutes,
      appointmentId
    );

    if (conflictingAppointment) {
      throw new ConflictError('Professional is not available at the new requested time');
    }

    // Add to rescheduling history
    const reschedulingEntry = {
      originalDate: appointment.scheduledAt,
      newDate: newScheduledAt,
      reason: reason.trim(),
      rescheduledBy,
      rescheduledAt: new Date(),
    };

    appointment.reschedulingHistory = [...(appointment.reschedulingHistory || []), reschedulingEntry];
    appointment.scheduledAt = newScheduledAt;
    appointment.status = AppointmentStatus.RESCHEDULED;

    return await this.appointmentRepository.save(appointment);
  }

  async getAvailableSlots(
    professionalId: string,
    date: Date,
    durationMinutes: number = 60
  ): Promise<Array<{ start: Date; end: Date }>> {
    const professional = await this.professionalRepository.findOne({
      where: { id: professionalId, isActive: true },
    });

    if (!professional || !professional.availability) {
      return [];
    }

    const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const daySchedule = professional.availability.schedule[dayOfWeek];

    if (!daySchedule || daySchedule.length === 0) {
      return [];
    }

    // Get existing appointments for the day
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const existingAppointments = await this.appointmentRepository.find({
      where: {
        professionalId,
        scheduledAt: {
          $gte: startOfDay,
          $lte: endOfDay,
        } as any,
        status: {
          $in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS]
        } as any,
      },
    });

    // Calculate available slots
    const availableSlots: Array<{ start: Date; end: Date }> = [];

    for (const timeSlot of daySchedule) {
      const slotStart = new Date(date);
      const [startHour, startMinute] = timeSlot.startTime.split(':').map(Number);
      slotStart.setHours(startHour, startMinute, 0, 0);

      const slotEnd = new Date(date);
      const [endHour, endMinute] = timeSlot.endTime.split(':').map(Number);
      slotEnd.setHours(endHour, endMinute, 0, 0);

      // Generate slots within this time range
      let currentTime = new Date(slotStart);
      while (currentTime.getTime() + (durationMinutes * 60 * 1000) <= slotEnd.getTime()) {
        const slotEndTime = new Date(currentTime.getTime() + (durationMinutes * 60 * 1000));

        // Check if this slot conflicts with existing appointments
        const hasConflict = existingAppointments.some(apt => {
          const aptStart = new Date(apt.scheduledAt);
          const aptEnd = new Date(aptStart.getTime() + (apt.durationMinutes * 60 * 1000));
          
          return (currentTime < aptEnd && slotEndTime > aptStart);
        });

        if (!hasConflict && currentTime > new Date()) {
          availableSlots.push({
            start: new Date(currentTime),
            end: new Date(slotEndTime),
          });
        }

        // Move to next slot (assuming 15-minute intervals)
        currentTime = new Date(currentTime.getTime() + (15 * 60 * 1000));
      }
    }

    return availableSlots;
  }

  private async checkForConflicts(
    professionalId: string,
    scheduledAt: Date,
    durationMinutes: number,
    excludeAppointmentId?: string
  ): Promise<Appointment | null> {
    const appointmentStart = new Date(scheduledAt);
    const appointmentEnd = new Date(scheduledAt.getTime() + (durationMinutes * 60 * 1000));

    const queryBuilder = this.appointmentRepository
      .createQueryBuilder('appointment')
      .where('appointment.professionalId = :professionalId', { professionalId })
      .andWhere('appointment.status IN (:...statuses)', { 
        statuses: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS] 
      });

    if (excludeAppointmentId) {
      queryBuilder.andWhere('appointment.id != :excludeId', { excludeId: excludeAppointmentId });
    }

    const existingAppointments = await queryBuilder.getMany();

    return existingAppointments.find(apt => {
      const aptStart = new Date(apt.scheduledAt);
      const aptEnd = new Date(aptStart.getTime() + (apt.durationMinutes * 60 * 1000));
      
      return (appointmentStart < aptEnd && appointmentEnd > aptStart);
    }) || null;
  }

  private async checkProfessionalAvailability(
    professionalId: string,
    scheduledAt: Date,
    durationMinutes: number
  ): Promise<boolean> {
    const professional = await this.professionalRepository.findOne({
      where: { id: professionalId },
    });

    if (!professional || !professional.availability) {
      return false;
    }

    const dayOfWeek = scheduledAt.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const daySchedule = professional.availability.schedule[dayOfWeek];

    if (!daySchedule || daySchedule.length === 0) {
      return false;
    }

    const appointmentTime = scheduledAt.getHours() * 60 + scheduledAt.getMinutes();
    const appointmentEndTime = appointmentTime + durationMinutes;

    return daySchedule.some(slot => {
      const [startHour, startMinute] = slot.startTime.split(':').map(Number);
      const [endHour, endMinute] = slot.endTime.split(':').map(Number);
      
      const slotStart = startHour * 60 + startMinute;
      const slotEnd = endHour * 60 + endMinute;

      return appointmentTime >= slotStart && appointmentEndTime <= slotEnd;
    });
  }

  async getAppointmentStats(): Promise<{
    totalAppointments: number;
    appointmentsByStatus: Array<{ status: string; count: number }>;
    appointmentsByType: Array<{ type: string; count: number }>;
    appointmentsByMode: Array<{ mode: string; count: number }>;
    averageRating: number;
    upcomingAppointments: number;
  }> {
    const [totalAppointments, allAppointments] = await Promise.all([
      this.appointmentRepository.count(),
      this.appointmentRepository.find(),
    ]);

    // Count by status
    const statusCount: Record<string, number> = {};
    allAppointments.forEach(apt => {
      statusCount[apt.status] = (statusCount[apt.status] || 0) + 1;
    });

    // Count by type
    const typeCount: Record<string, number> = {};
    allAppointments.forEach(apt => {
      typeCount[apt.appointmentType] = (typeCount[apt.appointmentType] || 0) + 1;
    });

    // Count by mode
    const modeCount: Record<string, number> = {};
    allAppointments.forEach(apt => {
      modeCount[apt.mode] = (modeCount[apt.mode] || 0) + 1;
    });

    // Calculate average rating
    const appointmentsWithRatings = allAppointments.filter(apt => apt.clientRating);
    const averageRating = appointmentsWithRatings.length > 0
      ? appointmentsWithRatings.reduce((sum, apt) => sum + (apt.clientRating || 0), 0) / appointmentsWithRatings.length
      : 0;

    // Count upcoming appointments
    const now = new Date();
    const upcomingAppointments = allAppointments.filter(apt => 
      apt.scheduledAt > now && 
      (apt.status === AppointmentStatus.SCHEDULED || apt.status === AppointmentStatus.CONFIRMED)
    ).length;

    return {
      totalAppointments,
      appointmentsByStatus: Object.entries(statusCount).map(([status, count]) => ({ status, count })),
      appointmentsByType: Object.entries(typeCount).map(([type, count]) => ({ type, count })),
      appointmentsByMode: Object.entries(modeCount).map(([mode, count]) => ({ mode, count })),
      averageRating: Math.round(averageRating * 100) / 100,
      upcomingAppointments,
    };
  }
}
