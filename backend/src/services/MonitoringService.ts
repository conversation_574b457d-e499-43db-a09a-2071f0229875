import { Request, Response } from 'express';

export class MonitoringService {
  private metrics: Map<string, any> = new Map();
  
  constructor() {
    this.initializeMetrics();
  }

  private initializeMetrics() {
    this.metrics.set('activeUsers', 0);
    this.metrics.set('serverLoad', 0);
    this.metrics.set('responseTime', 0);
    this.metrics.set('uptime', 100);
  }

  updateMetric(key: string, value: any) {
    this.metrics.set(key, value);
    this.metrics.set('lastUpdate', new Date().toISOString());
  }

  getMetrics() {
    return Object.fromEntries(this.metrics);
  }

  getSystemHealth() {
    const cpu = this.metrics.get('serverLoad') || 0;
    const responseTime = this.metrics.get('responseTime') || 0;
    
    return {
      status: cpu > 80 || responseTime > 500 ? 'warning' : 'healthy',
      cpu,
      responseTime,
      memory: process.memoryUsage(),
      uptime: process.uptime()
    };
  }

  async checkDatabaseHealth() {
    try {
      // Simulation check database
      const responseTime = Math.random() * 100 + 50;
      return {
        status: responseTime > 200 ? 'warning' : 'healthy',
        responseTime,
        connections: Math.floor(Math.random() * 50) + 10
      };
    } catch (error) {
      return {
        status: 'critical',
        error: error.message
      };
    }
  }
}

export const monitoringService = new MonitoringService();