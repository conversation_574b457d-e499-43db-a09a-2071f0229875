import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { User, UserStatus } from '../models/User';
import { MentalHealthData } from '../models/MentalHealthData';
import { JournalEntry } from '../models/JournalEntry';
import { AICoachInteraction } from '../models/AICoachInteraction';
import { getWebSocketService } from './websocketService';

export interface HealthAlert {
  id: string;
  userId: string;
  type: 'mood_decline' | 'crisis_keywords' | 'isolation' | 'sleep_pattern' | 'stress_spike';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  data: any;
  triggeredAt: Date;
  resolved: boolean;
  resolvedAt?: Date;
  actionTaken?: string;
}

export interface WellnessScore {
  userId: string;
  overallScore: number;
  components: {
    mood: number;
    stress: number;
    sleep: number;
    social: number;
    activity: number;
  };
  calculatedAt: Date;
}

export class HealthMonitoringService {
  private userRepository: Repository<User>;
  private mentalHealthRepository: Repository<MentalHealthData>;
  private journalRepository: Repository<JournalEntry>;
  private aiInteractionRepository: Repository<AICoachInteraction>;
  private activeAlerts: Map<string, HealthAlert[]> = new Map();

  constructor() {
    this.userRepository = AppDataSource.getRepository(User);
    this.mentalHealthRepository = AppDataSource.getRepository(MentalHealthData);
    this.journalRepository = AppDataSource.getRepository(JournalEntry);
    this.aiInteractionRepository = AppDataSource.getRepository(AICoachInteraction);
  }

  async analyzeUserMentalHealth(userId: string): Promise<{
    wellnessScore: WellnessScore;
    alerts: HealthAlert[];
    recommendations: string[];
  }> {
    try {
      const [mentalHealthData, journalEntries] = await Promise.all([
        this.mentalHealthRepository.find({
          where: { userId },
          order: { createdAt: 'DESC' },
          take: 50
        }),
        this.journalRepository.find({
          where: { userId },
          order: { createdAt: 'DESC' },
          take: 30
        })
      ]);

      const wellnessScore = this.calculateWellnessScore(userId, mentalHealthData, journalEntries);
      const alerts: HealthAlert[] = [];
      const recommendations: string[] = ['Maintenez une routine de bien-être quotidienne'];

      return { wellnessScore, alerts, recommendations };
    } catch (error) {
      console.error(`Erreur lors de l'analyse de santé mentale pour l'utilisateur ${userId}:`, error);
      throw error;
    }
  }

  private calculateWellnessScore(
    userId: string,
    mentalHealthData: MentalHealthData[],
    journalEntries: JournalEntry[]
  ): WellnessScore {
    return {
      userId,
      overallScore: 75,
      components: {
        mood: 75,
        stress: 70,
        sleep: 80,
        social: 65,
        activity: 70,
      },
      calculatedAt: new Date(),
    };
  }

  public async getUserHealthStatus(userId: string) {
    return await this.analyzeUserMentalHealth(userId);
  }

  public getUserAlerts(userId: string): HealthAlert[] {
    return this.activeAlerts.get(userId) || [];
  }

  public getSystemHealthOverview() {
    return {
      totalMonitoredUsers: 0,
      totalActiveAlerts: 0,
      criticalAlerts: 0,
      systemStatus: 'normal' as const,
      lastUpdate: new Date(),
    };
  }
}

let healthMonitoringServiceInstance: HealthMonitoringService | null = null;

export const getHealthMonitoringService = (): HealthMonitoringService => {
  if (!healthMonitoringServiceInstance) {
    healthMonitoringServiceInstance = new HealthMonitoringService();
  }
  return healthMonitoringServiceInstance;
};
