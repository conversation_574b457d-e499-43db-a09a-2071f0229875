import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { AICoachInteraction, InteractionType, InteractionStatus, SentimentScore } from '../models/AICoachInteraction';
import { User } from '../models/User';
import { ValidationError, NotFoundError } from '../utils/errors';
import { AICoachSession } from '../models/AICoachSession';
import { websocketServiceInstance } from './websocketInstance';

export interface CreateInteractionDto {
  userMessage: string;
  interactionType?: InteractionType;
  sessionId?: string;
  context?: Record<string, any>;
}

export interface AIResponse {
  response: string;
  sentimentScore?: SentimentScore;
  extractedTopics?: string[];
  suggestedActions?: string[];
  emotionalIndicators?: string[];
  confidenceScore?: number;
}

export class AICoachService {
  private interactionRepository: Repository<AICoachInteraction>;
  private userRepository: Repository<User>;

  constructor() {
    this.interactionRepository = AppDataSource.getRepository(AICoachInteraction);
    this.userRepository = AppDataSource.getRepository(User);
  }

  async createInteraction(userId: string, data: CreateInteractionDto): Promise<AICoachInteraction> {
    // Validate user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Validate input
    if (!data.userMessage || data.userMessage.trim().length === 0) {
      throw new ValidationError('User message is required');
    }

    if (data.userMessage.length > 5000) {
      throw new ValidationError('Message is too long (max 5000 characters)');
    }

    const startTime = Date.now();

    // Generate AI response (for now, we'll use rule-based responses)
    const aiResponse = await this.generateAIResponse(data.userMessage, data.context, user);
    
    const responseTime = Date.now() - startTime;

    // Create interaction record
    const interaction = this.interactionRepository.create({
      userId,
      interactionType: data.interactionType || InteractionType.CHAT,
      status: InteractionStatus.COMPLETED,
      userMessage: data.userMessage.trim(),
      aiResponse: aiResponse.response,
      sentimentScore: aiResponse.sentimentScore,
      context: data.context || {},
      extractedTopics: aiResponse.extractedTopics || [],
      suggestedActions: aiResponse.suggestedActions || [],
      emotionalIndicators: aiResponse.emotionalIndicators || [],
      confidenceScore: aiResponse.confidenceScore || 85,
      sessionId: data.sessionId || this.generateSessionId(),
      responseTimeMs: responseTime,
    });

    return await this.interactionRepository.save(interaction);
  }

  async getInteractions(
    userId: string,
    sessionId?: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ interactions: AICoachInteraction[]; total: number; page: number; totalPages: number }> {
    const queryBuilder = this.interactionRepository
      .createQueryBuilder('interaction')
      .where('interaction.userId = :userId', { userId });

    if (sessionId) {
      queryBuilder.andWhere('interaction.sessionId = :sessionId', { sessionId });
    }

    queryBuilder.orderBy('interaction.createdAt', 'ASC');

    const total = await queryBuilder.getCount();
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const interactions = await queryBuilder.getMany();

    return {
      interactions,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getInteractionById(userId: string, interactionId: string): Promise<AICoachInteraction> {
    const interaction = await this.interactionRepository.findOne({
      where: { id: interactionId, userId },
    });

    if (!interaction) {
      throw new NotFoundError('Interaction not found');
    }

    return interaction;
  }

  async rateInteraction(userId: string, interactionId: string, rating: number, feedback?: string): Promise<AICoachInteraction> {
    if (rating < 1 || rating > 5) {
      throw new ValidationError('Rating must be between 1 and 5');
    }

    const interaction = await this.getInteractionById(userId, interactionId);
    
    interaction.userRating = rating;
    if (feedback) {
      interaction.userFeedback = feedback.trim();
    }

    return await this.interactionRepository.save(interaction);
  }

  async getSessions(userId: string): Promise<Array<{ sessionId: string; lastInteraction: Date; messageCount: number }>> {
    const interactions = await this.interactionRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });

    const sessionGroups: Record<string, AICoachInteraction[]> = {};
    
    interactions.forEach(interaction => {
      const sessionId = interaction.sessionId || 'default';
      if (!sessionGroups[sessionId]) {
        sessionGroups[sessionId] = [];
      }
      sessionGroups[sessionId].push(interaction);
    });

    return Object.entries(sessionGroups).map(([sessionId, sessionInteractions]) => ({
      sessionId,
      lastInteraction: sessionInteractions[0].createdAt,
      messageCount: sessionInteractions.length,
    }));
  }

  private async generateAIResponse(userMessage: string, context?: Record<string, any>, user?: User): Promise<AIResponse> {
    // This is a simplified rule-based AI coach for now
    // In production, this would integrate with OpenAI or similar service
    
    const message = userMessage.toLowerCase();
    let response = '';
    let sentimentScore: SentimentScore = SentimentScore.NEUTRAL;
    let extractedTopics: string[] = [];
    let suggestedActions: string[] = [];
    let emotionalIndicators: string[] = [];

    // Analyze sentiment and extract topics
    if (message.includes('sad') || message.includes('depressed') || message.includes('down')) {
      sentimentScore = SentimentScore.NEGATIVE;
      emotionalIndicators.push('sadness');
      extractedTopics.push('depression');
    }

    if (message.includes('anxious') || message.includes('worried') || message.includes('nervous')) {
      sentimentScore = SentimentScore.NEGATIVE;
      emotionalIndicators.push('anxiety');
      extractedTopics.push('anxiety');
    }

    if (message.includes('happy') || message.includes('good') || message.includes('great')) {
      sentimentScore = SentimentScore.POSITIVE;
      emotionalIndicators.push('happiness');
    }

    if (message.includes('stress') || message.includes('overwhelmed')) {
      emotionalIndicators.push('stress');
      extractedTopics.push('stress_management');
    }

    if (message.includes('work') || message.includes('job')) {
      extractedTopics.push('work_life_balance');
    }

    if (message.includes('sleep') || message.includes('tired')) {
      extractedTopics.push('sleep_hygiene');
    }

    // Generate appropriate responses based on content
    if (message.includes('hello') || message.includes('hi')) {
      response = `Hello ${user?.firstName || 'there'}! I'm your AI wellness coach. I'm here to support you on your mental health journey. How are you feeling today?`;
      suggestedActions.push('Share how you\'re feeling today');
      suggestedActions.push('Set a wellness goal');
    } else if (emotionalIndicators.includes('anxiety')) {
      response = `I understand you're feeling anxious. That's completely normal and you're not alone. Let's work through this together. 

Here are some immediate techniques that might help:
• Take 5 deep breaths (4 seconds in, 6 seconds out)
• Try the 5-4-3-2-1 grounding technique
• Remember that anxiety is temporary and will pass

Would you like me to guide you through a breathing exercise or suggest some anxiety management resources?`;
      
      suggestedActions.push('Try a breathing exercise');
      suggestedActions.push('Practice grounding techniques');
      suggestedActions.push('Explore anxiety management modules');
    } else if (emotionalIndicators.includes('sadness')) {
      response = `I hear that you're going through a difficult time. It takes courage to reach out, and I'm glad you're here. Your feelings are valid and it's okay to not be okay sometimes.

Some gentle suggestions:
• Be kind to yourself today
• Consider reaching out to a trusted friend or family member
• Engage in a small activity that usually brings you comfort
• Remember that this feeling is temporary

Would you like to talk about what's contributing to these feelings, or would you prefer some coping strategies?`;
      
      suggestedActions.push('Journal about your feelings');
      suggestedActions.push('Practice self-compassion');
      suggestedActions.push('Consider professional support');
    } else if (extractedTopics.includes('stress_management')) {
      response = `Stress is a common experience, especially in today's world. The good news is that there are many effective ways to manage it.

Quick stress relief techniques:
• Progressive muscle relaxation
• Mindful breathing
• Brief meditation (even 5 minutes helps)
• Physical movement or stretching

Would you like me to suggest a specific stress management module or technique based on your current situation?`;
      
      suggestedActions.push('Try a 5-minute meditation');
      suggestedActions.push('Practice progressive muscle relaxation');
      suggestedActions.push('Explore stress management modules');
    } else if (extractedTopics.includes('work_life_balance')) {
      response = `Work-life balance is crucial for mental health. It sounds like you might be dealing with work-related stress or challenges.

Some strategies to consider:
• Set clear boundaries between work and personal time
• Take regular breaks during your workday
• Practice saying "no" to non-essential commitments
• Prioritize self-care activities

What specific aspect of work-life balance would you like to focus on?`;
      
      suggestedActions.push('Set work boundaries');
      suggestedActions.push('Schedule self-care time');
      suggestedActions.push('Explore work-life balance resources');
    } else if (sentimentScore === SentimentScore.POSITIVE) {
      response = `It's wonderful to hear that you're feeling good! Positive moments are important to acknowledge and celebrate. 

To maintain this positive state:
• Take a moment to appreciate what's going well
• Consider what contributed to these good feelings
• Think about how you can nurture more of these moments

Is there something specific that's contributing to your positive mood today?`;
      
      suggestedActions.push('Practice gratitude');
      suggestedActions.push('Journal about positive experiences');
      suggestedActions.push('Share positivity with others');
    } else {
      response = `Thank you for sharing with me. I'm here to listen and support you. Everyone's mental health journey is unique, and I want to provide you with the most helpful guidance.

Could you tell me a bit more about:
• How you're feeling right now
• What's been on your mind lately
• Any specific areas where you'd like support

Remember, there's no judgment here - this is a safe space for you to express yourself.`;
      
      suggestedActions.push('Share more about your feelings');
      suggestedActions.push('Explore wellness modules');
      suggestedActions.push('Set a personal goal');
    }

    // Add professional disclaimer
    response += `\n\n*Please remember: I'm an AI wellness coach designed to provide support and resources. If you're experiencing a mental health crisis or having thoughts of self-harm, please contact a mental health professional, call a crisis hotline, or seek immediate medical attention.*`;

    return {
      response,
      sentimentScore,
      extractedTopics,
      suggestedActions,
      emotionalIndicators,
      confidenceScore: 85,
    };
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async getInteractionStats(userId: string): Promise<{
    totalInteractions: number;
    interactionsThisWeek: number;
    averageRating: number;
    mostCommonTopics: Array<{ topic: string; count: number }>;
    sentimentTrend: Array<{ date: string; sentiment: number }>;
  }> {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const [totalInteractions, interactionsThisWeek, allInteractions] = await Promise.all([
      this.interactionRepository.count({ where: { userId } }),
      this.interactionRepository.count({ 
        where: { 
          userId, 
          createdAt: { $gte: weekAgo } as any 
        } 
      }),
      this.interactionRepository.find({ 
        where: { userId },
        order: { createdAt: 'DESC' },
        take: 100 // Last 100 interactions for analysis
      })
    ]);

    // Calculate average rating
    const ratedInteractions = allInteractions.filter(i => i.userRating !== null && i.userRating !== undefined);
    const averageRating = ratedInteractions.length > 0
      ? ratedInteractions.reduce((sum, i) => sum + i.userRating!, 0) / ratedInteractions.length
      : 0;

    // Calculate most common topics
    const topicCounts: Record<string, number> = {};
    allInteractions.forEach(interaction => {
      interaction.extractedTopics?.forEach(topic => {
        topicCounts[topic] = (topicCounts[topic] || 0) + 1;
      });
    });

    const mostCommonTopics = Object.entries(topicCounts)
      .map(([topic, count]) => ({ topic, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Calculate sentiment trend (last 7 days)
    const sentimentTrend: Array<{ date: string; sentiment: number }> = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      
      const dayInteractions = allInteractions.filter(interaction => {
        const interactionDate = new Date(interaction.createdAt).toISOString().split('T')[0];
        return interactionDate === dateStr && interaction.sentimentScore;
      });

      const averageSentiment = dayInteractions.length > 0
        ? dayInteractions.reduce((sum, i) => sum + (i.sentimentScore || 3), 0) / dayInteractions.length
        : 3; // Default to neutral

      sentimentTrend.push({ date: dateStr, sentiment: averageSentiment });
    }

    return {
      totalInteractions,
      interactionsThisWeek,
      averageRating: Math.round(averageRating * 100) / 100,
      mostCommonTopics,
      sentimentTrend,
    };
  }

  async createSession(userId: string) {
    const sessionRepo = AppDataSource.getRepository(AICoachSession);
    const session = sessionRepo.create({ userId });
    await sessionRepo.save(session);
    if (websocketServiceInstance) {
      websocketServiceInstance.emitToUser(userId, 'ia-message', { event: 'session-started', sessionId: session.id });
    }
    return session;
  }

  async endSession(sessionId: string, feedback?: string, rating?: number, stats?: any) {
    const sessionRepo = AppDataSource.getRepository(AICoachSession);
    const session = await sessionRepo.findOneBy({ id: sessionId });
    if (!session) throw new Error('Session not found');
    session.endTime = new Date();
    if (feedback !== undefined) {
      session.feedback = feedback;
    }
    if (rating !== undefined) {
      session.rating = rating;
    }
    session.stats = stats;
    await sessionRepo.save(session);
    if (websocketServiceInstance) {
      websocketServiceInstance.emitToUser(session.userId, 'ia-message', { event: 'session-ended', sessionId });
    }
    return session;
  }

  async deleteInteraction(userId: string, interactionId: string): Promise<void> {
    // Verify ownership
    const interaction = await this.interactionRepository.findOne({
      where: { id: interactionId, userId }
    });

    if (!interaction) {
      throw new NotFoundError('Interaction not found or not authorized');
    }

    await this.interactionRepository.remove(interaction);
  }

  static async getStats(userId: string) {
    const repo = AppDataSource.getRepository(AICoachSession);
    const sessions = await repo.find({ where: { userId } });
    // Exemples de stats : nombre de sessions, moyenne rating, etc.
    const count = sessions.length;
    const avgRating = sessions.reduce((acc, s) => acc + (s.rating || 0), 0) / (count || 1);
    return { count, avgRating };
  }
}
