import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { JournalEntry, EntryType, MoodLevel } from '../models/JournalEntry';
import { User } from '../models/User';
import { ValidationError, NotFoundError, ForbiddenError } from '../utils/errors';

export interface CreateJournalEntryDto {
  title: string;
  content: string;
  entryType?: EntryType;
  moodLevel?: MoodLevel;
  tags?: string[];
  emotions?: string[];
  stressLevel?: number;
  energyLevel?: number;
  sleepQuality?: number;
  gratitudeNotes?: string;
  goals?: string;
  challenges?: string;
  achievements?: string;
  isPrivate?: boolean;
}

export interface UpdateJournalEntryDto {
  title?: string;
  content?: string;
  entryType?: EntryType;
  moodLevel?: MoodLevel;
  tags?: string[];
  emotions?: string[];
  stressLevel?: number;
  energyLevel?: number;
  sleepQuality?: number;
  gratitudeNotes?: string;
  goals?: string;
  challenges?: string;
  achievements?: string;
  isPrivate?: boolean;
  isFavorite?: boolean;
}

export interface JournalFilters {
  entryType?: EntryType;
  moodLevel?: MoodLevel;
  tags?: string[];
  emotions?: string[];
  startDate?: Date;
  endDate?: Date;
  isPrivate?: boolean;
  isFavorite?: boolean;
}

export class JournalService {
  private journalRepository: Repository<JournalEntry>;
  private userRepository: Repository<User>;

  constructor() {
    this.journalRepository = AppDataSource.getRepository(JournalEntry);
    this.userRepository = AppDataSource.getRepository(User);
  }

  async createEntry(userId: string, data: CreateJournalEntryDto): Promise<JournalEntry> {
    // Validate user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Validate input
    if (!data.title || data.title.trim().length === 0) {
      throw new ValidationError('Title is required');
    }

    if (!data.content || data.content.trim().length === 0) {
      throw new ValidationError('Content is required');
    }

    if (data.title.length > 200) {
      throw new ValidationError('Title must be 200 characters or less');
    }

    // Validate numeric ranges
    if (data.stressLevel !== undefined && (data.stressLevel < 0 || data.stressLevel > 10)) {
      throw new ValidationError('Stress level must be between 0 and 10');
    }

    if (data.energyLevel !== undefined && (data.energyLevel < 0 || data.energyLevel > 10)) {
      throw new ValidationError('Energy level must be between 0 and 10');
    }

    if (data.sleepQuality !== undefined && (data.sleepQuality < 0 || data.sleepQuality > 10)) {
      throw new ValidationError('Sleep quality must be between 0 and 10');
    }

    const entry = this.journalRepository.create({
      userId,
      title: data.title.trim(),
      content: data.content.trim(),
      entryType: data.entryType || EntryType.DAILY,
      moodLevel: data.moodLevel,
      tags: data.tags || [],
      emotions: data.emotions || [],
      stressLevel: data.stressLevel,
      energyLevel: data.energyLevel,
      sleepQuality: data.sleepQuality,
      gratitudeNotes: data.gratitudeNotes?.trim(),
      goals: data.goals?.trim(),
      challenges: data.challenges?.trim(),
      achievements: data.achievements?.trim(),
      isPrivate: data.isPrivate || false,
    });

    return await this.journalRepository.save(entry);
  }

  async getEntries(
    userId: string, 
    filters: JournalFilters = {}, 
    page: number = 1, 
    limit: number = 20
  ): Promise<{ entries: JournalEntry[]; total: number; page: number; totalPages: number }> {
    const queryBuilder = this.journalRepository
      .createQueryBuilder('entry')
      .where('entry.userId = :userId', { userId });

    // Apply filters
    if (filters.entryType) {
      queryBuilder.andWhere('entry.entryType = :entryType', { entryType: filters.entryType });
    }

    if (filters.moodLevel) {
      queryBuilder.andWhere('entry.moodLevel = :moodLevel', { moodLevel: filters.moodLevel });
    }

    if (filters.isPrivate !== undefined) {
      queryBuilder.andWhere('entry.isPrivate = :isPrivate', { isPrivate: filters.isPrivate });
    }

    if (filters.isFavorite !== undefined) {
      queryBuilder.andWhere('entry.isFavorite = :isFavorite', { isFavorite: filters.isFavorite });
    }

    if (filters.startDate) {
      queryBuilder.andWhere('entry.createdAt >= :startDate', { startDate: filters.startDate });
    }

    if (filters.endDate) {
      queryBuilder.andWhere('entry.createdAt <= :endDate', { endDate: filters.endDate });
    }

    if (filters.tags && filters.tags.length > 0) {
      queryBuilder.andWhere('JSON_EXTRACT(entry.tags, "$") LIKE :tags', { 
        tags: `%${filters.tags.join('%')}%` 
      });
    }

    if (filters.emotions && filters.emotions.length > 0) {
      queryBuilder.andWhere('JSON_EXTRACT(entry.emotions, "$") LIKE :emotions', { 
        emotions: `%${filters.emotions.join('%')}%` 
      });
    }

    // Order by creation date (newest first)
    queryBuilder.orderBy('entry.createdAt', 'DESC');

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const entries = await queryBuilder.getMany();

    return {
      entries,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getEntryById(userId: string, entryId: string): Promise<JournalEntry> {
    const entry = await this.journalRepository.findOne({
      where: { id: entryId, userId },
    });

    if (!entry) {
      throw new NotFoundError('Journal entry not found');
    }

    return entry;
  }

  async updateEntry(userId: string, entryId: string, data: UpdateJournalEntryDto): Promise<JournalEntry> {
    const entry = await this.getEntryById(userId, entryId);

    // Validate input if provided
    if (data.title !== undefined) {
      if (!data.title || data.title.trim().length === 0) {
        throw new ValidationError('Title cannot be empty');
      }
      if (data.title.length > 200) {
        throw new ValidationError('Title must be 200 characters or less');
      }
      entry.title = data.title.trim();
    }

    if (data.content !== undefined) {
      if (!data.content || data.content.trim().length === 0) {
        throw new ValidationError('Content cannot be empty');
      }
      entry.content = data.content.trim();
    }

    // Update other fields
    if (data.entryType !== undefined) entry.entryType = data.entryType;
    if (data.moodLevel !== undefined) entry.moodLevel = data.moodLevel;
    if (data.tags !== undefined) entry.tags = data.tags;
    if (data.emotions !== undefined) entry.emotions = data.emotions;
    if (data.stressLevel !== undefined) entry.stressLevel = data.stressLevel;
    if (data.energyLevel !== undefined) entry.energyLevel = data.energyLevel;
    if (data.sleepQuality !== undefined) entry.sleepQuality = data.sleepQuality;
    if (data.gratitudeNotes !== undefined) entry.gratitudeNotes = data.gratitudeNotes?.trim();
    if (data.goals !== undefined) entry.goals = data.goals?.trim();
    if (data.challenges !== undefined) entry.challenges = data.challenges?.trim();
    if (data.achievements !== undefined) entry.achievements = data.achievements?.trim();
    if (data.isPrivate !== undefined) entry.isPrivate = data.isPrivate;
    if (data.isFavorite !== undefined) entry.isFavorite = data.isFavorite;

    return await this.journalRepository.save(entry);
  }

  async deleteEntry(userId: string, entryId: string): Promise<void> {
    const entry = await this.getEntryById(userId, entryId);
    await this.journalRepository.remove(entry);
  }

  async getEntryStats(userId: string): Promise<{
    totalEntries: number;
    entriesThisWeek: number;
    entriesThisMonth: number;
    averageMoodLevel: number;
    averageStressLevel: number;
    averageEnergyLevel: number;
    mostUsedTags: Array<{ tag: string; count: number }>;
    mostUsedEmotions: Array<{ emotion: string; count: number }>;
  }> {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [
      totalEntries,
      entriesThisWeek,
      entriesThisMonth,
      allEntries
    ] = await Promise.all([
      this.journalRepository.count({ where: { userId } }),
      this.journalRepository.count({ 
        where: { 
          userId, 
          createdAt: { $gte: weekAgo } as any 
        } 
      }),
      this.journalRepository.count({ 
        where: { 
          userId, 
          createdAt: { $gte: monthAgo } as any 
        } 
      }),
      this.journalRepository.find({ where: { userId } })
    ]);

    // Calculate averages
    const entriesWithMood = allEntries.filter(e => e.moodLevel !== null && e.moodLevel !== undefined);
    const entriesWithStress = allEntries.filter(e => e.stressLevel !== null && e.stressLevel !== undefined);
    const entriesWithEnergy = allEntries.filter(e => e.energyLevel !== null && e.energyLevel !== undefined);

    const averageMoodLevel = entriesWithMood.length > 0 
      ? entriesWithMood.reduce((sum, e) => sum + e.moodLevel!, 0) / entriesWithMood.length 
      : 0;

    const averageStressLevel = entriesWithStress.length > 0 
      ? entriesWithStress.reduce((sum, e) => sum + e.stressLevel!, 0) / entriesWithStress.length 
      : 0;

    const averageEnergyLevel = entriesWithEnergy.length > 0 
      ? entriesWithEnergy.reduce((sum, e) => sum + e.energyLevel!, 0) / entriesWithEnergy.length 
      : 0;

    // Calculate most used tags and emotions
    const tagCounts: Record<string, number> = {};
    const emotionCounts: Record<string, number> = {};

    allEntries.forEach(entry => {
      entry.tags?.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
      entry.emotions?.forEach(emotion => {
        emotionCounts[emotion] = (emotionCounts[emotion] || 0) + 1;
      });
    });

    const mostUsedTags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const mostUsedEmotions = Object.entries(emotionCounts)
      .map(([emotion, count]) => ({ emotion, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalEntries,
      entriesThisWeek,
      entriesThisMonth,
      averageMoodLevel: Math.round(averageMoodLevel * 100) / 100,
      averageStressLevel: Math.round(averageStressLevel * 100) / 100,
      averageEnergyLevel: Math.round(averageEnergyLevel * 100) / 100,
      mostUsedTags,
      mostUsedEmotions,
    };
  }
}
