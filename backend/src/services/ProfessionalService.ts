import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { ProfessionalProfile, ProfessionalType, VerificationStatus } from '../models/ProfessionalProfile';
import { User } from '../models/User';
import { ValidationError, NotFoundError } from '../utils/errors';

export interface CreateProfessionalProfileDto {
  professionalType: ProfessionalType;
  licenseNumber: string;
  licenseExpiryDate: Date;
  licensingBoard: string;
  licensingState: string;
  specializations: string[];
  treatmentApproaches?: string[];
  languagesSpoken?: string[];
  yearsOfExperience: number;
  professionalBio: string;
  education?: Array<{
    degree: string;
    institution: string;
    year: number;
    field: string;
  }>;
  certifications?: Array<{
    name: string;
    issuingOrganization: string;
    issueDate: Date;
    expiryDate?: Date;
    credentialId?: string;
  }>;
  workExperience?: Array<{
    position: string;
    organization: string;
    startDate: Date;
    endDate?: Date;
    description: string;
  }>;
  availability?: {
    timezone: string;
    schedule: Record<string, Array<{
      startTime: string;
      endTime: string;
    }>>;
    exceptions?: Array<{
      date: Date;
      reason: string;
      isAvailable: boolean;
    }>;
  };
  consultationTypes?: Array<{
    type: 'video' | 'phone' | 'chat' | 'in_person';
    duration: number;
    price: number;
    currency: string;
  }>;
  hourlyRate?: number;
  currency?: string;
  acceptingNewClients?: boolean;
  maxClientsPerWeek?: number;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  insuranceAccepted?: string[];
  specialNotes?: string;
}

export interface ProfessionalFilters {
  professionalType?: ProfessionalType;
  specializations?: string[];
  languagesSpoken?: string[];
  acceptingNewClients?: boolean;
  consultationType?: 'video' | 'phone' | 'chat' | 'in_person';
  minRating?: number;
  maxHourlyRate?: number;
  location?: {
    city?: string;
    state?: string;
    country?: string;
  };
  verificationStatus?: VerificationStatus;
  isActive?: boolean;
}

export interface ProfessionalSearchResult {
  professionals: ProfessionalProfile[];
  total: number;
  page: number;
  totalPages: number;
}

export class ProfessionalService {
  private professionalRepository: Repository<ProfessionalProfile>;
  private userRepository: Repository<User>;

  constructor() {
    this.professionalRepository = AppDataSource.getRepository(ProfessionalProfile);
    this.userRepository = AppDataSource.getRepository(User);
  }

  async createProfessionalProfile(userId: string, data: CreateProfessionalProfileDto): Promise<ProfessionalProfile> {
    // Validate user exists and doesn't already have a professional profile
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const existingProfile = await this.professionalRepository.findOne({ where: { userId } });
    if (existingProfile) {
      throw new ValidationError('User already has a professional profile');
    }

    // Validate required fields
    if (!data.licenseNumber || data.licenseNumber.trim().length === 0) {
      throw new ValidationError('License number is required');
    }

    if (!data.professionalBio || data.professionalBio.trim().length < 50) {
      throw new ValidationError('Professional bio must be at least 50 characters');
    }

    if (!data.specializations || data.specializations.length === 0) {
      throw new ValidationError('At least one specialization is required');
    }

    // Create professional profile
    const profile = this.professionalRepository.create({
      userId,
      professionalType: data.professionalType,
      licenseNumber: data.licenseNumber.trim(),
      licenseExpiryDate: data.licenseExpiryDate,
      licensingBoard: data.licensingBoard.trim(),
      licensingState: data.licensingState.trim(),
      verificationStatus: VerificationStatus.PENDING,
      specializations: data.specializations,
      treatmentApproaches: data.treatmentApproaches || [],
      languagesSpoken: data.languagesSpoken || ['English'],
      yearsOfExperience: data.yearsOfExperience,
      professionalBio: data.professionalBio.trim(),
      education: data.education || [],
      certifications: data.certifications || [],
      workExperience: data.workExperience || [],
      availability: data.availability,
      consultationTypes: data.consultationTypes || [],
      hourlyRate: data.hourlyRate,
      currency: data.currency || 'USD',
      acceptingNewClients: data.acceptingNewClients !== false,
      maxClientsPerWeek: data.maxClientsPerWeek,
      emergencyContact: data.emergencyContact,
      insuranceAccepted: data.insuranceAccepted || [],
      specialNotes: data.specialNotes?.trim(),
      isActive: true,
    });

    return await this.professionalRepository.save(profile);
  }

  async getProfessionals(
    filters: ProfessionalFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<ProfessionalSearchResult> {
    const queryBuilder = this.professionalRepository
      .createQueryBuilder('professional')
      .leftJoinAndSelect('professional.user', 'user')
      .where('professional.isActive = :isActive', { isActive: true })
      .andWhere('professional.verificationStatus = :verificationStatus', { 
        verificationStatus: filters.verificationStatus || VerificationStatus.VERIFIED 
      });

    // Apply filters
    if (filters.professionalType) {
      queryBuilder.andWhere('professional.professionalType = :professionalType', {
        professionalType: filters.professionalType
      });
    }

    if (filters.acceptingNewClients !== undefined) {
      queryBuilder.andWhere('professional.acceptingNewClients = :acceptingNewClients', {
        acceptingNewClients: filters.acceptingNewClients
      });
    }

    if (filters.specializations && filters.specializations.length > 0) {
      queryBuilder.andWhere(
        'JSON_EXTRACT(professional.specializations, "$") REGEXP :specializations',
        { specializations: filters.specializations.join('|') }
      );
    }

    if (filters.languagesSpoken && filters.languagesSpoken.length > 0) {
      queryBuilder.andWhere(
        'JSON_EXTRACT(professional.languagesSpoken, "$") REGEXP :languages',
        { languages: filters.languagesSpoken.join('|') }
      );
    }

    if (filters.minRating) {
      queryBuilder.andWhere('professional.averageRating >= :minRating', {
        minRating: filters.minRating
      });
    }

    if (filters.maxHourlyRate) {
      queryBuilder.andWhere('professional.hourlyRate <= :maxHourlyRate', {
        maxHourlyRate: filters.maxHourlyRate
      });
    }

    if (filters.consultationType) {
      queryBuilder.andWhere(
        'JSON_EXTRACT(professional.consultationTypes, "$[*].type") LIKE :consultationType',
        { consultationType: `%${filters.consultationType}%` }
      );
    }

    // Location filters (currently disabled as User model doesn't have location fields)
    // TODO: Add location fields to User model or create a separate Location entity
    // if (filters.location?.city) {
    //   queryBuilder.andWhere('user.city = :city', { city: filters.location.city });
    // }
    // if (filters.location?.state) {
    //   queryBuilder.andWhere('user.state = :state', { state: filters.location.state });
    // }
    // if (filters.location?.country) {
    //   queryBuilder.andWhere('user.country = :country', { country: filters.location.country });
    // }

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Order by rating and total appointments
    queryBuilder.orderBy('professional.averageRating', 'DESC')
                .addOrderBy('professional.totalAppointments', 'DESC');

    const [professionals, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      professionals,
      total,
      page,
      totalPages
    };
  }

  async getProfessionalById(professionalId: string): Promise<ProfessionalProfile> {
    const professional = await this.professionalRepository.findOne({
      where: { id: professionalId, isActive: true },
      relations: ['user']
    });
    
    if (!professional) {
      throw new NotFoundError('Professional not found');
    }
    
    return professional;
  }

  async getProfessionalByUserId(userId: string): Promise<ProfessionalProfile | null> {
    return await this.professionalRepository.findOne({
      where: { userId },
      relations: ['user']
    });
  }

  async updateProfessionalProfile(
    professionalId: string, 
    data: Partial<CreateProfessionalProfileDto>
  ): Promise<ProfessionalProfile> {
    const professional = await this.getProfessionalById(professionalId);

    // Update fields
    Object.keys(data).forEach(key => {
      if (data[key as keyof CreateProfessionalProfileDto] !== undefined) {
        (professional as any)[key] = data[key as keyof CreateProfessionalProfileDto];
      }
    });

    // If critical information is updated, reset verification status
    const criticalFields = ['licenseNumber', 'professionalType', 'licensingBoard', 'licensingState'];
    const hasCriticalChanges = criticalFields.some(field => 
      data[field as keyof CreateProfessionalProfileDto] !== undefined
    );

    if (hasCriticalChanges && professional.verificationStatus === VerificationStatus.VERIFIED) {
      professional.verificationStatus = VerificationStatus.PENDING;
      professional.verifiedAt = undefined;
      professional.verifiedBy = undefined;
    }

    return await this.professionalRepository.save(professional);
  }

  async verifyProfessional(
    professionalId: string,
    verifiedBy: string,
    notes?: string
  ): Promise<ProfessionalProfile> {
    const updateData = {
      verificationStatus: VerificationStatus.VERIFIED,
      verifiedAt: new Date(),
      verifiedBy,
      verificationNotes: notes
    };

    await this.professionalRepository.update(professionalId, updateData);
    return await this.getProfessionalById(professionalId);
  }

  async getProfessionalStats(): Promise<{
    totalProfessionals: number;
    verifiedProfessionals: number;
    pendingVerification: number;
    professionalsByType: Array<{ type: string; count: number }>;
    averageRating: number;
    totalAppointments: number;
  }> {
    const [
      totalProfessionals,
      verifiedProfessionals,
      pendingVerification,
      allProfessionals
    ] = await Promise.all([
      this.professionalRepository.count({ where: { isActive: true } }),
      this.professionalRepository.count({ 
        where: { 
          isActive: true, 
          verificationStatus: VerificationStatus.VERIFIED 
        } 
      }),
      this.professionalRepository.count({ 
        where: { 
          isActive: true, 
          verificationStatus: VerificationStatus.PENDING 
        } 
      }),
      this.professionalRepository.find({ where: { isActive: true } })
    ]);

    // Count by type
    const typeCount: Record<string, number> = {};
    allProfessionals.forEach(prof => {
      typeCount[prof.professionalType] = (typeCount[prof.professionalType] || 0) + 1;
    });

    // Calculate average rating
    const professionalsWithRatings = allProfessionals.filter(p => p.totalReviews > 0);
    const averageRating = professionalsWithRatings.length > 0
      ? professionalsWithRatings.reduce((sum, p) => sum + p.averageRating, 0) / professionalsWithRatings.length
      : 0;

    // Calculate total appointments
    const totalAppointments = allProfessionals.reduce((sum, p) => sum + p.totalAppointments, 0);

    return {
      totalProfessionals,
      verifiedProfessionals,
      pendingVerification,
      professionalsByType: Object.entries(typeCount).map(([type, count]) => ({ type, count })),
      averageRating: Math.round(averageRating * 100) / 100,
      totalAppointments,
    };
  }

  async searchProfessionals(
    searchTerm: string,
    filters: ProfessionalFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{ professionals: ProfessionalProfile[]; total: number; page: number; totalPages: number }> {
    const queryBuilder = this.professionalRepository
      .createQueryBuilder('profile')
      .leftJoinAndSelect('profile.user', 'user')
      .where('profile.isActive = :isActive', { isActive: true })
      .andWhere('profile.verificationStatus = :verificationStatus', { 
        verificationStatus: VerificationStatus.VERIFIED 
      });

    // Add search conditions
    if (searchTerm && searchTerm.trim().length > 0) {
      queryBuilder.andWhere(`(
        user.firstName LIKE :searchTerm OR 
        user.lastName LIKE :searchTerm OR 
        profile.professionalBio LIKE :searchTerm OR
        JSON_EXTRACT(profile.specializations, '$') LIKE :searchTerm OR
        JSON_EXTRACT(profile.treatmentApproaches, '$') LIKE :searchTerm
      )`, { searchTerm: `%${searchTerm.trim()}%` });
    }

    // Apply other filters (reuse logic from getProfessionals)
    // ... (same filter logic as above)

    // Order by relevance and rating
    queryBuilder.orderBy('profile.averageRating', 'DESC')
               .addOrderBy('profile.totalAppointments', 'DESC');

    const total = await queryBuilder.getCount();
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const professionals = await queryBuilder.getMany();

    return {
      professionals,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getFilterOptions(): Promise<{
    professionalTypes: string[];
    specializations: string[];
    languages: string[];
  }> {
    // Get unique values for filter dropdowns
    const professionalTypes = Object.values(ProfessionalType);
    
    // Get common specializations (simplified for now)
    const specializations = [
      'anxiety',
      'depression',
      'trauma',
      'relationships',
      'stress_management',
      'grief_counseling',
      'addiction',
      'eating_disorders',
      'sleep_disorders',
      'anger_management'
    ];

    // Get common languages (simplified for now)
    const languages = [
      'english',
      'spanish',
      'french',
      'mandarin',
      'arabic',
      'german',
      'portuguese',
      'italian',
      'japanese',
      'korean'
    ];

    return {
      professionalTypes,
      specializations,
      languages
    };
  }

  async getStatistics(): Promise<{
    totalProfessionals: number;
    verifiedProfessionals: number;
    acceptingNewClients: number;
    averageRating: number;
    byProfessionalType: Record<string, number>;
    bySpecialization: Record<string, number>;
  }> {
    const totalProfessionals = await this.professionalRepository.count({
      where: { isActive: true }
    });

    const verifiedProfessionals = await this.professionalRepository.count({
      where: { 
        isActive: true,
        verificationStatus: VerificationStatus.VERIFIED
      }
    });

    const acceptingNewClients = await this.professionalRepository.count({
      where: { 
        isActive: true,
        acceptingNewClients: true
      }
    });

    const ratingQuery = await this.professionalRepository
      .createQueryBuilder('professional')
      .select('AVG(professional.averageRating)', 'avgRating')
      .where('professional.isActive = :isActive', { isActive: true })
      .andWhere('professional.totalReviews > 0')
      .getRawOne();

    const averageRating = parseFloat(ratingQuery?.avgRating || '0');

    // Group by professional type
    const typeQuery = await this.professionalRepository
      .createQueryBuilder('professional')
      .select('professional.professionalType', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('professional.isActive = :isActive', { isActive: true })
      .groupBy('professional.professionalType')
      .getRawMany();

    const byProfessionalType = typeQuery.reduce((acc, row) => {
      acc[row.type] = parseInt(row.count);
      return acc;
    }, {} as Record<string, number>);

    // This is a simplified version for specializations
    const bySpecialization: Record<string, number> = {
      'anxiety': 0,
      'depression': 0,
      'trauma': 0,
      'relationships': 0
    };

    return {
      totalProfessionals,
      verifiedProfessionals,
      acceptingNewClients,
      averageRating: Math.round(averageRating * 100) / 100,
      byProfessionalType,
      bySpecialization
    };
  }
}
