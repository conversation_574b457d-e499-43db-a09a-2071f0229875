import { WebSocketService } from './websocketService';
import { Server as HTTPServer } from 'http';

let websocketServiceInstance: WebSocketService | null = null;

export function initWebSocketService(server: HTTPServer): WebSocketService {
  websocketServiceInstance = new WebSocketService(server);
  return websocketServiceInstance;
}

export function getWebSocketServiceInstance(): WebSocketService | null {
  return websocketServiceInstance;
}

export { websocketServiceInstance }; 