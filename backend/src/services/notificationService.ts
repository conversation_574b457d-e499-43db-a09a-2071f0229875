import { websocketServiceInstance } from './websocketInstance';
import {
  AppointmentReminderData,
  AppointmentStatusData,
  ProfessionalAvailabilityData,
  AppointmentBookingData,
  CrisisAlertData,
  EmergencyContactData,
  CrisisResourceData,
  MessageData,
  MessageReadData,
  TypingIndicatorData,
  UnreadCountData,
  DashboardUpdateData,
  MoodTrackingData,
  WellnessProgressData,
} from './websocketService';
import { AppDataSource } from '../config/database';
import { Notification, NotificationType } from '../models/Notification';
import { User } from '../models/User';

export class NotificationService {
  // Appointment Notifications
  public async sendAppointmentReminder(
    userId: string,
    reminderData: AppointmentReminderData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
        websocketServiceInstance.emitToUser(userId, 'appointment_reminder', reminderData);
        console.log(`📅 Appointment reminder sent to user ${userId}: ${reminderData.reminderType}`);
      }
    } catch (error) {
      console.error('Error sending appointment reminder:', error);
    }
  }

  public async sendAppointmentStatusUpdate(
    userId: string,
    statusData: AppointmentStatusData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
        websocketServiceInstance.emitToUser(userId, 'appointment_status_update', statusData);
        console.log(`📋 Appointment status update sent to user ${userId}: ${statusData.status}`);
      }
    } catch (error) {
      console.error('Error sending appointment status update:', error);
    }
  }

  public async sendProfessionalAvailabilityChange(
    availabilityData: ProfessionalAvailabilityData
  ): Promise<void> {
    try {
      if (websocketServiceInstance) {
        // Send to all users who might be interested in this professional
        websocketServiceInstance.emitToRole('user', 'professional_availability_change', availabilityData);
        console.log(`👨‍⚕️ Professional availability update: ${availabilityData.professionalName} - ${availabilityData.isOnline ? 'Online' : 'Offline'}`);
      }
    } catch (error) {
      console.error('Error sending professional availability change:', error);
    }
  }

  public async sendAppointmentBookingConfirmation(
    userId: string,
    bookingData: AppointmentBookingData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
        websocketServiceInstance.emitToUser(userId, 'appointment_booking_confirmation', bookingData);
        console.log(`✅ Appointment booking confirmation sent to user ${userId}`);
      }
    } catch (error) {
      console.error('Error sending appointment booking confirmation:', error);
    }
  }

  // Crisis Alert Notifications
  public async sendCrisisAlert(
    userId: string,
    alertData: CrisisAlertData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
        websocketServiceInstance.emitToUser(userId, 'crisis_alert', alertData);
        console.log(`🚨 Crisis alert sent to user ${userId}: ${alertData.severity}`);
      }
    } catch (error) {
      console.error('Error sending crisis alert:', error);
    }
  }

  public async sendEmergencyContactNotification(
    userId: string,
    contactData: EmergencyContactData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
        websocketServiceInstance.emitToUser(userId, 'emergency_contact_notification', contactData);
        console.log(`📞 Emergency contact notification sent to user ${userId}`);
      }
    } catch (error) {
      console.error('Error sending emergency contact notification:', error);
    }
  }

  public async sendCrisisResourceRecommendation(
    userId: string,
    resourceData: CrisisResourceData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
        websocketServiceInstance.emitToUser(userId, 'crisis_resource_recommendation', resourceData);
        console.log(`📚 Crisis resource recommendation sent to user ${userId}`);
      }
    } catch (error) {
      console.error('Error sending crisis resource recommendation:', error);
    }
  }

  // Messaging Notifications
  public async sendNewMessage(
    recipientId: string,
    messageData: MessageData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(recipientId)) {
        websocketServiceInstance.emitToUser(recipientId, 'new_message', messageData);
        console.log(`💬 New message sent to user ${recipientId} from ${messageData.senderName}`);
      }
    } catch (error) {
      console.error('Error sending new message notification:', error);
    }
  }

  public async sendMessageRead(
    senderId: string,
    readData: MessageReadData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(senderId)) {
        websocketServiceInstance.emitToUser(senderId, 'message_read', readData);
        console.log(`👁️ Message read notification sent to user ${senderId}`);
      }
    } catch (error) {
      console.error('Error sending message read notification:', error);
    }
  }

  public async sendTypingIndicator(
    conversationParticipants: string[],
    typingData: TypingIndicatorData
  ): Promise<void> {
    if (!websocketServiceInstance) {
      console.warn('WebSocket service not available for typing indicator');
      return;
    }

    try {
      // Envoyer l'indicateur de saisie à tous les participants sauf l'expéditeur
      conversationParticipants.forEach(participantId => {
        if (participantId !== typingData.userId && websocketServiceInstance && websocketServiceInstance.isUserConnected(participantId)) {
          websocketServiceInstance.emitToUser(participantId, 'typing_indicator', typingData);
        }
      });
    } catch (error) {
      console.error('Error sending typing indicator:', error);
    }
  }

  public async sendUnreadCountUpdate(
    userId: string,
    unreadData: UnreadCountData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
        websocketServiceInstance.emitToUser(userId, 'unread_count_update', unreadData);
        console.log(`🔢 Unread count update sent to user ${userId}: ${unreadData.totalUnreadCount}`);
      }
    } catch (error) {
      console.error('Error sending unread count update:', error);
    }
  }

  // Dashboard Updates
  public async sendDashboardUpdate(
    userId: string,
    updateData: DashboardUpdateData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
        websocketServiceInstance.emitToUser(userId, 'dashboard_update', updateData);
        console.log(`📊 Dashboard update sent to user ${userId}: ${updateData.type}`);
      }
    } catch (error) {
      console.error('Error sending dashboard update:', error);
    }
  }

  public async sendMoodTrackingUpdate(
    userId: string,
    moodData: MoodTrackingData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
        websocketServiceInstance.emitToUser(userId, 'mood_tracking_update', moodData);
        console.log(`😊 Mood tracking update sent to user ${userId}`);
      }
    } catch (error) {
      console.error('Error sending mood tracking update:', error);
    }
  }

  public async sendWellnessProgressUpdate(
    userId: string,
    progressData: WellnessProgressData
  ): Promise<void> {
    try {
      if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
        websocketServiceInstance.emitToUser(userId, 'wellness_progress_update', progressData);
        console.log(`🌱 Wellness progress update sent to user ${userId}: ${progressData.moduleName}`);
      }
    } catch (error) {
      console.error('Error sending wellness progress update:', error);
    }
  }

  // Bulk Notifications
  public async sendBulkNotification(
    userIds: string[],
    event: string,
    data: any
  ): Promise<void> {
    try {
      if (websocketServiceInstance) {
        userIds.forEach(userId => {
          if (websocketServiceInstance && websocketServiceInstance.isUserConnected(userId)) {
            websocketServiceInstance.emitToUser(userId, event as any, data);
          }
        });
        console.log(`📢 Bulk notification sent to ${userIds.length} users: ${event}`);
      }
    } catch (error) {
      console.error('Error sending bulk notification:', error);
    }
  }

  // Utility Methods
  public getConnectedUsersCount(): number {
    if (websocketServiceInstance) {
      return websocketServiceInstance.getConnectedUsers().length;
    }
    return 0;
  }

  public isUserOnline(userId: string): boolean {
    if (websocketServiceInstance) {
      return websocketServiceInstance.isUserConnected(userId);
    }
    return false;
  }

  public getConnectedUsers(): any[] {
    if (!websocketServiceInstance) return [];
    return websocketServiceInstance.getConnectedUsers().map(user => ({
        userId: user.userId,
        userRole: user.userRole,
        userType: user.userType,
        connectedAt: user.joinedAt,
        lastActivity: user.lastActivity,
        isOnline: user.isOnline
      }));
  }

  // Scheduled Notifications (for appointment reminders)
  public scheduleAppointmentReminders(): void {
    // This would typically be called by a cron job or scheduler
    // For now, we'll implement a simple interval-based check
    setInterval(async () => {
      try {
        await this.checkAndSendAppointmentReminders();
      } catch (error) {
        console.error('Error in scheduled appointment reminders:', error);
      }
    }, 60000); // Check every minute
  }

  private async checkAndSendAppointmentReminders(): Promise<void> {
    // This would query the database for upcoming appointments
    // and send appropriate reminders based on timing
    // Implementation would depend on your appointment model structure
    console.log('🔍 Checking for appointment reminders...');
  }

  static async create(
    userId: string,
    type: NotificationType,
    title: string,
    message: string,
    options?: Partial<Notification>
  ) {
    const repo = AppDataSource.getRepository(Notification);
    const notification = repo.create({
      userId,
      type,
      title,
      message,
      ...options,
      isRead: false,
    });
    await repo.save(notification);
    this.sendWebSocketNotification(userId, notification);
    return notification;
  }

  static async getAllForUser(userId: string) {
    const repo = AppDataSource.getRepository(Notification);
    return repo.find({ where: { userId }, order: { createdAt: 'DESC' } });
  }

  static async markAsRead(notificationId: string, userId: string) {
    const repo = AppDataSource.getRepository(Notification);
    const notif = await repo.findOneBy({ id: notificationId, userId });
    if (notif) {
      notif.isRead = true;
      await repo.save(notif);
      NotificationService.sendWebSocketNotification(userId, notif, 'read');
    }
    return notif;
  }

  static async delete(notificationId: string, userId: string) {
    const repo = AppDataSource.getRepository(Notification);
    const notif = await repo.findOneBy({ id: notificationId, userId });
    if (notif) {
      await repo.remove(notif);
      NotificationService.sendWebSocketNotification(userId, notif, 'deleted');
    }
    return notif;
  }

  static async markAllAsRead(userId: string) {
    const repo = AppDataSource.getRepository(Notification);
    await repo.update({ userId, isRead: false }, { isRead: true });
  }

  static async deleteAll(userId: string) {
    const repo = AppDataSource.getRepository(Notification);
    await repo.delete({ userId });
  }

  static sendWebSocketNotification(userId: string, notification: Notification, event: string = 'new') {
    if (!websocketServiceInstance) return;
    websocketServiceInstance.emitToUser(userId, 'notification', { event, notification });
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
export default NotificationService;
