import { AppDataSource } from '../config/database';
import { User, UserStatus } from '../models/User';
import { Role } from '../models/Role';
import { JwtUtils, TokenPair } from '../utils/jwt';
import {
  AuthenticationError,
  ConflictError,
  ValidationError,
  NotFoundError
} from '../utils/errors';
import { validate } from 'class-validator';
import { Session } from '../models/Session';
import bcrypt from 'bcrypt';
import jwt, { SignOptions } from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';

export interface RegisterDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface LoginDto {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: Partial<User>;
  tokens: TokenPair;
}

const userRepo = AppDataSource.getRepository(User);
const sessionRepo = AppDataSource.getRepository(Session);

const JWT_SECRET: string = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN: string = process.env.JWT_EXPIRES_IN || '24h';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

export class AuthService {
  private roleRepository = AppDataSource.getRepository(Role);

  /** Inscription utilisateur */
  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    const { email, password, firstName, lastName, phone } = registerDto;

    // Check if user already exists
    const existingUser = await userRepo.findOne({
      where: { email: email.toLowerCase() },
    });

    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    // Get default user role
    const userRole = await this.roleRepository.findOne({
      where: { name: 'user' },
    });

    if (!userRole) {
      throw new ValidationError('Default user role not found');
    }

    // Create new user
    const user = userRepo.create({
      email: email.toLowerCase(),
      password,
      firstName,
      lastName,
      phone,
      role: userRole,
      status: UserStatus.PENDING_VERIFICATION,
    });

    // Validate user data
    const errors = await validate(user);
    if (errors.length > 0) {
      const errorMessages = errors.map(error =>
        Object.values(error.constraints || {}).join(', ')
      ).join('; ');
      throw new ValidationError(`Validation failed: ${errorMessages}`);
    }

    // Save user
    const savedUser = await userRepo.save(user);

    // Generate tokens
    const tokens = JwtUtils.generateTokenPair({
      userId: savedUser.id,
      email: savedUser.email,
      roleId: savedUser.roleId,
      roleName: userRole.name,
    });

    return {
      user: savedUser.toJSON(),
      tokens,
    };
  }

  /** Connexion utilisateur */
  async login(loginDto: LoginDto, userAgent: string, ip: string): Promise<AuthResponse> {
    const { email, password } = loginDto;

    // Find user with role
    const user = await userRepo.findOne({
      where: { email: email.toLowerCase() },
      relations: ['role'],
    });

    if (!user) {
      throw new AuthenticationError('Invalid email or password');
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      throw new AuthenticationError('Invalid email or password');
    }

    // Check if user is active
    if (user.status !== UserStatus.ACTIVE) {
      throw new AuthenticationError('Account is not active');
    }

    // Update last login
    user.lastLoginAt = new Date();
    await userRepo.save(user);

    // Generate tokens
    const signOptions: SignOptions = { expiresIn: JWT_EXPIRES_IN as any };
    const accessToken = jwt.sign({ userId: user.id }, JWT_SECRET, signOptions);
    const refreshToken = uuidv4();

    // Créer session
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    const session = sessionRepo.create({
      userId: user.id,
      refreshToken,
      userAgent,
      ip,
      expiresAt,
    });
    await sessionRepo.save(session);

    return {
      user: user.toJSON(),
      tokens: { accessToken, refreshToken },
    };
  }

  /** Rafraîchir le token d'accès */
  async refresh(refreshToken: string, userAgent: string, ip: string): Promise<AuthResponse> {
    const session = await sessionRepo.findOneBy({ refreshToken });
    if (!session || session.expiresAt < new Date()) throw new Error('Refresh token invalide ou expiré');
    const user = await userRepo.findOneBy({ id: session.userId });
    if (!user) throw new Error('Utilisateur non trouvé');

    // Générer nouveau accessToken
    const signOptions: SignOptions = { expiresIn: JWT_EXPIRES_IN as any };
    const accessToken = jwt.sign({ userId: user.id }, JWT_SECRET, signOptions);

    // Optionnel : rotation du refreshToken
    return {
      user: user.toJSON(),
      tokens: { accessToken, refreshToken },
    };
  }

  /** Déconnexion (suppression de la session) */
  async logout(refreshToken: string) {
    await sessionRepo.delete({ refreshToken });
  }

  /** Placeholder 2FA (à implémenter phase avancée) */
  async enable2FA(userId: string, secret: string) {
    await userRepo.update(userId, { is2FAEnabled: true, twoFASecret: secret });
  }

  async getCurrentUser(userId: string): Promise<Partial<User>> {
    const user = await userRepo.findOne({
      where: { id: userId },
      relations: ['role', 'role.permissions'],
    });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    return user.toJSON();
  }
}
