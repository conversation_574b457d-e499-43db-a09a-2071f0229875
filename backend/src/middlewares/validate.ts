import { Request, Response, NextFunction } from 'express';
import { ObjectSchema } from 'joi';

export function validateBody(schema: ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body, { abortEarly: false, stripUnknown: true });
    if (error) {
      res.status(400).json({
        success: false,
        message: 'Validation échouée',
        details: error.details.map((d) => d.message),
      });
      return;
    }
    req.body = value;
    next();
  };
} 