import winston from 'winston';

// Configuration simple du logger Winston
export const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'mindflow-backend' },
  transports: [
    // Écrire les erreurs dans error.log
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    // Écrire tous les logs dans combined.log
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

// Si nous ne sommes pas en production, ajouter aussi un transport console
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

export default logger; 