import { Request, Response, NextFunction } from 'express';
import { logger } from './logger';

export function errorHandler(err: any, req: Request, res: Response, next: NextFunction) {
  logger.error({
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    userId: (req as any).userId || null,
  });
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Erreur serveur',
    details: err.details || undefined,
  });
} 