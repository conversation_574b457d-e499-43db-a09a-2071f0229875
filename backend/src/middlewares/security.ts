import helmet from 'helmet';
import cors from 'cors';
import rateLimit from 'express-rate-limit';

/**
 * Middlewares de sécurité : helmet, CORS strict, rate limiting.
 */
export const securityMiddlewares = [
  helmet(),
  cors({
    origin: process.env.CORS_ORIGIN,
    credentials: true,
  }),
  rateLimit({
    windowMs: (Number(process.env.RATE_LIMIT_WINDOW) || 15) * 60 * 1000,
    max: Number(process.env.RATE_LIMIT_MAX) || 100,
    message: 'Trop de requêtes, réessayez plus tard.',
  }),
]; 