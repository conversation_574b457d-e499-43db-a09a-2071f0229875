import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express, RequestHandler } from 'express';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'MindFlow API',
      version: '1.0.0',
      description: 'API documentation for MindFlow (Santé Mentale)',
    },
    servers: [
      { url: 'http://localhost:4000/api/v1' },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT pour authentification. Ajoutez le header Authorization: Bearer <token>'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            firstName: { type: 'string' },
            lastName: { type: 'string' },
            phone: { type: 'string' },
            status: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
          },
        },
        UserRegister: {
          type: 'object',
          required: ['email', 'password', 'firstName', 'lastName'],
          properties: {
            email: { type: 'string' },
            password: { type: 'string' },
            firstName: { type: 'string' },
            lastName: { type: 'string' },
            phone: { type: 'string' },
          },
        },
        UserLogin: {
          type: 'object',
          required: ['email', 'password'],
          properties: {
            email: { type: 'string' },
            password: { type: 'string' },
          },
        },
        AuthResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            accessToken: { type: 'string' },
            refreshToken: { type: 'string' },
            user: { $ref: '#/components/schemas/User' },
          },
        },
        UserResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            user: { $ref: '#/components/schemas/User' },
          },
        },
        MentalHealthData: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            userId: { type: 'string' },
            type: { type: 'string' },
            data: { type: 'object' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        MentalHealthDataInput: {
          type: 'object',
          required: ['type', 'data'],
          properties: {
            type: { type: 'string' },
            data: { type: 'object' },
          },
        },
        JournalEntry: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            userId: { type: 'string' },
            title: { type: 'string' },
            content: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        JournalEntryInput: {
          type: 'object',
          required: ['title', 'content'],
          properties: {
            title: { type: 'string' },
            content: { type: 'string' },
          },
        },
        Appointment: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            professionalId: { type: 'string' },
            clientId: { type: 'string' },
            appointmentType: { type: 'string' },
            mode: { type: 'string' },
            scheduledAt: { type: 'string', format: 'date-time' },
            durationMinutes: { type: 'integer' },
            status: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        AppointmentInput: {
          type: 'object',
          required: ['professionalId', 'appointmentType', 'mode', 'scheduledAt'],
          properties: {
            professionalId: { type: 'string' },
            appointmentType: { type: 'string' },
            mode: { type: 'string' },
            scheduledAt: { type: 'string', format: 'date-time' },
            durationMinutes: { type: 'integer' },
            clientNotes: { type: 'string' },
            isEmergency: { type: 'boolean' },
          },
        },
        ProfessionalProfile: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            profession: { type: 'string' },
            bio: { type: 'string' },
            rating: { type: 'number' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        ProfessionalProfileInput: {
          type: 'object',
          required: ['name', 'profession'],
          properties: {
            name: { type: 'string' },
            profession: { type: 'string' },
            bio: { type: 'string' },
          },
        },
        WellnessModule: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            title: { type: 'string' },
            description: { type: 'string' },
            difficultyLevel: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        WellnessModuleInput: {
          type: 'object',
          required: ['title'],
          properties: {
            title: { type: 'string' },
            description: { type: 'string' },
            difficultyLevel: { type: 'string' },
          },
        },
        Notification: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            userId: { type: 'string' },
            content: { type: 'string' },
            type: { type: 'string', example: 'info' },
            isRead: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        NotificationInput: {
          type: 'object',
          required: ['title', 'message'],
          properties: {
            title: { type: 'string' },
            message: { type: 'string' },
          },
        },
        NotificationPreferences: {
          type: 'object',
          properties: {
            types: { type: 'array', items: { type: 'string' } },
            categories: { type: 'array', items: { type: 'string' } },
            muteAll: { type: 'boolean' }
          }
        },
        AICoachSession: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            userId: { type: 'string' },
            startedAt: { type: 'string', format: 'date-time' },
            endedAt: { type: 'string', format: 'date-time' },
            feedback: { type: 'string' },
            rating: { type: 'integer' },
            stats: { type: 'object' }
          }
        },
        UserModuleProgress: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            userId: { type: 'string' },
            moduleId: { type: 'string' },
            progress: { type: 'number' },
            feedback: { type: 'string' },
            completedAt: { type: 'string', format: 'date-time' },
            stats: { type: 'object' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
      },
      responses: {
        UnauthorizedError: {
          description: 'Non authentifié',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: false },
                  message: { type: 'string', example: 'Non authentifié' }
                }
              }
            }
          }
        },
        ForbiddenError: {
          description: 'Accès refusé (permission manquante)',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: false },
                  message: { type: 'string', example: 'Permission refusée' }
                }
              }
            }
          }
        }
      },
      paths: {
        '/notifications': {
          get: {
            summary: 'Liste les notifications de l\'utilisateur',
            tags: ['Notifications'],
            security: [{ bearerAuth: [] }],
            responses: {
              200: {
                description: 'Liste des notifications',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        success: { type: 'boolean' },
                        notifications: { type: 'array', items: { $ref: '#/components/schemas/Notification' } }
                      }
                    }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          },
          post: {
            summary: 'Crée une notification (test)',
            tags: ['Notifications'],
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      content: { type: 'string' },
                      type: { type: 'string', example: 'info' }
                    }
                  }
                }
              }
            },
            responses: {
              200: {
                description: 'Notification créée',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        success: { type: 'boolean' },
                        notification: { $ref: '#/components/schemas/Notification' }
                      }
                    }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          }
        },
        '/notifications/{id}/read': {
          patch: {
            summary: 'Marque une notification comme lue',
            tags: ['Notifications'],
            security: [{ bearerAuth: [] }],
            parameters: [
              { name: 'id', in: 'path', required: true, schema: { type: 'string' } }
            ],
            responses: {
              200: {
                description: 'Notification mise à jour',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        success: { type: 'boolean' },
                        notification: { $ref: '#/components/schemas/Notification' }
                      }
                    }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' },
              403: { $ref: '#/components/responses/ForbiddenError' }
            }
          }
        },
        '/notifications/{id}': {
          delete: {
            summary: 'Supprime une notification',
            tags: ['Notifications'],
            security: [{ bearerAuth: [] }],
            parameters: [
              { name: 'id', in: 'path', required: true, schema: { type: 'string' } }
            ],
            responses: {
              200: {
                description: 'Notification supprimée',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        success: { type: 'boolean' },
                        notification: { $ref: '#/components/schemas/Notification' }
                      }
                    }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' },
              403: { $ref: '#/components/responses/ForbiddenError' }
            }
          }
        },
        '/notifications/preferences': {
          get: {
            summary: 'Récupère les préférences de notifications',
            tags: ['Notifications'],
            security: [{ bearerAuth: [] }],
            responses: {
              200: {
                description: 'Préférences utilisateur',
                content: {
                  'application/json': {
                    schema: { $ref: '#/components/schemas/NotificationPreferences' }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          },
          put: {
            summary: 'Met à jour les préférences de notifications',
            tags: ['Notifications'],
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/NotificationPreferences' }
                }
              }
            },
            responses: {
              200: {
                description: 'Préférences mises à jour',
                content: {
                  'application/json': {
                    schema: { $ref: '#/components/schemas/NotificationPreferences' }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          }
        },
        '/notifications/mark-all-read': {
          patch: {
            summary: 'Marque toutes les notifications comme lues',
            tags: ['Notifications'],
            security: [{ bearerAuth: [] }],
            responses: {
              200: { description: 'OK' },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          }
        },
        '/notifications/delete-all': {
          delete: {
            summary: 'Supprime toutes les notifications',
            tags: ['Notifications'],
            security: [{ bearerAuth: [] }],
            responses: {
              200: { description: 'OK' },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          }
        },
        '/ai-coach/sessions': {
          post: {
            summary: 'Créer une session IA coach',
            tags: ['AICoach'],
            security: [{ bearerAuth: [] }],
            responses: {
              200: {
                description: 'Session créée',
                content: {
                  'application/json': {
                    schema: { $ref: '#/components/schemas/AICoachSession' }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          }
        },
        '/ai-coach/sessions/end': {
          post: {
            summary: 'Terminer une session IA coach (feedback, rating, stats)',
            tags: ['AICoach'],
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      sessionId: { type: 'string' },
                      feedback: { type: 'string' },
                      rating: { type: 'integer' },
                      stats: { type: 'object' }
                    }
                  }
                }
              }
            },
            responses: {
              200: {
                description: 'Session terminée',
                content: {
                  'application/json': {
                    schema: { $ref: '#/components/schemas/AICoachSession' }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          }
        },
        '/ai-coach/sessions/stats': {
          get: {
            summary: 'Statistiques IA coach',
            tags: ['AICoach'],
            security: [{ bearerAuth: [] }],
            responses: {
              200: {
                description: 'Stats IA coach',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        success: { type: 'boolean' },
                        stats: { type: 'object' }
                      }
                    }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          }
        },
        '/wellness/{id}/progress': {
          patch: {
            summary: 'Met à jour la progression sur un module bien-être',
            tags: ['WellnessModules'],
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      progress: { type: 'number' },
                      stats: { type: 'object' }
                    }
                  }
                }
              }
            },
            responses: {
              200: {
                description: 'Progression mise à jour',
                content: {
                  'application/json': {
                    schema: { $ref: '#/components/schemas/UserModuleProgress' }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          }
        },
        '/wellness/{id}/feedback': {
          post: {
            summary: 'Ajoute un feedback utilisateur sur un module bien-être',
            tags: ['WellnessModules'],
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      feedback: { type: 'string' }
                    }
                  }
                }
              }
            },
            responses: {
              200: {
                description: 'Feedback ajouté',
                content: {
                  'application/json': {
                    schema: { $ref: '#/components/schemas/UserModuleProgress' }
                  }
                }
              },
              401: { $ref: '#/components/responses/UnauthorizedError' }
            }
          }
        },
        '/wellness/{id}/stats': {
          get: {
            summary: 'Statistiques d\'un module bien-être',
            tags: ['WellnessModules'],
            responses: {
              200: {
                description: 'Stats du module',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        success: { type: 'boolean' },
                        stats: { type: 'object' }
                      }
                    }
                  }
                }
              }
            }
          }
        },
      }
    },
    security: [{ bearerAuth: [] }],
    tags: [
      { name: 'AuditTrail', description: 'Toutes les actions sensibles sont tracées via le middleware d\'audit.' },
      { name: 'Permissions', description: 'Certaines routes nécessitent des permissions spécifiques (voir erreurs 403).' },
      { name: 'WebSocket', description: 'Événements temps réel : ia-message, monitoring-alert, system-alert, gestion des rooms (joinRoom, leaveRoom)' },
    ],
    'x-websocket-events': {
      'ia-message': {
        description: 'Message IA coach en temps réel',
        payload: { text: 'string', sessionId: 'string' }
      },
      'monitoring-alert': {
        description: 'Alerte monitoring (performance, sécurité, etc.)',
        payload: { level: 'string', message: 'string' }
      },
      'system-alert': {
        description: 'Alerte système globale',
        payload: { message: 'string' }
      },
      'joinRoom': {
        description: 'Rejoindre une room WebSocket',
        payload: { room: 'string' }
      },
      'leaveRoom': {
        description: 'Quitter une room WebSocket',
        payload: { room: 'string' }
      }
    },
  },
  apis: ['./src/routes/*.ts'],
};

const specs = swaggerJsdoc(options);

export function setupSwagger(app: Express) {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));
}

/**
 * @swagger
 * /auth/enable-2fa:
 *   post:
 *     summary: Activer le 2FA pour l'utilisateur
 *     tags: [Auth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: QR code et URL OTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 qr:
 *                   type: string
 *                 otpauthUrl:
 *                   type: string
 *
 * /auth/verify-2fa:
 *   post:
 *     summary: Vérifier le code 2FA
 *     tags: [Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               token:
 *                 type: string
 *     responses:
 *       200:
 *         description: Succès ou échec
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *
 * /auth/disable-2fa:
 *   post:
 *     summary: Désactiver le 2FA
 *     tags: [Auth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 */ 