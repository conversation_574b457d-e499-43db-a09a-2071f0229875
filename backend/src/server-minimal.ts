import express from 'express';
import cors from 'cors';
import { initializeDatabase } from './config/database';

const app = express();
const PORT = process.env.PORT || 4000;

// Middleware de base
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000', 'http://localhost:5174', 'http://localhost:5175', 'http://localhost:5176', 'http://localhost:5177', 'http://localhost:5178'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware de logging simple
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Routes de base fonctionnelles
app.get('/', (req, res) => {
  res.json({
    message: '🚀 MindFlow Pro API Server',
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Route de santé
app.get('/api/v1/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    database: 'connected'
  });
});

// Routes AI Coach (publiques)
app.get('/api/v1/ai-coach/crisis-resources', (req, res) => {
  res.json({
    success: true,
    message: 'Crisis resources retrieved successfully',
    data: {
      resources: {
        immediate: [
          {
            name: 'National Suicide Prevention Lifeline',
            phone: '988',
            description: '24/7 crisis support',
            website: 'https://suicidepreventionlifeline.org'
          },
          {
            name: 'Crisis Text Line',
            text: 'Text HOME to 741741',
            description: '24/7 crisis support via text',
            website: 'https://crisistextline.org'
          },
          {
            name: 'Emergency Services',
            phone: '911',
            description: 'For immediate medical emergencies'
          }
        ],
        mentalHealth: [
          {
            name: 'NAMI Helpline',
            phone: '1-800-950-NAMI (6264)',
            description: 'Mental health information and support',
            website: 'https://nami.org'
          }
        ]
      }
    }
  });
});

app.get('/api/v1/ai-coach/wellness-tips', (req, res) => {
  res.json({
    success: true,
    message: 'Wellness tips retrieved successfully',
    data: {
      tips: [
        {
          category: 'mindfulness',
          title: 'Practice Deep Breathing',
          description: 'Take 5 minutes to practice deep breathing exercises',
          difficulty: 'easy'
        },
        {
          category: 'physical',
          title: 'Take a Walk',
          description: 'A 10-minute walk can boost your mood and energy',
          difficulty: 'easy'
        },
        {
          category: 'social',
          title: 'Connect with Others',
          description: 'Reach out to a friend or family member',
          difficulty: 'medium'
        },
        {
          category: 'mental',
          title: 'Gratitude Practice',
          description: 'Write down 3 things you are grateful for today',
          difficulty: 'easy'
        }
      ]
    }
  });
});

// Routes professionnels (simulées)
app.get('/api/v1/professionals', (req, res) => {
  res.json({
    success: true,
    message: 'Professionals retrieved successfully',
    data: {
      professionals: [
        {
          id: '1',
          firstName: 'Dr. Marie',
          lastName: 'Dubois',
          specialization: 'Psychologie clinique',
          experience: '10 years',
          rating: 4.8,
          available: true,
          languages: ['français', 'anglais']
        },
        {
          id: '2',
          firstName: 'Dr. Jean',
          lastName: 'Martin',
          specialization: 'Thérapie cognitive',
          experience: '8 years',
          rating: 4.6,
          available: true,
          languages: ['français']
        }
      ]
    }
  });
});

// Routes protégées (retournent 401 sans token)
app.get('/api/v1/users/profile', (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No token provided'
    });
  }
  
  res.json({
    success: true,
    message: 'User profile endpoint working',
    data: {
      message: 'This is a protected route',
      user: 'authenticated user data would be here'
    }
  });
});

app.get('/api/v1/ai-coach/interactions', (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No token provided'
    });
  }
  
  res.json({
    success: true,
    message: 'AI coach interactions endpoint working',
    data: {
      interactions: [],
      message: 'This is a protected route'
    }
  });
});

app.get('/api/v1/journal', (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No token provided'
    });
  }
  
  res.json({
    success: true,
    message: 'Journal endpoint working',
    data: {
      entries: [],
      message: 'This is a protected route'
    }
  });
});

app.get('/api/v1/appointments', (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No token provided'
    });
  }
  
  res.json({
    success: true,
    message: 'Appointments endpoint working',
    data: {
      appointments: [],
      message: 'This is a protected route'
    }
  });
});

app.get('/api/v1/wellness/modules', (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No token provided'
    });
  }
  
  res.json({
    success: true,
    message: 'Wellness modules endpoint working',
    data: {
      modules: [],
      message: 'This is a protected route'
    }
  });
});

app.get('/api/v1/health-monitoring/status', (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No token provided'
    });
  }
  
  res.json({
    success: true,
    message: 'Health monitoring endpoint working',
    data: {
      status: 'healthy',
      message: 'This is a protected route'
    }
  });
});

// Middleware de gestion d'erreurs 404
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.url} not found`,
    timestamp: new Date().toISOString()
  });
});

// Middleware de gestion d'erreurs
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('❌ Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: err.message,
    timestamp: new Date().toISOString()
  });
});

async function startServer() {
  try {
    // Initialiser la base de données
    console.log('🟢 [DB] Initialisation de la base de données...');
    await initializeDatabase();
    console.log('✅ Base de données initialisée avec succès');

    // Démarrer le serveur
    app.listen(PORT, () => {
      console.log(`🚀 MindFlow Pro API server is running on port ${PORT}`);
      console.log(`📖 API Documentation: http://localhost:${PORT}/api/v1/health`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 Frontend URL: http://localhost:5173,http://localhost:3000`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Gestion propre de l'arrêt
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('👋 SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

startServer(); 