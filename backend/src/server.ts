import { createServer } from 'http';
import app from './app';
import { initializeDatabase } from './config/database';
import { initWebSocketService } from './services/websocketInstance';

const PORT = process.env.PORT || 4000;

const startServer = async (): Promise<void> => {
  try {
    // Initialize database connection
    await initializeDatabase();

    // Create HTTP server
    const server = createServer(app);

    // Initialize WebSocket service
    initWebSocketService(server);

    // Start the server
    server.listen(PORT, () => {
      console.log(`🚀 MindFlow Pro API server is running on port ${PORT}`);
      console.log(`🔌 WebSocket server is running on port ${PORT}`);
      console.log(`📖 API Documentation: http://localhost:${PORT}/api/v1/health`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);

      if (process.env.NODE_ENV === 'development') {
        console.log(`🔗 Frontend URL: ${process.env.CORS_ORIGIN || 'http://localhost:3000'}`);
      }
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err: Error) => {
  console.error('❌ Unhandled Promise Rejection:', err.message);
  console.error(err.stack);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err: Error) => {
  console.error('❌ Uncaught Exception:', err.message);
  console.error(err.stack);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('👋 SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

startServer();
