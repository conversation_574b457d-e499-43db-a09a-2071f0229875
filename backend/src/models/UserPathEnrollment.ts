import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { IsNotEmpty, IsEnum } from 'class-validator';
import { User } from './User';
import { TherapeuticPath } from './TherapeuticPath';

export enum EnrollmentStatus {
  ENROLLED = 'enrolled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  PAUSED = 'paused',
  DROPPED = 'dropped',
  SUSPENDED = 'suspended',
}

@Entity('user_path_enrollments')
@Index(['userId', 'pathId'], { unique: true })
export class UserPathEnrollment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  userId: string;

  @Column()
  @IsNotEmpty()
  pathId: string;

  @Column({
    type: 'simple-enum',
    enum: EnrollmentStatus,
    default: EnrollmentStatus.ENROLLED,
  })
  @IsEnum(EnrollmentStatus)
  status: EnrollmentStatus;

  @Column({ type: 'int', default: 0 })
  progressPercentage: number; // 0-100

  @Column({ type: 'json', nullable: true })
  completedModules: string[]; // Array of module IDs

  @Column({ type: 'json', nullable: true })
  currentModule?: string; // Current module ID

  @Column({ type: 'datetime' })
  enrolledAt: Date;

  @Column({ type: 'datetime', nullable: true })
  startedAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  completedAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  expectedCompletionDate?: Date;

  @Column({ type: 'int', default: 0 })
  totalTimeSpentMinutes: number;

  @Column({ type: 'json', nullable: true })
  moduleProgress: Record<string, {
    status: string;
    progressPercentage: number;
    timeSpent: number;
    startedAt?: Date;
    completedAt?: Date;
  }>;

  @Column({ type: 'json', nullable: true })
  personalGoals: Array<{
    description: string;
    targetDate?: Date;
    completed: boolean;
    completedAt?: Date;
  }>;

  @Column({ type: 'json', nullable: true })
  checkInResponses: Array<{
    date: Date;
    questions: Record<string, any>;
    responses: Record<string, any>;
    moodScore?: number;
    stressLevel?: number;
    notes?: string;
  }>;

  @Column({ type: 'json', nullable: true })
  milestones: Array<{
    id: string;
    name: string;
    description: string;
    achievedAt?: Date;
    isAchieved: boolean;
  }>;

  @Column({ type: 'int', nullable: true })
  userRating?: number; // 1-5 stars for the entire path

  @Column({ type: 'text', nullable: true })
  userFeedback?: string;

  @Column({ type: 'text', nullable: true })
  dropoutReason?: string;

  @Column({ type: 'text', nullable: true })
  personalNotes?: string;

  @Column({ default: false })
  certificateEarned: boolean;

  @Column({ type: 'datetime', nullable: true })
  certificateEarnedAt?: Date;

  @Column({ type: 'json', nullable: true })
  supportNeeded: Array<{
    type: 'technical' | 'emotional' | 'professional' | 'other';
    description: string;
    requestedAt: Date;
    resolved: boolean;
    resolvedAt?: Date;
  }>;

  @Column({ default: false })
  requiresProfessionalCheck: boolean;

  @Column({ type: 'datetime', nullable: true })
  lastProfessionalCheckAt?: Date;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => TherapeuticPath, (path) => path.enrollments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'pathId' })
  path: TherapeuticPath;

  // Methods
  updateProgress(): void {
    if (this.completedModules && this.path?.moduleLinks) {
      const totalModules = this.path.moduleLinks.length;
      const completedCount = this.completedModules.length;
      this.progressPercentage = Math.round((completedCount / totalModules) * 100);
      
      if (this.progressPercentage === 100 && this.status !== EnrollmentStatus.COMPLETED) {
        this.status = EnrollmentStatus.COMPLETED;
        this.completedAt = new Date();
      }
    }
  }

  toJSON(): Partial<UserPathEnrollment> {
    return {
      id: this.id,
      pathId: this.pathId,
      status: this.status,
      progressPercentage: this.progressPercentage,
      completedModules: this.completedModules,
      currentModule: this.currentModule,
      enrolledAt: this.enrolledAt,
      startedAt: this.startedAt,
      completedAt: this.completedAt,
      lastAccessedAt: this.lastAccessedAt,
      expectedCompletionDate: this.expectedCompletionDate,
      totalTimeSpentMinutes: this.totalTimeSpentMinutes,
      personalGoals: this.personalGoals,
      milestones: this.milestones,
      userRating: this.userRating,
      userFeedback: this.userFeedback,
      personalNotes: this.personalNotes,
      certificateEarned: this.certificateEarned,
      certificateEarnedAt: this.certificateEarnedAt,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
