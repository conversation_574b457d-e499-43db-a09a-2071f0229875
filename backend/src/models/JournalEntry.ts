import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum } from 'class-validator';
import { User } from './User';

export enum MoodLevel {
  VERY_LOW = 1,
  LOW = 2,
  NEUTRAL = 3,
  GOOD = 4,
  EXCELLENT = 5,
}

export enum EntryType {
  DAILY = 'daily',
  REFLECTION = 'reflection',
  GRATITUDE = 'gratitude',
  GOAL_SETTING = 'goal_setting',
  STRESS_TRACKING = 'stress_tracking',
  MOOD_TRACKING = 'mood_tracking',
}

@Entity('journal_entries')
export class JournalEntry {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  userId: string;

  @Column({ length: 200 })
  @IsNotEmpty()
  title: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  content: string;

  @Column({
    type: 'simple-enum',
    enum: EntryType,
    default: EntryType.DAILY,
  })
  @IsEnum(EntryType)
  entryType: EntryType;

  @Column({
    type: 'simple-enum',
    enum: MoodLevel,
    nullable: true,
  })
  @IsOptional()
  @IsEnum(MoodLevel)
  moodLevel?: MoodLevel;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  emotions: string[];

  @Column({ type: 'int', nullable: true, default: 0 })
  stressLevel?: number; // 0-10 scale

  @Column({ type: 'int', nullable: true, default: 0 })
  energyLevel?: number; // 0-10 scale

  @Column({ type: 'int', nullable: true, default: 0 })
  sleepQuality?: number; // 0-10 scale

  @Column({ type: 'text', nullable: true })
  gratitudeNotes?: string;

  @Column({ type: 'text', nullable: true })
  goals?: string;

  @Column({ type: 'text', nullable: true })
  challenges?: string;

  @Column({ type: 'text', nullable: true })
  achievements?: string;

  @Column({ default: false })
  isPrivate: boolean;

  @Column({ default: false })
  isFavorite: boolean;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  // Methods
  toJSON(): Partial<JournalEntry> {
    return {
      id: this.id,
      title: this.title,
      content: this.content,
      entryType: this.entryType,
      moodLevel: this.moodLevel,
      tags: this.tags,
      emotions: this.emotions,
      stressLevel: this.stressLevel,
      energyLevel: this.energyLevel,
      sleepQuality: this.sleepQuality,
      gratitudeNotes: this.gratitudeNotes,
      goals: this.goals,
      challenges: this.challenges,
      achievements: this.achievements,
      isPrivate: this.isPrivate,
      isFavorite: this.isFavorite,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
