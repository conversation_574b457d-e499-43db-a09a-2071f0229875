import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { IsNotEmpty, IsEnum, IsOptional } from 'class-validator';
import { User } from './User';

export enum BiometricType {
  HEART_RATE = 'heart_rate',
  BLOOD_PRESSURE = 'blood_pressure',
  SLEEP_DURATION = 'sleep_duration',
  SLEEP_QUALITY = 'sleep_quality',
  STEPS = 'steps',
  WEIGHT = 'weight',
  BODY_TEMPERATURE = 'body_temperature',
  STRESS_LEVEL = 'stress_level',
  MOOD_SCORE = 'mood_score',
  ANXIETY_LEVEL = 'anxiety_level',
  ENERGY_LEVEL = 'energy_level',
  MEDITATION_MINUTES = 'meditation_minutes',
  EXERCISE_MINUTES = 'exercise_minutes',
  WATER_INTAKE = 'water_intake',
  CAFFEINE_INTAKE = 'caffeine_intake',
  ALCOHOL_INTAKE = 'alcohol_intake',
}

export enum DataSource {
  MANUAL_ENTRY = 'manual_entry',
  WEARABLE_DEVICE = 'wearable_device',
  MOBILE_APP = 'mobile_app',
  SMART_SCALE = 'smart_scale',
  BLOOD_PRESSURE_MONITOR = 'blood_pressure_monitor',
  FITNESS_TRACKER = 'fitness_tracker',
  SMARTWATCH = 'smartwatch',
  HEALTH_APP = 'health_app',
}

export enum DataQuality {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor',
  UNRELIABLE = 'unreliable',
}

@Entity('biometric_data_entries')
@Index(['userId', 'biometricType', 'recordedAt'])
@Index(['userId', 'recordedAt'])
export class BiometricDataEntry {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  userId: string;

  @Column({
    type: 'simple-enum',
    enum: BiometricType,
  })
  @IsEnum(BiometricType)
  biometricType: BiometricType;

  @Column({ type: 'decimal', precision: 10, scale: 3 })
  @IsNotEmpty()
  value: number;

  @Column({ length: 20, nullable: true })
  unit?: string; // e.g., 'bpm', 'mmHg', 'hours', 'steps', 'kg', '°C'

  @Column({ type: 'decimal', precision: 10, scale: 3, nullable: true })
  secondaryValue?: number; // For blood pressure (diastolic), etc.

  @Column({ length: 20, nullable: true })
  secondaryUnit?: string;

  @Column({ type: 'datetime' })
  recordedAt: Date;

  @Column({
    type: 'simple-enum',
    enum: DataSource,
    default: DataSource.MANUAL_ENTRY,
  })
  @IsEnum(DataSource)
  dataSource: DataSource;

  @Column({ nullable: true })
  deviceId?: string; // Identifier for the device that recorded the data

  @Column({ nullable: true })
  deviceModel?: string;

  @Column({
    type: 'simple-enum',
    enum: DataQuality,
    default: DataQuality.GOOD,
  })
  @IsEnum(DataQuality)
  dataQuality: DataQuality;

  @Column({ type: 'json', nullable: true })
  context?: {
    activity?: string; // What the user was doing when recorded
    location?: string;
    weather?: string;
    mood?: string;
    stressLevel?: number;
    notes?: string;
  };

  @Column({ type: 'json', nullable: true })
  tags?: string[]; // User-defined tags

  @Column({ type: 'json', nullable: true })
  correlatedData?: Array<{
    type: BiometricType;
    value: number;
    recordedAt: Date;
  }>; // Related measurements taken at the same time

  @Column({ default: false })
  isAnomalous: boolean; // Flagged as unusual by system

  @Column({ type: 'text', nullable: true })
  anomalyReason?: string;

  @Column({ default: false })
  isValidated: boolean; // Validated by user or professional

  @Column({ type: 'datetime', nullable: true })
  validatedAt?: Date;

  @Column({ nullable: true })
  validatedBy?: string; // User ID who validated

  @Column({ type: 'json', nullable: true })
  trends?: {
    weeklyAverage?: number;
    monthlyAverage?: number;
    trend?: 'increasing' | 'decreasing' | 'stable';
    percentageChange?: number;
  };

  @Column({ type: 'json', nullable: true })
  goals?: {
    targetValue?: number;
    targetRange?: {
      min: number;
      max: number;
    };
    isWithinTarget?: boolean;
  };

  @Column({ default: true })
  isVisible: boolean; // User can hide certain entries

  @Column({ default: false })
  isSharedWithProfessional: boolean;

  @Column({ type: 'json', nullable: true })
  sharingPermissions?: {
    sharedWith: string[]; // Professional IDs
    sharedAt: Date;
    expiresAt?: Date;
  };

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  // Methods
  isWithinNormalRange(): boolean {
    // Define normal ranges for different biometric types
    const normalRanges: Record<BiometricType, { min: number; max: number }> = {
      [BiometricType.HEART_RATE]: { min: 60, max: 100 },
      [BiometricType.BLOOD_PRESSURE]: { min: 90, max: 140 }, // Systolic
      [BiometricType.SLEEP_DURATION]: { min: 7, max: 9 },
      [BiometricType.SLEEP_QUALITY]: { min: 7, max: 10 },
      [BiometricType.STRESS_LEVEL]: { min: 1, max: 5 },
      [BiometricType.MOOD_SCORE]: { min: 6, max: 10 },
      [BiometricType.ANXIETY_LEVEL]: { min: 1, max: 5 },
      [BiometricType.ENERGY_LEVEL]: { min: 6, max: 10 },
      [BiometricType.BODY_TEMPERATURE]: { min: 36.1, max: 37.2 },
      // Add more ranges as needed
    } as any;

    const range = normalRanges[this.biometricType];
    if (!range) return true; // No range defined, assume normal

    return this.value >= range.min && this.value <= range.max;
  }

  calculateTrend(historicalData: BiometricDataEntry[]): void {
    if (historicalData.length < 2) return;

    const recentData = historicalData.slice(-7); // Last 7 entries
    const values = recentData.map(entry => entry.value);
    const average = values.reduce((sum, val) => sum + val, 0) / values.length;

    const olderData = historicalData.slice(-14, -7); // Previous 7 entries
    if (olderData.length > 0) {
      const olderAverage = olderData.reduce((sum, entry) => sum + entry.value, 0) / olderData.length;
      const percentageChange = ((average - olderAverage) / olderAverage) * 100;

      this.trends = {
        weeklyAverage: average,
        trend: percentageChange > 5 ? 'increasing' : percentageChange < -5 ? 'decreasing' : 'stable',
        percentageChange: Math.round(percentageChange * 100) / 100,
      };
    }
  }

  toJSON(): Partial<BiometricDataEntry> {
    return {
      id: this.id,
      biometricType: this.biometricType,
      value: this.value,
      unit: this.unit,
      secondaryValue: this.secondaryValue,
      secondaryUnit: this.secondaryUnit,
      recordedAt: this.recordedAt,
      dataSource: this.dataSource,
      deviceModel: this.deviceModel,
      dataQuality: this.dataQuality,
      context: this.context,
      tags: this.tags,
      isAnomalous: this.isAnomalous,
      isValidated: this.isValidated,
      trends: this.trends,
      goals: this.goals,
      isVisible: this.isVisible,
      isSharedWithProfessional: this.isSharedWithProfessional,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
