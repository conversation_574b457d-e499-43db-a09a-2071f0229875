import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty, IsEnum } from 'class-validator';
import { User } from './User';
import { ProfessionalProfile } from './ProfessionalProfile';

export enum AppointmentType {
  INITIAL_CONSULTATION = 'initial_consultation',
  FOLLOW_UP = 'follow_up',
  THERAPY_SESSION = 'therapy_session',
  ASSESSMENT = 'assessment',
  GROUP_SESSION = 'group_session',
  CRISIS_INTERVENTION = 'crisis_intervention',
  MEDICATION_REVIEW = 'medication_review',
}

export enum AppointmentStatus {
  SCHEDULED = 'scheduled',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED_BY_CLIENT = 'cancelled_by_client',
  CANCELLED_BY_PROFESSIONAL = 'cancelled_by_professional',
  NO_SHOW = 'no_show',
  RESCHEDULED = 'rescheduled',
}

export enum AppointmentMode {
  VIDEO_CALL = 'video_call',
  PHONE_CALL = 'phone_call',
  CHAT = 'chat',
  IN_PERSON = 'in_person',
}

@Entity('appointments')
export class Appointment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  clientId: string;

  @Column()
  @IsNotEmpty()
  professionalId: string;

  @Column({
    type: 'simple-enum',
    enum: AppointmentType,
    default: AppointmentType.INITIAL_CONSULTATION,
  })
  @IsEnum(AppointmentType)
  appointmentType: AppointmentType;

  @Column({
    type: 'simple-enum',
    enum: AppointmentStatus,
    default: AppointmentStatus.SCHEDULED,
  })
  @IsEnum(AppointmentStatus)
  status: AppointmentStatus;

  @Column({
    type: 'simple-enum',
    enum: AppointmentMode,
    default: AppointmentMode.VIDEO_CALL,
  })
  @IsEnum(AppointmentMode)
  mode: AppointmentMode;

  @Column({ type: 'datetime' })
  scheduledAt: Date;

  @Column({ type: 'int', default: 60 })
  durationMinutes: number;

  @Column({ type: 'datetime', nullable: true })
  actualStartTime?: Date;

  @Column({ type: 'datetime', nullable: true })
  actualEndTime?: Date;

  @Column({ type: 'text', nullable: true })
  clientNotes?: string; // Notes from client before appointment

  @Column({ type: 'text', nullable: true })
  professionalNotes?: string; // Notes from professional after appointment

  @Column({ type: 'text', nullable: true })
  sessionSummary?: string;

  @Column({ type: 'json', nullable: true })
  preSessionAssessment?: {
    moodLevel: number;
    stressLevel: number;
    anxietyLevel: number;
    concerns: string[];
    goals: string[];
  };

  @Column({ type: 'json', nullable: true })
  postSessionAssessment?: {
    moodLevel: number;
    stressLevel: number;
    satisfactionLevel: number;
    helpfulnessRating: number;
    feedback: string;
  };

  @Column({ type: 'json', nullable: true })
  actionItems: Array<{
    description: string;
    dueDate?: Date;
    completed: boolean;
    completedAt?: Date;
  }>;

  @Column({ type: 'json', nullable: true })
  followUpPlan?: {
    nextAppointmentRecommended: boolean;
    recommendedTimeframe: string;
    specificGoals: string[];
    homeworkAssignments: string[];
  };

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  cost?: number;

  @Column({ length: 3, default: 'USD' })
  currency: string;

  @Column({ default: false })
  isPaid: boolean;

  @Column({ type: 'datetime', nullable: true })
  paidAt?: Date;

  @Column({ nullable: true })
  paymentMethod?: string;

  @Column({ nullable: true })
  paymentTransactionId?: string;

  @Column({ type: 'text', nullable: true })
  cancellationReason?: string;

  @Column({ type: 'datetime', nullable: true })
  cancelledAt?: Date;

  @Column({ nullable: true })
  cancelledBy?: string; // User ID who cancelled

  @Column({ type: 'json', nullable: true })
  reschedulingHistory: Array<{
    originalDate: Date;
    newDate: Date;
    reason: string;
    rescheduledBy: string;
    rescheduledAt: Date;
  }>;

  @Column({ type: 'json', nullable: true })
  meetingDetails?: {
    meetingUrl?: string;
    meetingId?: string;
    password?: string;
    dialInNumber?: string;
    accessCode?: string;
  };

  @Column({ type: 'json', nullable: true })
  reminders: Array<{
    type: 'email' | 'sms' | 'push';
    sentAt: Date;
    status: 'sent' | 'delivered' | 'failed';
  }>;

  @Column({ default: false })
  isEmergency: boolean;

  @Column({ type: 'int', nullable: true })
  clientRating?: number; // 1-5 stars

  @Column({ type: 'text', nullable: true })
  clientFeedback?: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'clientId' })
  client: User;

  @ManyToOne(() => ProfessionalProfile, (profile) => profile.appointments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'professionalId' })
  professional: ProfessionalProfile;

  // Methods
  calculateActualDuration(): number {
    if (this.actualStartTime && this.actualEndTime) {
      return Math.round((this.actualEndTime.getTime() - this.actualStartTime.getTime()) / (1000 * 60));
    }
    return 0;
  }

  toJSON(): Partial<Appointment> {
    return {
      id: this.id,
      appointmentType: this.appointmentType,
      status: this.status,
      mode: this.mode,
      scheduledAt: this.scheduledAt,
      durationMinutes: this.durationMinutes,
      actualStartTime: this.actualStartTime,
      actualEndTime: this.actualEndTime,
      clientNotes: this.clientNotes,
      sessionSummary: this.sessionSummary,
      preSessionAssessment: this.preSessionAssessment,
      postSessionAssessment: this.postSessionAssessment,
      actionItems: this.actionItems,
      followUpPlan: this.followUpPlan,
      cost: this.cost,
      currency: this.currency,
      isPaid: this.isPaid,
      paidAt: this.paidAt,
      isEmergency: this.isEmergency,
      clientRating: this.clientRating,
      clientFeedback: this.clientFeedback,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
