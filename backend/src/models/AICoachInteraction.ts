import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  BaseEntity,
} from 'typeorm';
import { IsNotEmpty, IsEnum, IsOptional } from 'class-validator';
import { User } from './User';

export enum InteractionType {
  CHAT = 'chat',
  ASSESSMENT = 'assessment',
  RECOMMENDATION = 'recommendation',
  CHECK_IN = 'check_in',
  CRISIS_SUPPORT = 'crisis_support',
  GOAL_SETTING = 'goal_setting',
  PROGRESS_REVIEW = 'progress_review',
}

export enum InteractionStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
}

export enum SentimentScore {
  VERY_NEGATIVE = 1,
  NEGATIVE = 2,
  NEUTRAL = 3,
  POSITIVE = 4,
  VERY_POSITIVE = 5,
}

@Entity('ai_coach_interactions')
export class AICoachInteraction extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  userId: string;

  @Column({
    type: 'simple-enum',
    enum: InteractionType,
    default: InteractionType.CHAT,
  })
  @IsEnum(InteractionType)
  interactionType: InteractionType;

  @Column({
    type: 'simple-enum',
    enum: InteractionStatus,
    default: InteractionStatus.ACTIVE,
  })
  @IsEnum(InteractionStatus)
  status: InteractionStatus;

  @Column({ type: 'text' })
  @IsNotEmpty()
  userMessage: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  aiResponse: string;

  @Column({
    type: 'simple-enum',
    enum: SentimentScore,
    nullable: true,
  })
  @IsOptional()
  @IsEnum(SentimentScore)
  sentimentScore?: SentimentScore;

  @Column({ type: 'json', nullable: true })
  context: Record<string, any>; // Previous conversation context

  @Column({ type: 'json', nullable: true })
  extractedTopics: string[]; // AI-extracted topics from conversation

  @Column({ type: 'json', nullable: true })
  suggestedActions: string[]; // AI-suggested follow-up actions

  @Column({ type: 'json', nullable: true })
  emotionalIndicators: string[]; // Detected emotional states

  @Column({ type: 'int', nullable: true })
  confidenceScore?: number; // AI confidence in response (0-100)

  @Column({ type: 'int', nullable: true })
  userRating?: number; // User rating of AI response (1-5)

  @Column({ type: 'text', nullable: true })
  userFeedback?: string;

  @Column({ default: false })
  flaggedForReview: boolean; // Flag for human review if needed

  @Column({ type: 'text', nullable: true })
  reviewNotes?: string;

  @Column({ nullable: true })
  sessionId?: string; // Group related interactions

  @Column({ type: 'int', nullable: true })
  responseTimeMs?: number; // AI response time in milliseconds

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  // Methods
  toJSON(): Partial<AICoachInteraction> {
    return {
      id: this.id,
      interactionType: this.interactionType,
      status: this.status,
      userMessage: this.userMessage,
      aiResponse: this.aiResponse,
      sentimentScore: this.sentimentScore,
      extractedTopics: this.extractedTopics,
      suggestedActions: this.suggestedActions,
      emotionalIndicators: this.emotionalIndicators,
      confidenceScore: this.confidenceScore,
      userRating: this.userRating,
      userFeedback: this.userFeedback,
      sessionId: this.sessionId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
