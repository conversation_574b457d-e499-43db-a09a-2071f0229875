import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, BaseEntity } from 'typeorm';
import { User } from './User';

@Entity('ai_coach_sessions')
export class AICoachSession extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column({ type: 'varchar', nullable: true })
  theme?: string;

  @Column({ type: 'text', nullable: true })
  goal?: string;

  @Column({ type: 'varchar', default: 'active' })
  status: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @CreateDateColumn()
  startTime: Date;

  @Column({ type: 'datetime', nullable: true })
  endTime?: Date;

  @Column({ type: 'text', nullable: true })
  feedback?: string;

  @Column({ type: 'int', nullable: true })
  rating?: number;

  @Column({ type: 'json', nullable: true })
  stats?: Record<string, any>;

  @UpdateDateColumn()
  updatedAt: Date;
} 