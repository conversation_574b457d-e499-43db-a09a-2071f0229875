import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { IsNotEmpty, IsEnum, IsOptional } from 'class-validator';
import { UserModuleProgress } from './UserModuleProgress';

export enum ModuleType {
  MINDFULNESS = 'mindfulness',
  STRESS_MANAGEMENT = 'stress_management',
  ANXIETY_RELIEF = 'anxiety_relief',
  DEPRESSION_SUPPORT = 'depression_support',
  SLEEP_HYGIENE = 'sleep_hygiene',
  EMOTIONAL_REGULATION = 'emotional_regulation',
  COMMUNICATION_SKILLS = 'communication_skills',
  WORK_LIFE_BALANCE = 'work_life_balance',
  SELF_ESTEEM = 'self_esteem',
  GOAL_SETTING = 'goal_setting',
  CRISIS_MANAGEMENT = 'crisis_management',
  RELATIONSHIP_BUILDING = 'relationship_building',
}

export enum DifficultyLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
}

export enum ModuleStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  UNDER_REVIEW = 'under_review',
}

@Entity('wellness_modules')
export class WellnessModule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200 })
  @IsNotEmpty()
  title: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  description: string;

  @Column({
    type: 'simple-enum',
    enum: ModuleType,
  })
  @IsEnum(ModuleType)
  moduleType: ModuleType;

  @Column({
    type: 'simple-enum',
    enum: DifficultyLevel,
    default: DifficultyLevel.BEGINNER,
  })
  @IsEnum(DifficultyLevel)
  difficultyLevel: DifficultyLevel;

  @Column({
    type: 'simple-enum',
    enum: ModuleStatus,
    default: ModuleStatus.DRAFT,
  })
  @IsEnum(ModuleStatus)
  status: ModuleStatus;

  @Column({ type: 'int', default: 0 })
  estimatedDurationMinutes: number;

  @Column({ type: 'json' })
  content: {
    sections: Array<{
      id: string;
      title: string;
      content: string;
      type: 'text' | 'video' | 'audio' | 'exercise' | 'quiz';
      duration?: number;
      resources?: string[];
    }>;
  };

  @Column({ type: 'json', nullable: true })
  learningObjectives: string[];

  @Column({ type: 'json', nullable: true })
  prerequisites: string[];

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  resources: Array<{
    title: string;
    url: string;
    type: 'article' | 'video' | 'podcast' | 'book' | 'app';
  }>;

  @Column({ nullable: true })
  thumbnailUrl?: string;

  @Column({ nullable: true })
  videoUrl?: string;

  @Column({ nullable: true })
  audioUrl?: string;

  @Column({ type: 'text', nullable: true })
  authorNotes?: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isFeatured: boolean;

  @Column({ default: false })
  requiresSupervision: boolean; // For modules that might need professional oversight

  @Column({ type: 'int', default: 0 })
  completionCount: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  averageRating: number;

  @Column({ type: 'int', default: 0 })
  ratingCount: number;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => UserModuleProgress, (progress) => progress.module)
  userProgress: UserModuleProgress[];

  // Methods
  toJSON(): Partial<WellnessModule> {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      moduleType: this.moduleType,
      difficultyLevel: this.difficultyLevel,
      status: this.status,
      estimatedDurationMinutes: this.estimatedDurationMinutes,
      content: this.content,
      learningObjectives: this.learningObjectives,
      prerequisites: this.prerequisites,
      tags: this.tags,
      resources: this.resources,
      thumbnailUrl: this.thumbnailUrl,
      isActive: this.isActive,
      isFeatured: this.isFeatured,
      requiresSupervision: this.requiresSupervision,
      completionCount: this.completionCount,
      averageRating: this.averageRating,
      ratingCount: this.ratingCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
