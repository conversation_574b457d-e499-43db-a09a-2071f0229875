import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { User } from './User';

export enum DataType {
  JOURNAL = 'journal',
  MOOD = 'mood',
  STRESS = 'stress',
  SLEEP = 'sleep',
  ENERGY = 'energy',
  CUSTOM = 'custom',
}

@Entity('mental_health_data')
export class MentalHealthData {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'enum', enum: DataType, default: DataType.JOURNAL })
  type: DataType;

  @Column({ type: 'jsonb' })
  data: any;

  @CreateDateColumn()
  createdAt: Date;
} 