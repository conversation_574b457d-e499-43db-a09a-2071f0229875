import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { IsNotEmpty, IsEnum } from 'class-validator';
import { PathModuleLink } from './PathModuleLink';
import { UserPathEnrollment } from './UserPathEnrollment';

export enum PathType {
  ANXIETY_MANAGEMENT = 'anxiety_management',
  DEPRESSION_SUPPORT = 'depression_support',
  STRESS_REDUCTION = 'stress_reduction',
  WORKPLACE_WELLNESS = 'workplace_wellness',
  RELATIONSHIP_BUILDING = 'relationship_building',
  SELF_CARE = 'self_care',
  CRISIS_RECOVERY = 'crisis_recovery',
  PERSONAL_GROWTH = 'personal_growth',
  TRAUMA_HEALING = 'trauma_healing',
  ADDICTION_RECOVERY = 'addiction_recovery',
}

export enum PathDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
}

export enum PathStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  UNDER_REVIEW = 'under_review',
}

@Entity('therapeutic_paths')
export class TherapeuticPath {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200 })
  @IsNotEmpty()
  title: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  description: string;

  @Column({ type: 'text', nullable: true })
  detailedDescription?: string;

  @Column({
    type: 'simple-enum',
    enum: PathType,
  })
  @IsEnum(PathType)
  pathType: PathType;

  @Column({
    type: 'simple-enum',
    enum: PathDifficulty,
    default: PathDifficulty.BEGINNER,
  })
  @IsEnum(PathDifficulty)
  difficulty: PathDifficulty;

  @Column({
    type: 'simple-enum',
    enum: PathStatus,
    default: PathStatus.DRAFT,
  })
  @IsEnum(PathStatus)
  status: PathStatus;

  @Column({ type: 'int', default: 0 })
  estimatedDurationWeeks: number;

  @Column({ type: 'json', nullable: true })
  learningOutcomes: string[];

  @Column({ type: 'json', nullable: true })
  targetAudience: string[];

  @Column({ type: 'json', nullable: true })
  prerequisites: string[];

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ nullable: true })
  thumbnailUrl?: string;

  @Column({ nullable: true })
  bannerUrl?: string;

  @Column({ type: 'text', nullable: true })
  introductionText?: string;

  @Column({ type: 'text', nullable: true })
  completionMessage?: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isFeatured: boolean;

  @Column({ default: false })
  requiresProfessionalSupport: boolean;

  @Column({ type: 'text', nullable: true })
  professionalGuidance?: string;

  @Column({ type: 'json', nullable: true })
  warningFlags: Array<{
    condition: string;
    message: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>;

  @Column({ type: 'int', default: 0 })
  enrollmentCount: number;

  @Column({ type: 'int', default: 0 })
  completionCount: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  averageRating: number;

  @Column({ type: 'int', default: 0 })
  ratingCount: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  completionRate: number; // Percentage

  @Column({ type: 'json', nullable: true })
  successMetrics: {
    averageCompletionTime: number; // in weeks
    userSatisfactionScore: number;
    improvementIndicators: string[];
  };

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => PathModuleLink, (link) => link.path)
  moduleLinks: PathModuleLink[];

  @OneToMany(() => UserPathEnrollment, (enrollment) => enrollment.path)
  enrollments: UserPathEnrollment[];

  // Methods
  calculateCompletionRate(): void {
    if (this.enrollmentCount > 0) {
      this.completionRate = (this.completionCount / this.enrollmentCount) * 100;
    }
  }

  toJSON(): Partial<TherapeuticPath> {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      detailedDescription: this.detailedDescription,
      pathType: this.pathType,
      difficulty: this.difficulty,
      status: this.status,
      estimatedDurationWeeks: this.estimatedDurationWeeks,
      learningOutcomes: this.learningOutcomes,
      targetAudience: this.targetAudience,
      prerequisites: this.prerequisites,
      tags: this.tags,
      thumbnailUrl: this.thumbnailUrl,
      bannerUrl: this.bannerUrl,
      introductionText: this.introductionText,
      isActive: this.isActive,
      isFeatured: this.isFeatured,
      requiresProfessionalSupport: this.requiresProfessionalSupport,
      enrollmentCount: this.enrollmentCount,
      completionCount: this.completionCount,
      averageRating: this.averageRating,
      ratingCount: this.ratingCount,
      completionRate: this.completionRate,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
