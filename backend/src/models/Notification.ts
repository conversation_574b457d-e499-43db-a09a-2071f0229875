import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { IsNotEmpty, IsEnum } from 'class-validator';
import { User } from './User';

export enum NotificationType {
  APPOINTMENT_REMINDER = 'appointment_reminder',
  APPOINTMENT_CONFIRMATION = 'appointment_confirmation',
  APPOINTMENT_CANCELLATION = 'appointment_cancellation',
  MODULE_COMPLETION = 'module_completion',
  PATH_MILESTONE = 'path_milestone',
  DAILY_CHECK_IN = 'daily_check_in',
  JOURNAL_REMINDER = 'journal_reminder',
  AI_COACH_MESSAGE = 'ai_coach_message',
  SYSTEM_UPDATE = 'system_update',
  SECURITY_ALERT = 'security_alert',
  WELLNESS_TIP = 'wellness_tip',
  GOAL_REMINDER = 'goal_reminder',
  PROGRESS_REPORT = 'progress_report',
  EMERGENCY_ALERT = 'emergency_alert',
  PROFESSIONAL_MESSAGE = 'professional_message',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical',
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum NotificationChannel {
  IN_APP = 'in_app',
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
}

@Entity('notifications')
@Index(['userId', 'status'])
@Index(['userId', 'createdAt'])
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  userId: string;

  @Column({
    type: 'simple-enum',
    enum: NotificationType,
  })
  @IsEnum(NotificationType)
  type: NotificationType;

  @Column({
    type: 'simple-enum',
    enum: NotificationPriority,
    default: NotificationPriority.NORMAL,
  })
  @IsEnum(NotificationPriority)
  priority: NotificationPriority;

  @Column({
    type: 'simple-enum',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING,
  })
  @IsEnum(NotificationStatus)
  status: NotificationStatus;

  @Column({
    type: 'simple-enum',
    enum: NotificationChannel,
    default: NotificationChannel.IN_APP,
  })
  @IsEnum(NotificationChannel)
  channel: NotificationChannel;

  @Column({ length: 200 })
  @IsNotEmpty()
  title: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  message: string;

  @Column({ type: 'json', nullable: true })
  data?: Record<string, any>; // Additional data for the notification

  @Column({ type: 'json', nullable: true })
  actionButtons?: Array<{
    label: string;
    action: string;
    style: 'primary' | 'secondary' | 'danger';
  }>;

  @Column({ nullable: true })
  relatedEntityId?: string; // ID of related entity (appointment, module, etc.)

  @Column({ nullable: true })
  relatedEntityType?: string; // Type of related entity

  @Column({ type: 'datetime', nullable: true })
  scheduledFor?: Date; // When to send the notification

  @Column({ type: 'datetime', nullable: true })
  sentAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  deliveredAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  readAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  expiresAt?: Date;

  @Column({ type: 'int', default: 0 })
  retryCount: number;

  @Column({ type: 'int', default: 3 })
  maxRetries: number;

  @Column({ type: 'text', nullable: true })
  failureReason?: string;

  @Column({ type: 'json', nullable: true })
  deliveryAttempts: Array<{
    attemptedAt: Date;
    channel: NotificationChannel;
    status: 'success' | 'failed';
    error?: string;
  }>;

  @Column({ nullable: true })
  imageUrl?: string;

  @Column({ nullable: true })
  iconUrl?: string;

  @Column({ nullable: true })
  deepLinkUrl?: string; // URL to open when notification is clicked

  @Column({ default: false })
  isRead: boolean;

  @Column({ default: false })
  isArchived: boolean;

  @Column({ default: true })
  canDismiss: boolean;

  @Column({ default: false })
  requiresAction: boolean;

  @Column({ type: 'json', nullable: true })
  userPreferences?: {
    allowEmail: boolean;
    allowSMS: boolean;
    allowPush: boolean;
    quietHours: {
      start: string;
      end: string;
    };
  };

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  // Methods
  markAsRead(): void {
    this.isRead = true;
    this.readAt = new Date();
    this.status = NotificationStatus.READ;
  }

  markAsDelivered(): void {
    this.deliveredAt = new Date();
    this.status = NotificationStatus.DELIVERED;
  }

  markAsSent(): void {
    this.sentAt = new Date();
    this.status = NotificationStatus.SENT;
  }

  markAsFailed(reason: string): void {
    this.status = NotificationStatus.FAILED;
    this.failureReason = reason;
    this.retryCount++;
  }

  canRetry(): boolean {
    return this.retryCount < this.maxRetries && this.status === NotificationStatus.FAILED;
  }

  isExpired(): boolean {
    return this.expiresAt ? new Date() > this.expiresAt : false;
  }

  toJSON(): Partial<Notification> {
    return {
      id: this.id,
      type: this.type,
      priority: this.priority,
      status: this.status,
      channel: this.channel,
      title: this.title,
      message: this.message,
      data: this.data,
      actionButtons: this.actionButtons,
      relatedEntityId: this.relatedEntityId,
      relatedEntityType: this.relatedEntityType,
      scheduledFor: this.scheduledFor,
      sentAt: this.sentAt,
      deliveredAt: this.deliveredAt,
      readAt: this.readAt,
      expiresAt: this.expiresAt,
      imageUrl: this.imageUrl,
      iconUrl: this.iconUrl,
      deepLinkUrl: this.deepLinkUrl,
      isRead: this.isRead,
      isArchived: this.isArchived,
      canDismiss: this.canDismiss,
      requiresAction: this.requiresAction,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
