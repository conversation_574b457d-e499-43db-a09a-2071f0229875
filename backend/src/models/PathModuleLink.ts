import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { TherapeuticPath } from './TherapeuticPath';
import { WellnessModule } from './WellnessModule';

@Entity('path_module_links')
@Index(['pathId', 'moduleId'], { unique: true })
@Index(['pathId', 'orderIndex'])
export class PathModuleLink {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  pathId: string;

  @Column()
  @IsNotEmpty()
  moduleId: string;

  @Column({ type: 'int' })
  @IsNotEmpty()
  orderIndex: number; // Order of module in the path (0-based)

  @Column({ default: true })
  isRequired: boolean;

  @Column({ default: false })
  isOptional: boolean;

  @Column({ type: 'json', nullable: true })
  prerequisites: string[]; // Module IDs that must be completed first

  @Column({ type: 'int', nullable: true })
  unlockAfterDays?: number; // Days after path start to unlock this module

  @Column({ type: 'text', nullable: true })
  pathSpecificInstructions?: string; // Special instructions for this module in this path

  @Column({ type: 'json', nullable: true })
  customContent?: {
    introduction?: string;
    objectives?: string[];
    additionalResources?: Array<{
      title: string;
      url: string;
      type: string;
    }>;
  };

  @Column({ type: 'int', nullable: true })
  estimatedCompletionDays?: number; // Expected days to complete this module in this path

  @Column({ type: 'json', nullable: true })
  completionCriteria?: {
    minimumTimeSpent?: number; // minutes
    requiredSections?: string[];
    minimumScore?: number; // for assessments
    customRequirements?: string[];
  };

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => TherapeuticPath, (path) => path.moduleLinks, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'pathId' })
  path: TherapeuticPath;

  @ManyToOne(() => WellnessModule, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'moduleId' })
  module: WellnessModule;

  // Methods
  toJSON(): Partial<PathModuleLink> {
    return {
      id: this.id,
      pathId: this.pathId,
      moduleId: this.moduleId,
      orderIndex: this.orderIndex,
      isRequired: this.isRequired,
      isOptional: this.isOptional,
      prerequisites: this.prerequisites,
      unlockAfterDays: this.unlockAfterDays,
      pathSpecificInstructions: this.pathSpecificInstructions,
      customContent: this.customContent,
      estimatedCompletionDays: this.estimatedCompletionDays,
      completionCriteria: this.completionCriteria,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
