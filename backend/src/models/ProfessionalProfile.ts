import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty, IsEnum, IsOptional } from 'class-validator';
import { User } from './User';
import { Appointment } from './Appointment';

export enum ProfessionalType {
  PSYCHOLOGIST = 'psychologist',
  PSYCHIATRIST = 'psychiatrist',
  THERAPIST = 'therapist',
  COUNSELOR = 'counselor',
  SOCIAL_WORKER = 'social_worker',
  LIFE_COACH = 'life_coach',
  WELLNESS_COACH = 'wellness_coach',
  PEER_SUPPORT = 'peer_support',
  GENERAL_PRACTITIONER = 'general_practitioner',
  FAMILY_DOCTOR = 'family_doctor',
  INTERNIST = 'internist',
  CARDIOLOGIST = 'cardiologist',
  NEUROLOGIST = 'neurologist',
  DERMATOLOGIST = 'dermatologist',
  ENDOCRINOLOGIST = 'endocrinologist',
  GYNECOLOGIST = 'gynecologist',
  PEDIATRICIAN = 'pediatrician',
  GERIATRICIAN = 'geriatrician',
  ONCOLOGIST = 'oncologist',
  RADIOLOGIST = 'radiologist',
  ANESTHESIOLOGIST = 'anesthesiologist',
  SURGEON = 'surgeon',
  ORTHOPEDIC_SURGEON = 'orthopedic_surgeon',
  PLASTIC_SURGEON = 'plastic_surgeon',
  OPHTHALMOLOGIST = 'ophthalmologist',
  ENT_SPECIALIST = 'ent_specialist',
  UROLOGIST = 'urologist',
  RHEUMATOLOGIST = 'rheumatologist',
  PULMONOLOGIST = 'pulmonologist',
  GASTROENTEROLOGIST = 'gastroenterologist',
  NEPHROLOGIST = 'nephrologist',
  ALLERGIST = 'allergist',
  PATHOLOGIST = 'pathologist',
  EMERGENCY_PHYSICIAN = 'emergency_physician',
  SPORTS_MEDICINE = 'sports_medicine',
  OCCUPATIONAL_MEDICINE = 'occupational_medicine',
  NURSE = 'nurse',
  NURSE_PRACTITIONER = 'nurse_practitioner',
  PHYSICIAN_ASSISTANT = 'physician_assistant',
  PHARMACIST = 'pharmacist',
  PHYSICAL_THERAPIST = 'physical_therapist',
  OCCUPATIONAL_THERAPIST = 'occupational_therapist',
  SPEECH_THERAPIST = 'speech_therapist',
  NUTRITIONIST = 'nutritionist',
  DIETITIAN = 'dietitian',
  MIDWIFE = 'midwife',
  CHIROPRACTOR = 'chiropractor',
  OSTEOPATH = 'osteopath',
  ACUPUNCTURIST = 'acupuncturist',
  MASSAGE_THERAPIST = 'massage_therapist',
  NEUROPSYCHOLOGIST = 'neuropsychologist',
  CHILD_PSYCHOLOGIST = 'child_psychologist',
  FORENSIC_PSYCHOLOGIST = 'forensic_psychologist',
  ADDICTION_SPECIALIST = 'addiction_specialist',
  SEX_THERAPIST = 'sex_therapist',
  TRAUMA_SPECIALIST = 'trauma_specialist',
  EATING_DISORDER_SPECIALIST = 'eating_disorder_specialist',
}

export enum LicenseStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  EXPIRED = 'expired',
  PENDING = 'pending',
}

export enum VerificationStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  REJECTED = 'rejected',
  UNDER_REVIEW = 'under_review',
}

@Entity('professional_profiles')
export class ProfessionalProfile {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  userId: string;

  @Column({
    type: 'simple-enum',
    enum: ProfessionalType,
  })
  @IsEnum(ProfessionalType)
  professionalType: ProfessionalType;

  @Column({ length: 200 })
  @IsNotEmpty()
  licenseNumber: string;

  @Column({
    type: 'simple-enum',
    enum: LicenseStatus,
    default: LicenseStatus.PENDING,
  })
  @IsEnum(LicenseStatus)
  licenseStatus: LicenseStatus;

  @Column({ type: 'date' })
  licenseExpiryDate: Date;

  @Column({ length: 200 })
  @IsNotEmpty()
  licensingBoard: string;

  @Column({ length: 200 })
  @IsNotEmpty()
  licensingState: string;

  @Column({
    type: 'simple-enum',
    enum: VerificationStatus,
    default: VerificationStatus.PENDING,
  })
  @IsEnum(VerificationStatus)
  verificationStatus: VerificationStatus;

  @Column({ type: 'datetime', nullable: true })
  verifiedAt?: Date;

  @Column({ nullable: true })
  verifiedBy?: string; // Admin user ID who verified

  @Column({ type: 'text', nullable: true })
  verificationNotes?: string;

  @Column({ type: 'json' })
  specializations: string[];

  @Column({ type: 'json', nullable: true })
  treatmentApproaches: string[];

  @Column({ type: 'json', nullable: true })
  languagesSpoken: string[];

  @Column({ type: 'int', default: 0 })
  yearsOfExperience: number;

  @Column({ type: 'text' })
  @IsNotEmpty()
  professionalBio: string;

  @Column({ type: 'json', nullable: true })
  education: Array<{
    degree: string;
    institution: string;
    year: number;
    field: string;
  }>;

  @Column({ type: 'json', nullable: true })
  certifications: Array<{
    name: string;
    issuingOrganization: string;
    issueDate: Date;
    expiryDate?: Date;
    credentialId?: string;
  }>;

  @Column({ type: 'json', nullable: true })
  workExperience: Array<{
    position: string;
    organization: string;
    startDate: Date;
    endDate?: Date;
    description: string;
  }>;

  @Column({ type: 'json', nullable: true })
  availability: {
    timezone: string;
    schedule: Record<string, Array<{
      startTime: string;
      endTime: string;
    }>>;
    exceptions: Array<{
      date: Date;
      reason: string;
      isAvailable: boolean;
    }>;
  };

  @Column({ type: 'json', nullable: true })
  consultationTypes: Array<{
    type: 'video' | 'phone' | 'chat' | 'in_person';
    duration: number; // minutes
    price: number;
    currency: string;
  }>;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  hourlyRate?: number;

  @Column({ length: 3, default: 'USD' })
  currency: string;

  @Column({ default: true })
  acceptingNewClients: boolean;

  @Column({ type: 'int', nullable: true })
  maxClientsPerWeek?: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  averageRating: number;

  @Column({ type: 'int', default: 0 })
  totalReviews: number;

  @Column({ type: 'int', default: 0 })
  totalAppointments: number;

  @Column({ type: 'json', nullable: true })
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };

  @Column({ type: 'json', nullable: true })
  insuranceAccepted: string[];

  @Column({ type: 'text', nullable: true })
  specialNotes?: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @OneToMany(() => Appointment, (appointment) => appointment.professional)
  appointments: Appointment[];

  // Nouveaux champs pour le système médical
  @Column({ nullable: true })
  medicalLicenseNumber?: string; // Numéro RPPS pour la France

  @Column({ nullable: true })
  medicalSchoolDegree?: string; // Diplôme de médecine

  @Column({ type: 'date', nullable: true })
  medicalLicenseExpiryDate?: Date;

  @Column({ nullable: true })
  medicalSpecialtyBoard?: string; // Conseil de spécialité

  @Column({ type: 'json', nullable: true })
  medicalCertifications?: Array<{
    name: string;
    boardName: string;
    issueDate: Date;
    expiryDate?: Date;
    certificateNumber: string;
    isActive: boolean;
  }>;

  @Column({ type: 'json', nullable: true })
  hospitalAffiliations?: Array<{
    hospitalName: string;
    department: string;
    position: string;
    startDate: Date;
    endDate?: Date;
    isActive: boolean;
  }>;

  @Column({ type: 'json', nullable: true })
  prescriptionAuthority?: {
    hasAuthority: boolean;
    authorityType: string; // 'full' | 'limited' | 'none'
    restrictions?: string[];
    expiryDate?: Date;
  };

  @Column({ type: 'json', nullable: true })
  telemedicineCapabilities?: {
    hasEquipment: boolean;
    platforms: string[]; // ['zoom', 'teams', 'custom']
    equipmentDetails?: string;
    internetSpeedMbps?: number;
    backupConnection: boolean;
  };

  @Column({ type: 'json', nullable: true })
  billingInformation?: {
    taxId: string;
    billingAddress: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
    bankAccount?: {
      accountNumber: string;
      routingNumber: string;
      bankName: string;
    };
    acceptedPaymentMethods: string[];
  };

  @Column({ type: 'json', nullable: true })
  complianceCertifications?: Array<{
    name: string; // 'HIPAA', 'GDPR', etc.
    issueDate: Date;
    expiryDate?: Date;
    certificateId?: string;
    isValid: boolean;
  }>;

  @Column({ type: 'json', nullable: true })
  clinicalSettings?: Array<{
    type: 'hospital' | 'clinic' | 'private_practice' | 'telehealth_only';
    name: string;
    address?: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
    contactInfo?: {
      phone: string;
      email: string;
      website?: string;
    };
    isPrimary: boolean;
  }>;

  @Column({ type: 'json', nullable: true })
  continuingEducation?: Array<{
    courseName: string;
    provider: string;
    completionDate: Date;
    creditHours: number;
    certificateUrl?: string;
    category: string;
  }>;

  @Column({ type: 'json', nullable: true })
  researchExperience?: Array<{
    title: string;
    role: string;
    institution: string;
    startDate: Date;
    endDate?: Date;
    publications?: string[];
    description: string;
  }>;

  @Column({ type: 'json', nullable: true })
  professionalMemberships?: Array<{
    organizationName: string;
    membershipType: string;
    startDate: Date;
    endDate?: Date;
    membershipId?: string;
    isActive: boolean;
  }>;

  @Column({ type: 'json', nullable: true })
  performanceMetrics?: {
    patientSatisfactionScore?: number;
    averageAppointmentDuration?: number;
    cancellationRate?: number;
    noShowRate?: number;
    responseTime?: number; // minutes
    availabilityScore?: number;
  };

  // Methods
  toJSON(): Partial<ProfessionalProfile> {
    return {
      id: this.id,
      professionalType: this.professionalType,
      licenseNumber: this.licenseNumber,
      licenseStatus: this.licenseStatus,
      licensingBoard: this.licensingBoard,
      licensingState: this.licensingState,
      verificationStatus: this.verificationStatus,
      verifiedAt: this.verifiedAt,
      specializations: this.specializations,
      treatmentApproaches: this.treatmentApproaches,
      languagesSpoken: this.languagesSpoken,
      yearsOfExperience: this.yearsOfExperience,
      professionalBio: this.professionalBio,
      education: this.education,
      certifications: this.certifications,
      availability: this.availability,
      consultationTypes: this.consultationTypes,
      hourlyRate: this.hourlyRate,
      currency: this.currency,
      acceptingNewClients: this.acceptingNewClients,
      averageRating: this.averageRating,
      totalReviews: this.totalReviews,
      totalAppointments: this.totalAppointments,
      insuranceAccepted: this.insuranceAccepted,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
