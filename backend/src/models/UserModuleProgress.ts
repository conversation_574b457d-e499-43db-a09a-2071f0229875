import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { IsNotEmpty, IsEnum, IsOptional } from 'class-validator';
import { User } from './User';
import { WellnessModule } from './WellnessModule';

export enum ProgressStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  PAUSED = 'paused',
  ABANDONED = 'abandoned',
}

@Entity('user_module_progress')
@Index(['userId', 'moduleId'], { unique: true })
export class UserModuleProgress {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  userId: string;

  @Column()
  @IsNotEmpty()
  moduleId: string;

  @Column({
    type: 'simple-enum',
    enum: ProgressStatus,
    default: ProgressStatus.NOT_STARTED,
  })
  @IsEnum(ProgressStatus)
  status: ProgressStatus;

  @Column({ type: 'int', default: 0 })
  progressPercentage: number; // 0-100

  @Column({ type: 'json', nullable: true })
  completedSections: string[]; // Array of section IDs

  @Column({ type: 'json', nullable: true })
  sectionProgress: Record<string, {
    completed: boolean;
    timeSpent: number; // in minutes
    lastAccessed: Date;
    notes?: string;
  }>;

  @Column({ type: 'int', default: 0 })
  totalTimeSpentMinutes: number;

  @Column({ type: 'datetime', nullable: true })
  startedAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  completedAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt?: Date;

  @Column({ type: 'int', nullable: true })
  userRating?: number; // 1-5 stars

  @Column({ type: 'text', nullable: true })
  userFeedback?: string;

  @Column({ type: 'text', nullable: true })
  personalNotes?: string;

  @Column({ type: 'json', nullable: true })
  achievements: Array<{
    id: string;
    name: string;
    description: string;
    earnedAt: Date;
  }>;

  @Column({ type: 'json', nullable: true })
  challenges: Array<{
    section: string;
    difficulty: string;
    resolved: boolean;
    notes?: string;
  }>;

  @Column({ type: 'json', nullable: true })
  goals: Array<{
    description: string;
    targetDate?: Date;
    completed: boolean;
    completedAt?: Date;
  }>;

  @Column({ default: false })
  certificateEarned: boolean;

  @Column({ type: 'datetime', nullable: true })
  certificateEarnedAt?: Date;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'float', default: 0 })
  progress: number;

  @Column({ type: 'text', nullable: true })
  feedback: string;

  @Column({ type: 'json', nullable: true })
  stats: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => WellnessModule, (module) => module.userProgress, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'moduleId' })
  module: WellnessModule;

  // Methods
  updateProgress(): void {
    if (this.completedSections && this.module?.content?.sections) {
      const totalSections = this.module.content.sections.length;
      const completedCount = this.completedSections.length;
      this.progressPercentage = Math.round((completedCount / totalSections) * 100);
      
      if (this.progressPercentage === 100 && this.status !== ProgressStatus.COMPLETED) {
        this.status = ProgressStatus.COMPLETED;
        this.completedAt = new Date();
      }
    }
  }

  toJSON(): Partial<UserModuleProgress> {
    return {
      id: this.id,
      moduleId: this.moduleId,
      status: this.status,
      progressPercentage: this.progressPercentage,
      completedSections: this.completedSections,
      totalTimeSpentMinutes: this.totalTimeSpentMinutes,
      startedAt: this.startedAt,
      completedAt: this.completedAt,
      lastAccessedAt: this.lastAccessedAt,
      userRating: this.userRating,
      userFeedback: this.userFeedback,
      personalNotes: this.personalNotes,
      achievements: this.achievements,
      goals: this.goals,
      certificateEarned: this.certificateEarned,
      certificateEarnedAt: this.certificateEarnedAt,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
