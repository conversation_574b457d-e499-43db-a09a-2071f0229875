import Joi from 'joi';

export const createMentalHealthDataSchema = Joi.object({
  type: Joi.string().valid('journal', 'mood', 'stress', 'sleep', 'energy', 'custom').required(),
  data: Joi.object().required(),
  recordedAt: Joi.date().optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  notes: Joi.string().optional(),
});

export const updateMentalHealthDataSchema = Joi.object({
  type: Joi.string().valid('journal', 'mood', 'stress', 'sleep', 'energy', 'custom').optional(),
  data: Joi.object().optional(),
  recordedAt: Joi.date().optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  notes: Joi.string().optional(),
}); 