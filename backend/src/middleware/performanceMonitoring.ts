import { Request, Response, NextFunction } from 'express';
import { performance } from 'perf_hooks';

interface PerformanceMetric {
  operation: string;
  duration: number;
  success: boolean;
  timestamp: Date;
  userId?: string;
  endpoint: string;
  method: string;
  statusCode: number;
  userAgent?: string;
  ip?: string;
}

interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  errorRate: number;
  slowRequests: number;
  requestsPerMinute: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private readonly maxMetrics = 10000; // Keep last 10k metrics in memory
  private readonly slowRequestThreshold = 1000; // 1 second

  /**
   * Middleware to track API performance
   */
  trackApiPerformance = (operation: string) => {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = performance.now();
      const originalSend = res.send;

      // Override res.send to capture response time
      res.send = function(data) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        const metric: PerformanceMetric = {
          operation,
          duration,
          success: res.statusCode < 400,
          timestamp: new Date(),
          userId: (req as any).user?.id,
          endpoint: req.path,
          method: req.method,
          statusCode: res.statusCode,
          userAgent: req.get('User-Agent'),
          ip: req.ip
        };

        performanceMonitor.recordMetric(metric);
        
        // Log slow requests
        if (duration > performanceMonitor.slowRequestThreshold) {
          console.warn(`Slow request detected: ${req.method} ${req.path} took ${duration.toFixed(2)}ms`);
        }

        return originalSend.call(this, data);
      };

      next();
    };
  };

  /**
   * Record a performance metric
   */
  recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only the most recent metrics to prevent memory issues
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Send to external monitoring service if configured
    this.sendToExternalService(metric);
  }

  /**
   * Get performance statistics for a specific operation
   */
  getOperationStats(operation: string, timeWindow: number = 3600000): PerformanceStats {
    const cutoffTime = new Date(Date.now() - timeWindow);
    const relevantMetrics = this.metrics.filter(
      m => m.operation === operation && m.timestamp >= cutoffTime
    );

    if (relevantMetrics.length === 0) {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        errorRate: 0,
        slowRequests: 0,
        requestsPerMinute: 0
      };
    }

    const totalRequests = relevantMetrics.length;
    const averageResponseTime = relevantMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests;
    const errorCount = relevantMetrics.filter(m => !m.success).length;
    const errorRate = (errorCount / totalRequests) * 100;
    const slowRequests = relevantMetrics.filter(m => m.duration > this.slowRequestThreshold).length;
    const requestsPerMinute = (totalRequests / (timeWindow / 60000));

    return {
      totalRequests,
      averageResponseTime: Math.round(averageResponseTime * 100) / 100,
      errorRate: Math.round(errorRate * 100) / 100,
      slowRequests,
      requestsPerMinute: Math.round(requestsPerMinute * 100) / 100
    };
  }

  /**
   * Get overall system performance statistics
   */
  getSystemStats(timeWindow: number = 3600000): Record<string, PerformanceStats> {
    const operations = [...new Set(this.metrics.map(m => m.operation))];
    const stats: Record<string, PerformanceStats> = {};

    operations.forEach(operation => {
      stats[operation] = this.getOperationStats(operation, timeWindow);
    });

    return stats;
  }

  /**
   * Get performance alerts based on thresholds
   */
  getPerformanceAlerts(): Array<{
    type: 'warning' | 'critical';
    message: string;
    operation: string;
    value: number;
    threshold: number;
  }> {
    const alerts: Array<{
      type: 'warning' | 'critical';
      message: string;
      operation: string;
      value: number;
      threshold: number;
    }> = [];

    const stats = this.getSystemStats();

    Object.entries(stats).forEach(([operation, operationStats]) => {
      // Check response time alerts
      if (operationStats.averageResponseTime > 2000) {
        alerts.push({
          type: 'critical',
          message: `High response time for ${operation}`,
          operation,
          value: operationStats.averageResponseTime,
          threshold: 2000
        });
      } else if (operationStats.averageResponseTime > 1000) {
        alerts.push({
          type: 'warning',
          message: `Elevated response time for ${operation}`,
          operation,
          value: operationStats.averageResponseTime,
          threshold: 1000
        });
      }

      // Check error rate alerts
      if (operationStats.errorRate > 10) {
        alerts.push({
          type: 'critical',
          message: `High error rate for ${operation}`,
          operation,
          value: operationStats.errorRate,
          threshold: 10
        });
      } else if (operationStats.errorRate > 5) {
        alerts.push({
          type: 'warning',
          message: `Elevated error rate for ${operation}`,
          operation,
          value: operationStats.errorRate,
          threshold: 5
        });
      }
    });

    return alerts;
  }

  /**
   * Send metrics to external monitoring service
   */
  private sendToExternalService(metric: PerformanceMetric): void {
    // Example: Send to New Relic, DataDog, or custom analytics service
    if (process.env.NEW_RELIC_LICENSE_KEY) {
      // New Relic integration would go here
      this.sendToNewRelic(metric);
    }

    if (process.env.DATADOG_API_KEY) {
      // DataDog integration would go here
      this.sendToDataDog(metric);
    }

    // Custom analytics service
    if (process.env.ANALYTICS_ENDPOINT) {
      this.sendToCustomAnalytics(metric);
    }
  }

  private sendToNewRelic(metric: PerformanceMetric): void {
    // New Relic custom metrics implementation
    try {
      // This would use the New Relic Node.js agent
      // newrelic.recordMetric(`Custom/Journal/${metric.operation}/Duration`, metric.duration);
      // newrelic.recordMetric(`Custom/Journal/${metric.operation}/Success`, metric.success ? 1 : 0);
    } catch (error) {
      console.error('Failed to send metric to New Relic:', error);
    }
  }

  private sendToDataDog(metric: PerformanceMetric): void {
    // DataDog StatsD implementation
    try {
      // This would use the DataDog StatsD client
      // dogstatsd.histogram('journal.response_time', metric.duration, [`operation:${metric.operation}`]);
      // dogstatsd.increment('journal.requests', 1, [`operation:${metric.operation}`, `success:${metric.success}`]);
    } catch (error) {
      console.error('Failed to send metric to DataDog:', error);
    }
  }

  private sendToCustomAnalytics(metric: PerformanceMetric): void {
    // Custom analytics service implementation
    try {
      // This would send to your custom analytics endpoint
      fetch(process.env.ANALYTICS_ENDPOINT!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(metric)
      }).catch(error => {
        console.error('Failed to send metric to custom analytics:', error);
      });
    } catch (error) {
      console.error('Failed to send metric to custom analytics:', error);
    }
  }

  /**
   * Generate performance report
   */
  generateReport(timeWindow: number = 86400000): {
    summary: PerformanceStats;
    operations: Record<string, PerformanceStats>;
    alerts: Array<any>;
    topSlowRequests: PerformanceMetric[];
    topErrors: PerformanceMetric[];
  } {
    const cutoffTime = new Date(Date.now() - timeWindow);
    const relevantMetrics = this.metrics.filter(m => m.timestamp >= cutoffTime);

    // Overall summary
    const summary = this.getOperationStats('all', timeWindow);
    
    // Per-operation stats
    const operations = this.getSystemStats(timeWindow);
    
    // Current alerts
    const alerts = this.getPerformanceAlerts();
    
    // Top slow requests
    const topSlowRequests = relevantMetrics
      .filter(m => m.duration > this.slowRequestThreshold)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);
    
    // Top errors
    const topErrors = relevantMetrics
      .filter(m => !m.success)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10);

    return {
      summary,
      operations,
      alerts,
      topSlowRequests,
      topErrors
    };
  }

  /**
   * Clear old metrics to free memory
   */
  clearOldMetrics(maxAge: number = 86400000): void {
    const cutoffTime = new Date(Date.now() - maxAge);
    this.metrics = this.metrics.filter(m => m.timestamp >= cutoffTime);
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Middleware functions for specific journal operations
export const journalPerformanceMiddleware = {
  create: performanceMonitor.trackApiPerformance('journal_create'),
  read: performanceMonitor.trackApiPerformance('journal_read'),
  update: performanceMonitor.trackApiPerformance('journal_update'),
  delete: performanceMonitor.trackApiPerformance('journal_delete'),
  list: performanceMonitor.trackApiPerformance('journal_list'),
  stats: performanceMonitor.trackApiPerformance('journal_stats')
};

// Cleanup job to prevent memory leaks
setInterval(() => {
  performanceMonitor.clearOldMetrics();
}, 3600000); // Run every hour
