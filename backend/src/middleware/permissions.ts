import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';

export function requirePermission(permission: string) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    const user = req.user;
    const userPermissions = user?.role?.permissions?.map(p => p.name) || [];
    if (userPermissions.includes(permission)) {
      next();
      return;
    }
    res.status(403).json({ success: false, message: 'Permission refusée' });
  };
} 