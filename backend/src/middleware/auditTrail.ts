import { Response, NextFunction } from 'express';
import { createAuditLogger } from '../utils/auditTrail';
import { AuthenticatedRequest } from './auth';

const auditLogger = createAuditLogger();

export function auditTrail(action: string) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    auditLogger.info({
      action,
      userId: req.user?.id,
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString(),
    });
    next();
  };
} 