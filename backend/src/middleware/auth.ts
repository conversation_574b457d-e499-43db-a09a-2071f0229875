import { Request, Response, NextFunction } from 'express';
import { AppDataSource } from '../config/database';
import { User } from '../models/User';
import { JwtUtils, JwtPayload } from '../utils/jwt';
import { AuthenticationError, AuthorizationError } from '../utils/errors';

export interface AuthenticatedRequest extends Request {
  user?: User;
  jwtPayload?: JwtPayload;
}

export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ success: false, message: 'No token provided' });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    const payload = JwtUtils.verifyToken(token);

    // Get user from database to ensure they still exist and are active
    const userRepository = AppDataSource.getRepository(User);
    const user = await userRepository.findOne({
      where: { id: payload.userId },
      relations: ['role', 'role.permissions'],
    });

    if (!user) {
      res.status(401).json({ success: false, message: 'User not found' });
      return;
    }

    if (user.status !== 'active') {
      res.status(401).json({ success: false, message: 'User account is not active' });
      return;
    }

    req.user = user;
    req.jwtPayload = payload;
    next();
  } catch (error) {
    res.status(401).json({ success: false, message: 'Invalid token' });
  }
};

export const authorize = (permissions: string[] = []) => {
  return async (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      // Root users have all permissions
      if (req.user.role.name === 'root') {
        return next();
      }

      // Check if user has required permissions
      if (permissions.length > 0) {
        const userPermissions = req.user.role.permissions?.map(p => p.name) || [];
        const hasPermission = permissions.some(permission =>
          userPermissions.includes(permission)
        );

        if (!hasPermission) {
          throw new AuthorizationError(
            `Access denied. Required permissions: ${permissions.join(', ')}`
          );
        }
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

export const requireRole = (roles: string[] = []) => {
  return async (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      if (roles.length > 0 && !roles.includes(req.user.role.name)) {
        throw new AuthorizationError(
          `Access denied. Required roles: ${roles.join(', ')}`
        );
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};
