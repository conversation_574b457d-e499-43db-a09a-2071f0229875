import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import StripeService from '../services/StripeService';
import { logger } from '../middlewares/logger';
import { AppDataSource } from '../config/database';
import { Payment, PaymentStatus } from '../models/Payment';
import { websocketServiceInstance } from '../services/websocketInstance';

const paymentRepository = AppDataSource.getRepository(Payment);

const PaymentController = {
  createPayment: (async (req, res) => {
    try {
      const { userId, amount, type } = req.body;
      logger.info(`[Paiement] Demande de création - userId: ${userId}, amount: ${amount}, type: ${type}`);
      const paymentIntent = await StripeService.createPaymentIntent(userId, amount, type);
      logger.info(`[Paiement] PaymentIntent créé - id: ${paymentIntent.id}, userId: ${userId}`);
      if (websocketServiceInstance) {
        websocketServiceInstance.emitToRole('admin', 'monitoring_alert', {
          type: 'paiement',
          message: 'Nouveau paiement créé',
          userId,
          amount,
          paymentIntentId: paymentIntent.id,
        });
      }
      res.json({ clientSecret: paymentIntent.client_secret });
    } catch (error: unknown) {
      logger.error(`[Paiement] Erreur création paiement: ${error}`);
      if (websocketServiceInstance) {
        websocketServiceInstance.emitToRole('admin', 'monitoring_alert', {
          type: 'payment_error',
          message: 'Erreur de paiement détectée',
          details: (error as Error).message,
        });
      }
      res.status(500).json({ error: 'Erreur lors de la création du paiement.' });
    }
  }) as RequestHandler,

  handleWebhook: (async (req, res) => {
    try {
      const event = req.body;
      logger.info(`[Paiement] Webhook Stripe reçu - type: ${event.type}`);
      await StripeService.handleWebhook(event);
      res.status(200).send('Webhook reçu');
    } catch (error: unknown) {
      logger.error(`[Paiement] Erreur webhook Stripe: ${error}`);
      res.status(400).json({ error: 'Erreur webhook Stripe.' });
    }
  }) as RequestHandler,

  createSubscription: (async (req, res) => {
    try {
      const { userId, priceId } = req.body;
      logger.info(`[Paiement] Demande abonnement - userId: ${userId}, priceId: ${priceId}`);
      const subscription = await StripeService.createSubscription(userId, priceId);
      logger.info(`[Paiement] Abonnement créé - userId: ${userId}`);
      res.json({ subscription });
    } catch (error: unknown) {
      logger.error(`[Paiement] Erreur création abonnement: ${error}`);
      res.status(500).json({ error: 'Erreur lors de la création de l\'abonnement.' });
    }
  }) as RequestHandler,

  getStats: (async (req, res) => {
    try {
      const total = await paymentRepository.count();
      const sum = await paymentRepository.sum('amount');
      const statusStats = await paymentRepository
        .createQueryBuilder('payment')
        .select('payment.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .groupBy('status')
        .getRawMany();
      res.json({ total, sum, statusStats });
    } catch (error: unknown) {
      logger.error(`[Paiement] Erreur stats: ${error}`);
      res.status(500).json({ error: 'Erreur lors de la récupération des stats.' });
    }
  }) as RequestHandler,
};

export default PaymentController; 