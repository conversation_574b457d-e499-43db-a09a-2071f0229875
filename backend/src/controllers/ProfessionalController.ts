import { Request, Response, NextFunction } from 'express';
import { ProfessionalService, ProfessionalFilters } from '../services/ProfessionalService';
import { AuthenticatedRequest } from '../middleware/auth';
import { ValidationError } from '../utils/errors';
import { ProfessionalType, VerificationStatus } from '../models/ProfessionalProfile';

export class ProfessionalController {
  private professionalService = new ProfessionalService();

  createProfessionalProfile = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const data = req.body;
      const profile = await this.professionalService.createProfessionalProfile(req.user.id, data);

      res.status(201).json({
        success: true,
        message: 'Professional profile created successfully. Verification pending.',
        data: { profile: profile.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getProfessionals = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Parse query parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);

      const filters: ProfessionalFilters = {};

      if (req.query.professionalType) {
        filters.professionalType = req.query.professionalType as ProfessionalType;
      }

      if (req.query.acceptingNewClients !== undefined) {
        filters.acceptingNewClients = req.query.acceptingNewClients === 'true';
      }

      if (req.query.minRating) {
        filters.minRating = parseFloat(req.query.minRating as string);
      }

      if (req.query.maxHourlyRate) {
        filters.maxHourlyRate = parseFloat(req.query.maxHourlyRate as string);
      }

      if (req.query.specializations) {
        filters.specializations = Array.isArray(req.query.specializations) 
          ? req.query.specializations as string[]
          : [req.query.specializations as string];
      }

      if (req.query.languagesSpoken) {
        filters.languagesSpoken = Array.isArray(req.query.languagesSpoken) 
          ? req.query.languagesSpoken as string[]
          : [req.query.languagesSpoken as string];
      }

      if (req.query.consultationType) {
        filters.consultationType = req.query.consultationType as 'video' | 'phone' | 'chat' | 'in_person';
      }

      // Location filters (without lat/lng/radius for now as they're not supported)
      if (req.query.city || req.query.state || req.query.country) {
        filters.location = {
          city: req.query.city as string,
          state: req.query.state as string,
          country: req.query.country as string,
        };
      }

      const result = await this.professionalService.getProfessionals(filters, page, limit);

      res.json({
        success: true,
        message: 'Professionals retrieved successfully',
        data: {
          professionals: result.professionals.map(prof => prof.toJSON()),
          pagination: {
            page: result.page,
            limit,
            total: result.total,
            totalPages: result.totalPages,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  };

  getProfessionalById = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const professional = await this.professionalService.getProfessionalById(id);

      if (!professional) {
        res.status(404).json({
          success: false,
          message: 'Professional not found',
        });
        return;
      }

      res.json({
        success: true,
        message: 'Professional retrieved successfully',
        data: { professional: professional.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getMyProfessionalProfile = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const professional = await this.professionalService.getProfessionalByUserId(req.user.id);
      
      if (!professional) {
        res.status(404).json({
          success: false,
          message: 'Professional profile not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Professional profile retrieved successfully',
        data: { professional: professional.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  updateProfessionalProfile = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const data = req.body;
      
      // First get the professional by userId
      const existingProfessional = await this.professionalService.getProfessionalByUserId(req.user.id);
      if (!existingProfessional) {
        throw new ValidationError('Professional profile not found');
      }

      const professional = await this.professionalService.updateProfessionalProfile(existingProfessional.id, data);

      res.json({
        success: true,
        message: 'Professional profile updated successfully',
        data: { professional: professional.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  verifyProfessional = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      // Only admins can verify professionals
      if (req.user.role.name !== 'admin' && req.user.role.name !== 'root') {
        throw new ValidationError('Insufficient permissions to verify professionals');
      }

      const { id } = req.params;
      const { notes } = req.body;

      const professional = await this.professionalService.verifyProfessional(
        id,
        req.user.id,
        notes
      );

      res.json({
        success: true,
        message: 'Professional verified successfully',
        data: { professional: professional.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getProfessionalStats = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const stats = await this.professionalService.getStatistics();

      res.json({
        success: true,
        message: 'Professional statistics retrieved successfully',
        data: { stats },
      });
    } catch (error) {
      next(error);
    }
  };

  // Helper endpoint to get available filter options
  getFilterOptions = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const options = await this.professionalService.getFilterOptions();

      res.json({
        success: true,
        message: 'Filter options retrieved successfully',
        data: options,
      });
    } catch (error) {
      next(error);
    }
  };

  updateProfessional = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      const data = req.body;
      
      // Check if user has permission to update this professional (admin or the professional themselves)
      const existingProfessional = await this.professionalService.getProfessionalById(id);
      if (existingProfessional.userId !== req.user.id && req.user.role.name !== 'admin' && req.user.role.name !== 'root') {
        throw new ValidationError('Insufficient permissions to update this professional profile');
      }

      const professional = await this.professionalService.updateProfessionalProfile(existingProfessional.id, data);

      res.json({
        success: true,
        message: 'Professional profile updated successfully',
        data: { professional: professional.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  deleteProfessional = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      
      // Check if user has permission to delete this professional (admin or the professional themselves)
      const existingProfessional = await this.professionalService.getProfessionalById(id);
      if (existingProfessional.userId !== req.user.id && req.user.role.name !== 'admin' && req.user.role.name !== 'root') {
        throw new ValidationError('Insufficient permissions to delete this professional profile');
      }

      // Deactivate the professional profile by setting acceptingNewClients to false
      await this.professionalService.updateProfessionalProfile(existingProfessional.id, { 
        acceptingNewClients: false 
      });

      res.json({
        success: true,
        message: 'Professional profile deactivated successfully',
      });
    } catch (error) {
      next(error);
    }
  };
}
