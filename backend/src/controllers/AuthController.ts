import { Request, Response, NextFunction, RequestHandler } from 'express';
import { AuthService, RegisterDto, LoginDto } from '../services/AuthService';
import { AuthenticatedRequest } from '../middleware/auth';
import { ValidationError } from '../utils/errors';
import speakeasy from 'speakeasy';
import qrcode from 'qrcode';
import { AppDataSource } from '../config/database';
import { User } from '../models/User';

export class AuthController {
  private authService = new AuthService();

  register = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { email, password, firstName, lastName, phone } = req.body;

      // Basic validation
      if (!email || !password || !firstName || !lastName) {
        throw new ValidationError('Email, password, firstName, and lastName are required');
      }

      if (password.length < 8) {
        throw new ValidationError('Password must be at least 8 characters long');
      }

      const registerDto: RegisterDto = {
        email,
        password,
        firstName,
        lastName,
        phone,
      };

      const result = await this.authService.register(registerDto);

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  login = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { email, password } = req.body;
      const userAgent = req.headers['user-agent'] || '';
      const ip = req.ip || '';

      if (!email || !password) {
        throw new ValidationError('Email and password are required');
      }

      const loginDto: LoginDto = { email, password };
      const result = await this.authService.login(loginDto, userAgent, ip);

      res.json({
        success: true,
        message: 'Login successful',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  refreshToken = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { refreshToken } = req.body;
      const userAgent = req.headers['user-agent'] || '';
      const ip = req.ip || '';

      if (!refreshToken) {
        throw new ValidationError('Refresh token is required');
      }

      const result = await this.authService.refresh(refreshToken, userAgent, ip);

      res.json({
        success: true,
        message: 'Token refreshed successfully',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  getCurrentUser = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const user = await this.authService.getCurrentUser(req.user.id);

      res.json({
        success: true,
        message: 'User retrieved successfully',
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  };

  logout = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { refreshToken } = req.body;
      await this.authService.logout(refreshToken);
      res.json({
        success: true,
        message: 'Logout successful',
      });
    } catch (error) {
      next(error);
    }
  };

  enable2FA = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      const secret = speakeasy.generateSecret({ name: 'MindFlow' });
      user.twoFASecret = secret.base32;
      user.is2FAEnabled = true;
      await AppDataSource.getRepository(User).save(user);
      const otpauthUrl = secret.otpauth_url || '';
      const qr = await qrcode.toDataURL(otpauthUrl);
      res.json({ success: true, qr, otpauthUrl });
    } catch (error) {
      next(error);
    }
  };

  verify2FA = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      const { token } = req.body;
      const verified = speakeasy.totp.verify({
        secret: user.twoFASecret || '',
        encoding: 'base32',
        token,
      });
      if (verified) {
        res.json({ success: true });
      } else {
        res.status(400).json({ success: false, message: 'Code invalide' });
      }
    } catch (error) {
      next(error);
    }
  };

  disable2FA = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      user.twoFASecret = undefined;
      user.is2FAEnabled = false;
      await AppDataSource.getRepository(User).save(user);
      res.json({ success: true });
    } catch (error) {
      next(error);
    }
  };
}

const authController = new AuthController();
export const register: RequestHandler = authController.register.bind(authController);
export const login: RequestHandler = authController.login.bind(authController);
export const refresh: RequestHandler = authController.refreshToken.bind(authController);
export const logout: RequestHandler = authController.logout.bind(authController);
export const me: RequestHandler = authController.getCurrentUser.bind(authController);
export const enable2FA: RequestHandler = authController.enable2FA.bind(authController);
export const verify2FA: RequestHandler = authController.verify2FA.bind(authController);
export const disable2FA: RequestHandler = authController.disable2FA.bind(authController);
