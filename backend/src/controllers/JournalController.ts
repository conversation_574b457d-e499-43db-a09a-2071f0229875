import { Request, Response, NextFunction } from 'express';
import { JournalService, CreateJournalEntryDto, UpdateJournalEntryDto, JournalFilters } from '../services/JournalService';
import { AuthenticatedRequest } from '../middleware/auth';
import { ValidationError } from '../utils/errors';
import { EntryType, MoodLevel } from '../models/JournalEntry';

export class JournalController {
  private journalService = new JournalService();

  createEntry = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const data: CreateJournalEntryDto = req.body;
      const entry = await this.journalService.createEntry(req.user.id, data);

      res.status(201).json({
        success: true,
        message: 'Journal entry created successfully',
        data: { entry: entry.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getEntries = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      // Parse query parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100); // Max 100 per page

      const filters: JournalFilters = {};

      if (req.query.entryType) {
        filters.entryType = req.query.entryType as EntryType;
      }

      if (req.query.moodLevel) {
        filters.moodLevel = parseInt(req.query.moodLevel as string) as MoodLevel;
      }

      if (req.query.isPrivate !== undefined) {
        filters.isPrivate = req.query.isPrivate === 'true';
      }

      if (req.query.isFavorite !== undefined) {
        filters.isFavorite = req.query.isFavorite === 'true';
      }

      if (req.query.startDate) {
        filters.startDate = new Date(req.query.startDate as string);
      }

      if (req.query.endDate) {
        filters.endDate = new Date(req.query.endDate as string);
      }

      if (req.query.tags) {
        filters.tags = Array.isArray(req.query.tags) 
          ? req.query.tags as string[]
          : [req.query.tags as string];
      }

      if (req.query.emotions) {
        filters.emotions = Array.isArray(req.query.emotions) 
          ? req.query.emotions as string[]
          : [req.query.emotions as string];
      }

      const result = await this.journalService.getEntries(req.user.id, filters, page, limit);

      res.json({
        success: true,
        message: 'Journal entries retrieved successfully',
        data: {
          entries: result.entries.map(entry => entry.toJSON()),
          pagination: {
            page: result.page,
            limit,
            total: result.total,
            totalPages: result.totalPages,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  };

  getEntryById = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      const entry = await this.journalService.getEntryById(req.user.id, id);

      res.json({
        success: true,
        message: 'Journal entry retrieved successfully',
        data: { entry: entry.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  updateEntry = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      const data: UpdateJournalEntryDto = req.body;
      const entry = await this.journalService.updateEntry(req.user.id, id, data);

      res.json({
        success: true,
        message: 'Journal entry updated successfully',
        data: { entry: entry.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  deleteEntry = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      await this.journalService.deleteEntry(req.user.id, id);

      res.json({
        success: true,
        message: 'Journal entry deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  getStats = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const stats = await this.journalService.getEntryStats(req.user.id);

      res.json({
        success: true,
        message: 'Journal statistics retrieved successfully',
        data: { stats },
      });
    } catch (error) {
      next(error);
    }
  };

  // Helper endpoint to get available options for filters
  getFilterOptions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const entryTypes = Object.values(EntryType);
      const moodLevels = Object.values(MoodLevel).filter(value => typeof value === 'number');

      res.json({
        success: true,
        message: 'Filter options retrieved successfully',
        data: {
          entryTypes,
          moodLevels,
          stressLevels: Array.from({ length: 11 }, (_, i) => i), // 0-10
          energyLevels: Array.from({ length: 11 }, (_, i) => i), // 0-10
          sleepQualityLevels: Array.from({ length: 11 }, (_, i) => i), // 0-10
        },
      });
    } catch (error) {
      next(error);
    }
  };

  // Endpoint to get suggested tags and emotions based on user's history
  getSuggestions = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const stats = await this.journalService.getEntryStats(req.user.id);

      // Common mental health tags and emotions for suggestions
      const commonTags = [
        'work', 'family', 'friends', 'health', 'exercise', 'sleep', 'stress', 'anxiety',
        'depression', 'therapy', 'medication', 'goals', 'achievements', 'challenges',
        'gratitude', 'mindfulness', 'meditation', 'self-care', 'relationships'
      ];

      const commonEmotions = [
        'happy', 'sad', 'angry', 'anxious', 'excited', 'frustrated', 'calm', 'overwhelmed',
        'grateful', 'hopeful', 'worried', 'content', 'lonely', 'confident', 'stressed',
        'peaceful', 'motivated', 'tired', 'energetic', 'confused'
      ];

      // Combine user's most used with common suggestions
      const suggestedTags = [
        ...stats.mostUsedTags.slice(0, 5).map(t => t.tag),
        ...commonTags.filter(tag => !stats.mostUsedTags.some(t => t.tag === tag))
      ].slice(0, 20);

      const suggestedEmotions = [
        ...stats.mostUsedEmotions.slice(0, 5).map(e => e.emotion),
        ...commonEmotions.filter(emotion => !stats.mostUsedEmotions.some(e => e.emotion === emotion))
      ].slice(0, 20);

      res.json({
        success: true,
        message: 'Suggestions retrieved successfully',
        data: {
          tags: suggestedTags,
          emotions: suggestedEmotions,
        },
      });
    } catch (error) {
      next(error);
    }
  };
}
