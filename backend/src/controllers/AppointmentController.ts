import { Request, Response, NextFunction } from 'express';
import { AppointmentService, CreateAppointmentDto, UpdateAppointmentDto, AppointmentFilters } from '../services/AppointmentService';
import { AuthenticatedRequest } from '../middleware/auth';
import { ValidationError } from '../utils/errors';
import { AppointmentType, AppointmentStatus, AppointmentMode } from '../models/Appointment';

export class AppointmentController {
  private appointmentService = new AppointmentService();

  createAppointment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const data: CreateAppointmentDto = req.body;
      
      // Validate required fields
      if (!data.professionalId || !data.scheduledAt) {
        throw new ValidationError('Professional ID and scheduled time are required');
      }

      // Parse scheduledAt if it's a string
      if (typeof data.scheduledAt === 'string') {
        data.scheduledAt = new Date(data.scheduledAt);
      }

      const appointment = await this.appointmentService.createAppointment(req.user.id, data);

      res.status(201).json({
        success: true,
        message: 'Appointment created successfully',
        data: { appointment: appointment.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getAppointments = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      // Parse query parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);

      const filters: AppointmentFilters = {};

      // For regular users, only show their own appointments
      // For professionals, show appointments where they are the professional
      // For admins, show all appointments
      if (req.user.role.name === 'admin' || req.user.role.name === 'root') {
        // Admins can filter by client or professional
        if (req.query.clientId) {
          filters.clientId = req.query.clientId as string;
        }
        if (req.query.professionalId) {
          filters.professionalId = req.query.professionalId as string;
        }
      } else {
        // Check if user is a professional
        try {
          // This would require checking if user has a professional profile
          // For now, we'll assume regular users are clients
          filters.clientId = req.user.id;
        } catch {
          filters.clientId = req.user.id;
        }
      }

      if (req.query.status) {
        filters.status = req.query.status as AppointmentStatus;
      }

      if (req.query.appointmentType) {
        filters.appointmentType = req.query.appointmentType as AppointmentType;
      }

      if (req.query.mode) {
        filters.mode = req.query.mode as AppointmentMode;
      }

      if (req.query.isEmergency !== undefined) {
        filters.isEmergency = req.query.isEmergency === 'true';
      }

      if (req.query.dateFrom) {
        filters.dateFrom = new Date(req.query.dateFrom as string);
      }

      if (req.query.dateTo) {
        filters.dateTo = new Date(req.query.dateTo as string);
      }

      const result = await this.appointmentService.getAppointments(filters, page, limit);

      res.json({
        success: true,
        message: 'Appointments retrieved successfully',
        data: {
          appointments: result.appointments.map(apt => apt.toJSON()),
          pagination: {
            page: result.page,
            limit,
            total: result.total,
            totalPages: result.totalPages,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  };

  getAppointmentById = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      const appointment = await this.appointmentService.getAppointmentById(id);

      // Check if user has permission to view this appointment
      const isClient = appointment.clientId === req.user.id;
      const isProfessional = appointment.professional?.userId === req.user.id;
      const isAdmin = req.user.role.name === 'admin' || req.user.role.name === 'root';

      if (!isClient && !isProfessional && !isAdmin) {
        throw new ValidationError('Insufficient permissions to view this appointment');
      }

      res.json({
        success: true,
        message: 'Appointment retrieved successfully',
        data: { appointment: appointment.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  updateAppointment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      let data: UpdateAppointmentDto = req.body;

      // Get appointment to check permissions
      const appointment = await this.appointmentService.getAppointmentById(id);
      
      const isClient = appointment.clientId === req.user.id;
      const isProfessional = appointment.professional?.userId === req.user.id;
      const isAdmin = req.user.role.name === 'admin' || req.user.role.name === 'root';

      if (!isClient && !isProfessional && !isAdmin) {
        throw new ValidationError('Insufficient permissions to update this appointment');
      }

      // Restrict what clients can update vs professionals
      if (isClient && !isProfessional && !isAdmin) {
        // Clients can only update certain fields
        const allowedFields = ['clientNotes', 'preSessionAssessment', 'postSessionAssessment', 'clientRating', 'clientFeedback'];
        const restrictedData: Partial<UpdateAppointmentDto> = {};
        
        allowedFields.forEach(field => {
          if (data[field as keyof UpdateAppointmentDto] !== undefined) {
            (restrictedData as any)[field] = data[field as keyof UpdateAppointmentDto];
          }
        });
        
        data = restrictedData;
      }

      const updatedAppointment = await this.appointmentService.updateAppointment(id, data);

      res.json({
        success: true,
        message: 'Appointment updated successfully',
        data: { appointment: updatedAppointment.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  cancelAppointment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      const { reason } = req.body;

      if (!reason || reason.trim().length === 0) {
        throw new ValidationError('Cancellation reason is required');
      }

      // Get appointment to check permissions
      const appointment = await this.appointmentService.getAppointmentById(id);
      
      const isClient = appointment.clientId === req.user.id;
      const isProfessional = appointment.professional?.userId === req.user.id;
      const isAdmin = req.user.role.name === 'admin' || req.user.role.name === 'root';

      if (!isClient && !isProfessional && !isAdmin) {
        throw new ValidationError('Insufficient permissions to cancel this appointment');
      }

      const cancelledAppointment = await this.appointmentService.cancelAppointment(
        id,
        req.user.id,
        reason.trim()
      );

      res.json({
        success: true,
        message: 'Appointment cancelled successfully',
        data: { appointment: cancelledAppointment.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  rescheduleAppointment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      const { newScheduledAt, reason } = req.body;

      if (!newScheduledAt || !reason) {
        throw new ValidationError('New scheduled time and reason are required');
      }

      // Get appointment to check permissions
      const appointment = await this.appointmentService.getAppointmentById(id);
      
      const isClient = appointment.clientId === req.user.id;
      const isProfessional = appointment.professional?.userId === req.user.id;
      const isAdmin = req.user.role.name === 'admin' || req.user.role.name === 'root';

      if (!isClient && !isProfessional && !isAdmin) {
        throw new ValidationError('Insufficient permissions to reschedule this appointment');
      }

      const rescheduledAppointment = await this.appointmentService.rescheduleAppointment(
        id,
        new Date(newScheduledAt),
        req.user.id,
        reason.trim()
      );

      res.json({
        success: true,
        message: 'Appointment rescheduled successfully',
        data: { appointment: rescheduledAppointment.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getAvailableSlots = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { professionalId } = req.params;
      const { date, duration } = req.query;

      if (!date) {
        throw new ValidationError('Date is required');
      }

      const requestedDate = new Date(date as string);
      const durationMinutes = parseInt(duration as string) || 60;

      const availableSlots = await this.appointmentService.getAvailableSlots(
        professionalId,
        requestedDate,
        durationMinutes
      );

      res.json({
        success: true,
        message: 'Available slots retrieved successfully',
        data: { 
          availableSlots,
          date: requestedDate,
          durationMinutes,
        },
      });
    } catch (error) {
      next(error);
    }
  };

  getAppointmentStats = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const stats = await this.appointmentService.getAppointmentStats();

      res.json({
        success: true,
        message: 'Appointment statistics retrieved successfully',
        data: { stats },
      });
    } catch (error) {
      next(error);
    }
  };

  // Helper endpoint to get available filter options
  getFilterOptions = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const appointmentTypes = Object.values(AppointmentType);
      const appointmentStatuses = Object.values(AppointmentStatus);
      const appointmentModes = Object.values(AppointmentMode);

      res.json({
        success: true,
        message: 'Filter options retrieved successfully',
        data: {
          appointmentTypes,
          appointmentStatuses,
          appointmentModes,
        },
      });
    } catch (error) {
      next(error);
    }
  };
}
