import { Request, Response, NextFunction } from 'express';
import { getHealthMonitoringService } from '../services/HealthMonitoringService';
import { AuthenticatedRequest } from '../types/express';
import { ValidationError } from '../utils/errors';

export class HealthMonitoringController {
  private healthMonitoringService = getHealthMonitoringService();

  /**
   * Get comprehensive health status for authenticated user
   */
  getUserHealthStatus = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const healthStatus = await this.healthMonitoringService.getUserHealthStatus(req.user.id);

      res.json({
        success: true,
        message: 'Health status retrieved successfully',
        data: healthStatus,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get health status for specific user (admin/professional only)
   */
  getSpecificUserHealthStatus = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'Non autorisé'
        });
        return;
      }

      const { userId } = req.params;

      // Vérifier les permissions - admin seulement ou utilisateur consultant son propre profil
      if (req.user.role?.name !== 'admin' && req.user.id !== userId) {
        res.status(403).json({
          success: false,
          message: 'Accès refusé'
        });
        return;
      }

      if (!userId) {
        res.status(400).json({
          success: false,
          message: 'ID utilisateur requis'
        });
        return;
      }

      const healthStatus = await this.healthMonitoringService.getUserHealthStatus(userId);

      res.status(200).json({
        success: true,
        message: 'Statut de santé utilisateur récupéré avec succès',
        data: {
          userId,
          wellnessScore: healthStatus.wellnessScore,
          alerts: healthStatus.alerts,
          recommendations: healthStatus.recommendations,
          lastUpdated: new Date()
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get active alerts for authenticated user
   */
  getUserAlerts = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const alerts = this.healthMonitoringService.getUserAlerts(req.user.id);

      res.json({
        success: true,
        message: 'Health alerts retrieved successfully',
        data: {
          alerts,
          count: alerts.length,
          criticalCount: alerts.filter(a => a.severity === 'critical').length,
          highCount: alerts.filter(a => a.severity === 'high').length,
        },
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get system-wide health monitoring overview (admin only)
   */
  getSystemHealthOverview = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      // Check if user is admin
      const userRole = req.user.role?.name;
      if (userRole !== 'admin') {
        res.status(403).json({
          success: false,
          message: 'Admin access required',
        });
        return;
      }

      const overview = this.healthMonitoringService.getSystemHealthOverview();

      res.json({
        success: true,
        message: 'System health overview retrieved successfully',
        data: overview,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get wellness insights and recommendations
   */
  getWellnessInsights = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const healthStatus = await this.healthMonitoringService.getUserHealthStatus(req.user.id);

      // Generate additional insights
      const insights = {
        wellnessScore: healthStatus.wellnessScore,
        trends: {
          overall: healthStatus.wellnessScore.overallScore,
          mood: healthStatus.wellnessScore.components.mood,
          stress: healthStatus.wellnessScore.components.stress,
          sleep: healthStatus.wellnessScore.components.sleep
        },
        riskLevel: healthStatus.wellnessScore.overallScore < 50 ? 'high' : 
                  healthStatus.wellnessScore.overallScore < 70 ? 'medium' : 'low',
        recommendations: healthStatus.recommendations,
        alerts: healthStatus.alerts,
        improvementAreas: this.identifyImprovementAreas(healthStatus.wellnessScore),
        strengths: this.identifyStrengths(healthStatus.wellnessScore),
      };

      res.json({
        success: true,
        message: 'Wellness insights retrieved successfully',
        data: insights,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get crisis support resources based on user's current status
   */
  getCrisisSupportResources = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const healthStatus = await this.healthMonitoringService.getUserHealthStatus(req.user.id);
      const hasActiveCrisisAlerts = healthStatus.alerts.some(alert => 
        alert.type === 'crisis_keywords' && alert.severity === 'critical'
      );

      const resources = {
        immediate: [
          {
            name: 'National Suicide Prevention Lifeline',
            phone: '988',
            description: '24/7 crisis support',
            website: 'https://suicidepreventionlifeline.org',
            priority: hasActiveCrisisAlerts ? 'critical' : 'high'
          },
          {
            name: 'Crisis Text Line',
            text: 'Text HOME to 741741',
            description: '24/7 crisis support via text',
            website: 'https://crisistextline.org',
            priority: hasActiveCrisisAlerts ? 'critical' : 'high'
          },
          {
            name: 'Emergency Services',
            phone: '911',
            description: 'For immediate medical emergencies',
            priority: 'critical'
          }
        ],
        mentalHealth: [
          {
            name: 'NAMI Helpline',
            phone: '1-800-950-NAMI (6264)',
            description: 'Mental health information and support',
            website: 'https://nami.org'
          },
          {
            name: 'SAMHSA National Helpline',
            phone: '**************',
            description: 'Treatment referral and information service',
            website: 'https://samhsa.gov'
          }
        ],
        wellnessResources: this.getWellnessResourcesForScore(healthStatus.wellnessScore),
        recommendedActions: this.getCrisisActionPlan(healthStatus)
      };

      res.json({
        success: true,
        message: 'Crisis support resources retrieved successfully',
        data: {
          resources,
          userRiskLevel: healthStatus.wellnessScore.overallScore < 50 ? 'high' : 
                        healthStatus.wellnessScore.overallScore < 70 ? 'medium' : 'low',
          hasCrisisAlerts: hasActiveCrisisAlerts,
          timestamp: new Date().toISOString()
        },
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Trigger manual health assessment
   */
  triggerHealthAssessment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      console.log(`🔍 Manual health assessment triggered for user ${req.user.id}`);
      
      const healthStatus = await this.healthMonitoringService.getUserHealthStatus(req.user.id);

      res.json({
        success: true,
        message: 'Health assessment completed successfully',
        data: {
          assessmentTime: new Date().toISOString(),
          ...healthStatus,
        },
      });
    } catch (error) {
      next(error);
    }
  };

  // Helper methods
  private identifyImprovementAreas(wellnessScore: any): string[] {
    const areas = [];
    const { components } = wellnessScore;

    if (components.mood < 60) areas.push('mood');
    if (components.stress < 60) areas.push('stress_management');
    if (components.sleep < 60) areas.push('sleep_quality');
    if (components.social < 60) areas.push('social_connection');
    if (components.activity < 60) areas.push('physical_activity');

    return areas;
  }

  private identifyStrengths(wellnessScore: any): string[] {
    const strengths = [];
    const { components } = wellnessScore;

    if (components.mood >= 70) strengths.push('positive_mood');
    if (components.stress >= 70) strengths.push('stress_resilience');
    if (components.sleep >= 70) strengths.push('good_sleep_habits');
    if (components.social >= 70) strengths.push('strong_social_connections');
    if (components.activity >= 70) strengths.push('active_lifestyle');

    return strengths;
  }

  private getWellnessResourcesForScore(wellnessScore: any) {
    const resources = [];

    if (wellnessScore.components.mood < 60) {
      resources.push({
        type: 'mood',
        title: 'Mood Improvement Techniques',
        description: 'Evidence-based techniques to improve mood',
        actions: ['Practice gratitude journaling', 'Engage in pleasant activities', 'Exercise regularly']
      });
    }

    if (wellnessScore.components.stress > 40) { // High stress (inverted scale)
      resources.push({
        type: 'stress',
        title: 'Stress Management Tools',
        description: 'Techniques to manage and reduce stress',
        actions: ['Deep breathing exercises', 'Progressive muscle relaxation', 'Mindfulness meditation']
      });
    }

    if (wellnessScore.components.sleep < 60) {
      resources.push({
        type: 'sleep',
        title: 'Sleep Hygiene Improvement',
        description: 'Tips for better sleep quality',
        actions: ['Consistent sleep schedule', 'Limit screen time before bed', 'Create relaxing bedtime routine']
      });
    }

    return resources;
  }

  private getCrisisActionPlan(healthStatus: any): string[] {
    const plan = [];
    const { alerts } = healthStatus;

    const hasCrisisAlert = alerts.some((a: any) => a.severity === 'critical');
    
    if (hasCrisisAlert) {
      plan.push('Call 988 (Suicide Prevention Lifeline) immediately');
      plan.push('Go to nearest emergency room if in immediate danger');
      plan.push('Contact trusted friend or family member');
      plan.push('Remove access to means of self-harm');
    } else {
      plan.push('Continue regular mental health maintenance');
      plan.push('Use learned coping strategies');
      plan.push('Seek support when needed');
    }

    return plan;
  }
} 