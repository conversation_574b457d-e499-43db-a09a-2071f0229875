import { Request, Response, NextFunction } from 'express';
import { AICoachService, CreateInteractionDto } from '../services/AICoachService';
import { AuthenticatedRequest } from '../middleware/auth';
import { ValidationError } from '../utils/errors';
import { AICoachInteraction } from '../models/AICoachInteraction';
import { AICoachSession } from '../models/AICoachSession';

export class AICoachController {
  private aiCoachService = new AICoachService();

  createInteraction = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const data: CreateInteractionDto = req.body;
      
      // Basic validation
      if (!data.userMessage || data.userMessage.trim().length === 0) {
        throw new ValidationError('User message is required');
      }

      const interaction = await this.aiCoachService.createInteraction(req.user.id, data);

      res.status(201).json({
        success: true,
        message: 'AI coach interaction created successfully',
        data: { interaction: interaction.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getInteractions = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
      const sessionId = req.query.sessionId as string;

      const result = await this.aiCoachService.getInteractions(req.user.id, sessionId, page, limit);

      res.json({
        success: true,
        message: 'AI coach interactions retrieved successfully',
        data: {
          interactions: result.interactions.map(interaction => interaction.toJSON()),
          pagination: {
            page: result.page,
            limit,
            total: result.total,
            totalPages: result.totalPages,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  };

  getInteractionById = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      const interaction = await this.aiCoachService.getInteractionById(req.user.id, id);

      res.json({
        success: true,
        message: 'AI coach interaction retrieved successfully',
        data: { interaction: interaction.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  rateInteraction = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      const { rating, feedback } = req.body;

      if (!rating || typeof rating !== 'number') {
        throw new ValidationError('Rating is required and must be a number');
      }

      const interaction = await this.aiCoachService.rateInteraction(req.user.id, id, rating, feedback);

      res.json({
        success: true,
        message: 'Interaction rated successfully',
        data: { interaction: interaction.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getSessions = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const sessions = await this.aiCoachService.getSessions(req.user.id);

      res.json({
        success: true,
        message: 'AI coach sessions retrieved successfully',
        data: { sessions },
      });
    } catch (error) {
      next(error);
    }
  };

  getStats = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const stats = await this.aiCoachService.getInteractionStats(req.user.id);

      res.json({
        success: true,
        message: 'AI coach statistics retrieved successfully',
        data: { stats },
      });
    } catch (error) {
      next(error);
    }
  };

  // Helper endpoint for crisis support
  getCrisisResources = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const crisisResources = {
        immediate: [
          {
            name: 'National Suicide Prevention Lifeline',
            phone: '988',
            description: '24/7 crisis support',
            website: 'https://suicidepreventionlifeline.org'
          },
          {
            name: 'Crisis Text Line',
            text: 'Text HOME to 741741',
            description: '24/7 crisis support via text',
            website: 'https://crisistextline.org'
          },
          {
            name: 'Emergency Services',
            phone: '911',
            description: 'For immediate medical emergencies'
          }
        ],
        mentalHealth: [
          {
            name: 'NAMI Helpline',
            phone: '1-800-950-NAMI (6264)',
            description: 'Mental health information and support',
            website: 'https://nami.org'
          },
          {
            name: 'SAMHSA National Helpline',
            phone: '**************',
            description: 'Treatment referral and information service',
            website: 'https://samhsa.gov'
          }
        ],
        workplace: [
          {
            name: 'Employee Assistance Program (EAP)',
            description: 'Check with your employer for available mental health resources'
          },
          {
            name: 'Workplace Mental Health',
            website: 'https://workplacementalhealth.org',
            description: 'Resources for workplace mental health'
          }
        ]
      };

      res.json({
        success: true,
        message: 'Crisis resources retrieved successfully',
        data: { resources: crisisResources },
      });
    } catch (error) {
      next(error);
    }
  };

  // Helper endpoint for wellness tips
  getWellnessTips = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const tips = [
        {
          category: 'Daily Wellness',
          tips: [
            'Start your day with 5 minutes of deep breathing',
            'Take regular breaks from screens throughout the day',
            'Practice gratitude by writing down 3 things you\'re thankful for',
            'Stay hydrated and eat nutritious meals',
            'Get some natural sunlight when possible'
          ]
        },
        {
          category: 'Stress Management',
          tips: [
            'Use the 4-7-8 breathing technique when feeling overwhelmed',
            'Try progressive muscle relaxation before bed',
            'Break large tasks into smaller, manageable steps',
            'Set boundaries and learn to say no when needed',
            'Practice mindfulness meditation for 10 minutes daily'
          ]
        },
        {
          category: 'Sleep Hygiene',
          tips: [
            'Maintain a consistent sleep schedule',
            'Create a relaxing bedtime routine',
            'Avoid screens 1 hour before bedtime',
            'Keep your bedroom cool, dark, and quiet',
            'Limit caffeine intake after 2 PM'
          ]
        },
        {
          category: 'Social Connection',
          tips: [
            'Reach out to a friend or family member regularly',
            'Join a community group or hobby club',
            'Practice active listening in conversations',
            'Express appreciation to people in your life',
            'Volunteer for a cause you care about'
          ]
        }
      ];

      res.json({
        success: true,
        message: 'Wellness tips retrieved successfully',
        data: { tips },
      });
    } catch (error) {
      next(error);
    }
  };

  createSession = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }
      
      const userId = req.user.id;
      const { theme, goal } = req.body;

      const session = new AICoachSession();
      session.userId = userId;
      session.theme = theme || 'conversation_generale';
      session.goal = goal || 'Soutien et guidance personnalisée';
      session.startTime = new Date();
      session.status = 'active';

      await session.save();

      res.status(201).json({
        success: true,
        data: session,
        message: 'Session de coaching créée'
      });
    } catch (error) {
      next(error);
    }
  };

  endSession = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }
      
      const { sessionId } = req.body;
      const userId = req.user.id;

      const session = await AICoachSession.findOne({ 
        where: { id: sessionId, userId } 
      });

      if (!session) {
        res.status(404).json({ 
          success: false, 
          message: 'Session non trouvée' 
        });
        return;
      }

      session.endTime = new Date();
      session.status = 'completed';
      await session.save();

      res.status(200).json({
        success: true,
        data: session,
        message: 'Session terminée'
      });
    } catch (error) {
      next(error);
    }
  };

  deleteInteraction = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }
      
      const { id } = req.params;
      const userId = req.user.id;

      await AICoachInteraction.delete({ id, userId });
      
      res.status(200).json({ 
        success: true, 
        message: 'Interaction supprimée avec succès' 
      });
    } catch (error) {
      next(error);
    }
  };
}
