import { Request, Response, NextFunction } from 'express';
import { NotificationService } from '../services/notificationService';
import { AppDataSource } from '../config/database';
import { User } from '../models/User';
import { AuthenticatedRequest } from '../middleware/auth';

export class NotificationController {
  static async getAll(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      const notifications = await NotificationService.getAllForUser(user.id);
      res.json({ success: true, notifications });
    } catch (err) {
      next(err);
    }
  }

  static async markAsRead(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      const notif = await NotificationService.markAsRead(req.params.id, user.id);
      res.json({ success: true, notification: notif });
    } catch (err) {
      next(err);
    }
  }

  static async delete(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      const notif = await NotificationService.delete(req.params.id, user.id);
      res.json({ success: true, notification: notif });
    } catch (err) {
      next(err);
    }
  }

  static async create(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      const { type, title, message, ...options } = req.body;
      const notif = await NotificationService.create(user.id, type, title, message, options);
      res.json({ success: true, notification: notif });
    } catch (err) {
      next(err);
    }
  }

  static async getPreferences(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      res.json({ success: true, preferences: user.notificationPreferences || {} });
    } catch (err) {
      next(err);
    }
  }

  static async updatePreferences(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      user.notificationPreferences = req.body;
      await AppDataSource.getRepository(User).save(user);
      res.json({ success: true, preferences: user.notificationPreferences });
    } catch (err) {
      next(err);
    }
  }

  static async markAllAsRead(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      await NotificationService.markAllAsRead(user.id);
      res.json({ success: true });
    } catch (err) {
      next(err);
    }
  }

  static async deleteAll(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }
      await NotificationService.deleteAll(user.id);
      res.json({ success: true });
    } catch (err) {
      next(err);
    }
  }
} 