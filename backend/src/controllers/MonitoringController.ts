import { Request, Response } from 'express';

const MonitoringController = {
  async getLogs(req: Request, res: Response) {
    // Mock : retourne des logs fictifs
    res.json({ logs: [
      { level: 'info', message: 'Ser<PERSON>ur démarré', timestamp: new Date().toISOString() },
      { level: 'error', message: 'Erreur critique', timestamp: new Date().toISOString() }
    ] });
  },

  async getAudit(req: Request, res: Response) {
    // Mock : retourne un audit trail fictif
    res.json({ audit: [
      { action: 'login', userId: 1, timestamp: new Date().toISOString() },
      { action: 'paiement', userId: 2, timestamp: new Date().toISOString() }
    ] });
  },

  async getStats(req: Request, res: Response) {
    // Mock : retourne des stats globales fictives
    res.json({
      users: 100,
      paiements: 25,
      erreurs: 2,
      uptime: '24h'
    });
  },
};

export default MonitoringController; 