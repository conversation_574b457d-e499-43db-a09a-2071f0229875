import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { ProfessionalService, ProfessionalFilters } from '../services/ProfessionalService';
import { AppointmentService, AppointmentFilters } from '../services/AppointmentService';
import { AppointmentType, AppointmentMode, AppointmentStatus } from '../models/Appointment';
import { ProfessionalType } from '../models/ProfessionalProfile';

export class BookingController {
  private professionalService = new ProfessionalService();
  private appointmentService = new AppointmentService();

  /**
   * Search available professionals
   */
  searchProfessionals = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const {
        professionalType,
        specializations,
        languagesSpoken,
        acceptingNewClients = true,
        consultationType,
        minRating,
        maxHourlyRate,
        city,
        state,
        country,
        page = 1,
        limit = 20
      } = req.query;

      const filters: ProfessionalFilters = {
        acceptingNewClients: acceptingNewClients === 'true',
        consultationType: consultationType as 'video' | 'phone' | 'chat' | 'in_person',
        minRating: minRating ? parseFloat(minRating as string) : undefined,
        maxHourlyRate: maxHourlyRate ? parseFloat(maxHourlyRate as string) : undefined,
      };

      // Type-safe assignment of professional type
      if (professionalType && Object.values(ProfessionalType).includes(professionalType as ProfessionalType)) {
        filters.professionalType = professionalType as ProfessionalType;
      }

      // Handle array fields
      if (specializations) {
        filters.specializations = (specializations as string).split(',').map(s => s.trim());
      }

      if (languagesSpoken) {
        filters.languagesSpoken = (languagesSpoken as string).split(',').map(l => l.trim());
      }

      // Location filters (currently not supported in User model)
      if (city || state || country) {
        filters.location = {
          city: city as string,
          state: state as string,
          country: country as string,
        };
      }

      const result = await this.professionalService.getProfessionals(
        filters,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.json({
        success: true,
        data: {
          professionals: result.professionals,
          pagination: {
            page: result.page,
            totalPages: result.totalPages,
            total: result.total,
            limit: parseInt(limit as string)
          }
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get professional details with availability
   */
  getProfessionalDetails = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { professionalId } = req.params;
      const { date, duration = 60 } = req.query;

      const professional = await this.professionalService.getProfessionalById(professionalId);
      
      let availableSlots: Array<{ start: Date; end: Date }> = [];
      if (date) {
        availableSlots = await this.appointmentService.getAvailableSlots(
          professionalId,
          new Date(date as string),
          parseInt(duration as string)
        );
      }

      res.json({
        success: true,
        data: {
          professional,
          availableSlots,
          consultationTypes: professional.consultationTypes || []
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Book an appointment
   */
  bookAppointment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ success: false, message: 'Authentication required' });
        return;
      }

      const {
        professionalId,
        appointmentType,
        mode,
        scheduledAt,
        durationMinutes = 60,
        clientNotes,
        preSessionAssessment,
        isEmergency = false
      } = req.body;

      // Validate required fields
      if (!professionalId || !appointmentType || !mode || !scheduledAt) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields: professionalId, appointmentType, mode, scheduledAt'
        });
        return;
      }

      // Validate enum values
      if (!Object.values(AppointmentType).includes(appointmentType)) {
        res.status(400).json({
          success: false,
          message: 'Invalid appointment type'
        });
        return;
      }

      if (!Object.values(AppointmentMode).includes(mode)) {
        res.status(400).json({
          success: false,
          message: 'Invalid appointment mode'
        });
        return;
      }

      const appointmentData = {
        professionalId,
        appointmentType: appointmentType as AppointmentType,
        mode: mode as AppointmentMode,
        scheduledAt: new Date(scheduledAt),
        durationMinutes,
        clientNotes,
        preSessionAssessment,
        isEmergency
      };

      const appointment = await this.appointmentService.createAppointment(req.user.id, appointmentData);

      res.status(201).json({
        success: true,
        message: 'Appointment booked successfully',
        data: { appointment }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get user's appointments
   */
  getUserAppointments = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ success: false, message: 'Authentication required' });
        return;
      }

      const {
        status,
        appointmentType,
        mode,
        dateFrom,
        dateTo,
        page = 1,
        limit = 20
      } = req.query;

      const filters: AppointmentFilters = {
        clientId: req.user.id,
        dateFrom: dateFrom ? new Date(dateFrom as string) : undefined,
        dateTo: dateTo ? new Date(dateTo as string) : undefined
      };

      // Type-safe assignment of enum values
      if (status && Object.values(AppointmentStatus).includes(status as AppointmentStatus)) {
        filters.status = status as AppointmentStatus;
      }

      if (appointmentType && Object.values(AppointmentType).includes(appointmentType as AppointmentType)) {
        filters.appointmentType = appointmentType as AppointmentType;
      }

      if (mode && Object.values(AppointmentMode).includes(mode as AppointmentMode)) {
        filters.mode = mode as AppointmentMode;
      }

      const result = await this.appointmentService.getAppointments(
        filters,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.json({
        success: true,
        data: {
          appointments: result.appointments,
          pagination: {
            page: result.page,
            totalPages: result.totalPages,
            total: result.total,
            limit: parseInt(limit as string)
          }
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Cancel an appointment
   */
  cancelAppointment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ success: false, message: 'Authentication required' });
        return;
      }

      const { appointmentId } = req.params;
      const { reason } = req.body;

      if (!reason || reason.trim().length < 10) {
        res.status(400).json({
          success: false,
          message: 'Cancellation reason must be at least 10 characters long'
        });
        return;
      }

      const appointment = await this.appointmentService.cancelAppointment(
        appointmentId,
        req.user.id,
        reason.trim()
      );

      res.json({
        success: true,
        message: 'Appointment cancelled successfully',
        data: { appointment }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Reschedule an appointment
   */
  rescheduleAppointment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ success: false, message: 'Authentication required' });
        return;
      }

      const { appointmentId } = req.params;
      const { newScheduledAt, reason } = req.body;

      if (!newScheduledAt || !reason) {
        res.status(400).json({
          success: false,
          message: 'New scheduled time and reason are required'
        });
        return;
      }

      const appointment = await this.appointmentService.rescheduleAppointment(
        appointmentId,
        new Date(newScheduledAt),
        req.user.id,
        reason
      );

      res.json({
        success: true,
        message: 'Appointment rescheduled successfully',
        data: { appointment }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Rate an appointment
   */
  rateAppointment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ success: false, message: 'Authentication required' });
        return;
      }

      const { appointmentId } = req.params;
      const { rating, feedback } = req.body;

      if (!rating || rating < 1 || rating > 5) {
        res.status(400).json({
          success: false,
          message: 'Rating must be between 1 and 5'
        });
        return;
      }

      const appointment = await this.appointmentService.updateAppointment(appointmentId, {
        clientRating: rating,
        clientFeedback: feedback
      });

      res.json({
        success: true,
        message: 'Rating submitted successfully',
        data: { appointment }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get filter options for professionals search
   */
  getFilterOptions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const options = await this.professionalService.getFilterOptions();
      
      res.json({
        success: true,
        data: {
          professionalTypes: options.professionalTypes.map(type => ({
            id: type,
            name: type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
          })),
          specializations: options.specializations.map(spec => ({
            id: spec,
            name: spec.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
          })),
          languages: options.languages.map(lang => ({
            id: lang,
            name: lang.charAt(0).toUpperCase() + lang.slice(1)
          })),
          consultationTypes: [
            { id: 'video', name: 'Video Call', icon: '📹' },
            { id: 'phone', name: 'Phone Call', icon: '📞' },
            { id: 'chat', name: 'Text Chat', icon: '💬' },
            { id: 'in_person', name: 'In Person', icon: '👥' }
          ],
          appointmentTypes: [
            { id: 'initial_consultation', name: 'Initial Consultation' },
            { id: 'follow_up', name: 'Follow-up Session' },
            { id: 'therapy_session', name: 'Therapy Session' },
            { id: 'assessment', name: 'Assessment' },
            { id: 'group_session', name: 'Group Session' },
            { id: 'crisis_intervention', name: 'Crisis Intervention' }
          ]
        }
      });
    } catch (error) {
      next(error);
    }
  };
} 