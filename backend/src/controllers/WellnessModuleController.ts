import { Request, Response, NextFunction } from 'express';
import { WellnessModuleService, CreateModuleDto, ModuleFilters } from '../services/WellnessModuleService';
import { AuthenticatedRequest } from '../middleware/auth';
import { ValidationError } from '../utils/errors';
import { ModuleType, DifficultyLevel } from '../models/WellnessModule';

export class WellnessModuleController {
  private wellnessModuleService = new WellnessModuleService();

  createModule = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      // Only admins can create modules (you'd check permissions here)
      if (req.user.role.name !== 'admin' && req.user.role.name !== 'root') {
        throw new ValidationError('Insufficient permissions to create modules');
      }

      const data: CreateModuleDto = req.body;
      const module = await this.wellnessModuleService.createModule(data);

      res.status(201).json({
        success: true,
        message: 'Wellness module created successfully',
        data: { module: module.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getModules = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Parse query parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);

      const filters: ModuleFilters = {};

      if (req.query.moduleType) {
        filters.moduleType = req.query.moduleType as ModuleType;
      }

      if (req.query.difficultyLevel) {
        filters.difficultyLevel = req.query.difficultyLevel as DifficultyLevel;
      }

      if (req.query.isFeatured !== undefined) {
        filters.isFeatured = req.query.isFeatured === 'true';
      }

      if (req.query.requiresSupervision !== undefined) {
        filters.requiresSupervision = req.query.requiresSupervision === 'true';
      }

      if (req.query.tags) {
        filters.tags = Array.isArray(req.query.tags) 
          ? req.query.tags as string[]
          : [req.query.tags as string];
      }

      const result = await this.wellnessModuleService.getModules(filters, page, limit);

      res.json({
        success: true,
        message: 'Wellness modules retrieved successfully',
        data: {
          modules: result.modules.map(module => module.toJSON()),
          pagination: {
            page: result.page,
            limit,
            total: result.total,
            totalPages: result.totalPages,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  };

  getModuleById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const module = await this.wellnessModuleService.getModuleById(id);

      res.json({
        success: true,
        message: 'Wellness module retrieved successfully',
        data: { module: module.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  startModule = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      const progress = await this.wellnessModuleService.startModule(req.user.id, id);

      res.json({
        success: true,
        message: 'Module started successfully',
        data: { progress: progress.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  updateProgress = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id, sectionId } = req.params;
      const { timeSpentMinutes, notes } = req.body;

      const progress = await this.wellnessModuleService.updateProgress(
        req.user.id,
        id,
        sectionId,
        timeSpentMinutes || 0,
        notes
      );

      res.json({
        success: true,
        message: 'Progress updated successfully',
        data: { progress: progress.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getUserProgress = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);

      const result = await this.wellnessModuleService.getUserProgress(req.user.id, page, limit);

      res.json({
        success: true,
        message: 'User progress retrieved successfully',
        data: {
          progress: result.progress.map(p => p.toJSON()),
          pagination: {
            page: result.page,
            limit,
            total: result.total,
            totalPages: result.totalPages,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  };

  rateModule = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ValidationError('User not authenticated');
      }

      const { id } = req.params;
      const { rating, feedback } = req.body;

      if (!rating || typeof rating !== 'number') {
        throw new ValidationError('Rating is required and must be a number');
      }

      const progress = await this.wellnessModuleService.rateModule(
        req.user.id,
        id,
        rating,
        feedback
      );

      res.json({
        success: true,
        message: 'Module rated successfully',
        data: { progress: progress.toJSON() },
      });
    } catch (error) {
      next(error);
    }
  };

  getStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const stats = await this.wellnessModuleService.getModuleStats();

      res.json({
        success: true,
        message: 'Wellness module statistics retrieved successfully',
        data: { stats },
      });
    } catch (error) {
      next(error);
    }
  };

  // Helper endpoint to get available filter options
  getFilterOptions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const moduleTypes = Object.values(ModuleType);
      const difficultyLevels = Object.values(DifficultyLevel);

      res.json({
        success: true,
        message: 'Filter options retrieved successfully',
        data: {
          moduleTypes,
          difficultyLevels,
        },
      });
    } catch (error) {
      next(error);
    }
  };

  static async updateProgress(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }
      const { moduleId } = req.params;
      const { progress, stats } = req.body;
      const record = await WellnessModuleService.updateProgress(req.user.id, moduleId, progress, stats);
      res.json({ success: true, record });
    } catch (err) {
      next(err);
    }
  }

  static async addFeedback(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }
      const { moduleId } = req.params;
      const { feedback } = req.body;
      const record = await WellnessModuleService.addFeedback(req.user.id, moduleId, feedback);
      res.json({ success: true, record });
    } catch (err) {
      next(err);
    }
  }

  static async getStats(req: Request, res: Response, next: NextFunction) {
    try {
      const { id: moduleId } = req.params;
      const stats = await WellnessModuleService.getStats(moduleId);
      res.json({ success: true, stats });
    } catch (err) {
      next(err);
    }
  }
}
