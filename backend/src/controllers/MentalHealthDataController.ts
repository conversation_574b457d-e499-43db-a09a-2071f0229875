import { Request, Response, NextFunction } from 'express';
import { MentalHealthDataService } from '../services/MentalHealthDataService';
import { DataType } from '../models/MentalHealthData';

export class MentalHealthDataController {
  static async create(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).userId;
      const { type, data } = req.body;
      const entry = await MentalHealthDataService.create(userId, type as DataType, data);
      res.status(201).json({ success: true, entry });
    } catch (err) {
      next(err);
    }
  }

  static async getAll(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).userId;
      const { type } = req.query;
      const entries = await MentalHealthDataService.getAll(userId, type as DataType);
      res.json({ success: true, entries });
    } catch (err) {
      next(err);
    }
  }

  static async getById(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).userId;
      const { id } = req.params;
      const entry = await MentalHealthDataService.getById(userId, id);
      res.json({ success: true, entry });
    } catch (err) {
      next(err);
    }
  }

  static async update(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).userId;
      const { id } = req.params;
      const { data } = req.body;
      const entry = await MentalHealthDataService.update(userId, id, data);
      res.json({ success: true, entry });
    } catch (err) {
      next(err);
    }
  }

  static async remove(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).userId;
      const { id } = req.params;
      await MentalHealthDataService.remove(userId, id);
      res.json({ success: true });
    } catch (err) {
      next(err);
    }
  }
} 