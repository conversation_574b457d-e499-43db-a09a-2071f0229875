/**
 * @swagger
 * tags:
 *   name: Notifications
 *   description: Gestion des notifications push et messages système
 */

/**
 * @swagger
 * /notifications:
 *   get:
 *     summary: Liste toutes les notifications de l'utilisateur
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Liste des notifications
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Notification'
 *   post:
 *     summary: Envoyer une notification
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/NotificationInput'
 *     responses:
 *       201:
 *         description: Notification envoyée
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Notification'
 */

import { Router } from 'express';
import { NotificationController } from '../controllers/NotificationController';
import { authenticate } from '../middlewares/auth';

const router = Router();

router.get('/', authenticate, NotificationController.getAll);
router.post('/', authenticate, NotificationController.create); // optionnel, pour tests
router.patch('/:id/read', authenticate, NotificationController.markAsRead);
router.delete('/:id', authenticate, NotificationController.delete);
router.get('/preferences', authenticate, NotificationController.getPreferences);
router.put('/preferences', authenticate, NotificationController.updatePreferences);
router.patch('/mark-all-read', authenticate, NotificationController.markAllAsRead);
router.delete('/delete-all', authenticate, NotificationController.deleteAll);

export default router; 