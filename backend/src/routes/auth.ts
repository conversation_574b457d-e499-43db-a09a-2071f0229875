import { Router } from 'express';
import { register, login, refresh, logout, me, enable2FA, verify2FA, disable2FA } from '../controllers/AuthController';
import { authenticate } from '../middlewares/auth';
import { validateBody } from '../middlewares/validate';
import { registerSchema, loginSchema } from '../validation/auth';
import { auditTrail } from '../middleware/auditTrail';
import Joi from 'joi';

const router = Router();

// Validation schemas
const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required(),
});

const enable2FASchema = Joi.object({
  secret: Joi.string().required(),
});

const verify2FASchema = Joi.object({
  token: Joi.string().length(6).required(),
  secret: Joi.string().required(),
});

/**
 * @swagger
 * tags:
 *   name: Authentication
 *   description: Gestion de l'authentification et des comptes utilisateurs
 */

/**
 * @route   POST /api/v1/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', auditTrail('user-register'), validateBody(registerSchema), register);

/**
 * @route   POST /api/v1/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login', auditTrail('user-login'), validateBody(loginSchema), login);

/**
 * @route   POST /api/v1/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh', auditTrail('token-refresh'), validateBody(refreshTokenSchema), refresh);

/**
 * @route   GET /api/v1/auth/me
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/me', authenticate, auditTrail('user-profile-get'), me);

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.post('/logout', authenticate, auditTrail('user-logout'), logout);

/**
 * @route   POST /api/v1/auth/2fa/enable
 * @desc    Enable 2FA for user
 * @access  Private
 */
router.post('/2fa/enable', authenticate, auditTrail('2fa-enable'), validateBody(enable2FASchema), enable2FA);

/**
 * @route   POST /api/v1/auth/2fa/verify
 * @desc    Verify 2FA token
 * @access  Private
 */
router.post('/2fa/verify', authenticate, auditTrail('2fa-verify'), validateBody(verify2FASchema), verify2FA);

/**
 * @route   POST /api/v1/auth/2fa/disable
 * @desc    Disable 2FA for user
 * @access  Private
 */
router.post('/2fa/disable', authenticate, auditTrail('2fa-disable'), disable2FA);

export default router;
