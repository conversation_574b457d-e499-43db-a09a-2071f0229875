import { Router } from 'express';
import PaymentController from '../controllers/PaymentController';
import { authenticate } from '../middleware/auth';

const router = Router();

router.post('/create', authenticate, PaymentController.createPayment);
router.post('/webhook', PaymentController.handleWebhook); // Pas d'auth pour Stripe webhook
router.post('/subscription', authenticate, PaymentController.createSubscription);
// Route status à compléter plus tard
// router.get('/status', ...);
router.get('/stats', authenticate, PaymentController.getStats);

export default router; 