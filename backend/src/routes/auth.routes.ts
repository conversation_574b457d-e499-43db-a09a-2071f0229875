import { Router, RequestHandler } from 'express';
import { register, login, refresh, logout, me, enable2FA, verify2FA, disable2FA } from '../controllers/AuthController';
import { validateBody } from '../middlewares/validate';
import { registerSchema, loginSchema } from '../validation/auth';
import { authenticate } from '../middleware/auth';
import { auditTrail } from '../middleware/auditTrail';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Auth
 *   description: Gestion de l'authentification
 */

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: Inscription d'un nouvel utilisateur
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UserRegister'
 *     responses:
 *       201:
 *         description: Utilisateur créé
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserResponse'
 *       400:
 *         description: Erreur de validation
 */
router.post('/register', auditTrail('register'), validateBody(registerSchema), register);

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: Connexion utilisateur
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UserLogin'
 *     responses:
 *       200:
 *         description: Connexion réussie
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       401:
 *         description: Identifiants invalides
 */
router.post('/login', auditTrail('login'), validateBody(loginSchema), login);
router.post('/refresh', refresh);
router.post('/logout', logout);
router.get('/me', me);
router.post('/2fa/enable', enable2FA);
router.post('/enable-2fa', authenticate, auditTrail('enable-2fa'), enable2FA);
router.post('/verify-2fa', authenticate, auditTrail('verify-2fa'), verify2FA);
router.post('/disable-2fa', authenticate, auditTrail('disable-2fa'), disable2FA);

export default router; 