import { Router } from 'express';
import { MentalHealthDataController } from '../controllers/MentalHealthDataController';
import { authenticate } from '../middlewares/auth';
import { validateBody } from '../middlewares/validate';
import { createMentalHealthDataSchema, updateMentalHealthDataSchema } from '../validation/mentalHealthData';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: MentalHealthData
 *   description: Gestion des données de santé mentale
 */

/**
 * @swagger
 * /mental-health-data:
 *   get:
 *     summary: Liste toutes les entrées de santé mentale de l'utilisateur
 *     tags: [MentalHealthData]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Liste des entrées
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/MentalHealthData'
 *   post:
 *     summary: Crée une nouvelle entrée de santé mentale
 *     tags: [MentalHealthData]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MentalHealthDataInput'
 *     responses:
 *       201:
 *         description: Entrée créée
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MentalHealthData'
 */

router.post('/', authenticate, validateBody(createMentalHealthDataSchema), MentalHealthDataController.create);
router.get('/', authenticate, MentalHealthDataController.getAll);
router.get('/:id', authenticate, MentalHealthDataController.getById);
router.put('/:id', authenticate, validateBody(updateMentalHealthDataSchema), MentalHealthDataController.update);
router.delete('/:id', authenticate, MentalHealthDataController.remove);

export default router; 