import { Router } from 'express';
import { HealthController } from '../controllers/HealthController';
import { BookingController } from '../controllers/BookingController';
import { authenticate } from '../middlewares/auth';
import { auditTrail } from '../middleware/auditTrail';

// Import route modules
import authRoutes from './auth';
import journalRoutes from './journal';
import aiCoachRoutes from './ai-coach';
import wellnessRoutes from './wellness';
import userRoutes from './user.routes';
import mentalHealthDataRoutes from './mentalHealthData.routes';
import notificationRoutes from './notification';
import appointmentRoutes from './appointments';
import professionalRoutes from './professionals';
import auditRoutes from './audit';
import monitoringRoutes from './monitoring';
import sessionRoutes from './session';
import healthMonitoringRoutes from './health-monitoring';

const router = Router();
const bookingController = new BookingController();

// Health check route
router.get('/health', HealthController.health);

// Test route
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'MindFlow Pro API is running',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  });
});

// Booking routes (direct implementation)
router.get('/booking/professionals/search', bookingController.searchProfessionals);
router.get('/booking/professionals/:professionalId', bookingController.getProfessionalDetails);
router.get('/booking/filter-options', bookingController.getFilterOptions);
router.post('/booking/appointments', authenticate, auditTrail('appointment-book'), bookingController.bookAppointment);
router.get('/booking/appointments', authenticate, auditTrail('appointment-list'), bookingController.getUserAppointments);
router.put('/booking/appointments/:appointmentId/cancel', authenticate, auditTrail('appointment-cancel'), bookingController.cancelAppointment);
router.put('/booking/appointments/:appointmentId/reschedule', authenticate, auditTrail('appointment-reschedule'), bookingController.rescheduleAppointment);
router.post('/booking/appointments/:appointmentId/rate', authenticate, auditTrail('appointment-rate'), bookingController.rateAppointment);

// API routes
router.use('/auth', authRoutes);
router.use('/journal', journalRoutes);
router.use('/ai-coach', aiCoachRoutes);
router.use('/wellness', wellnessRoutes);
router.use('/users', userRoutes);
router.use('/mental-health-data', mentalHealthDataRoutes);
router.use('/notifications', notificationRoutes);
router.use('/appointments', appointmentRoutes);
router.use('/professionals', professionalRoutes);
router.use('/audit', auditRoutes);
router.use('/monitoring', monitoringRoutes);
router.use('/sessions', sessionRoutes);
router.use('/health-monitoring', healthMonitoringRoutes);

export default router;
