import { Router } from 'express';
import { AppointmentController } from '../controllers/AppointmentController';
import { authenticate } from '../middlewares/auth';
import { auditTrail } from '../middleware/auditTrail';

const router = Router();
const appointmentController = new AppointmentController();

/**
 * @swagger
 * tags:
 *   name: Appointments
 *   description: Gestion des rendez-vous
 */

/**
 * @swagger
 * /appointments:
 *   get:
 *     summary: Liste tous les rendez-vous
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Liste des rendez-vous
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Appointment'
 *   post:
 *     summary: Crée un nouveau rendez-vous
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AppointmentInput'
 *     responses:
 *       201:
 *         description: Rendez-vous créé
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Appointment'
 */

/**
 * @route   GET /api/v1/appointments/filter-options
 * @desc    Get available filter options for appointments
 * @access  Public
 */
router.get('/filter-options', appointmentController.getFilterOptions);

/**
 * @route   GET /api/v1/appointments/stats
 * @desc    Get appointment statistics
 * @access  Public
 */
router.get('/stats', authenticate, auditTrail('appointments-stats'), appointmentController.getAppointmentStats);

/**
 * @route   GET /api/v1/appointments/professionals/:professionalId/available-slots
 * @desc    Get available appointment slots for a professional
 * @access  Public
 * @query   date, duration
 */
router.get('/professionals/:professionalId/available-slots', appointmentController.getAvailableSlots);

/**
 * @route   POST /api/v1/appointments
 * @desc    Create a new appointment
 * @access  Private
 */
router.post('/', authenticate, auditTrail('appointment-create'), appointmentController.createAppointment);

/**
 * @route   GET /api/v1/appointments
 * @desc    Get appointments with optional filtering
 * @access  Private
 * @query   page, limit, status, appointmentType, mode, dateFrom, dateTo, professionalId, clientId, isEmergency
 */
router.get('/', authenticate, auditTrail('appointments-list'), appointmentController.getAppointments);

/**
 * @route   GET /api/v1/appointments/:id
 * @desc    Get a specific appointment by ID
 * @access  Private
 */
router.get('/:id', authenticate, auditTrail('appointment-get'), appointmentController.getAppointmentById);

/**
 * @route   PUT /api/v1/appointments/:id
 * @desc    Update an appointment
 * @access  Private
 */
router.put('/:id', authenticate, auditTrail('appointment-update'), appointmentController.updateAppointment);

/**
 * @route   POST /api/v1/appointments/:id/cancel
 * @desc    Cancel an appointment
 * @access  Private
 */
router.post('/:id/cancel', authenticate, auditTrail('appointment-cancel'), appointmentController.cancelAppointment);

/**
 * @route   POST /api/v1/appointments/:id/reschedule
 * @desc    Reschedule an appointment
 * @access  Private
 */
router.post('/:id/reschedule', authenticate, appointmentController.rescheduleAppointment);

export default router;
