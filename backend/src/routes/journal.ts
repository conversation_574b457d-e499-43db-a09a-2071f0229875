import { Router } from 'express';
import { JournalController } from '../controllers/JournalController';
import { authenticate } from '../middlewares/auth';
import { validateBody } from '../middlewares/validate';
import { auditTrail } from '../middleware/auditTrail';
import { requirePermission } from '../middleware/permissions';
import Joi from 'joi';

const router = Router();
const journalController = new JournalController();

// Validation schemas
const createEntrySchema = Joi.object({
  title: Joi.string().min(1).max(200).required(),
  content: Joi.string().min(1).required(),
  entryType: Joi.string().valid('daily', 'reflection', 'gratitude', 'goal_setting', 'stress_tracking', 'mood_tracking').optional(),
  moodLevel: Joi.number().integer().min(1).max(5).optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  emotions: Joi.array().items(Joi.string()).optional(),
  stressLevel: Joi.number().integer().min(0).max(10).optional(),
  energyLevel: Joi.number().integer().min(0).max(10).optional(),
  sleepQuality: Joi.number().integer().min(0).max(10).optional(),
  gratitudeNotes: Joi.string().optional(),
  goals: Joi.string().optional(),
  challenges: Joi.string().optional(),
  achievements: Joi.string().optional(),
  isPrivate: Joi.boolean().optional(),
});

const updateEntrySchema = Joi.object({
  title: Joi.string().min(1).max(200).optional(),
  content: Joi.string().min(1).optional(),
  entryType: Joi.string().valid('daily', 'reflection', 'gratitude', 'goal_setting', 'stress_tracking', 'mood_tracking').optional(),
  moodLevel: Joi.number().integer().min(1).max(5).optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  emotions: Joi.array().items(Joi.string()).optional(),
  stressLevel: Joi.number().integer().min(0).max(10).optional(),
  energyLevel: Joi.number().integer().min(0).max(10).optional(),
  sleepQuality: Joi.number().integer().min(0).max(10).optional(),
  gratitudeNotes: Joi.string().optional(),
  goals: Joi.string().optional(),
  challenges: Joi.string().optional(),
  achievements: Joi.string().optional(),
  isPrivate: Joi.boolean().optional(),
  isFavorite: Joi.boolean().optional(),
});

/**
 * @swagger
 * tags:
 *   name: Journal
 *   description: Gestion du journal personnel
 */

/**
 * @route   POST /api/v1/journal
 * @desc    Create a new journal entry
 * @access  Private
 */
router.post('/', authenticate, requirePermission('journal:create'), auditTrail('journal-create'), validateBody(createEntrySchema), journalController.createEntry);

/**
 * @route   GET /api/v1/journal
 * @desc    Get journal entries for the authenticated user
 * @access  Private
 */
router.get('/', authenticate, requirePermission('journal:read'), auditTrail('journal-read'), journalController.getEntries);

/**
 * @route   GET /api/v1/journal/stats
 * @desc    Get journal statistics
 * @access  Private
 */
router.get('/stats', authenticate, requirePermission('journal:read'), auditTrail('journal-stats'), journalController.getStats);

/**
 * @route   GET /api/v1/journal/filter-options
 * @desc    Get filter options for journal entries
 * @access  Private
 */
router.get('/filter-options', authenticate, journalController.getFilterOptions);

/**
 * @route   GET /api/v1/journal/suggestions
 * @desc    Get journal writing suggestions
 * @access  Private
 */
router.get('/suggestions', authenticate, requirePermission('journal:read'), auditTrail('journal-suggestions'), journalController.getSuggestions);

/**
 * @route   GET /api/v1/journal/:id
 * @desc    Get a specific journal entry by ID
 * @access  Private
 */
router.get('/:id', authenticate, requirePermission('journal:read'), auditTrail('journal-read-single'), journalController.getEntryById);

/**
 * @route   PUT /api/v1/journal/:id
 * @desc    Update a journal entry
 * @access  Private
 */
router.put('/:id', authenticate, requirePermission('journal:update'), auditTrail('journal-update'), validateBody(updateEntrySchema), journalController.updateEntry);

/**
 * @route   DELETE /api/v1/journal/:id
 * @desc    Delete a journal entry
 * @access  Private
 */
router.delete('/:id', authenticate, requirePermission('journal:delete'), auditTrail('journal-delete'), journalController.deleteEntry);

export default router;
