import { Router } from 'express';
import { ProfessionalController } from '../controllers/ProfessionalController';
import { authenticate } from '../middleware/auth';
import { auditTrail } from '../middleware/auditTrail';
import { requirePermission } from '../middleware/permissions';

const router = Router();
const professionalController = new ProfessionalController();

/**
 * @swagger
 * tags:
 *   name: Professionals
 *   description: Gestion du réseau professionnel
 */

/**
 * @swagger
 * /professionals:
 *   get:
 *     summary: Liste tous les professionnels
 *     tags: [Professionals]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Liste des professionnels
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ProfessionalProfile'
 *   post:
 *     summary: Crée un nouveau professionnel
 *     tags: [Professionals]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ProfessionalProfileInput'
 *     responses:
 *       201:
 *         description: Professionnel créé
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ProfessionalProfile'
 */

/**
 * @route   GET /api/v1/professionals
 * @desc    Get all professionals
 * @access  Public
 */
router.get('/', professionalController.getProfessionals);

/**
 * @route   GET /api/v1/professionals/stats
 * @desc    Get professional statistics
 * @access  Public
 */
router.get('/stats', professionalController.getProfessionalStats);

/**
 * @route   GET /api/v1/professionals/filter-options
 * @desc    Get filter options for professionals
 * @access  Public
 */
router.get('/filter-options', professionalController.getFilterOptions);

/**
 * @route   GET /api/v1/professionals/search
 * @desc    Search professionals with filters
 * @access  Public
 */
router.get('/search', professionalController.getProfessionals);

/**
 * @route   GET /api/v1/professionals/me
 * @desc    Get my professional profile
 * @access  Private
 */
router.get('/me', authenticate, auditTrail('professional-profile-get'), professionalController.getMyProfessionalProfile);

/**
 * @route   PUT /api/v1/professionals/me
 * @desc    Update my professional profile
 * @access  Private
 */
router.put('/me', authenticate, auditTrail('professional-profile-update'), professionalController.updateProfessionalProfile);

/**
 * @route   POST /api/v1/professionals
 * @desc    Create professional profile
 * @access  Private
 */
router.post('/', requirePermission('professional:create'), auditTrail('professional-create'), professionalController.createProfessionalProfile);

/**
 * @route   GET /api/v1/professionals/:id
 * @desc    Get professional by ID
 * @access  Public
 */
router.get('/:id', professionalController.getProfessionalById);

/**
 * @route   PUT /api/v1/professionals/:id
 * @desc    Update professional profile (admin only)
 * @access  Private (Admin)
 */
router.put('/:id', requirePermission('professional:update'), auditTrail('professional-update'), professionalController.updateProfessional);

/**
 * @route   DELETE /api/v1/professionals/:id
 * @desc    Delete professional profile (admin only)
 * @access  Private (Admin)
 */
router.delete('/:id', requirePermission('professional:delete'), auditTrail('professional-delete'), professionalController.deleteProfessional);

/**
 * @route   POST /api/v1/professionals/:id/verify
 * @desc    Verify professional (admin only)
 * @access  Private (Admin)
 */
router.post('/:id/verify', authenticate, auditTrail('professional-verify'), professionalController.verifyProfessional);

export default router;
