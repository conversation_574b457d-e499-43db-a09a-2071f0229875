import { Router } from 'express';
import { HealthController } from '../controllers/HealthController';

const router = Router();

export const setupBasicRoutes = (app: any) => {
  // Health check route
  app.get('/api/v1/health', HealthController.health);
  
  // Basic test route
  app.get('/api/v1/test', (req: any, res: any) => {
    res.json({
      success: true,
      message: 'MindFlow Pro API is running',
      timestamp: new Date().toISOString(),
    });
  });
  
  // Status route  
  app.get('/api/v1/status', (req: any, res: any) => {
    res.json({
      success: true,
      status: 'OK',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0',
    });
  });

  return router;
};

export default setupBasicRoutes; 