import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { authenticate } from '../middleware/auth';
import { auditTrail } from '../middleware/auditTrail';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: Gestion des profils utilisateurs
 */

/**
 * @route   GET /api/v1/users/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile', authenticate, auditTrail('user-profile-read'), UserController.getProfile);

/**
 * @route   PUT /api/v1/users/profile
 * @desc    Update current user profile
 * @access  Private
 */
router.put('/profile', authenticate, auditTrail('user-profile-update'), UserController.updateProfile);

/**
 * @route   DELETE /api/v1/users/profile
 * @desc    Delete current user profile
 * @access  Private
 */
router.delete('/profile', authenticate, auditTrail('user-profile-delete'), UserController.deleteProfile);

export default router; 