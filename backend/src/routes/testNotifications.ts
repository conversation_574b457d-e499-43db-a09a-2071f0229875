import { Router, Response } from 'express';
import { notificationService } from '../services/notificationService';
import { authenticate, AuthenticatedRequest } from '../middleware/auth';

const router = Router();

// Test endpoint to send sample notifications
router.post('/test-appointment-reminder', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({ error: 'User not authenticated' });
      return;
    }

    await notificationService.sendAppointmentReminder(userId, {
      appointmentId: 'test-apt-123',
      professionalName: 'Dr. <PERSON>',
      scheduledAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(), // 15 minutes from now
      reminderType: '15min',
      appointmentType: 'Therapy Session',
      mode: 'video'
    });

    res.json({ message: 'Appointment reminder sent successfully' });
  } catch (error) {
    console.error('Error sending test appointment reminder:', error);
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

router.post('/test-appointment-status', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({ error: 'User not authenticated' });
      return;
    }

    await notificationService.sendAppointmentStatusUpdate(userId, {
      appointmentId: 'test-apt-123',
      status: 'confirmed',
      professionalName: 'Dr. Sarah <PERSON>'
    });

    res.json({ message: 'Appointment status update sent successfully' });
  } catch (error) {
    console.error('Error sending test appointment status:', error);
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

router.post('/test-new-message', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({ error: 'User not authenticated' });
      return;
    }

    await notificationService.sendNewMessage(userId, {
      messageId: 'msg-123',
      conversationId: 'conv-456',
      senderId: 'prof-789',
      senderName: 'Dr. Sarah Johnson',
      senderType: 'professional',
      content: 'Hello! I wanted to follow up on our last session. How are you feeling today?',
      timestamp: new Date().toISOString(),
      messageType: 'text'
    });

    res.json({ message: 'New message notification sent successfully' });
  } catch (error) {
    console.error('Error sending test message:', error);
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

router.post('/test-crisis-alert', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({ error: 'User not authenticated' });
      return;
    }

    await notificationService.sendCrisisAlert(userId, {
      alertId: 'crisis-123',
      severity: 'medium',
      message: 'We noticed you might need some support. Here are some resources that can help.',
      resources: [
        {
          type: 'hotline',
          name: 'National Suicide Prevention Lifeline',
          contact: '988',
          available24h: true
        },
        {
          type: 'chat',
          name: 'Crisis Text Line',
          contact: 'Text HOME to 741741',
          available24h: true
        }
      ]
    });

    res.json({ message: 'Crisis alert sent successfully' });
  } catch (error) {
    console.error('Error sending test crisis alert:', error);
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

router.post('/test-wellness-progress', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({ error: 'User not authenticated' });
      return;
    }

    await notificationService.sendWellnessProgressUpdate(userId, {
      moduleId: 'module-123',
      moduleName: 'Introduction to Mindfulness',
      progress: 100,
      completed: true,
      timestamp: new Date().toISOString()
    });

    res.json({ message: 'Wellness progress update sent successfully' });
  } catch (error) {
    console.error('Error sending test wellness progress:', error);
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

router.post('/test-mood-update', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({ error: 'User not authenticated' });
      return;
    }

    await notificationService.sendMoodTrackingUpdate(userId, {
      entryId: 'mood-123',
      mood: 7,
      energy: 6,
      stress: 4,
      anxiety: 3,
      timestamp: new Date().toISOString()
    });

    res.json({ message: 'Mood tracking update sent successfully' });
  } catch (error) {
    console.error('Error sending test mood update:', error);
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

router.post('/test-professional-availability', authenticate, async (_req: AuthenticatedRequest, res: Response) => {
  try {
    await notificationService.sendProfessionalAvailabilityChange({
      professionalId: 'prof-123',
      professionalName: 'Dr. Sarah Johnson',
      isOnline: true,
      availableSlots: [
        new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
        new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(), // 4 hours from now
      ]
    });

    res.json({ message: 'Professional availability update sent successfully' });
  } catch (error) {
    console.error('Error sending test professional availability:', error);
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

router.post('/test-dashboard-update', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({ error: 'User not authenticated' });
      return;
    }

    await notificationService.sendDashboardUpdate(userId, {
      type: 'stats',
      data: {
        newAppointments: 2,
        completedModules: 1,
        unreadMessages: 3
      },
      timestamp: new Date().toISOString()
    });

    res.json({ message: 'Dashboard update sent successfully' });
  } catch (error) {
    console.error('Error sending test dashboard update:', error);
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

// Get WebSocket connection status
router.get('/websocket-status', authenticate, async (_req: AuthenticatedRequest, res: Response) => {
  try {
    const connectedUsers = notificationService.getConnectedUsers();
    const totalConnections = notificationService.getConnectedUsersCount();

    res.json({
      totalConnections,
      connectedUsers: connectedUsers.map(user => ({
        userId: user.userId,
        email: user.email,
        name: user.name,
        connectedAt: user.connectedAt
      }))
    });
  } catch (error) {
    console.error('Error getting WebSocket status:', error);
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

export default router;
