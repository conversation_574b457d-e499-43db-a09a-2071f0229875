import { Router } from 'express';
import { Request, Response, NextFunction } from 'express';
import { getHealthMonitoringService } from '../services/HealthMonitoringService';
import { authenticate } from '../middleware/auth';
import { auditTrail } from '../middleware/auditTrail';
import { ValidationError } from '../utils/errors';

const router = Router();
const healthMonitoringService = getHealthMonitoringService();

/**
 * @swagger
 * tags:
 *   name: HealthMonitoring
 *   description: Advanced mental health monitoring and analytics
 */

/**
 * @route   GET /api/v1/health-monitoring/status
 * @desc    Get comprehensive health status for authenticated user
 * @access  Private
 */
router.get('/status', authenticate, auditTrail('health-status-view'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    if (!user) {
      throw new ValidationError('User not authenticated');
    }

    const healthStatus = await healthMonitoringService.getUserHealthStatus(user.id);

    res.json({
      success: true,
      message: 'Health status retrieved successfully',
      data: healthStatus,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/v1/health-monitoring/alerts
 * @desc    Get active health alerts for authenticated user
 * @access  Private
 */
router.get('/alerts', authenticate, auditTrail('health-alerts-view'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    if (!user) {
      throw new ValidationError('User not authenticated');
    }

    const alerts = healthMonitoringService.getUserAlerts(user.id);

    res.json({
      success: true,
      message: 'Health alerts retrieved successfully',
      data: {
        alerts,
        count: alerts.length,
        criticalCount: alerts.filter(a => a.severity === 'critical').length,
        highCount: alerts.filter(a => a.severity === 'high').length,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/v1/health-monitoring/insights
 * @desc    Get wellness insights and recommendations
 * @access  Private
 */
router.get('/insights', authenticate, auditTrail('wellness-insights-view'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    if (!user) {
      throw new ValidationError('User not authenticated');
    }

    const healthStatus = await healthMonitoringService.getUserHealthStatus(user.id);

    // Generate additional insights
    const insights = {
      wellnessScore: healthStatus.wellnessScore,
      trends: {
        overall: healthStatus.wellnessScore.overallScore,
        mood: healthStatus.wellnessScore.components.mood,
        stress: healthStatus.wellnessScore.components.stress,
        sleep: healthStatus.wellnessScore.components.sleep
      },
      riskLevel: healthStatus.wellnessScore.overallScore < 50 ? 'high' : 
                healthStatus.wellnessScore.overallScore < 70 ? 'medium' : 'low',
      recommendations: healthStatus.recommendations,
      alerts: healthStatus.alerts,
      improvementAreas: identifyImprovementAreas(healthStatus.wellnessScore),
      strengths: identifyStrengths(healthStatus.wellnessScore),
    };

    res.json({
      success: true,
      message: 'Wellness insights retrieved successfully',
      data: insights,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/v1/health-monitoring/assess
 * @desc    Trigger manual health assessment
 * @access  Private
 */
router.post('/assess', authenticate, auditTrail('health-assessment-trigger'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    if (!user) {
      throw new ValidationError('User not authenticated');
    }

    console.log(`🔍 Manual health assessment triggered for user ${user.id}`);
    
    const healthStatus = await healthMonitoringService.getUserHealthStatus(user.id);

    res.json({
      success: true,
      message: 'Health assessment completed successfully',
      data: {
        assessmentTime: new Date().toISOString(),
        ...healthStatus,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/v1/health-monitoring/crisis-resources
 * @desc    Get crisis support resources based on user's current status
 * @access  Private
 */
router.get('/crisis-resources', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    if (!user) {
      throw new ValidationError('User not authenticated');
    }

    const healthStatus = await healthMonitoringService.getUserHealthStatus(user.id);
    const hasActiveCrisisAlerts = healthStatus.alerts.some(alert => 
      alert.type === 'crisis_keywords' && alert.severity === 'critical'
    );

    const resources = {
      immediate: [
        {
          name: 'National Suicide Prevention Lifeline',
          phone: '988',
          description: '24/7 crisis support',
          website: 'https://suicidepreventionlifeline.org',
          priority: hasActiveCrisisAlerts ? 'critical' : 'high'
        },
        {
          name: 'Crisis Text Line',
          text: 'Text HOME to 741741',
          description: '24/7 crisis support via text',
          website: 'https://crisistextline.org',
          priority: hasActiveCrisisAlerts ? 'critical' : 'high'
        },
        {
          name: 'Emergency Services',
          phone: '911',
          description: 'For immediate medical emergencies',
          priority: 'critical'
        }
      ],
      mentalHealth: [
        {
          name: 'NAMI Helpline',
          phone: '1-800-950-NAMI (6264)',
          description: 'Mental health information and support',
          website: 'https://nami.org'
        },
        {
          name: 'SAMHSA National Helpline',
          phone: '1-************',
          description: 'Treatment referral and information service',
          website: 'https://samhsa.gov'
        }
      ],
      wellnessResources: getWellnessResourcesForScore(healthStatus.wellnessScore),
      recommendedActions: getCrisisActionPlan(healthStatus)
    };

    res.json({
      success: true,
      message: 'Crisis support resources retrieved successfully',
      data: {
        resources,
        userRiskLevel: healthStatus.wellnessScore.overallScore < 50 ? 'high' : 
                      healthStatus.wellnessScore.overallScore < 70 ? 'medium' : 'low',
        hasCrisisAlerts: hasActiveCrisisAlerts,
        timestamp: new Date().toISOString()
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/v1/health-monitoring/system-overview
 * @desc    Get system-wide health monitoring overview (admin only)
 * @access  Private (Admin only)
 */
router.get('/system-overview', authenticate, auditTrail('system-health-overview'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    if (!user) {
      throw new ValidationError('User not authenticated');
    }

    // Check if user is admin
    const userRole = user.role?.name;
    if (userRole !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Admin access required',
      });
      return;
    }

    const overview = healthMonitoringService.getSystemHealthOverview();

    res.json({
      success: true,
      message: 'System health overview retrieved successfully',
      data: overview,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/v1/health-monitoring/user/:userId
 * @desc    Get health status for specific user (admin/professional only)
 * @access  Private (Admin/Professional only)
 */
router.get('/user/:userId', authenticate, auditTrail('user-health-status-view'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    if (!user) {
      throw new ValidationError('User not authenticated');
    }

    const { userId } = req.params;

    // Check if user has permission (admin or professional)
    const userRole = user.role?.name;
    if (userRole !== 'admin' && userRole !== 'professional') {
      res.status(403).json({
        success: false,
        message: 'Insufficient permissions to access user health data',
      });
      return;
    }

    const healthStatus = await healthMonitoringService.getUserHealthStatus(userId);

    res.json({
      success: true,
      message: 'Health status retrieved successfully',
      data: {
        ...healthStatus,
        userId, // Include userId for context
      },
    });
  } catch (error) {
    next(error);
  }
});

// Helper functions
function identifyImprovementAreas(wellnessScore: any): string[] {
  const areas = [];
  const { components } = wellnessScore;

  if (components.mood < 60) areas.push('mood');
  if (components.stress < 60) areas.push('stress_management');
  if (components.sleep < 60) areas.push('sleep_quality');
  if (components.social < 60) areas.push('social_connection');
  if (components.activity < 60) areas.push('physical_activity');

  return areas;
}

function identifyStrengths(wellnessScore: any): string[] {
  const strengths = [];
  const { components } = wellnessScore;

  if (components.mood >= 70) strengths.push('positive_mood');
  if (components.stress >= 70) strengths.push('stress_resilience');
  if (components.sleep >= 70) strengths.push('good_sleep_habits');
  if (components.social >= 70) strengths.push('strong_social_connections');
  if (components.activity >= 70) strengths.push('active_lifestyle');

  return strengths;
}

function getWellnessResourcesForScore(wellnessScore: any) {
  const resources = [];

  if (wellnessScore.components.mood < 60) {
    resources.push({
      type: 'mood',
      title: 'Mood Improvement Techniques',
      description: 'Evidence-based techniques to improve mood',
      actions: ['Practice gratitude journaling', 'Engage in pleasant activities', 'Exercise regularly']
    });
  }

  if (wellnessScore.components.stress > 40) { // High stress (inverted scale)
    resources.push({
      type: 'stress',
      title: 'Stress Management Tools',
      description: 'Techniques to manage and reduce stress',
      actions: ['Deep breathing exercises', 'Progressive muscle relaxation', 'Mindfulness meditation']
    });
  }

  if (wellnessScore.components.sleep < 60) {
    resources.push({
      type: 'sleep',
      title: 'Sleep Hygiene Improvement',
      description: 'Tips for better sleep quality',
      actions: ['Consistent sleep schedule', 'Limit screen time before bed', 'Create relaxing bedtime routine']
    });
  }

  return resources;
}

function getCrisisActionPlan(healthStatus: any): string[] {
  const plan = [];
  const { alerts, wellnessScore } = healthStatus;

  const hasCrisisAlert = alerts.some((a: any) => a.severity === 'critical');
  
  if (hasCrisisAlert) {
    plan.push('Call 988 (Suicide Prevention Lifeline) immediately');
    plan.push('Go to nearest emergency room if in immediate danger');
    plan.push('Contact trusted friend or family member');
    plan.push('Remove access to means of self-harm');
  } else if (wellnessScore.overallScore < 50) {
    plan.push('Schedule urgent appointment with mental health professional');
    plan.push('Implement safety plan strategies');
    plan.push('Increase social support activities');
    plan.push('Monitor symptoms closely');
  } else {
    plan.push('Continue regular mental health maintenance');
    plan.push('Use learned coping strategies');
    plan.push('Seek support when needed');
  }

  return plan;
}

export default router; 