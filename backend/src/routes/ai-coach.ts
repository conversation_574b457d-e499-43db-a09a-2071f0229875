import { Router } from 'express';
import { AICoachController } from '../controllers/AICoachController';
import { authenticate } from '../middleware/auth';
import { auditTrail } from '../middleware/auditTrail';
import { requirePermission } from '../middleware/permissions';

const router = Router();
const aiCoachController = new AICoachController();

/**
 * @swagger
 * tags:
 *   name: AICoach
 *   description: IA conversationnelle et coaching
 */

/**
 * @route   GET /api/v1/ai-coach/crisis-resources
 * @desc    Get crisis support resources
 * @access  Public
 */
router.get('/crisis-resources', aiCoachController.getCrisisResources);

/**
 * @route   GET /api/v1/ai-coach/wellness-tips
 * @desc    Get wellness tips and suggestions
 * @access  Public
 */
router.get('/wellness-tips', aiCoachController.getWellnessTips);

/**
 * @route   GET /api/v1/ai-coach/stats
 * @desc    Get AI coach interaction statistics
 * @access  Private
 */
router.get('/stats', authenticate, aiCoachController.getStats);

/**
 * @route   GET /api/v1/ai-coach/sessions
 * @desc    Get AI coach conversation sessions
 * @access  Private
 */
router.get('/sessions', authenticate, aiCoachController.getSessions);

/**
 * @route   POST /api/v1/ai-coach/interact
 * @desc    Create a new AI coach interaction
 * @access  Private
 */
router.post('/interact', authenticate, requirePermission('ai:interact'), auditTrail('ai-interact'), aiCoachController.createInteraction);

/**
 * @route   GET /api/v1/ai-coach/interactions
 * @desc    Get AI coach interactions for the authenticated user
 * @access  Private
 * @query   page, limit, sessionId
 */
router.get('/interactions', authenticate, aiCoachController.getInteractions);

/**
 * @route   GET /api/v1/ai-coach/interactions/:id
 * @desc    Get a specific AI coach interaction by ID
 * @access  Private
 */
router.get('/interactions/:id', authenticate, aiCoachController.getInteractionById);

/**
 * @route   POST /api/v1/ai-coach/interactions/:id/rate
 * @desc    Rate an AI coach interaction
 * @access  Private
 */
router.post('/interactions/:id/rate', authenticate, requirePermission('ai:rate'), auditTrail('ai-rate'), aiCoachController.rateInteraction);

/**
 * @route   DELETE /api/v1/ai-coach/interactions/:id
 * @desc    Delete an AI coach interaction
 * @access  Private
 */
router.delete('/interactions/:id', authenticate, requirePermission('ai:delete'), auditTrail('ai-delete'), aiCoachController.deleteInteraction);

/**
 * @route   POST /api/v1/ai-coach/sessions
 * @desc    Create a new AI coach session
 * @access  Private
 */
router.post('/sessions', authenticate, aiCoachController.createSession);

/**
 * @route   POST /api/v1/ai-coach/sessions/end
 * @desc    End an AI coach session
 * @access  Private
 */
router.post('/sessions/end', authenticate, aiCoachController.endSession);

/**
 * @route   GET /api/v1/ai-coach/sessions/stats
 * @desc    Get AI coach session statistics
 * @access  Private
 */
router.get('/sessions/stats', authenticate, aiCoachController.getStats);

/**
 * @swagger
 * /ai-coach/interact:
 *   post:
 *     summary: Interagir avec l'IA Coach
 *     tags: [AICoach]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userMessage:
 *                 type: string
 *               interactionType:
 *                 type: string
 *               sessionId:
 *                 type: string
 *               context:
 *                 type: object
 *     responses:
 *       200:
 *         description: Réponse de l'IA
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 aiMessage:
 *                   type: string
 *                 sessionId:
 *                   type: string
 *
 * /ai-coach/interactions:
 *   get:
 *     summary: Historique des interactions IA
 *     tags: [AICoach]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Liste des interactions
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   userMessage:
 *                     type: string
 *                   aiMessage:
 *                     type: string
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *
 * /ai-coach/stats:
 *   get:
 *     summary: Statistiques IA Coach
 *     tags: [AICoach]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Statistiques d'usage
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalInteractions:
 *                   type: integer
 *                 activeSessions:
 *                   type: integer
 */

export default router;
