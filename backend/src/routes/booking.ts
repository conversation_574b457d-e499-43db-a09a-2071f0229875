import { Router } from 'express';
import { BookingController } from '../controllers/BookingController';
import { authenticate } from '../middlewares/auth';
import { auditTrail } from '../middleware/auditTrail';

const router = Router();
const bookingController = new BookingController();

/**
 * @swagger
 * tags:
 *   name: Booking
 *   description: Recherche de professionnels et réservation de rendez-vous
 */

/**
 * @route   GET /api/v1/booking/professionals/search
 * @desc    Search for available professionals
 * @access  Public
 */
router.get('/professionals/search', bookingController.searchProfessionals);

/**
 * @route   GET /api/v1/booking/professionals/:professionalId
 * @desc    Get professional details with availability
 * @access  Public
 */
router.get('/professionals/:professionalId', bookingController.getProfessionalDetails);

/**
 * @route   GET /api/v1/booking/filter-options
 * @desc    Get available filter options for professional search
 * @access  Public
 */
router.get('/filter-options', bookingController.getFilterOptions);

/**
 * @route   POST /api/v1/booking/appointments
 * @desc    Book a new appointment
 * @access  Private
 */
router.post('/appointments', authenticate, auditTrail('appointment-book'), bookingController.bookAppointment);

/**
 * @route   GET /api/v1/booking/appointments
 * @desc    Get user's appointments
 * @access  Private
 */
router.get('/appointments', authenticate, auditTrail('appointment-list'), bookingController.getUserAppointments);

/**
 * @route   PUT /api/v1/booking/appointments/:appointmentId/cancel
 * @desc    Cancel an appointment
 * @access  Private
 */
router.put('/appointments/:appointmentId/cancel', authenticate, auditTrail('appointment-cancel'), bookingController.cancelAppointment);

/**
 * @route   PUT /api/v1/booking/appointments/:appointmentId/reschedule
 * @desc    Reschedule an appointment
 * @access  Private
 */
router.put('/appointments/:appointmentId/reschedule', authenticate, auditTrail('appointment-reschedule'), bookingController.rescheduleAppointment);

/**
 * @route   POST /api/v1/booking/appointments/:appointmentId/rate
 * @desc    Rate an appointment
 * @access  Private
 */
router.post('/appointments/:appointmentId/rate', authenticate, auditTrail('appointment-rate'), bookingController.rateAppointment);

export default router; 