import { Router } from 'express';
import { WellnessModuleController } from '../controllers/WellnessModuleController';
import { authenticate } from '../middlewares/auth';
import { validateBody } from '../middlewares/validate';
import { auditTrail } from '../middleware/auditTrail';
import { requirePermission } from '../middleware/permissions';
import Joi from 'joi';

const router = Router();
const wellnessController = new WellnessModuleController();

// Validation schemas
const rateModuleSchema = Joi.object({
  rating: Joi.number().integer().min(1).max(5).required(),
  feedback: Joi.string().optional(),
});

const updateProgressSchema = Joi.object({
  sectionId: Joi.string().required(),
  timeSpentMinutes: Joi.number().integer().min(0).optional(),
  notes: Joi.string().optional(),
});

/**
 * @swagger
 * tags:
 *   name: Wellness
 *   description: Modules de bien-être et parcours thérapeutiques
 */

/**
 * @route   GET /api/v1/wellness
 * @desc    Get wellness modules
 * @access  Public
 */
router.get('/', wellnessController.getModules);

/**
 * @route   GET /api/v1/wellness/stats
 * @desc    Get wellness module statistics
 * @access  Public
 */
router.get('/stats', wellnessController.getStats);

/**
 * @route   GET /api/v1/wellness/filter-options
 * @desc    Get filter options for wellness modules
 * @access  Public
 */
router.get('/filter-options', wellnessController.getFilterOptions);

/**
 * @route   GET /api/v1/wellness/progress
 * @desc    Get user's wellness module progress
 * @access  Private
 */
router.get('/progress', authenticate, requirePermission('wellness:read'), auditTrail('wellness-progress-read'), wellnessController.getUserProgress);

/**
 * @route   GET /api/v1/wellness/:id
 * @desc    Get a specific wellness module
 * @access  Public
 */
router.get('/:id', wellnessController.getModuleById);

/**
 * @route   POST /api/v1/wellness/:id/start
 * @desc    Start a wellness module
 * @access  Private
 */
router.post('/:id/start', authenticate, requirePermission('wellness:start'), auditTrail('wellness-module-start'), wellnessController.startModule);

/**
 * @route   POST /api/v1/wellness/:id/progress
 * @desc    Update progress for a wellness module
 * @access  Private
 */
router.post('/:id/progress', authenticate, requirePermission('wellness:update'), auditTrail('wellness-progress-update'), validateBody(updateProgressSchema), wellnessController.updateProgress);

/**
 * @route   POST /api/v1/wellness/:id/rate
 * @desc    Rate a wellness module
 * @access  Private
 */
router.post('/:id/rate', authenticate, requirePermission('wellness:rate'), auditTrail('wellness-module-rate'), validateBody(rateModuleSchema), wellnessController.rateModule);

// Additional admin routes - commented out until methods are implemented
// router.post('/', authenticate, requirePermission('wellness:create'), auditTrail('wellness-create'), wellnessController.createModule);
// router.put('/:id', authenticate, requirePermission('wellness:update'), auditTrail('wellness-update'), wellnessController.updateModule);
// router.delete('/:id', authenticate, requirePermission('wellness:delete'), auditTrail('wellness-delete'), wellnessController.deleteModule);
// router.post('/:id/feedback', authenticate, wellnessController.addFeedback);
// router.get('/:id/stats', authenticate, wellnessController.getStats);

export default router;
