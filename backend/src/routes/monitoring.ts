import { Router } from 'express';
import MonitoringController from '../controllers/MonitoringController';
import { authenticate } from '../middlewares/auth';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Monitoring
 *   description: Surveillance et monitoring du système
 */

/**
 * @route   GET /api/v1/monitoring/logs
 * @desc    Get system logs
 * @access  Private (Admin)
 */
router.get('/logs', authenticate, MonitoringController.getLogs);

/**
 * @route   GET /api/v1/monitoring/audit
 * @desc    Get audit trail
 * @access  Private (Admin)
 */
router.get('/audit', authenticate, MonitoringController.getAudit);

/**
 * @route   GET /api/v1/monitoring/stats
 * @desc    Get system statistics
 * @access  Private (Admin)
 */
router.get('/stats', authenticate, MonitoringController.getStats);

export default router; 