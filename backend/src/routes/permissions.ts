/**
 * @swagger
 * tags:
 *   name: Permissions
 *   description: Gestion des permissions utilisateur
 */

/**
 * @swagger
 * /permissions:
 *   get:
 *     summary: Liste toutes les permissions
 *     tags: [Permissions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Liste des permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Permission'
 *   post:
 *     summary: Crée une nouvelle permission
 *     tags: [Permissions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PermissionInput'
 *     responses:
 *       201:
 *         description: Permission créée
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Permission'
 */
// ... existing code ... 