import 'reflect-metadata';
import { AppDataSource } from '../config/database';

async function initializeDatabase() {
  try {
    console.log('🔄 Initializing database...');

    // Initialize the data source
    await AppDataSource.initialize();
    console.log('✅ Database connection established');

    // Force synchronization (drop and recreate tables)
    await AppDataSource.synchronize(true);
    console.log('✅ Database schema synchronized');

    // Close the connection
    await AppDataSource.destroy();
    console.log('✅ Database initialization completed');

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  }
}

initializeDatabase();
