import { AppDataSource } from '../config/database';
import { User, UserType, UserStatus } from '../models/User';
import { ProfessionalProfile, ProfessionalType, VerificationStatus } from '../models/ProfessionalProfile';
import { Role } from '../models/Role';

async function createSampleData() {
  try {
    console.log('🔄 Initializing database connection...');
    await AppDataSource.initialize();

    const userRepository = AppDataSource.getRepository(User);
    const professionalRepository = AppDataSource.getRepository(ProfessionalProfile);
    const roleRepository = AppDataSource.getRepository(Role);

    console.log('🔧 Creating roles...');
    
    // Create basic roles
    const userRole = await roleRepository.save({
      name: 'user',
      description: 'Regular user role',
    });

    const professionalRole = await roleRepository.save({
      name: 'professional', 
      description: 'Professional user role',
    });

    const adminRole = await roleRepository.save({
      name: 'admin',
      description: 'Administrator role',
    });

    console.log('👥 Creating sample users...');

    // Create sample professional users
    const professionals = [
      {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Dr. Sarah',
        lastName: 'Wilson',
        phone: '+1234567892',
        status: UserStatus.ACTIVE,
        userType: UserType.PROFESSIONAL,
        isEmailVerified: true,
        roleId: professionalRole.id,
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Dr. Michael',
        lastName: 'Brown',
        phone: '+1234567893',
        status: UserStatus.ACTIVE,
        userType: UserType.PROFESSIONAL,
        isEmailVerified: true,
        roleId: professionalRole.id,
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Dr. Emily',
        lastName: 'Garcia',
        phone: '+**********',
        status: UserStatus.ACTIVE,
        userType: UserType.PROFESSIONAL,
        isEmailVerified: true,
        roleId: professionalRole.id,
      },
    ];

    const savedProfessionals = await userRepository.save(professionals);

    console.log('🏥 Creating professional profiles...');

    // Create professional profiles
    const professionalProfiles = [
      {
        userId: savedProfessionals[0].id,
        professionalType: ProfessionalType.PSYCHOLOGIST,
        licenseNumber: 'PSY-12345',
        licenseExpiryDate: new Date('2025-12-31'),
        licensingBoard: 'California Board of Psychology',
        licensingState: 'CA',
        verificationStatus: VerificationStatus.VERIFIED,
        verifiedAt: new Date(),
        specializations: ['anxiety', 'depression', 'trauma'],
        languagesSpoken: ['english', 'spanish'],
        yearsOfExperience: 8,
        professionalBio: 'Dr. Sarah Wilson is a licensed clinical psychologist with over 8 years of experience helping individuals overcome anxiety, depression, and trauma.',
        consultationTypes: [
          { type: 'video' as const, duration: 50, price: 150, currency: 'USD' },
          { type: 'phone' as const, duration: 50, price: 130, currency: 'USD' },
        ],
        hourlyRate: 150,
        currency: 'USD',
        acceptingNewClients: true,
        averageRating: 4.8,
        totalReviews: 47,
        totalAppointments: 234,
        isActive: true,
      },
      {
        userId: savedProfessionals[1].id,
        professionalType: ProfessionalType.THERAPIST,
        licenseNumber: 'MFT-67890',
        licenseExpiryDate: new Date('2025-12-31'),
        licensingBoard: 'California Board of Behavioral Sciences',
        licensingState: 'CA',
        verificationStatus: VerificationStatus.VERIFIED,
        verifiedAt: new Date(),
        specializations: ['relationships', 'grief_counseling'],
        languagesSpoken: ['english'],
        yearsOfExperience: 15,
        professionalBio: 'Dr. Michael Brown is a licensed marriage and family therapist with over 15 years of experience.',
        consultationTypes: [
          { type: 'video' as const, duration: 60, price: 120, currency: 'USD' },
          { type: 'in_person' as const, duration: 60, price: 140, currency: 'USD' },
        ],
        hourlyRate: 120,
        currency: 'USD',
        acceptingNewClients: true,
        averageRating: 4.9,
        totalReviews: 73,
        totalAppointments: 891,
        isActive: true,
      },
      {
        userId: savedProfessionals[2].id,
        professionalType: ProfessionalType.WELLNESS_COACH,
        licenseNumber: 'WC-54321',
        licenseExpiryDate: new Date('2025-12-31'),
        licensingBoard: 'International Coach Federation',
        licensingState: 'CA',
        verificationStatus: VerificationStatus.VERIFIED,
        verifiedAt: new Date(),
        specializations: ['stress_management', 'sleep_disorders'],
        languagesSpoken: ['english', 'spanish', 'french'],
        yearsOfExperience: 5,
        professionalBio: 'Dr. Emily Garcia is a certified wellness coach who takes a holistic approach to mental health.',
        consultationTypes: [
          { type: 'video' as const, duration: 45, price: 80, currency: 'USD' },
          { type: 'chat' as const, duration: 30, price: 50, currency: 'USD' },
        ],
        hourlyRate: 80,
        currency: 'USD',
        acceptingNewClients: true,
        averageRating: 4.7,
        totalReviews: 28,
        totalAppointments: 156,
        isActive: true,
      },
    ];

    await professionalRepository.save(professionalProfiles);

    console.log('✅ Sample data created successfully!');
    console.log('📊 Summary:');
    console.log(`   - ${savedProfessionals.length} professional users created`);
    console.log(`   - ${professionalProfiles.length} professional profiles created`);

    console.log('\n🔑 Test credentials:');
    console.log('Professionals:');
    console.log('  - <EMAIL> / password123');
    console.log('  - <EMAIL> / password123');
    console.log('  - <EMAIL> / password123');

  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  } finally {
    await AppDataSource.destroy();
  }
}

// Run the script
createSampleData();
