import 'reflect-metadata';
import { AppDataSource } from '../config/database';
import { Role } from '../models/Role';
import { Permission } from '../models/Permission';
import { User, UserStatus } from '../models/User';
import * as bcrypt from 'bcryptjs';

async function seedData() {
  try {
    console.log('🔄 Seeding initial data...');

    // Initialize the data source
    await AppDataSource.initialize();
    console.log('✅ Database connection established');

    const roleRepository = AppDataSource.getRepository(Role);
    const permissionRepository = AppDataSource.getRepository(Permission);
    const userRepository = AppDataSource.getRepository(User);

    // Create permissions if they don't exist
    const permissions = [
      { name: 'read_users', description: 'Read user data' },
      { name: 'write_users', description: 'Create and update users' },
      { name: 'delete_users', description: 'Delete users' },
      { name: 'manage_roles', description: 'Manage roles and permissions' },
      { name: 'access_admin', description: 'Access admin panel' },
    ];

    for (const permData of permissions) {
      const existingPerm = await permissionRepository.findOne({
        where: { name: permData.name }
      });
      if (!existingPerm) {
        const permission = permissionRepository.create(permData);
        await permissionRepository.save(permission);
        console.log(`✅ Created permission: ${permData.name}`);
      }
    }

    // Create roles if they don't exist
    const roles = [
      { name: 'root', description: 'Super administrator with all permissions' },
      { name: 'admin', description: 'Administrator with most permissions' },
      { name: 'user', description: 'Regular user with basic permissions' },
    ];

    for (const roleData of roles) {
      const existingRole = await roleRepository.findOne({
        where: { name: roleData.name }
      });
      if (!existingRole) {
        const role = roleRepository.create(roleData);
        await roleRepository.save(role);
        console.log(`✅ Created role: ${roleData.name}`);
      }
    }

    // Create root user if it doesn't exist
    const rootUser = await userRepository.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!rootUser) {
      const rootRole = await roleRepository.findOne({ where: { name: 'root' } });

      if (rootRole) {
        const hashedPassword = await bcrypt.hash('Password123', 12);
        const user = userRepository.create({
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: 'Root',
          lastName: 'Admin',
          role: rootRole,
          isEmailVerified: true,
          status: UserStatus.ACTIVE,
        });
        await userRepository.save(user);
        console.log('✅ Root user created: <EMAIL> / Password123');
      }
    }

    // Close the connection
    await AppDataSource.destroy();
    console.log('✅ Data seeding completed');

  } catch (error) {
    console.error('❌ Error seeding data:', error);
    process.exit(1);
  }
}

seedData();
