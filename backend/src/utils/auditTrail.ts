import { logger } from '../middlewares/logger';

// Export logger pour usage dans les middlewares
export { logger };

/**
 * Fonction d'audit trail pour tracer toutes les actions sensibles.
 */
export function auditTrail(userId: string, action: string, details?: any) {
  logger.info({
    type: 'audit',
    userId,
    action,
    details,
    timestamp: new Date().toISOString(),
  });
}

/**
 * Fonction utilitaire pour créer un logger d'audit simple
 */
export function createAuditLogger() {
  return {
    info: (data: any) => {
      console.log(`[AUDIT] ${new Date().toISOString()}:`, JSON.stringify(data, null, 2));
    },
    error: (data: any) => {
      console.error(`[AUDIT ERROR] ${new Date().toISOString()}:`, JSON.stringify(data, null, 2));
    }
  };
} 