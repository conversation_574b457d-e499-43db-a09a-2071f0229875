import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🟢 [MINIMAL] Démarrage du serveur minimal MindFlow Pro');

const app = express();
const PORT = process.env.PORT || 4000;

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: 'cross-origin' },
}));

// CORS configuration
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Simple logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Health check route
app.get('/api/v1/health', (req, res) => {
  res.json({
    success: true,
    status: 'OK',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    database: 'SQLite (development)',
    api: 'MindFlow Pro API',
  });
});

// Test route
app.get('/api/v1/test', (req, res) => {
  res.json({
    success: true,
    message: 'MindFlow Pro API is working correctly',
    timestamp: new Date().toISOString(),
  });
});

// Status route
app.get('/api/v1/status', (req, res) => {
  res.json({
    success: true,
    server: 'MindFlow Pro Backend',
    uptime: Math.floor(process.uptime()),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0',
    activeSessions: 0,
    registeredUsers: 0,
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Welcome to MindFlow Pro API',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    documentation: {
      health: '/api/v1/health',
      test: '/api/v1/test',
      status: '/api/v1/status',
    },
  });
});

// 404 handler
app.use((req, res, next) => {
  res.status(404).json({
    success: false,
    error: {
      message: 'Route not found',
      statusCode: 404,
      path: req.originalUrl,
      method: req.method,
    },
  });
});

// Global error handler
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(err.statusCode || 500).json({
    success: false,
    error: {
      message: err.message || 'Internal server error',
      statusCode: err.statusCode || 500,
    },
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 MindFlow Pro API server is running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/v1/health`);
  console.log(`🔧 Test endpoint: http://localhost:${PORT}/api/v1/test`);
  console.log(`📈 Status: http://localhost:${PORT}/api/v1/status`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`✅ Backend successfully started and ready to accept connections!`);
});

export default app; 