# Server Configuration
PORT=4000
NODE_ENV=development

# Database Configuration
DB_TYPE=sqlite
DB_DATABASE=./database.sqlite

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Security
BCRYPT_ROUNDS=12

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=debug
LOG_FILE=logs/app.log

# Email Configuration (for future email verification)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# AI Configuration (for AI coach integration)
OPENAI_API_KEY=your-openai-api-key
AI_MODEL=gpt-3.5-turbo

# File Upload Configuration
MAX_FILE_SIZE=10mb
UPLOAD_PATH=uploads/

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
