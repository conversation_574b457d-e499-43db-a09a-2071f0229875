const http = require('http');
const url = require('url');

const PORT = process.env.PORT || 4000;

// Fonction helper pour les en-têtes CORS
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
}

// Fonction helper pour répondre en JSON
function jsonResponse(res, statusCode, data) {
  setCORSHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data));
}

// Fonction helper pour vérifier l'authentification
function checkAuth(req) {
  const authHeader = req.headers.authorization;
  return authHeader && authHeader.startsWith('Bearer ');
}

// Données mock
const crisisResources = {
  success: true,
  message: 'Crisis resources retrieved successfully',
  data: {
    resources: {
      immediate: [
        {
          name: 'National Suicide Prevention Lifeline',
          phone: '988',
          description: '24/7 crisis support',
          website: 'https://suicidepreventionlifeline.org'
        },
        {
          name: 'Crisis Text Line',
          text: 'Text HOME to 741741',
          description: '24/7 crisis support via text',
          website: 'https://crisistextline.org'
        },
        {
          name: 'Emergency Services',
          phone: '911',
          description: 'For immediate medical emergencies'
        }
      ],
      mentalHealth: [
        {
          name: 'NAMI Helpline',
          phone: '1-800-950-NAMI (6264)',
          description: 'Mental health information and support',
          website: 'https://nami.org'
        }
      ]
    }
  }
};

const wellnessTips = {
  success: true,
  message: 'Wellness tips retrieved successfully',
  data: {
    tips: [
      {
        category: 'mindfulness',
        title: 'Practice Deep Breathing',
        description: 'Take 5 minutes to practice deep breathing exercises',
        difficulty: 'easy'
      },
      {
        category: 'physical',
        title: 'Take a Walk',
        description: 'A 10-minute walk can boost your mood and energy',
        difficulty: 'easy'
      },
      {
        category: 'social',
        title: 'Connect with Others',
        description: 'Reach out to a friend or family member',
        difficulty: 'medium'
      },
      {
        category: 'mental',
        title: 'Gratitude Practice',
        description: 'Write down 3 things you are grateful for today',
        difficulty: 'easy'
      }
    ]
  }
};

const professionals = {
  success: true,
  message: 'Professionals retrieved successfully',
  data: {
    professionals: [
      {
        id: '1',
        firstName: 'Dr. Marie',
        lastName: 'Dubois',
        specialization: 'Psychologie clinique',
        experience: '10 years',
        rating: 4.8,
        available: true,
        languages: ['français', 'anglais']
      },
      {
        id: '2',
        firstName: 'Dr. Jean',
        lastName: 'Martin',
        specialization: 'Thérapie cognitive',
        experience: '8 years',
        rating: 4.6,
        available: true,
        languages: ['français']
      }
    ]
  }
};

// Créer le serveur
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;

  // Log des requêtes
  console.log(`${new Date().toISOString()} - ${method} ${pathname}`);

  // Gestion des preflight OPTIONS pour CORS
  if (method === 'OPTIONS') {
    setCORSHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  // Routes
  if (method === 'GET') {
    switch (pathname) {
      case '/':
        jsonResponse(res, 200, {
          message: '🚀 MindFlow Pro API Server (Ultra Simple)',
          status: 'running',
          timestamp: new Date().toISOString(),
          environment: process.env.NODE_ENV || 'development'
        });
        break;

      case '/api/v1/health':
        jsonResponse(res, 200, {
          status: 'ok',
          timestamp: new Date().toISOString(),
          database: 'connected'
        });
        break;

      case '/api/v1/ai-coach/crisis-resources':
        jsonResponse(res, 200, crisisResources);
        break;

      case '/api/v1/ai-coach/wellness-tips':
        jsonResponse(res, 200, wellnessTips);
        break;

      case '/api/v1/professionals':
        jsonResponse(res, 200, professionals);
        break;

      // Routes protégées
      case '/api/v1/users/profile':
        if (!checkAuth(req)) {
          jsonResponse(res, 401, {
            success: false,
            message: 'No token provided'
          });
        } else {
          jsonResponse(res, 200, {
            success: true,
            message: 'User profile endpoint working',
            data: {
              message: 'This is a protected route',
              user: 'authenticated user data would be here'
            }
          });
        }
        break;

      case '/api/v1/ai-coach/interactions':
        if (!checkAuth(req)) {
          jsonResponse(res, 401, {
            success: false,
            message: 'No token provided'
          });
        } else {
          jsonResponse(res, 200, {
            success: true,
            message: 'AI coach interactions endpoint working',
            data: {
              interactions: [],
              message: 'This is a protected route'
            }
          });
        }
        break;

      case '/api/v1/journal':
        if (!checkAuth(req)) {
          jsonResponse(res, 401, {
            success: false,
            message: 'No token provided'
          });
        } else {
          jsonResponse(res, 200, {
            success: true,
            message: 'Journal endpoint working',
            data: {
              entries: [],
              message: 'This is a protected route'
            }
          });
        }
        break;

      case '/api/v1/appointments':
        if (!checkAuth(req)) {
          jsonResponse(res, 401, {
            success: false,
            message: 'No token provided'
          });
        } else {
          jsonResponse(res, 200, {
            success: true,
            message: 'Appointments endpoint working',
            data: {
              appointments: [],
              message: 'This is a protected route'
            }
          });
        }
        break;

      case '/api/v1/wellness/modules':
        if (!checkAuth(req)) {
          jsonResponse(res, 401, {
            success: false,
            message: 'No token provided'
          });
        } else {
          jsonResponse(res, 200, {
            success: true,
            message: 'Wellness modules endpoint working',
            data: {
              modules: [],
              message: 'This is a protected route'
            }
          });
        }
        break;

      case '/api/v1/health-monitoring/status':
        if (!checkAuth(req)) {
          jsonResponse(res, 401, {
            success: false,
            message: 'No token provided'
          });
        } else {
          jsonResponse(res, 200, {
            success: true,
            message: 'Health monitoring endpoint working',
            data: {
              status: 'healthy',
              message: 'This is a protected route'
            }
          });
        }
        break;

      default:
        jsonResponse(res, 404, {
          success: false,
          message: `Route ${pathname} not found`,
          timestamp: new Date().toISOString()
        });
        break;
    }
  } else {
    jsonResponse(res, 405, {
      success: false,
      message: `Method ${method} not allowed`,
      timestamp: new Date().toISOString()
    });
  }
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`🚀 MindFlow Pro API server (Ultra Simple HTTP) is running on port ${PORT}`);
  console.log(`📖 API Documentation: http://localhost:${PORT}/api/v1/health`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Frontend URL: http://localhost:5173,http://localhost:3000`);
});

// Gestion propre de l'arrêt
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received. Shutting down gracefully...');
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('👋 SIGINT received. Shutting down gracefully...');
  server.close(() => {
    process.exit(0);
  });
}); 