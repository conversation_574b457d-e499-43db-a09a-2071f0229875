# Backend MindFlow

## Démarrage rapide

```bash
npm install
npm run dev
```

## Structure du projet
- `src/` : code source principal
  - `controllers/` : logique des routes
  - `models/` : modèles de données
  - `services/` : logique métier
  - `middlewares/` : middlewares Express
  - `routes/` : routes Express
  - `validation/` : schémas de validation Joi
  - `swagger.ts` : documentation Swagger
  - `app.ts` : configuration Express
  - `server.ts` : point d'entrée
- `tests/` : tests Jest/Supertest

## Endpoints principaux
- Authentification : `/api/v1/auth/register`, `/api/v1/auth/login`, `/api/v1/auth/refresh`, `/api/v1/auth/me`, `/api/v1/auth/logout`
- Données santé mentale : `/api/v1/mental-health-data`
- Documentation Swagger : `/api-docs`

## Tests
- Lancer tous les tests :
  ```bash
  npx jest --coverage
  ```
- Les tests couvrent l'authentification, la gestion des données santé mentale, et les services principaux.

## Documentation API (Swagger)
- Accès : [http://localhost:4000/api-docs](http://localhost:4000/api-docs)
- Endpoints, schémas, exemples, sécurité JWT

## Sécurité
- Authentification JWT, hashage des mots de passe (bcrypt), validation des entrées (Joi), gestion centralisée des erreurs, logs.

## À compléter
- Enrichir la doc Swagger (autres modèles/endpoints)
- Ajouter des tests unitaires sur tous les services
- Générer la couverture de tests régulièrement 