const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api/v1';

async function testEndpoints() {
  console.log('🧪 Test des endpoints principaux...\n');

  const tests = [
    {
      name: 'Health Check',
      url: `${BASE_URL}/health`,
      method: 'GET'
    },
    {
      name: 'Swagger Documentation',
      url: `${BASE_URL}/docs`,
      method: 'GET'
    },
    {
      name: 'Crisis Resources (AI Coach)',
      url: `${BASE_URL}/ai-coach/crisis-resources`,
      method: 'GET'
    },
    {
      name: 'Wellness Tips (AI Coach)',
      url: `${BASE_URL}/ai-coach/wellness-tips`,
      method: 'GET'
    }
  ];

  for (const test of tests) {
    try {
      const response = await axios({
        method: test.method,
        url: test.url,
        timeout: 5000
      });
      
      console.log(`✅ ${test.name}: ${response.status} ${response.statusText}`);
      if (response.data) {
        console.log(`   Réponse: ${JSON.stringify(response.data).substring(0, 100)}...\n`);
      }
    } catch (error) {
      if (error.response) {
        console.log(`❌ ${test.name}: ${error.response.status} ${error.response.statusText}`);
      } else {
        console.log(`❌ ${test.name}: ${error.message}`);
      }
    }
  }

  console.log('🔐 Test des endpoints protégés (sans authentification)...\n');

  const protectedTests = [
    {
      name: 'User Profile',
      url: `${BASE_URL}/users/profile`,
      method: 'GET'
    },
    {
      name: 'AI Coach Interactions',
      url: `${BASE_URL}/ai-coach/interactions`,
      method: 'GET'
    },
    {
      name: 'Journal Entries',
      url: `${BASE_URL}/journal`,
      method: 'GET'
    }
  ];

  for (const test of protectedTests) {
    try {
      const response = await axios({
        method: test.method,
        url: test.url,
        timeout: 5000
      });
      
      console.log(`⚠️  ${test.name}: ${response.status} (devrait être 401)`);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log(`✅ ${test.name}: 401 Unauthorized (correct)`);
      } else if (error.response) {
        console.log(`❌ ${test.name}: ${error.response.status} ${error.response.statusText}`);
      } else {
        console.log(`❌ ${test.name}: ${error.message}`);
      }
    }
  }

  console.log('\n✨ Tests terminés !');
}

testEndpoints().catch(console.error); 