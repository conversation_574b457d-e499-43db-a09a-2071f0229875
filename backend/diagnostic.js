/**
 * Script de diagnostic pour MindFlow Pro
 * Vérifie l'état du backend et les connexions
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🔍 Diagnostic de MindFlow Pro en cours...');

// Vérifier si le serveur est en cours d'exécution
function checkServerStatus() {
  return new Promise((resolve) => {
    const req = http.request({
      host: 'localhost',
      port: 4000,
      path: '/api/v1/health',
      method: 'GET',
      timeout: 3000,
    }, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({
            status: res.statusCode === 200 ? 'OK' : 'ERROR',
            data: result,
          });
        } catch (e) {
          resolve({
            status: 'ERROR',
            error: 'Invalid JSON response',
          });
        }
      });
    });

    req.on('error', (err) => {
      resolve({
        status: 'ERROR',
        error: err.message,
      });
    });

    req.end();
  });
}

// Vérifier l'authentification
function checkAuthentication() {
  return new Promise((resolve) => {
    const data = JSON.stringify({
      email: '<EMAIL>',
      password: 'password123',
    });

    const req = http.request({
      host: 'localhost',
      port: 4000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length,
      },
      timeout: 3000,
    }, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve({
            status: result.success ? 'OK' : 'ERROR',
            data: result,
          });
        } catch (e) {
          resolve({
            status: 'ERROR',
            error: 'Invalid JSON response',
          });
        }
      });
    });

    req.on('error', (err) => {
      resolve({
        status: 'ERROR',
        error: err.message,
      });
    });

    req.write(data);
    req.end();
  });
}

// Vérifier les fichiers de configuration
function checkConfigFiles() {
  const configFiles = [
    { path: path.join(__dirname, '.env'), required: false },
    { path: path.join(__dirname, 'package.json'), required: true },
    { path: path.join(__dirname, 'tsconfig.json'), required: true },
  ];

  return configFiles.map((file) => {
    const exists = fs.existsSync(file.path);
    return {
      file: file.path,
      exists,
      status: exists || !file.required ? 'OK' : 'ERROR',
    };
  });
}

// Exécuter tous les diagnostics
async function runDiagnostics() {
  console.log('🔄 Vérification du serveur...');
  const serverStatus = await checkServerStatus();
  
  console.log('🔄 Vérification de l\'authentification...');
  const authStatus = await checkAuthentication();
  
  console.log('🔄 Vérification des fichiers de configuration...');
  const configStatus = checkConfigFiles();
  
  // Afficher les résultats
  console.log('\n📊 Résultats du diagnostic:');
  console.log('=======================');
  
  console.log(`\n🖥️  Statut du serveur: ${serverStatus.status === 'OK' ? '✅ OK' : '❌ ERREUR'}`);
  if (serverStatus.status === 'OK') {
    console.log(`   Version: ${serverStatus.data.version || 'N/A'}`);
    console.log(`   Environnement: ${serverStatus.data.environment || 'N/A'}`);
  } else {
    console.log(`   Erreur: ${serverStatus.error || 'Inconnu'}`);
  }
  
  console.log(`\n🔐 Authentification: ${authStatus.status === 'OK' ? '✅ OK' : '❌ ERREUR'}`);
  if (authStatus.status === 'ERROR') {
    console.log(`   Erreur: ${authStatus.error || 'Échec d\'authentification'}`);
  }
  
  console.log('\n📁 Fichiers de configuration:');
  configStatus.forEach((file) => {
    console.log(`   ${file.exists ? '✅' : '❌'} ${path.basename(file.file)}`);
  });
  
  console.log('\n🔧 Recommandations:');
  if (serverStatus.status === 'ERROR') {
    console.log('   - Vérifiez que le serveur backend est démarré');
    console.log('   - Exécutez "node enhanced-server.js" dans le dossier backend');
  }
  
  if (authStatus.status === 'ERROR') {
    console.log('   - Vérifiez les identifiants de test');
    console.log('   - Assurez-vous que le service d\'authentification fonctionne');
  }
  
  const missingConfigFiles = configStatus.filter(f => !f.exists && f.status === 'ERROR');
  if (missingConfigFiles.length > 0) {
    console.log('   - Fichiers de configuration manquants:');
    missingConfigFiles.forEach(f => {
      console.log(`     - ${path.basename(f.file)}`);
    });
  }
}

runDiagnostics().catch(console.error); 