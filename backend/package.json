{"name": "mindflow-pro-backend", "version": "1.0.0", "description": "Backend API for MindFlow Pro - Mental Health Platform for New Workers", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "NODE_ENV=development nodemon src/server.ts", "dev:prod-db": "NODE_ENV=production nodemon src/server.ts", "db:migrate": "npx typeorm migration:run", "db:seed": "npx typeorm migration:generate -- --name InitialData", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mental-health", "api", "typescript", "express"], "author": "MindFlow Pro Team", "license": "MIT", "dependencies": {"@types/bcrypt": "^5.0.2", "@types/express-rate-limit": "^5.1.3", "@types/joi": "^17.2.2", "@types/sequelize": "^4.28.20", "@types/stripe": "^8.0.416", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "qrcode": "^1.5.1", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "sqlite3": "^5.1.7", "stripe": "^18.2.1", "typeorm": "^0.3.24", "winston": "^3.17.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.21", "@types/qrcode": "^1.5.5", "@types/redis": "^4.0.10", "@types/speakeasy": "^2.0.10", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}