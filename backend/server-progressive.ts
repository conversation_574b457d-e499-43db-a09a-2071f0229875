import express from 'express';
import cors from 'cors';
import path from 'path';

const app = express();
const PORT = process.env.PORT || 4000;

// Configuration CORS permissive pour développement
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Middleware basique
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Trust proxy for correct IP detection
app.set('trust proxy', 1);

// Logging middleware simple
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Variable pour tracker l'état des features
let featuresEnabled = {
  database: false,
  auth: false,
  advanced: false
};

// Health check dynamique
app.get('/api/v1/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    database: featuresEnabled.database ? 'connected' : 'disconnected',
    features: featuresEnabled
  });
});

// Routes de base
app.get('/', (req, res) => {
  res.json({
    message: 'MindFlow Pro API - Serveur Progressif',
    status: 'running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    endpoints: {
      health: '/api/v1/health',
      test: '/api/v1/test',
      status: '/api/v1/status',
      auth: featuresEnabled.auth ? '/api/v1/auth' : '/api/v1/auth (disabled)',
    }
  });
});

app.get('/api/v1/test', (req, res) => {
  res.json({
    message: 'Test endpoint OK',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString(),
    requestInfo: {
      method: req.method,
      path: req.path,
      ip: req.ip,
      userAgent: req.headers['user-agent']
    }
  });
});

// Status endpoint pour debug
app.get('/api/v1/status', (req, res) => {
  res.json({
    server: 'MindFlow Pro Progressive Server',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    features: featuresEnabled,
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString()
  });
});

// Fonction pour initialiser progressivement les features
async function initializeFeatures() {
  try {
    console.log('🚀 Initialisation du serveur progressif MindFlow Pro...');
    
    // Phase 1: Test de connexion DB
    console.log('📊 Phase 1: Test de connexion base de données...');
    try {
      const { AppDataSource } = await import('./src/config/database');
      await AppDataSource.initialize();
      console.log('✅ Base de données connectée avec succès');
      featuresEnabled.database = true;
      
    } catch (error) {
      console.log('⚠️  Base de données non disponible, continuons en mode dégradé');
      console.log('Error:', error instanceof Error ? error.message : 'Unknown error');
    }
    
    // Phase 2: Routes d'authentification (si DB disponible)
    if (featuresEnabled.database) {
      console.log('🔐 Phase 2: Activation des routes d\'authentification...');
      try {
        // Routes d'auth simples
        app.post('/api/v1/auth/test-login', (req, res) => {
          res.json({
            success: true,
            message: 'Test login endpoint',
            timestamp: new Date().toISOString()
          });
        });
        
        app.get('/api/v1/auth/test-me', (req, res) => {
          res.json({
            success: true,
            message: 'Test me endpoint',
            user: { id: 'test', email: '<EMAIL>' },
            timestamp: new Date().toISOString()
          });
        });
        
        featuresEnabled.auth = true;
        console.log('✅ Routes d\'authentification de test activées');
      } catch (error) {
        console.log('⚠️  Routes d\'authentification non disponibles:', error instanceof Error ? error.message : 'Unknown error');
      }
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error);
  }
}

// Error handler global
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', error);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint non trouvé',
    path: req.path,
    method: req.method,
    availableEndpoints: [
      'GET /',
      'GET /api/v1/health',
      'GET /api/v1/test',
      'GET /api/v1/status'
    ],
    timestamp: new Date().toISOString()
  });
});

// Démarrage du serveur
const server = app.listen(PORT, () => {
  console.log(`🌟 Serveur MindFlow Pro démarré sur http://localhost:${PORT}`);
  console.log(`📚 Health check: http://localhost:${PORT}/api/v1/health`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/v1/test`);
  console.log(`📊 Status: http://localhost:${PORT}/api/v1/status`);
  
  // Initialisation asynchrone des features
  initializeFeatures();
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Arrêt gracieux du serveur...');
  server.close(() => {
    console.log('✅ Serveur arrêté');
    process.exit(0);
  });
});

export default app; 