#!/bin/bash

BASE_URL="http://localhost:4000/api/v1"

echo "🧪 Test des endpoints principaux..."
echo ""

echo "✅ Health Check:"
curl -s "${BASE_URL}/health" | jq .
echo ""

echo "✅ Crisis Resources (AI Coach):"
curl -s "${BASE_URL}/ai-coach/crisis-resources" | jq '.data.resources.immediate[0]'
echo ""

echo "✅ Wellness Tips (AI Coach):"
curl -s "${BASE_URL}/ai-coach/wellness-tips" | jq '.data.tips[0]'
echo ""

echo "🔐 Test des endpoints protégés (sans authentification)..."
echo ""

echo "❌ User Profile (devrait être 401):"
curl -s -w "Status: %{http_code}\n" "${BASE_URL}/users/profile"
echo ""

echo "❌ AI Coach Interactions (devrait être 401):"
curl -s -w "Status: %{http_code}\n" "${BASE_URL}/ai-coach/interactions"
echo ""

echo "❌ Journal Entries (devrait être 401):"
curl -s -w "Status: %{http_code}\n" "${BASE_URL}/journal"
echo ""

echo "✨ Tests terminés !" 