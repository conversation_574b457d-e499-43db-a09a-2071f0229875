#!/usr/bin/env node

/**
 * 🗄️ EXÉCUTEUR SQL AUTOMATIQUE SUPABASE
 * Exécute le script SQL directement dans Supabase et insère les données
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

console.log('🚀 AUTOMATISATION SUPABASE - SYSTÈME DE RENDEZ-VOUS MINDFLOW PRO');
console.log('================================================================');

// 🔧 Configuration Supabase
const SUPABASE_URL = 'https://kvdrukmoxetoiojazukf.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4';

async function executeSupabaseSQL() {
  try {
    console.log('📡 Initialisation du client Supabase...');
    
    // Client avec service key pour les opérations admin
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('✅ Client Supabase initialisé');

    // Lire le fichier SQL
    const sqlFile = path.join(__dirname, 'frontend', 'sql-direct-supabase.sql');
    console.log(`📄 Lecture du fichier SQL: ${sqlFile}`);
    
    if (!fs.existsSync(sqlFile)) {
      throw new Error(`Fichier SQL non trouvé: ${sqlFile}`);
    }

    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    console.log(`✅ Fichier SQL lu (${sqlContent.length} caractères)`);

    // Diviser en requêtes individuelles
    const queries = sqlContent
      .split(';')
      .map(q => q.trim())
      .filter(q => q.length > 0 && !q.startsWith('--') && q !== '')
      .filter(q => !q.includes('SELECT \'') && !q.includes('as info'));

    console.log(`🔍 ${queries.length} requêtes SQL trouvées`);

    // Exécuter les requêtes une par une
    console.log('⚡ Début de l\'exécution SQL...');
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < queries.length; i++) {
      const query = queries[i];
      
      if (query.length < 10) continue; // Ignorer les requêtes trop courtes
      
      try {
        console.log(`  📝 Exécution ${i + 1}/${queries.length}: ${query.substring(0, 50)}...`);
        
        const { data, error } = await supabase.rpc('exec_sql', {
          sql_query: query
        });

        if (error) {
          console.log(`  ❌ Erreur RPC: ${error.message}`);
          
          // Essayer une approche directe pour les requêtes CREATE/INSERT
          if (query.includes('CREATE TABLE') || query.includes('INSERT INTO')) {
            console.log('  🔄 Tentative d\'approche alternative...');
            
            // Pour les tables, essayer l'approche directe
            if (query.includes('CREATE TABLE professionals')) {
              await createProfessionalsTable(supabase);
            } else if (query.includes('CREATE TABLE appointments')) {
              await createAppointmentsTable(supabase);
            } else if (query.includes('INSERT INTO professionals')) {
              await insertProfessionals(supabase);
            } else if (query.includes('INSERT INTO appointments')) {
              await insertAppointments(supabase);
            }
            
            successCount++;
            console.log('  ✅ Succès avec approche alternative');
          } else {
            errorCount++;
          }
        } else {
          successCount++;
          console.log('  ✅ Succès');
        }
        
        // Petite pause entre les requêtes
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (err) {
        console.log(`  ❌ Erreur d'exécution: ${err.message}`);
        errorCount++;
      }
    }

    console.log('\n📊 RÉSULTATS DE L\'EXÉCUTION:');
    console.log(`✅ Succès: ${successCount}`);
    console.log(`❌ Erreurs: ${errorCount}`);
    console.log(`📈 Taux de réussite: ${Math.round((successCount / (successCount + errorCount)) * 100)}%`);

    // Validation des données
    console.log('\n🔍 VALIDATION DES DONNÉES...');
    await validateData(supabase);

    console.log('\n🎉 AUTOMATISATION TERMINÉE !');
    console.log('🌐 Testez maintenant sur: http://localhost:3001/test-appointments-supabase');
    
  } catch (error) {
    console.error('💥 Erreur fatale:', error.message);
    process.exit(1);
  }
}

// Fonctions pour créer les tables directement
async function createProfessionalsTable(supabase) {
  const { data, error } = await supabase
    .from('professionals')
    .select('id')
    .limit(1);
    
  if (error && error.message.includes('does not exist')) {
    throw new Error('Table professionals doit être créée manuellement dans l\'interface Supabase');
  }
}

async function createAppointmentsTable(supabase) {
  const { data, error } = await supabase
    .from('appointments')
    .select('id')
    .limit(1);
    
  if (error && error.message.includes('does not exist')) {
    throw new Error('Table appointments doit être créée manuellement dans l\'interface Supabase');
  }
}

// Fonctions pour insérer les données
async function insertProfessionals(supabase) {
  const professionals = [
    {
      name: 'Dr. Sophie Martin',
      role: 'Psychologue clinicienne',
      email: '<EMAIL>',
      specialties: ['Thérapie cognitive comportementale', 'Gestion du stress', 'Anxiété'],
      price_per_session: 85.00,
      location: '15 rue de la Paix, 75001 Paris',
      bio: 'Spécialisée en thérapie cognitive comportementale avec plus de 10 ans d\'expérience dans le traitement des troubles anxieux.'
    },
    {
      name: 'Dr. Jean Dupont',
      role: 'Psychiatre',
      email: '<EMAIL>',
      specialties: ['Psychiatrie générale', 'Troubles bipolaires', 'Dépression'],
      price_per_session: 120.00,
      location: '42 avenue Victor Hugo, 69003 Lyon',
      bio: 'Psychiatre expérimenté spécialisé dans les troubles de l\'humeur et la prise en charge des troubles bipolaires.'
    },
    {
      name: 'Marie Leblanc',
      role: 'Thérapeute comportementale',
      email: '<EMAIL>',
      specialties: ['Thérapie comportementale', 'Phobies', 'Addictions'],
      price_per_session: 75.00,
      location: '8 place Bellecour, 69002 Lyon',
      bio: 'Thérapeute spécialisée dans les troubles comportementaux et le traitement des phobies spécifiques.'
    },
    {
      name: 'Dr. Ahmed Benali',
      role: 'Psychothérapeute',
      email: '<EMAIL>',
      specialties: ['Psychothérapie humaniste', 'Thérapie de couple', 'Traumatismes'],
      price_per_session: 90.00,
      location: '25 cours Mirabeau, 13100 Aix-en-Provence',
      bio: 'Psychothérapeute avec une approche humaniste centrée sur la personne et spécialisé en thérapie de couple.'
    }
  ];

  const { data, error } = await supabase
    .from('professionals')
    .insert(professionals)
    .select();
    
  if (error) throw error;
  console.log(`  ✅ ${data.length} professionnels insérés`);
  return data;
}

async function insertAppointments(supabase) {
  // Récupérer les professionnels pour les IDs
  const { data: professionals, error: profError } = await supabase
    .from('professionals')
    .select('id, name, role, price_per_session');
    
  if (profError) throw profError;
  
  const appointments = [];
  const today = new Date();
  
  professionals.forEach((prof, index) => {
    const appointmentDate = new Date(today);
    appointmentDate.setDate(today.getDate() + index + 1);
    
    appointments.push({
      professional_id: prof.id,
      professional_name: prof.name,
      professional_role: prof.role,
      client_id: `demo-user-${index + 1}`,
      client_name: `Client Démo ${index + 1}`,
      appointment_date: appointmentDate.toISOString().split('T')[0],
      appointment_time: '14:00',
      duration_minutes: 60,
      type: 'video',
      status: 'scheduled',
      price: prof.price_per_session,
      currency: 'EUR',
      notes: `Rendez-vous de test avec ${prof.name}`,
      meeting_link: `https://meet.mindflow.com/session-${prof.id}`,
      reminder_sent: false
    });
  });

  const { data, error } = await supabase
    .from('appointments')
    .insert(appointments)
    .select();
    
  if (error) throw error;
  console.log(`  ✅ ${data.length} rendez-vous insérés`);
  return data;
}

// Validation des données
async function validateData(supabase) {
  try {
    // Test professionals
    const { data: professionals, error: profError } = await supabase
      .from('professionals')
      .select('*');
      
    if (profError) {
      console.log('❌ Erreur professionnels:', profError.message);
    } else {
      console.log(`✅ Professionnels: ${professionals.length} enregistrements`);
    }

    // Test appointments  
    const { data: appointments, error: apptError } = await supabase
      .from('appointments')
      .select('*');
      
    if (apptError) {
      console.log('❌ Erreur rendez-vous:', apptError.message);
    } else {
      console.log(`✅ Rendez-vous: ${appointments.length} enregistrements`);
    }

    // Test jointure
    const { data: joined, error: joinError } = await supabase
      .from('appointments')
      .select(`
        *,
        professional:professionals(name, role)
      `)
      .limit(3);
      
    if (joinError) {
      console.log('❌ Erreur jointure:', joinError.message);
    } else {
      console.log(`✅ Jointure: ${joined.length} enregistrements liés`);
    }
    
  } catch (error) {
    console.log('❌ Erreur de validation:', error.message);
  }
}

// Lancement du script
if (require.main === module) {
  executeSupabaseSQL();
}

module.exports = { executeSupabaseSQL }; 