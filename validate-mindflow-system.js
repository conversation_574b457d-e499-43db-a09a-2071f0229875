#!/usr/bin/env node

/**
 * 🧪 Validation Complète du Système MindFlow Pro
 * Script de validation pour vérifier tous les composants
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const FRONTEND_PORT = 3000;
const BACKEND_PORT = 4000;
const TIMEOUT = 30000;

// Couleurs pour les logs
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log(`\n${'='.repeat(50)}`, 'cyan');
  log(`🧪 ${title}`, 'cyan');
  log(`${'='.repeat(50)}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Utilitaires
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function execPromise(command, options = {}) {
  return new Promise((resolve, reject) => {
    exec(command, { timeout: TIMEOUT, ...options }, (error, stdout, stderr) => {
      if (error) {
        reject({ error, stdout, stderr });
      } else {
        resolve({ stdout, stderr });
      }
    });
  });
}

async function checkPort(port) {
  try {
    const { stdout } = await execPromise(`lsof -ti:${port}`);
    return stdout.trim() !== '';
  } catch {
    return false;
  }
}

async function httpRequest(url, method = 'GET') {
  try {
    const curlCommand = method === 'HEAD' 
      ? `curl -s -I "${url}"` 
      : `curl -s "${url}"`;
    
    const { stdout } = await execPromise(curlCommand);
    return { success: true, data: stdout };
  } catch (error) {
    return { success: false, error: error.error?.message || 'Request failed' };
  }
}

// Tests de validation
class SystemValidator {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      warnings: 0,
      tests: []
    };
  }

  addResult(name, status, message, details = null) {
    this.results.total++;
    const result = { name, status, message, details, timestamp: new Date().toISOString() };
    
    if (status === 'passed') {
      this.results.passed++;
      logSuccess(`${name}: ${message}`);
    } else if (status === 'failed') {
      this.results.failed++;
      logError(`${name}: ${message}`);
    } else if (status === 'warning') {
      this.results.warnings++;
      logWarning(`${name}: ${message}`);
    }
    
    this.results.tests.push(result);
  }

  async validateFileStructure() {
    logSection('Validation Structure des Fichiers');
    
    const requiredFiles = [
      'package.json',
      'frontend/package.json',
      'backend/package.json',
      'playwright.config.ts',
      'SUPABASE_MIGRATION_GUIDE.md',
      'frontend/src/lib/supabase/client.ts',
      'frontend/src/lib/database/index.ts',
      'frontend/src/lib/database/supabase-adapter.ts',
      'frontend/src/lib/database/sqlite-adapter.ts',
      'frontend/src/lib/migration/data-migrator.ts',
      'frontend/src/lib/config/feature-flags.ts',
      'tests/supabase-migration.spec.ts',
      'tests/server-communication.spec.ts'
    ];

    let allFilesExist = true;

    for (const file of requiredFiles) {
      const exists = fs.existsSync(file);
      if (exists) {
        const stats = fs.statSync(file);
        this.addResult(
          `File: ${file}`, 
          'passed', 
          `Exists (${stats.size} bytes)`
        );
      } else {
        this.addResult(`File: ${file}`, 'failed', 'Missing');
        allFilesExist = false;
      }
    }

    if (allFilesExist) {
      this.addResult('File Structure', 'passed', 'All required files present');
    } else {
      this.addResult('File Structure', 'failed', 'Some required files missing');
    }
  }

  async validateDependencies() {
    logSection('Validation des Dépendances');

    try {
      // Vérifier les dépendances du frontend
      const frontendPkg = JSON.parse(fs.readFileSync('frontend/package.json', 'utf8'));
      const requiredFrontendDeps = [
        '@supabase/supabase-js',
        '@supabase/ssr',
        '@tanstack/react-query',
        '@tailwindcss/forms',
        '@tailwindcss/typography',
        '@playwright/test'
      ];

      const allDeps = { ...frontendPkg.dependencies, ...frontendPkg.devDependencies };
      
      for (const dep of requiredFrontendDeps) {
        if (allDeps[dep]) {
          this.addResult(
            `Frontend Dep: ${dep}`, 
            'passed', 
            `Version ${allDeps[dep]}`
          );
        } else {
          this.addResult(`Frontend Dep: ${dep}`, 'failed', 'Missing');
        }
      }

      // Vérifier Playwright
      try {
        await execPromise('npx playwright --version');
        this.addResult('Playwright', 'passed', 'Installed and accessible');
      } catch {
        this.addResult('Playwright', 'failed', 'Not accessible');
      }

    } catch (error) {
      this.addResult('Dependencies Check', 'failed', `Error reading package.json: ${error.message}`);
    }
  }

  async validateServers() {
    logSection('Validation des Serveurs');

    // Vérifier si les serveurs tournent
    const frontendRunning = await checkPort(FRONTEND_PORT);
    const backendRunning = await checkPort(BACKEND_PORT);

    if (frontendRunning) {
      this.addResult('Frontend Server', 'passed', `Running on port ${FRONTEND_PORT}`);
      
      // Test de connectivité frontend
      const frontendHealth = await httpRequest(`http://localhost:${FRONTEND_PORT}/`);
      if (frontendHealth.success) {
        this.addResult('Frontend HTTP', 'passed', 'Responds to requests');
      } else {
        this.addResult('Frontend HTTP', 'warning', 'Server running but not responding correctly');
      }
    } else {
      this.addResult('Frontend Server', 'warning', `Not running on port ${FRONTEND_PORT}`);
    }

    if (backendRunning) {
      this.addResult('Backend Server', 'passed', `Running on port ${BACKEND_PORT}`);
      
      // Test API health
      const backendHealth = await httpRequest(`http://localhost:${BACKEND_PORT}/api/v1/health`);
      if (backendHealth.success) {
        try {
          const healthData = JSON.parse(backendHealth.data);
          this.addResult(
            'Backend API Health', 
            'passed', 
            `Status: ${healthData.status}`,
            healthData
          );
        } catch {
          this.addResult('Backend API Health', 'warning', 'Response not JSON');
        }
      } else {
        this.addResult('Backend API Health', 'failed', 'Health endpoint not responding');
      }
    } else {
      this.addResult('Backend Server', 'warning', `Not running on port ${BACKEND_PORT}`);
    }
  }

  async validatePlaywrightTests() {
    logSection('Validation des Tests Playwright');

    try {
      // Test des tests de migration Supabase
      logInfo('Exécution des tests de migration Supabase...');
      const migrationTests = await execPromise(
        'npx playwright test tests/supabase-migration.spec.ts --reporter=json'
      );
      
      try {
        const results = JSON.parse(migrationTests.stdout);
        const passed = results.suites?.[0]?.specs?.filter(spec => 
          spec.tests?.[0]?.results?.[0]?.status === 'passed'
        ).length || 0;
        
        this.addResult(
          'Supabase Migration Tests', 
          passed > 0 ? 'passed' : 'failed', 
          `${passed} tests passed`
        );
      } catch {
        this.addResult('Supabase Migration Tests', 'passed', 'Tests executed');
      }

    } catch (error) {
      this.addResult('Playwright Tests', 'failed', `Tests failed: ${error.error?.message || 'Unknown error'}`);
    }
  }

  async validateArchitecture() {
    logSection('Validation Architecture Supabase');

    // Vérifier que les fichiers d'architecture existent et sont bien formés
    const architectureFiles = [
      'frontend/src/lib/supabase/client.ts',
      'frontend/src/lib/database/index.ts',
      'frontend/src/lib/database/supabase-adapter.ts',
      'frontend/src/lib/database/sqlite-adapter.ts'
    ];

    let validArchitecture = true;

    for (const file of architectureFiles) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // Vérifications basiques de contenu
        if (file.includes('supabase-adapter') && content.includes('createSupabaseClient')) {
          this.addResult(`Architecture: ${path.basename(file)}`, 'passed', 'Contains Supabase integration');
        } else if (file.includes('sqlite-adapter') && content.includes('mockUsers')) {
          this.addResult(`Architecture: ${path.basename(file)}`, 'passed', 'Contains SQLite compatibility');
        } else if (file.includes('index.ts') && content.includes('DatabaseAdapter')) {
          this.addResult(`Architecture: ${path.basename(file)}`, 'passed', 'Contains abstraction layer');
        } else if (file.includes('client.ts') && content.includes('createBrowserClient')) {
          this.addResult(`Architecture: ${path.basename(file)}`, 'passed', 'Contains Supabase client');
        } else {
          this.addResult(`Architecture: ${path.basename(file)}`, 'warning', 'Content structure unclear');
        }
      } catch (error) {
        this.addResult(`Architecture: ${path.basename(file)}`, 'failed', `Cannot read file: ${error.message}`);
        validArchitecture = false;
      }
    }

    if (validArchitecture) {
      this.addResult('Supabase Architecture', 'passed', 'All architecture files in place');
    } else {
      this.addResult('Supabase Architecture', 'failed', 'Architecture incomplete');
    }
  }

  async validateConfiguration() {
    logSection('Validation Configuration');

    // Vérifier la configuration Playwright
    try {
      const playwrightConfig = fs.readFileSync('playwright.config.ts', 'utf8');
      if (playwrightConfig.includes('defineConfig') && playwrightConfig.includes('chromium')) {
        this.addResult('Playwright Config', 'passed', 'Configuration valid');
      } else {
        this.addResult('Playwright Config', 'warning', 'Configuration might be incomplete');
      }
    } catch {
      this.addResult('Playwright Config', 'failed', 'Configuration file missing');
    }

    // Vérifier le guide de migration
    try {
      const migrationGuide = fs.readFileSync('SUPABASE_MIGRATION_GUIDE.md', 'utf8');
      if (migrationGuide.includes('Phase 1') && migrationGuide.includes('Supabase')) {
        this.addResult('Migration Guide', 'passed', 'Complete documentation available');
      } else {
        this.addResult('Migration Guide', 'warning', 'Documentation might be incomplete');
      }
    } catch {
      this.addResult('Migration Guide', 'failed', 'Documentation missing');
    }
  }

  generateReport() {
    logSection('Rapport de Validation');

    const successRate = ((this.results.passed / this.results.total) * 100).toFixed(1);
    
    log(`\n📊 RÉSULTATS GLOBAUX`, 'bright');
    log(`${'─'.repeat(30)}`, 'blue');
    log(`Total des tests     : ${this.results.total}`, 'blue');
    log(`Tests réussis      : ${this.results.passed}`, 'green');
    log(`Tests échoués      : ${this.results.failed}`, 'red');
    log(`Avertissements     : ${this.results.warnings}`, 'yellow');
    log(`Taux de réussite   : ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');

    // Recommandations
    log(`\n🎯 RECOMMANDATIONS`, 'bright');
    log(`${'─'.repeat(30)}`, 'magenta');

    if (this.results.failed === 0 && this.results.warnings === 0) {
      log(`🎉 Système parfaitement validé ! Prêt pour la production.`, 'green');
    } else if (this.results.failed === 0) {
      log(`✅ Système fonctionnel avec quelques avertissements mineurs.`, 'yellow');
      log(`📋 Consultez les avertissements ci-dessus pour les optimisations.`, 'yellow');
    } else {
      log(`🔧 Corrections nécessaires avant mise en production.`, 'red');
      log(`📋 Traitez les erreurs critiques listées ci-dessus.`, 'red');
    }

    // Prochaines étapes
    log(`\n🚀 PROCHAINES ÉTAPES`, 'bright');
    log(`${'─'.repeat(30)}`, 'cyan');
    log(`1. Créer le projet Supabase si pas déjà fait`, 'cyan');
    log(`2. Configurer les variables d'environnement (.env.local)`, 'cyan');
    log(`3. Exécuter les tests avec: npm run test`, 'cyan');
    log(`4. Suivre le guide: SUPABASE_MIGRATION_GUIDE.md`, 'cyan');

    // Statut final
    const overallStatus = this.results.failed === 0 ? 'SUCCESS' : 'NEEDS_ATTENTION';
    log(`\n🏁 STATUT FINAL: ${overallStatus}`, overallStatus === 'SUCCESS' ? 'green' : 'yellow');

    return {
      status: overallStatus,
      summary: this.results,
      successRate: parseFloat(successRate)
    };
  }

  async runAllValidations() {
    log(`🚀 Démarrage de la validation complète du système MindFlow Pro`, 'bright');
    log(`⏰ ${new Date().toLocaleString()}`, 'blue');

    try {
      await this.validateFileStructure();
      await this.validateDependencies();
      await this.validateServers();
      await this.validateArchitecture();
      await this.validateConfiguration();
      await this.validatePlaywrightTests();
      
      return this.generateReport();
    } catch (error) {
      logError(`Erreur critique pendant la validation: ${error.message}`);
      this.addResult('System Validation', 'failed', `Critical error: ${error.message}`);
      return this.generateReport();
    }
  }
}

// Exécution du script
async function main() {
  const validator = new SystemValidator();
  
  try {
    const results = await validator.runAllValidations();
    
    // Sauvegarde du rapport
    const reportPath = 'validation-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    logInfo(`Rapport détaillé sauvegardé dans: ${reportPath}`);
    
    // Code de sortie
    process.exit(results.status === 'SUCCESS' ? 0 : 1);
    
  } catch (error) {
    logError(`Erreur fatale: ${error.message}`);
    process.exit(1);
  }
}

// Point d'entrée
if (require.main === module) {
  main();
}

module.exports = { SystemValidator }; 