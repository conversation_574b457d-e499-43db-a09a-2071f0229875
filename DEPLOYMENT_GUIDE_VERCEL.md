
# 🚀 GUIDE DE DÉPLOIEMENT VERCEL - MINDFLOW PRO

## 1. Connexion du Repository

1. <PERSON><PERSON> <PERSON> https://vercel.com/dashboard
2. C<PERSON><PERSON> sur "Add New" > "Project"
3. Sélectionner le repository GitHub "MindFlow-Pro"
4. Configurer les paramètres:
   - Framework: Next.js
   - Root Directory: ./frontend
   - Build Command: npm run build
   - Output Directory: .next

## 2. Variables d'environnement

Ajouter ces variables dans Vercel Dashboard > Settings > Environment Variables:

```
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
NEXT_PUBLIC_USE_SUPABASE_DATABASE=true
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true
```

## 3. Déploiement

1. <PERSON><PERSON>r sur "Deploy"
2. Attendre la fin du build (2-5 minutes)
3. Vérifier le déploiement sur l'URL fournie

## 4. Domaine personnalisé (optionnel)

1. Aller dans Settings > Domains
2. Ajouter votre domaine personnalisé
3. Configurer les DNS selon les instructions

## 5. Monitoring

- Analytics: Activé automatiquement
- Speed Insights: Activé automatiquement
- Functions: Monitoring des API routes

## 6. URLs importantes

- Dashboard Vercel: https://vercel.com/dashboard
- Repository GitHub: Voir la sortie Git
- Application déployée: Voir après déploiement

---

🎉 MindFlow Pro sera accessible publiquement après le déploiement !
