#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

console.log('🚀 Configuration Automatisée Supabase pour MindFlow Pro\n');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function setupSupabase() {
  try {
    console.log('📋 Collecte des informations Supabase...\n');

    console.log('1️⃣  Créez votre projet sur https://supabase.com');
    console.log('   - Nom du projet: mindflow-pro');
    console.log('   - Région recommandée: West EU (Ireland)\n');

    const projectUrl = await question('🔗 URL du projet (https://xxx.supabase.co): ');
    const anonKey = await question('🔑 Clé publique (anon key): ');
    const serviceKey = await question('🔐 Clé de service (service_role key): ');

    console.log('\n2️⃣  Création du fichier .env.local...');

    const envContent = `# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=${projectUrl}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${anonKey}
SUPABASE_SERVICE_ROLE_KEY=${serviceKey}

# Feature Flags - Migration Progressive
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_ENABLE_REAL_TIME=false
NEXT_PUBLIC_MIGRATE_USER_DATA=false
NEXT_PUBLIC_MIGRATE_MOOD_TRACKING=false
NEXT_PUBLIC_MIGRATE_JOURNAL_ENTRIES=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=false

# Backend Configuration
NEXT_PUBLIC_BACKEND_URL=http://localhost:4000
NEXT_PUBLIC_API_BASE_URL=http://localhost:4000/api/v1

# Development Configuration
NODE_ENV=development
NEXT_PUBLIC_NODE_ENV=development
`;

    const envPath = path.join(__dirname, 'frontend', '.env.local');
    fs.writeFileSync(envPath, envContent);
    console.log('✅ Fichier .env.local créé avec succès');

    console.log('\n✨ Configuration terminée !');
    console.log('\n📊 Prochaines étapes:');
    console.log('1. Exécuter le script SQL dans Supabase');
    console.log('2. Configurer Authentication URLs');
    console.log('3. Tester: npm test -- tests/supabase-migration.spec.ts');
    console.log('4. Activer DUAL_DATABASE_MODE=true');

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    rl.close();
  }
}

setupSupabase();
