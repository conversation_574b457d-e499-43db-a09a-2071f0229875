import { defineConfig, devices } from '@playwright/test';

/**
 * 🚀 CONFIGURATION PLAYWRIGHT COMPLÈTE - MINDFLOW PRO
 * Tests E2E pour toutes les phases développées
 */
export default defineConfig({
  testDir: './tests/e2e',
  
  /* Configuration globale */
  timeout: 120 * 1000, // 2 minutes par test
  fullyParallel: false, // Tests en série pour éviter conflits
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1,
  workers: process.env.CI ? 1 : 2,
  
  /* Reporters */
  reporter: [
    ['html', { 
      open: 'never',
      outputFolder: 'test-results/html-report'
    }],
    ['json', { 
      outputFile: 'test-results/results.json' 
    }],
    ['junit', { 
      outputFile: 'test-results/junit.xml' 
    }],
    ['line'],
    ['github']
  ],
  
  /* Configuration use globale */
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 15 * 1000,
    navigationTimeout: 30 * 1000,
    
    /* Headers par défaut */
    extraHTTPHeaders: {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
      'Cache-Control': 'no-cache'
    }
  },

  /* Configuration expect */
  expect: {
    timeout: 10 * 1000,
    toHaveScreenshot: { 
      threshold: 0.3
    }
  },

  /* Dossier de sortie */
  outputDir: 'test-results/artifacts',

  /* Projets de test */
  projects: [
    // Setup Project
    {
      name: 'setup',
      testMatch: '**/setup.ts',
      teardown: 'teardown',
    },
    
    // Tests Desktop Chrome
    {
      name: 'desktop-chrome',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 }
      },
      dependencies: ['setup'],
    },

    // Tests Desktop Firefox
    {
      name: 'desktop-firefox',
      use: { 
        ...devices['Desktop Firefox'],
        viewport: { width: 1280, height: 720 }
      },
      dependencies: ['setup'],
    },

    // Tests Mobile Chrome
    {
      name: 'mobile-chrome',
      use: { 
        ...devices['Pixel 7'],
      },
      dependencies: ['setup'],
    },

    // Tests Tablet
    {
      name: 'tablet',
      use: {
        ...devices['iPad Pro'],
      },
      dependencies: ['setup'],
    },

    // Teardown Project
    {
      name: 'teardown',
      testMatch: '**/teardown.ts',
    }
  ],

  /* Serveurs web automatiques */
  webServer: process.env.CI ? undefined : [
    {
      command: 'cd frontend && npm run dev',
      port: 3000,
      timeout: 120 * 1000,
      reuseExistingServer: !process.env.CI,
      stdout: 'pipe',
      stderr: 'pipe',
    }
  ],

  /* Configuration globale des tests */
  globalSetup: require.resolve('./tests/e2e/global-setup.ts'),
  globalTeardown: require.resolve('./tests/e2e/global-teardown.ts'),
}); 