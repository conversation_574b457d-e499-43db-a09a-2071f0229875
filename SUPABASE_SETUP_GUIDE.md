# Guide de Configuration Supabase pour MindFlow Pro

## Étape 1 : <PERSON><PERSON><PERSON> le Projet Supabase

1. <PERSON><PERSON> sur https://supabase.com
2. Créer un nouveau projet "mindflow-pro"
3. Noter les clés API (URL, anon key, service role key)

## Étape 2 : Variables d'Environnement

Créer `frontend/.env.local` avec :
```
NEXT_PUBLIC_SUPABASE_URL=https://votre-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=votre-anon-key
SUPABASE_SERVICE_ROLE_KEY=votre-service-role-key
```

## Étape 3 : Migration Progressive

1. Activer DUAL_DATABASE_MODE=true
2. Migrer les données progressivement
3. Basculer vers Supabase

Voir SUPABASE_MIGRATION_GUIDE.md pour les détails.
