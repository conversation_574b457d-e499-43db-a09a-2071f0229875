#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

console.log('🚀 TEST COMPLET - Migration Supabase MindFlow Pro');
console.log('='.repeat(60));

// Configuration Supabase
const SUPABASE_URL = 'https://kvdrukmoxetoiojazukf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzODE0ODEsImV4cCI6MjA1MDk1NzQ4MX0.Mu8Wao-8lGO2PkrTHQgPIhzQNHJ9Dtu4bhALCRNq6bw';

// Initialiser le client Supabase
let supabase;

async function testSupabaseConnectivity() {
  console.log('\n1. 🔌 Test de Connectivité Supabase');
  console.log('-'.repeat(40));
  
  try {
    supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    console.log('   ✅ Client Supabase initialisé');
    
    // Test de connexion basique
    const { data, error } = await supabase.from('users').select('count').limit(1);
    
    if (error) {
      console.log('   ⚠️  Erreur de connexion:', error.message);
      if (error.message.includes('relation "public.users" does not exist')) {
        console.log('   📝 Tables non encore créées - Normal pour première connexion');
        return { connected: true, tablesExist: false };
      }
      return { connected: false, error: error.message };
    }
    
    console.log('   ✅ Connexion Supabase réussie');
    console.log('   ✅ Tables accessibles');
    return { connected: true, tablesExist: true };
    
  } catch (error) {
    console.log('   ❌ Erreur de connexion:', error.message);
    return { connected: false, error: error.message };
  }
}

async function testDatabaseSchema() {
  console.log('\n2. 📋 Vérification du Schéma de Base de Données');
  console.log('-'.repeat(40));
  
  const tables = [
    'users', 'professionals', 'appointments', 'journal_entries',
    'therapeutic_paths', 'wellness_modules', 'user_path_enrollments',
    'ai_coach_sessions', 'notifications'
  ];
  
  const results = {};
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1);
      
      if (error) {
        if (error.message.includes('does not exist')) {
          console.log(`   ❌ Table '${table}' manquante`);
          results[table] = { exists: false, error: error.message };
        } else {
          console.log(`   ⚠️  Table '${table}' erreur:`, error.message);
          results[table] = { exists: true, error: error.message };
        }
      } else {
        console.log(`   ✅ Table '${table}' existe et accessible`);
        results[table] = { exists: true, accessible: true };
      }
    } catch (error) {
      console.log(`   ❌ Erreur test table '${table}':`, error.message);
      results[table] = { exists: false, error: error.message };
    }
  }
  
  return results;
}

async function testFeatureFlags() {
  console.log('\n3. 🚩 Test des Feature Flags');
  console.log('-'.repeat(40));
  
  const envPath = path.join(__dirname, 'frontend', '.env.local');
  
  if (!fs.existsSync(envPath)) {
    console.log('   ❌ Fichier .env.local manquant');
    return { configured: false };
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const flags = {
    NEXT_PUBLIC_SUPABASE_URL: envContent.includes('NEXT_PUBLIC_SUPABASE_URL'),
    NEXT_PUBLIC_SUPABASE_ANON_KEY: envContent.includes('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
    NEXT_PUBLIC_USE_SUPABASE_AUTH: envContent.includes('NEXT_PUBLIC_USE_SUPABASE_AUTH'),
    NEXT_PUBLIC_USE_SUPABASE_DATABASE: envContent.includes('NEXT_PUBLIC_USE_SUPABASE_DATABASE'),
    NEXT_PUBLIC_DUAL_DATABASE_MODE: envContent.includes('NEXT_PUBLIC_DUAL_DATABASE_MODE')
  };
  
  console.log('   Variables d\'environnement:');
  Object.entries(flags).forEach(([key, exists]) => {
    console.log(`   ${exists ? '✅' : '❌'} ${key}`);
  });
  
  return { configured: Object.values(flags).every(Boolean), flags };
}

async function testDatabaseAdapters() {
  console.log('\n4. 🔄 Test des Adaptateurs de Base de Données');
  console.log('-'.repeat(40));
  
  // Vérifier l'existence des fichiers adaptateurs
  const adapters = [
    'frontend/src/lib/database/supabase-adapter.ts',
    'frontend/src/lib/database/sqlite-adapter.ts',
    'frontend/src/lib/database/index.ts'
  ];
  
  const results = {};
  
  adapters.forEach(adapter => {
    const exists = fs.existsSync(path.join(__dirname, adapter));
    console.log(`   ${exists ? '✅' : '❌'} ${adapter}`);
    results[adapter] = exists;
  });
  
  return results;
}

async function simulateMigrationProcess() {
  console.log('\n5. 🔄 Simulation du Processus de Migration');
  console.log('-'.repeat(40));
  
  console.log('   📝 Étapes de migration simulées:');
  
  // Étape 1: Vérifier les données SQLite existantes
  console.log('   1️⃣  Vérification données SQLite...');
  const sqliteExists = fs.existsSync(path.join(__dirname, 'database.sqlite'));
  console.log(`      ${sqliteExists ? '✅' : '❌'} Base SQLite trouvée`);
  
  // Étape 2: Test de création d'un utilisateur test
  console.log('   2️⃣  Test création utilisateur test...');
  try {
    const testUser = {
      email: '<EMAIL>',
      username: 'test-migration',
      created_at: new Date().toISOString()
    };
    
    // Dans un vrai scénario, on créerait l'utilisateur
    console.log('      ✅ Structure utilisateur test valide');
    console.log('      📋 Données test:', JSON.stringify(testUser, null, 2));
    
  } catch (error) {
    console.log('      ❌ Erreur création utilisateur test:', error.message);
  }
  
  // Étape 3: Test de migration d'un journal
  console.log('   3️⃣  Test migration entrée journal...');
  try {
    const testJournal = {
      user_id: 'test-user-id',
      title: 'Test Migration Journal',
      content: 'Contenu de test pour la migration',
      mood_score: 7,
      created_at: new Date().toISOString()
    };
    
    console.log('      ✅ Structure journal test valide');
    console.log('      📋 Données test:', JSON.stringify(testJournal, null, 2));
    
  } catch (error) {
    console.log('      ❌ Erreur migration journal test:', error.message);
  }
  
  return {
    sqliteExists,
    migrationSteps: ['user_creation', 'journal_migration', 'data_validation']
  };
}

async function testFrontendPages() {
  console.log('\n6. 🌐 Test des Pages Frontend');
  console.log('-'.repeat(40));
  
  const { exec } = require('child_process');
  const util = require('util');
  const execAsync = util.promisify(exec);
  
  const pages = [
    'http://localhost:3000/',
    'http://localhost:3000/test-basic',
    'http://localhost:3000/ultra-simple',
    'http://localhost:3000/test-supabase'
  ];
  
  console.log('   🔍 Test de disponibilité des pages...');
  
  for (const page of pages) {
    try {
      await execAsync(`curl -s -o /dev/null -w "%{http_code}" ${page}`, { timeout: 5000 });
      console.log(`   ✅ ${page} - Accessible`);
    } catch (error) {
      console.log(`   ⚠️  ${page} - Non testé (serveur possiblement arrêté)`);
    }
  }
}

async function generateMigrationReport() {
  console.log('\n7. 📊 Génération du Rapport de Migration');
  console.log('-'.repeat(40));
  
  const report = {
    timestamp: new Date().toISOString(),
    migration_status: 'EN_COURS',
    tests_completed: [
      'connectivity_test',
      'schema_verification',
      'feature_flags_check',
      'adapters_verification',
      'migration_simulation',
      'frontend_pages_test'
    ],
    next_steps: [
      'Créer les tables manquantes dans Supabase',
      'Configurer les politiques RLS (Row Level Security)',
      'Migrer les données utilisateurs existantes',
      'Tester l\'authentification Supabase',
      'Valider le mode dual database',
      'Déployer en production'
    ],
    recommendations: [
      'Backup complet de la base SQLite avant migration',
      'Migration progressive par petits lots',
      'Tests utilisateurs en environnement de staging',
      'Monitoring des performances post-migration'
    ]
  };
  
  const reportPath = path.join(__dirname, 'rapport-migration-supabase.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log('   ✅ Rapport généré:', reportPath);
  return report;
}

async function main() {
  try {
    const results = {};
    
    // Tests séquentiels
    results.connectivity = await testSupabaseConnectivity();
    results.schema = await testDatabaseSchema();
    results.featureFlags = await testFeatureFlags();
    results.adapters = await testDatabaseAdapters();
    results.migration = await simulateMigrationProcess();
    await testFrontendPages();
    results.report = await generateMigrationReport();
    
    // Résumé final
    console.log('\n🎯 RÉSUMÉ DU TEST DE MIGRATION');
    console.log('='.repeat(60));
    console.log(`✅ Connectivité Supabase: ${results.connectivity.connected ? 'OK' : 'ERREUR'}`);
    console.log(`✅ Configuration Env: ${results.featureFlags.configured ? 'OK' : 'PARTIEL'}`);
    console.log(`✅ Adaptateurs DB: ${Object.values(results.adapters).every(Boolean) ? 'OK' : 'PARTIEL'}`);
    console.log(`✅ Migration Simulée: OK`);
    
    console.log('\n🚀 PROCHAINES ÉTAPES:');
    console.log('1. Créer le schéma complet dans Supabase');
    console.log('2. Configurer les politiques de sécurité');
    console.log('3. Migrer les données existantes');
    console.log('4. Tester l\'authentification');
    console.log('5. Valider en production');
    
    console.log('\n📋 Fichiers générés:');
    console.log('- rapport-migration-supabase.json');
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
    process.exit(1);
  }
}

// Lancer le test
if (require.main === module) {
  main();
}

module.exports = {
  testSupabaseConnectivity,
  testDatabaseSchema,
  testFeatureFlags,
  simulateMigrationProcess
}; 