#!/usr/bin/env node

/**
 * 🗄️ ARCHITECTURE BASE DE DONNÉES PRINCIPALE - MINDFLOW PRO
 * 📅 28 Décembre 2024
 * 🎯 Structure centralisée avec mise à jour automatique
 */

const fs = require('fs');
const path = require('path');

console.log('�� CONFIGURATION ARCHITECTURE BASE DE DONNÉES PRINCIPALE');
console.log('🎯 Structure centralisée pour MindFlow Pro');
console.log('=' .repeat(60));

// 1️⃣ SCHÉMA PRINCIPAL UNIFIÉ
const schemaSQL = `-- 🗄️ MINDFLOW PRO - SCHÉMA PRINCIPAL UNIFIÉ
-- 📅 Généré le ${new Date().toLocaleDateString('fr-FR')}
-- 🎯 Base de données centralisée pour toutes les fonctionnalités

-- ============================
-- 🔐 MODULE AUTHENTIFICATION
-- ============================

CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    profile_complete BOOLEAN DEFAULT false,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ============================
-- 👨‍⚕️ MODULE PROFESSIONNELS
-- ============================

CREATE TABLE IF NOT EXISTS professionals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(100) NOT NULL,
    specialties TEXT[] DEFAULT '{}',
    bio TEXT,
    avatar_url TEXT,
    phone VARCHAR(20),
    location JSONB,
    pricing JSONB DEFAULT '{}',
    rating DECIMAL(3,2) DEFAULT 0.00,
    reviews_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ============================
-- 📅 MODULE RENDEZ-VOUS
-- ============================

CREATE TABLE IF NOT EXISTS appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    professional_id UUID REFERENCES professionals(id),
    client_id UUID REFERENCES users(id),
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration INTEGER DEFAULT 60,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'scheduled',
    price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'EUR',
    notes TEXT,
    client_name VARCHAR(255),
    client_email VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ============================
-- 📝 MODULE JOURNAL
-- ============================

CREATE TABLE IF NOT EXISTS journal_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    mood_score INTEGER CHECK (mood_score >= 1 AND mood_score <= 10),
    emotions TEXT[],
    tags TEXT[],
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ============================
-- 🤖 MODULE IA COACH
-- ============================

CREATE TABLE IF NOT EXISTS ai_coach_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_type VARCHAR(100) NOT NULL,
    status VARCHAR(50) DEFAULT 'active',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    session_data JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ============================
-- 📊 MODULE ANALYTICS
-- ============================

CREATE TABLE IF NOT EXISTS mood_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    mood_score INTEGER NOT NULL,
    stress_level INTEGER,
    energy_level INTEGER,
    sleep_hours DECIMAL(3,1),
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- ============================
-- 🔔 MODULE NOTIFICATIONS
-- ============================

CREATE TABLE IF NOT EXISTS smart_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    priority VARCHAR(20) DEFAULT 'normal',
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT NOW()
);

-- ============================
-- 🔍 INDEX DE PERFORMANCE
-- ============================

CREATE INDEX IF NOT EXISTS idx_appointments_professional ON appointments(professional_id);
CREATE INDEX IF NOT EXISTS idx_appointments_client ON appointments(client_id);
CREATE INDEX IF NOT EXISTS idx_journal_user ON journal_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_mood_user ON mood_analytics(user_id);

-- ============================
-- 🔄 TRIGGERS AUTOMATIQUES
-- ============================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_professionals_updated_at BEFORE UPDATE ON professionals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON appointments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
`;

// 2️⃣ HOOK UNIFIÉ FRONTEND
const hookUnifie = `import { useState, useEffect, useCallback } from 'react';
import { createBrowserClient } from '@supabase/ssr';

/**
 * 🗄️ HOOK UNIFIÉ MINDFLOW PRO
 * 📅 Hook principal pour toutes les données
 */

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export function usePrincipalDatabase<T = any>(tableName: string) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // FETCH DATA
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      const { data: result, error: fetchError } = await supabase
        .from(tableName)
        .select('*')
        .order('created_at', { ascending: false });

      if (fetchError) throw fetchError;
      setData(result || []);
    } catch (err) {
      console.error('Erreur fetch:', err);
      setError(err instanceof Error ? err.message : 'Erreur');
    } finally {
      setLoading(false);
    }
  }, [tableName]);

  // CREATE
  const create = useCallback(async (newData: Partial<T>) => {
    try {
      const { data: result, error } = await supabase
        .from(tableName)
        .insert(newData)
        .select()
        .single();

      if (error) throw error;
      setData(prev => [result, ...prev]);
      return result;
    } catch (err) {
      console.error('Erreur création:', err);
      throw err;
    }
  }, [tableName]);

  // UPDATE
  const update = useCallback(async (id: string, updates: Partial<T>) => {
    try {
      const { data: result, error } = await supabase
        .from(tableName)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      setData(prev => prev.map(item => 
        (item as any).id === id ? result : item
      ));
      return result;
    } catch (err) {
      console.error('Erreur update:', err);
      throw err;
    }
  }, [tableName]);

  // DELETE
  const remove = useCallback(async (id: string) => {
    try {
      const { error } = await supabase
        .from(tableName)
        .delete()
        .eq('id', id);

      if (error) throw error;
      setData(prev => prev.filter(item => (item as any).id !== id));
    } catch (err) {
      console.error('Erreur suppression:', err);
      throw err;
    }
  }, [tableName]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, create, update, remove, refresh: fetchData };
}

// HOOKS SPÉCIALISÉS
export const useUsers = () => usePrincipalDatabase('users');
export const useAppointments = () => usePrincipalDatabase('appointments');
export const useProfessionals = () => usePrincipalDatabase('professionals');
export const useJournalEntries = () => usePrincipalDatabase('journal_entries');
export const useMoodAnalytics = () => usePrincipalDatabase('mood_analytics');
export const useNotifications = () => usePrincipalDatabase('smart_notifications');
`;

// 3️⃣ CONFIGURATION SYSTÈME
const config = {
  "database": {
    "type": "supabase",
    "url": "https://kvdrukmoxetoiojazukf.supabase.co",
    "auto_sync": true,
    "real_time": true
  },
  "tables": [
    "users",
    "professionals", 
    "appointments",
    "journal_entries",
    "ai_coach_sessions",
    "mood_analytics",
    "smart_notifications"
  ],
  "features": {
    "auto_migration": true,
    "data_validation": true,
    "performance_monitoring": true
  },
  "version": "1.0.0",
  "generated": new Date().toISOString()
};

// GÉNÉRATION DES FICHIERS
console.log('📊 Génération du schéma SQL...');
fs.writeFileSync('schema-principal.sql', schemaSQL);
console.log('✅ Créé: schema-principal.sql');

console.log('🔗 Génération du hook unifié...');
if (!fs.existsSync('frontend/src/hooks')) {
    fs.mkdirSync('frontend/src/hooks', { recursive: true });
}
fs.writeFileSync('frontend/src/hooks/usePrincipalDatabase.ts', hookUnifie);
console.log('✅ Créé: frontend/src/hooks/usePrincipalDatabase.ts');

console.log('⚙️ Génération de la configuration...');
fs.writeFileSync('database-config-principal.json', JSON.stringify(config, null, 2));
console.log('✅ Créé: database-config-principal.json');

console.log('\n🎉 ARCHITECTURE BASE DE DONNÉES PRINCIPALE CONFIGURÉE !');
console.log('=' .repeat(60));
console.log('📁 Fichiers générés:');
console.log('  📊 schema-principal.sql');
console.log('  🔗 frontend/src/hooks/usePrincipalDatabase.ts');
console.log('  ⚙️ database-config-principal.json');

console.log('\n🚀 PROCHAINES ÉTAPES:');
console.log('1. Appliquer le schéma en Supabase');
console.log('2. Migrer les données existantes');
console.log('3. Tester l\'intégration frontend/backend');
console.log('4. Déployer en production');

console.log('\n✅ BASE DE DONNÉES PRINCIPALE PRÊTE !');
console.log('🎯 Architecture centralisée avec mise à jour automatique');
