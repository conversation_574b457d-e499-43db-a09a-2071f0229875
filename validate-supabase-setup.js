#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

console.log('🔍 VALIDATION SETUP SUPABASE - MINDFLOW PRO');
console.log('===========================================');

async function validateSetup() {
  try {
    console.log('📡 Connexion à Supabase...');
    const supabase = createClient(
      'https://kvdrukmoxetoiojazukf.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
    );

    let score = 0;
    const maxScore = 5;

    console.log('\n🧪 TEST 1: Table professionals');
    try {
      const { data, error } = await supabase
        .from('professionals')
        .select('id, name')
        .limit(1);
      
      if (error) {
        console.log('❌ Échec:', error.message);
      } else {
        console.log('✅ Succès: Table professionals accessible');
        score++;
      }
    } catch (e) {
      console.log('❌ Erreur:', e.message);
    }

    console.log('\n🧪 TEST 2: Table appointments');
    try {
      const { data, error } = await supabase
        .from('appointments')
        .select('id, professional_name')
        .limit(1);
      
      if (error) {
        console.log('❌ Échec:', error.message);
      } else {
        console.log('✅ Succès: Table appointments accessible');
        score++;
      }
    } catch (e) {
      console.log('❌ Erreur:', e.message);
    }

    console.log('\n🧪 TEST 3: Compte des professionnels');
    try {
      const { count, error } = await supabase
        .from('professionals')
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log('❌ Échec:', error.message);
      } else {
        console.log('✅ Succès: ' + (count || 0) + ' professionnels trouvés');
        if (count > 0) score++;
      }
    } catch (e) {
      console.log('❌ Erreur:', e.message);
    }

    console.log('\n🧪 TEST 4: Compte des rendez-vous');
    try {
      const { count, error } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log('❌ Échec:', error.message);
      } else {
        console.log('✅ Succès: ' + (count || 0) + ' rendez-vous trouvés');
        if (count > 0) score++;
      }
    } catch (e) {
      console.log('❌ Erreur:', e.message);
    }

    console.log('\n🧪 TEST 5: Jointure tables');
    try {
      const { data, error } = await supabase
        .from('appointments')
        .select('professional_name, appointment_date')
        .limit(3);
      
      if (error) {
        console.log('❌ Échec:', error.message);
      } else {
        console.log('✅ Succès: Jointure fonctionnelle');
        score++;
        if (data.length > 0) {
          console.log('📅 Exemple:', data[0].professional_name + ' - ' + data[0].appointment_date);
        }
      }
    } catch (e) {
      console.log('❌ Erreur:', e.message);
    }

    // Résultats
    console.log('\n📊 RÉSULTATS DE VALIDATION:');
    console.log('Score: ' + score + '/' + maxScore);
    console.log('Pourcentage: ' + Math.round((score / maxScore) * 100) + '%');

    if (score === maxScore) {
      console.log('\n🎉 PARFAIT ! Configuration Supabase 100% fonctionnelle');
      console.log('🚀 L\'intégration peut continuer');
      console.log('\n🔄 Prochaines étapes:');
      console.log('1. Tester: http://localhost:3001/test-appointments-supabase');
      console.log('2. Intégrer dans /appointments');
      console.log('3. Déployer en production');
    } else if (score >= 3) {
      console.log('\n✅ BON ! Configuration partiellement fonctionnelle');
      console.log('⚠️  Quelques ajustements nécessaires');
      console.log('\n🔧 Actions recommandées:');
      console.log('- Vérifier l\'exécution complète du SQL');
      console.log('- Lancer: node setup-supabase-fixed.js');
    } else {
      console.log('\n❌ PROBLÈME ! Configuration incomplète');
      console.log('🔧 Actions requises:');
      console.log('1. Exécuter le SQL dans Supabase');
      console.log('2. Vérifier les permissions');
      console.log('3. Relancer la validation');
    }

  } catch (error) {
    console.error('\n💥 Erreur de validation:', error.message);
    console.log('\n🔧 Vérifiez:');
    console.log('- Connexion Internet');
    console.log('- Clés Supabase valides');
    console.log('- Projet Supabase actif');
  }
}

validateSetup();
