-- Script de création du schéma Supabase pour MindFlow Pro
-- Exécutez ce script dans l'éditeur SQL de Supabase

-- Enable UUID extension
create extension if not exists "uuid-ossp";

-- Create users table with RLS
create table public.users (
  id uuid default uuid_generate_v4() primary key,
  email text unique not null,
  full_name text,
  avatar_url text,
  role text default 'user' check (role in ('user', 'professional', 'admin')),
  preferences jsonb default '{}'::jsonb,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create mood_entries table
create table public.mood_entries (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade not null,
  mood_level integer not null check (mood_level >= 1 and mood_level <= 10),
  energy_level integer check (energy_level >= 1 and energy_level <= 10),
  stress_level integer check (stress_level >= 1 and stress_level <= 10),
  notes text,
  triggers text[],
  activities text[],
  date date not null default current_date,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create journal_entries table
create table public.journal_entries (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade not null,
  title text not null,
  content text not null,
  mood_before integer check (mood_before >= 1 and mood_before <= 10),
  mood_after integer check (mood_after >= 1 and mood_after <= 10),
  tags text[],
  is_private boolean default true,
  ai_insights jsonb default '{}'::jsonb,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create updated_at triggers
create or replace function public.handle_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

create trigger handle_users_updated_at
  before update on public.users
  for each row execute function public.handle_updated_at();

create trigger handle_mood_entries_updated_at
  before update on public.mood_entries
  for each row execute function public.handle_updated_at();

create trigger handle_journal_entries_updated_at
  before update on public.journal_entries
  for each row execute function public.handle_updated_at();

-- Create indexes for better performance
create index mood_entries_user_id_date_idx on public.mood_entries(user_id, date desc);
create index journal_entries_user_id_created_idx on public.journal_entries(user_id, created_at desc);
create index users_email_idx on public.users(email);

-- Enable Row Level Security (RLS)
alter table public.users enable row level security;
alter table public.mood_entries enable row level security;
alter table public.journal_entries enable row level security;

-- Create RLS policies
create policy "Users can view own profile" on public.users
  for select using (auth.uid() = id);

create policy "Users can update own profile" on public.users
  for update using (auth.uid() = id);

-- Mood entries policies
create policy "Users can view own mood entries" on public.mood_entries
  for select using (auth.uid() = user_id);

create policy "Users can insert own mood entries" on public.mood_entries
  for insert with check (auth.uid() = user_id);

create policy "Users can update own mood entries" on public.mood_entries
  for update using (auth.uid() = user_id);

create policy "Users can delete own mood entries" on public.mood_entries
  for delete using (auth.uid() = user_id);

-- Journal entries policies  
create policy "Users can view own journal entries" on public.journal_entries
  for select using (auth.uid() = user_id);

create policy "Users can insert own journal entries" on public.journal_entries
  for insert with check (auth.uid() = user_id);

create policy "Users can update own journal entries" on public.journal_entries
  for update using (auth.uid() = user_id);

create policy "Users can delete own journal entries" on public.journal_entries
  for delete using (auth.uid() = user_id);

-- Success message
select 'Schema Supabase créé avec succès!' as message;
