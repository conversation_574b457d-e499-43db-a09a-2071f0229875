{"buildCommand": "cd frontend && npm run build", "outputDirectory": "frontend/.next", "installCommand": "cd frontend && npm install", "framework": "nextjs", "env": {"NEXT_PUBLIC_SUPABASE_URL": "https://kvdrukmoxetoiojazukf.supabase.co", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ", "NEXT_PUBLIC_USE_SUPABASE_DATABASE": "true", "NEXT_PUBLIC_PRINCIPAL_DATABASE_MODE": "true", "NEXT_PUBLIC_AUTO_SYNC_ENABLED": "true"}}