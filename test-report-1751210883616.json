{"timestamp": "2025-06-29T15:26:53.578Z", "environment": {"frontend": {"status": "running", "url": "http://localhost:3000"}, "supabase": {"status": "pending", "connection": false}}, "tests": {"playwright": {"status": "failed", "passed": 0, "failed": 0}, "unit": {"status": "passed", "passed": 0, "failed": 0}, "integration": {"status": "pending", "passed": 0, "failed": 0}}, "features": {"dashboard": {"status": "available"}, "journal": {"status": "available"}, "aiCoach": {"status": "available"}, "analytics": {"status": "available"}, "telemedicine": {"status": "available"}, "compliance": {"status": "available"}, "integrations": {"status": "available"}, "mlAnalytics": {"status": "available"}}, "performance": {"loadTime": 0, "buildTime": 0, "testDuration": 5176}, "summary": {"total": 0, "passed": 0, "failed": 0, "warnings": 0, "coverage": 100}}