#!/usr/bin/env node

/**
 * EXÉCUTION AUTOMATIQUE MIGRATION PHASE 1
 * Applique le script SQL phase1-migration-complete.sql dans Supabase
 */

const fs = require('fs');
const { createClient } = require('@supabase/supabase-js');

// Configuration Supabase
const SUPABASE_URL = 'https://kvdrukmoxetoiojazukf.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4';

class MigrationExecutor {
    constructor() {
        this.supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
        this.logFile = 'migration-phase1.log';
        this.results = {
            success: false,
            tablesCreated: [],
            recordsInserted: {},
            errors: [],
            startTime: new Date(),
            endTime: null
        };
    }

    log(message) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] ${message}`;
        console.log(logMessage);
        fs.appendFileSync(this.logFile, logMessage + '\n');
    }

    async executeMigration() {
        this.log('🚀 DÉBUT MIGRATION PHASE 1 - 4 HOOKS VERS SUPABASE');
        this.log('====================================================');
        
        try {
            // 1. Lire le fichier SQL
            this.log('📖 Lecture du script SQL...');
            const sqlContent = fs.readFileSync('phase1-migration-complete.sql', 'utf8');
            
            // 2. Diviser le script en requêtes individuelles
            this.log('🔄 Division du script en requêtes...');
            const queries = this.parseSQLQueries(sqlContent);
            this.log(`📊 ${queries.length} requêtes SQL trouvées`);

            // 3. Exécuter les requêtes une par une
            for (let i = 0; i < queries.length; i++) {
                const query = queries[i].trim();
                if (query && !query.startsWith('--') && query !== '') {
                    await this.executeQuery(query, i + 1);
                }
            }

            // 4. Validation finale
            await this.validateMigration();

            this.results.success = true;
            this.results.endTime = new Date();
            
            this.log('✅ MIGRATION TERMINÉE AVEC SUCCÈS !');
            this.log('====================================================');
            
            // 5. Générer le rapport
            await this.generateReport();
            
            return this.results;

        } catch (error) {
            this.log('❌ ERREUR MIGRATION: ' + error.message);
            this.results.errors.push(error.message);
            this.results.endTime = new Date();
            throw error;
        }
    }

    parseSQLQueries(sqlContent) {
        // Retirer les commentaires et diviser par ';'
        const lines = sqlContent.split('\n');
        const cleanedLines = lines.filter(line => 
            !line.trim().startsWith('--') && line.trim() !== ''
        );
        
        const content = cleanedLines.join('\n');
        return content.split(';').filter(query => query.trim() !== '');
    }

    async executeQuery(query, queryNumber) {
        try {
            this.log(`🔄 Exécution requête ${queryNumber}...`);
            
            // Identifier le type de requête
            const queryType = this.identifyQueryType(query);
            this.log(`   Type: ${queryType}`);

            const { data, error } = await this.supabase.rpc('exec_sql', {
                sql_query: query
            });

            if (error) {
                // Si la fonction RPC n'existe pas, essayer l'exécution directe
                if (error.code === '42883') {
                    return await this.executeDirectQuery(query, queryNumber);
                }
                throw error;
            }

            this.log(`✅ Requête ${queryNumber} exécutée avec succès`);
            
            if (queryType.includes('CREATE TABLE')) {
                const tableName = this.extractTableName(query);
                if (tableName) {
                    this.results.tablesCreated.push(tableName);
                    this.log(`   📊 Table créée: ${tableName}`);
                }
            }

            return data;

        } catch (error) {
            this.log(`❌ Erreur requête ${queryNumber}: ${error.message}`);
            // Continue avec les autres requêtes même en cas d'erreur
            this.results.errors.push(`Requête ${queryNumber}: ${error.message}`);
        }
    }

    async executeDirectQuery(query, queryNumber) {
        try {
            this.log(`🔄 Exécution directe requête ${queryNumber}...`);
            
            if (query.trim().toUpperCase().startsWith('CREATE TABLE')) {
                // Pour les CREATE TABLE, utiliser une approche différente
                const { error } = await this.supabase
                    .from('_supabase_migrations')
                    .select('version')
                    .limit(1);
                
                if (error && error.code === '42P01') {
                    this.log('   📝 Exécution manuelle requise pour CREATE TABLE');
                    return null;
                }
            }
            
            // Pour d'autres types de requêtes, essayer l'exécution normale
            const { data, error } = await this.supabase
                .rpc('sql', { query });

            if (error) throw error;

            this.log(`✅ Requête directe ${queryNumber} exécutée`);
            return data;

        } catch (error) {
            this.log(`❌ Erreur exécution directe ${queryNumber}: ${error.message}`);
            this.results.errors.push(`Requête directe ${queryNumber}: ${error.message}`);
        }
    }

    identifyQueryType(query) {
        const upperQuery = query.trim().toUpperCase();
        if (upperQuery.startsWith('CREATE TABLE')) return 'CREATE TABLE';
        if (upperQuery.startsWith('CREATE INDEX')) return 'CREATE INDEX';
        if (upperQuery.startsWith('ALTER TABLE')) return 'ALTER TABLE';
        if (upperQuery.startsWith('CREATE POLICY')) return 'CREATE POLICY';
        if (upperQuery.startsWith('INSERT INTO')) return 'INSERT INTO';
        if (upperQuery.startsWith('SELECT')) return 'SELECT';
        return 'OTHER';
    }

    extractTableName(query) {
        const match = query.match(/CREATE TABLE (?:IF NOT EXISTS )?(\w+)/i);
        return match ? match[1] : null;
    }

    async validateMigration() {
        this.log('🧪 VALIDATION DE LA MIGRATION...');
        
        const tables = ['journal_entries', 'ai_coaching_sessions', 'mood_analytics', 'smart_notifications'];
        
        for (const table of tables) {
            try {
                const { data, error } = await this.supabase
                    .from(table)
                    .select('*', { count: 'exact', head: true });

                if (error) {
                    this.log(`❌ Table ${table} non trouvée: ${error.message}`);
                    this.results.errors.push(`Table ${table} non trouvée`);
                } else {
                    const count = data?.length || 0;
                    this.results.recordsInserted[table] = count;
                    this.log(`✅ Table ${table}: ${count} enregistrements`);
                }
            } catch (error) {
                this.log(`❌ Erreur validation ${table}: ${error.message}`);
            }
        }
    }

    async generateReport() {
        const report = {
            migration: 'Phase 1 - Migration 4 hooks vers Supabase',
            timestamp: new Date().toISOString(),
            duration: this.results.endTime - this.results.startTime,
            status: this.results.success ? 'SUCCÈS' : 'ÉCHEC',
            summary: {
                tablesCreated: this.results.tablesCreated.length,
                totalRecords: Object.values(this.results.recordsInserted).reduce((sum, count) => sum + count, 0),
                errors: this.results.errors.length
            },
            details: {
                tables: this.results.tablesCreated,
                records: this.results.recordsInserted,
                errors: this.results.errors
            },
            nextSteps: [
                'Vérifier les tables dans Supabase Dashboard',
                'Tester la connectivité depuis l\'application',
                'Migrer les hooks frontend vers Supabase',
                'Exécuter les tests de validation'
            ]
        };

        const reportFile = 'migration-phase1-report.json';
        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
        this.log(`📋 Rapport généré: ${reportFile}`);

        // Afficher le résumé
        this.log('');
        this.log('📊 RÉSUMÉ DE LA MIGRATION:');
        this.log(`   Tables créées: ${report.summary.tablesCreated}`);
        this.log(`   Enregistrements insérés: ${report.summary.totalRecords}`);
        this.log(`   Erreurs: ${report.summary.errors}`);
        this.log(`   Durée: ${Math.round(report.duration / 1000)} secondes`);
    }

    async testConnection() {
        try {
            this.log('🔌 Test de connexion Supabase...');
            const { data, error } = await this.supabase
                .from('appointments')
                .select('*')
                .limit(1);

            if (error) throw error;

            this.log('✅ Connexion Supabase validée');
            return true;
        } catch (error) {
            this.log('❌ Erreur connexion Supabase: ' + error.message);
            return false;
        }
    }
}

// Exécution
if (require.main === module) {
    const executor = new MigrationExecutor();
    
    executor.testConnection().then(connected => {
        if (connected) {
            return executor.executeMigration();
        } else {
            throw new Error('Connexion Supabase impossible');
        }
    }).then(results => {
        console.log('\n🎉 MIGRATION COMPLÉTÉE !');
        console.log('Consultez migration-phase1-report.json pour les détails.');
        process.exit(0);
    }).catch(error => {
        console.error('\n❌ ÉCHEC MIGRATION:', error.message);
        process.exit(1);
    });
}

module.exports = MigrationExecutor; 