
#!/usr/bin/env node

/**
 * Script de validation post-déploiement
 */

const https = require('https');

async function validateDeployment(url) {
    console.log('🔍 Validation du déploiement:', url);
    
    const checks = [
        { path: '/', name: '<PERSON> d\'accueil' },
        { path: '/dashboard', name: 'Dashboard' },
        { path: '/appointments', name: '<PERSON><PERSON>-vous' },
        { path: '/journal', name: 'Journal' },
        { path: '/ai-coach', name: 'IA <PERSON>' },
        { path: '/analytics', name: 'Analytics' }
    ];
    
    for (const check of checks) {
        try {
            const response = await fetch(url + check.path);
            const status = response.status === 200 ? '✅' : '❌';
            console.log(`${status} ${check.name}: ${response.status}`);
        } catch (error) {
            console.log(`❌ ${check.name}: Erreur de connexion`);
        }
    }
}

// Usage: node validate-deployment.js https://your-app.vercel.app
const url = process.argv[2];
if (url) {
    validateDeployment(url);
} else {
    console.log('Usage: node validate-deployment.js <URL>');
}
