#!/bin/bash

echo "🧪 TESTS MIGRATION SUPABASE - AUTOMATION COMPLÈTE"
echo "=================================================="
echo ""

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages avec couleurs
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérification des prérequis
log_info "Vérification des prérequis..."

if ! command -v node &> /dev/null; then
    log_error "Node.js n'est pas installé"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    log_error "npm n'est pas installé"
    exit 1
fi

if [ ! -d "frontend" ]; then
    log_error "Dossier frontend introuvable"
    exit 1
fi

log_success "Prérequis validés"

# Étape 1: Correction du conflit de routing
log_info "Étape 1: Correction des conflits de routing..."

if [ -f "frontend/src/pages/index.tsx" ]; then
    log_warning "Suppression de pages/index.tsx (conflit avec App Router)"
    rm -f frontend/src/pages/index.tsx
fi

if [ -d "frontend/src/pages" ] && [ -z "$(ls -A frontend/src/pages)" ]; then
    log_warning "Suppression du dossier pages vide"
    rmdir frontend/src/pages
fi

log_success "Conflits de routing résolus"

# Étape 2: Vérification du fichier .env.local
log_info "Étape 2: Vérification de la configuration..."

if [ ! -f "frontend/.env.local" ]; then
    log_error "Fichier .env.local manquant!"
    log_info "Création du fichier .env.local avec la configuration par défaut..."
    
    cat > frontend/.env.local << 'EOF'
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.A0HBpJZKqQlUQLGnQK5p3FsHxJKqGZHrUHOEh_Bs2gg
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.X_C8yzQ1S_xaDKrQS4mY_UrJOxhwRGLl1sLEVECCFdE

# Feature Flags
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true
NEXT_PUBLIC_MIGRATE_USER_DATA=true
NEXT_PUBLIC_MIGRATE_MOOD_TRACKING=true
NEXT_PUBLIC_MIGRATE_JOURNAL_ENTRIES=true
NEXT_PUBLIC_DEBUG_MIGRATION=true
NEXT_PUBLIC_ENABLE_MIGRATION_LOGS=true

# Development
NODE_ENV=development
NEXT_PUBLIC_NODE_ENV=development
EOF
    
    log_success "Fichier .env.local créé"
else
    log_success "Fichier .env.local détecté"
fi

# Étape 3: Installation des dépendances
log_info "Étape 3: Installation des dépendances..."

cd frontend
if [ ! -d "node_modules" ]; then
    log_info "Installation des dépendances npm..."
    npm install
    if [ $? -ne 0 ]; then
        log_error "Échec de l'installation des dépendances"
        exit 1
    fi
    log_success "Dépendances installées"
else
    log_success "Dépendances déjà installées"
fi

# Étape 4: Build de test
log_info "Étape 4: Test de compilation..."

npm run build > /tmp/build.log 2>&1
if [ $? -eq 0 ]; then
    log_success "Compilation réussie"
else
    log_error "Échec de la compilation"
    log_info "Dernières lignes du log de build:"
    tail -10 /tmp/build.log
    log_warning "Continuation malgré l'échec de build pour tester en mode dev"
fi

cd ..

# Étape 5: Démarrage du serveur Next.js
log_info "Étape 5: Démarrage du serveur Next.js..."

cd frontend
npm run dev > /tmp/nextjs.log 2>&1 &
NEXTJS_PID=$!

log_info "Serveur Next.js démarré (PID: $NEXTJS_PID)"
log_info "Attente du démarrage du serveur..."

# Attendre que le serveur soit prêt
for i in {1..30}; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        log_success "Serveur Next.js prêt sur http://localhost:3000"
        break
    fi
    if [ $i -eq 30 ]; then
        log_error "Timeout: le serveur n'a pas démarré dans les 30 secondes"
        kill $NEXTJS_PID 2>/dev/null
        exit 1
    fi
    sleep 1
done

cd ..

# Étape 6: Exécution des tests Playwright
log_info "Étape 6: Exécution des tests Playwright..."

# Installer Playwright si nécessaire
if [ ! -d "node_modules/@playwright" ]; then
    log_info "Installation de Playwright..."
    npm install @playwright/test
    npx playwright install
fi

# Créer le dossier de résultats
mkdir -p test-results

# Lancer les tests Playwright
log_info "Lancement des tests de migration..."

npx playwright test tests/supabase-migration.spec.ts --reporter=html --output-dir=test-results/playwright

PLAYWRIGHT_EXIT_CODE=$?

# Étape 7: Tests Node.js personnalisés
log_info "Étape 7: Tests Node.js personnalisés..."

if [ -f "test-supabase-migration.js" ]; then
    node test-supabase-migration.js
    NODEJS_EXIT_CODE=$?
else
    log_warning "Script de test Node.js introuvable"
    NODEJS_EXIT_CODE=1
fi

# Étape 8: Nettoyage
log_info "Étape 8: Nettoyage..."

log_info "Arrêt du serveur Next.js..."
kill $NEXTJS_PID 2>/dev/null
wait $NEXTJS_PID 2>/dev/null

# Étape 9: Rapport final
log_info "Étape 9: Génération du rapport final..."

echo ""
echo "📊 RÉSUMÉ DES TESTS"
echo "=================="

if [ $PLAYWRIGHT_EXIT_CODE -eq 0 ]; then
    log_success "Tests Playwright: RÉUSSIS"
else
    log_error "Tests Playwright: ÉCHEC"
fi

if [ $NODEJS_EXIT_CODE -eq 0 ]; then
    log_success "Tests Node.js: RÉUSSIS"
else
    log_error "Tests Node.js: ÉCHEC"
fi

echo ""
log_info "Fichiers de résultats générés:"
echo "  - test-results/playwright/ (rapport HTML Playwright)"
echo "  - test-results/migration-report.json (rapport JSON détaillé)"
echo "  - /tmp/nextjs.log (logs du serveur Next.js)"
echo "  - /tmp/build.log (logs de compilation)"

echo ""
if [ $PLAYWRIGHT_EXIT_CODE -eq 0 ] && [ $NODEJS_EXIT_CODE -eq 0 ]; then
    log_success "🎉 TOUS LES TESTS SONT RÉUSSIS!"
    echo ""
    log_info "La migration Supabase est prête. Vous pouvez procéder aux étapes suivantes:"
    echo "  1. Activer progressivement les feature flags"
    echo "  2. Migrer les données utilisateur"
    echo "  3. Basculer en mode Supabase complet"
    exit 0
else
    log_error "❌ CERTAINS TESTS ONT ÉCHOUÉ"
    echo ""
    log_info "Consultez les rapports de tests pour plus de détails."
    echo "Corrigez les erreurs avant de continuer la migration."
    exit 1
fi 