#!/usr/bin/env node

const fs = require('fs');

console.log('🚀 MIGRATION PHASE 1 - MINDFLOW PRO');
console.log('====================================');
console.log('');

// Vérifier que le fichier SQL existe
if (!fs.existsSync('phase1-migration-complete.sql')) {
    console.log('❌ Fichier phase1-migration-complete.sql non trouvé');
    process.exit(1);
}

console.log('✅ Script SQL trouvé: phase1-migration-complete.sql');
console.log('');

// Instructions pour l'utilisateur
console.log('📋 INSTRUCTIONS MIGRATION:');
console.log('');
console.log('1. Ouvrez Supabase Dashboard:');
console.log('   https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf');
console.log('');
console.log('2. Allez dans "SQL Editor"');
console.log('');
console.log('3. Copiez et exécutez le contenu de phase1-migration-complete.sql');
console.log('');
console.log('4. Vérifiez que les 4 tables ont été créées:');
console.log('   - journal_entries');
console.log('   - ai_coaching_sessions');
console.log('   - mood_analytics');
console.log('   - smart_notifications');
console.log('');

// Afficher le contenu du fichier SQL pour référence
const sqlContent = fs.readFileSync('phase1-migration-complete.sql', 'utf8');
const lines = sqlContent.split('\n').length;
const tablesCount = (sqlContent.match(/CREATE TABLE/g) || []).length;
const insertsCount = (sqlContent.match(/INSERT INTO/g) || []).length;

console.log('📊 DÉTAILS DU SCRIPT:');
console.log(`   Lignes de code: ${lines}`);
console.log(`   Tables à créer: ${tablesCount}`);
console.log(`   Données d'exemple: ${insertsCount} insertions`);
console.log('');

console.log('⚡ PRÊT POUR LA MIGRATION !');
console.log('Copiez le contenu du fichier SQL et exécutez-le dans Supabase.');
console.log('');

// Créer un script de validation
const validationScript = `
-- Script de validation migration Phase 1
SELECT 'VALIDATION MIGRATION PHASE 1' as status;

-- Vérifier les tables créées
SELECT schemaname, tablename 
FROM pg_tables 
WHERE tablename IN ('journal_entries', 'ai_coaching_sessions', 'mood_analytics', 'smart_notifications');

-- Compter les enregistrements
SELECT 'journal_entries' as table_name, COUNT(*) as records FROM journal_entries
UNION ALL SELECT 'ai_coaching_sessions', COUNT(*) FROM ai_coaching_sessions  
UNION ALL SELECT 'mood_analytics', COUNT(*) FROM mood_analytics
UNION ALL SELECT 'smart_notifications', COUNT(*) FROM smart_notifications;

-- Vérifier les index
SELECT indexname, tablename FROM pg_indexes 
WHERE tablename IN ('journal_entries', 'ai_coaching_sessions', 'mood_analytics', 'smart_notifications');
`;

fs.writeFileSync('validation-phase1.sql', validationScript);
console.log('📝 Script de validation créé: validation-phase1.sql');
console.log('   Exécutez ce script après la migration pour valider le résultat.'); 