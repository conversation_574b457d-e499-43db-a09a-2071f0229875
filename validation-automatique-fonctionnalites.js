#!/usr/bin/env node

/**
 * 🧪 VALIDATION AUTOMATIQUE DES FONCTIONNALITÉS - MINDFLOW PRO
 * Script de tests automatisés pour valider l'ensemble des fonctionnalités
 */

const http = require('http');
const fs = require('fs');

class ValidationMindFlowPro {
    constructor() {
        this.baseUrl = 'http://localhost:3000';
        this.testResults = [];
        this.startTime = Date.now();
    }

    log(message, type = 'INFO') {
        const timestamp = new Date().toISOString();
        const colors = {
            'INFO': '\x1b[36m',
            'SUCCESS': '\x1b[32m',
            'ERROR': '\x1b[31m',
            'WARNING': '\x1b[33m'
        };
        
        console.log(`${colors[type]}[${timestamp}] ${type}: ${message}\x1b[0m`);
    }

    async makeRequest(endpoint) {
        return new Promise((resolve, reject) => {
            const req = http.get(`http://localhost:3000${endpoint}`, (res) => {
                resolve({ statusCode: res.statusCode, headers: res.headers });
            });
            req.on('error', reject);
            req.setTimeout(5000, () => {
                req.destroy();
                reject(new Error('Timeout'));
            });
        });
    }

    async testEndpoint(name, endpoint, expectedStatus = 200) {
        try {
            const startTime = Date.now();
            const response = await this.makeRequest(endpoint);
            const duration = Date.now() - startTime;
            
            const success = response.statusCode === expectedStatus;
            
            this.testResults.push({
                name, endpoint, expectedStatus,
                actualStatus: response.statusCode,
                duration, success,
                timestamp: new Date().toISOString()
            });

            if (success) {
                this.log(`✅ ${name} - ${endpoint} (${duration}ms)`, 'SUCCESS');
            } else {
                this.log(`❌ ${name} - Expected ${expectedStatus}, got ${response.statusCode}`, 'ERROR');
            }

            return success;
        } catch (error) {
            this.log(`❌ ${name} - Error: ${error.message}`, 'ERROR');
            return false;
        }
    }

    async validateCore() {
        this.log('🔍 VALIDATION FONCTIONNALITÉS PRINCIPALES', 'INFO');
        
        const tests = [
            ['Page Accueil', '/'],
            ['Dashboard Principal', '/dashboard'],
            ['Journal Utilisateur', '/journal'],
            ['IA Coach', '/ai-coach'],
            ['Analytics', '/analytics'],
            ['Rendez-vous', '/appointments'],
            ['Professionnels', '/professionals'],
            ['Conformité', '/compliance'],
            ['Intégrations B2B', '/integrations-b2b'],
            ['Télémédecine', '/telemedicine-advanced'],
            ['Tests Modules', '/test-healthcare-modules'],
            ['Monitoring', '/monitoring-dashboard']
        ];

        let successCount = 0;
        for (const [name, endpoint] of tests) {
            if (await this.testEndpoint(name, endpoint)) {
                successCount++;
            }
        }

        return { total: tests.length, success: successCount };
    }

    async runValidation() {
        this.log('🚀 DÉMARRAGE VALIDATION MINDFLOW PRO', 'INFO');

        const results = await this.validateCore();
        const totalDuration = Date.now() - this.startTime;
        const successRate = ((results.success / results.total) * 100).toFixed(1);

        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalTests: results.total,
                successfulTests: results.success,
                failedTests: results.total - results.success,
                successRate: `${successRate}%`,
                totalDuration: `${totalDuration}ms`
            },
            results: this.testResults
        };

        fs.writeFileSync('validation-report.json', JSON.stringify(report, null, 2));

        console.log('\n' + '='.repeat(60));
        console.log('📊 RAPPORT DE VALIDATION MINDFLOW PRO');
        console.log('='.repeat(60));
        console.log(`Tests Total: ${results.total}`);
        console.log(`✅ Réussis: ${results.success}`);
        console.log(`❌ Échecs: ${results.total - results.success}`);
        console.log(`📊 Taux Succès: ${successRate}%`);
        console.log(`⏱️ Durée: ${totalDuration}ms`);
        console.log('='.repeat(60));

        if (successRate === '100.0') {
            console.log('🎉 VALIDATION COMPLÈTE RÉUSSIE !');
        } else {
            console.log(`⚠️ ${results.total - results.success} corrections nécessaires`);
        }

        return report;
    }
}

if (require.main === module) {
    const validator = new ValidationMindFlowPro();
    validator.runValidation().catch(console.error);
}
