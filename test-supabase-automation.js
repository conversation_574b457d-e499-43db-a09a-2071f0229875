#!/usr/bin/env node

/**
 * 🧪 SCRIPT DE TEST - AUTOMATISATION SUPABASE
 * Lance tous les tests et valide l'intégration complète
 */

const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

console.log('🧪 LANCEMENT DES TESTS D\'AUTOMATISATION SUPABASE');
console.log('=================================================\n');

async function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  console.log(`🔧 Commande: ${command}\n`);
  
  try {
    const { stdout, stderr } = await execAsync(command, { 
      cwd: process.cwd(),
      timeout: 60000 // 60 secondes max
    });
    
    if (stdout) {
      console.log('📤 SORTIE:');
      console.log(stdout);
    }
    
    if (stderr) {
      console.log('⚠️ ERREURS/AVERTISSEMENTS:');
      console.log(stderr);
    }
    
    console.log('✅ Commande terminée avec succès\n');
    return true;
  } catch (error) {
    console.log(`❌ Erreur: ${error.message}\n`);
    return false;
  }
}

async function testServerRunning() {
  console.log('🔍 Vérification du serveur de développement...');
  
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('✅ Serveur Next.js en cours d\'exécution');
      return true;
    }
  } catch (error) {
    console.log('❌ Serveur Next.js non accessible');
    console.log('ℹ️ Assurez-vous que "npm run dev" est en cours');
    return false;
  }
}

async function main() {
  console.log('🚀 Démarrage des tests automatisés...\n');
  
  const tests = [
    {
      name: 'Installation des dépendances',
      command: 'cd frontend && npm install @supabase/supabase-js',
      required: true
    },
    {
      name: 'Exécution du script d\'automatisation principal',
      command: 'cd frontend && node setup-appointments-supabase-auto.js',
      required: true
    },
    {
      name: 'Test de l\'API Supabase',
      command: 'cd frontend && node -e "const {createClient} = require(\'@supabase/supabase-js\'); const supabase = createClient(\'https://kvdrukmoxetoiojazukf.supabase.co\', \'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ\'); supabase.from(\'professionals\').select(\'count\').then(r => console.log(\'API OK:\', !r.error))"',
      required: false
    },
    {
      name: 'Validation TypeScript',
      command: 'cd frontend && npx tsc --noEmit --project .',
      required: false
    }
  ];
  
  let successCount = 0;
  let totalRequired = tests.filter(t => t.required).length;
  
  for (const test of tests) {
    const success = await runCommand(test.command, test.name);
    
    if (success) {
      successCount++;
    } else if (test.required) {
      console.log(`❌ Test critique échoué: ${test.name}`);
      console.log('🛑 Arrêt des tests\n');
      break;
    }
    
    console.log('─'.repeat(60));
  }
  
  // Test du serveur si disponible
  const serverRunning = await testServerRunning();
  
  console.log('\n📊 RÉSUMÉ DES TESTS:');
  console.log('═'.repeat(40));
  console.log(`✅ Tests réussis: ${successCount}/${tests.length}`);
  console.log(`🖥️ Serveur Next.js: ${serverRunning ? 'En cours' : 'Arrêté'}`);
  
  if (successCount >= totalRequired) {
    console.log('\n🎉 TESTS RÉUSSIS !');
    console.log('📋 Actions suivantes:');
    console.log('1. Si le serveur n\'est pas lancé: npm run dev');
    console.log('2. Tester: http://localhost:3000/test-appointments-supabase');
    console.log('3. Vérifier les données dans Supabase Dashboard');
    console.log('4. Intégrer dans la page /appointments');
  } else {
    console.log('\n❌ CERTAINS TESTS ONT ÉCHOUÉ');
    console.log('🔧 Solutions:');
    console.log('1. Vérifier la connexion Internet');
    console.log('2. Vérifier les clés Supabase dans .env');
    console.log('3. Créer manuellement les tables dans Supabase');
    console.log('4. Relancer les tests');
  }
  
  console.log(`\n🔗 Liens utiles:`);
  console.log(`📊 Supabase Dashboard: https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf`);
  console.log(`🧪 Page de test: http://localhost:3000/test-appointments-supabase`);
  console.log(`📋 Éditeur SQL: https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf/sql`);
}

if (require.main === module) {
  main();
}

module.exports = { main, runCommand, testServerRunning }; 