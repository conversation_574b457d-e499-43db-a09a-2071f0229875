-- 🛠️ CORRECTION IMMÉDIATE SUPABASE - MINDFLOW PRO
-- 📅 Script de correction pour l'erreur "specialization does not exist"  
-- 🎯 À exécuter directement dans l'éditeur SQL Supabase

-- =====================================================
-- ÉTAPE 1: SUPPRESSION DES TABLES POUR RECRÉATION
-- =====================================================

DROP TABLE IF EXISTS appointments CASCADE;
DROP TABLE IF EXISTS professionals CASCADE;

-- =====================================================
-- ÉTAPE 2: CRÉATION DES TABLES CORRIGÉES
-- =====================================================

-- Table: Professionals (CORRIGÉE avec specialties)
CREATE TABLE IF NOT EXISTS professionals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    specialties TEXT[] DEFAULT '{}', -- ✅ CORRECTION: specialties (pluriel, array)
    experience_years INTEGER DEFAULT 0,
    rating DECIMAL(2,1) DEFAULT 0.0,
    hourly_rate DECIMAL(10,2) DEFAULT 0.00,
    availability_status TEXT DEFAULT 'available',
    profile_image_url TEXT,
    bio TEXT,
    languages TEXT[] DEFAULT '{}',
    location TEXT,
    phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: Appointments
CREATE TABLE IF NOT EXISTS appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    professional_id UUID REFERENCES professionals(id) ON DELETE CASCADE,
    client_name TEXT NOT NULL,
    client_email TEXT NOT NULL,
    appointment_date TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    type TEXT CHECK (type IN ('video', 'in-person', 'chat')) DEFAULT 'video',
    status TEXT CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no-show')) DEFAULT 'scheduled',
    notes TEXT,
    price DECIMAL(10,2) DEFAULT 0.00,
    currency TEXT DEFAULT 'EUR',
    meeting_link TEXT,
    cancellation_reason TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- ÉTAPE 3: INDEX ET OPTIMISATIONS
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_appointments_professional ON appointments(professional_id);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);

-- =====================================================
-- ÉTAPE 4: TRIGGERS AUTOMATIQUES
-- =====================================================

-- Fonction de mise à jour automatique
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers
CREATE TRIGGER update_professionals_updated_at 
    BEFORE UPDATE ON professionals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at 
    BEFORE UPDATE ON appointments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ÉTAPE 5: DONNÉES DE TEST CORRIGÉES (AVEC ARRAY)
-- =====================================================

-- Professionnels avec specialties en format ARRAY
INSERT INTO professionals (name, email, specialties, experience_years, rating, hourly_rate, availability_status, bio, location) 
VALUES 
('Dr. Sophie Martin', '<EMAIL>', 
 ARRAY['Psychologue clinicienne', 'TCC', 'Gestion du stress'], 
 8, 4.8, 80.00, 'available', 
 'Psychologue clinicienne spécialisée en TCC.',
 'Paris 15ème'),

('Dr. Jean Dupont', '<EMAIL>', 
 ARRAY['Psychiatre', 'Troubles anxieux', 'Dépression'], 
 12, 4.9, 120.00, 'available',
 'Psychiatre expérimenté.',
 'Lyon 6ème'),

('Marie Leblanc', '<EMAIL>', 
 ARRAY['Thérapeute TCC', 'Phobies', 'Addictions'], 
 6, 4.7, 70.00, 'busy',
 'Thérapeute spécialisée en TCC.',
 'Marseille 8ème'),

('Dr. Ahmed Benali', '<EMAIL>', 
 ARRAY['Psychanalyste', 'Thérapie de couple'], 
 15, 4.9, 100.00, 'available',
 'Psychanalyste moderne.',
 'Bordeaux Centre')
ON CONFLICT (email) DO NOTHING;

-- Rendez-vous de test
INSERT INTO appointments (professional_id, client_name, client_email, appointment_date, duration_minutes, type, status, notes, price, currency)
SELECT 
    p.id, 'Alice Dubois', '<EMAIL>',
    '2024-12-30 14:00:00+00'::timestamp with time zone,
    60, 'video', 'scheduled', 'Première consultation', 80.00, 'EUR'
FROM professionals p WHERE p.email = '<EMAIL>' LIMIT 1;

INSERT INTO appointments (professional_id, client_name, client_email, appointment_date, duration_minutes, type, status, notes, price, currency)
SELECT 
    p.id, 'Pierre Martin', '<EMAIL>',
    '2024-12-30 10:30:00+00'::timestamp with time zone,
    45, 'in-person', 'confirmed', 'Suivi psychiatrique', 120.00, 'EUR'
FROM professionals p WHERE p.email = '<EMAIL>' LIMIT 1;

-- =====================================================
-- ÉTAPE 6: PERMISSIONS POUR TESTS
-- =====================================================

ALTER TABLE professionals ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public access professionals" ON professionals FOR ALL USING (true);
CREATE POLICY "Public access appointments" ON appointments FOR ALL USING (true);

-- =====================================================
-- ÉTAPE 7: VALIDATION
-- =====================================================

SELECT 'professionals' as table_name, COUNT(*) as records FROM professionals
UNION ALL
SELECT 'appointments' as table_name, COUNT(*) as records FROM appointments;

-- ✅ CORRECTION TERMINÉE - specialization -> specialties (array)
