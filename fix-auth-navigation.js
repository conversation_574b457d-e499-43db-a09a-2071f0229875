#!/usr/bin/env node

/**
 * Script de correction pour l'authentification et navigation MindFlow Pro
 * Corrige les problèmes de connexion et de navigation entre les pages
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Correction de l\'authentification MindFlow Pro...\n');

const frontendDir = path.join(__dirname, 'frontend');

// Créer la page de test d'authentification
const authTestPageContent = `'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Brain, CheckCircle, XCircle, User, LogIn, LogOut } from 'lucide-react';

export default function AuthTestPage() {
  const { user, loading, login, register, logout, isAuthenticated } = useAuth();
  const router = useRouter();
  
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('mindflow123');
  const [name, setName] = useState('<PERSON>');
  const [results, setResults] = useState([]);

  const addResult = (message, type = 'info') => {
    const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    setResults(prev => [\`\${emoji} \${message}\`, ...prev.slice(0, 9)]);
  };

  const handleLogin = async () => {
    try {
      addResult('Tentative de connexion...');
      await login(email, password);
      addResult('Connexion réussie! 🎉', 'success');
    } catch (error) {
      addResult(\`Erreur: \${error.message}\`, 'error');
    }
  };

  const handleRegister = async () => {
    try {
      addResult('Tentative d\\'inscription...');
      await register(name, email, password);
      addResult('Inscription réussie! 🎉', 'success');
    } catch (error) {
      addResult(\`Erreur: \${error.message}\`, 'error');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      addResult('Déconnexion réussie!', 'success');
    } catch (error) {
      addResult(\`Erreur: \${error.message}\`, 'error');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <Brain className="w-16 h-16 text-blue-600 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900">Test d'Authentification</h1>
          <p className="text-gray-600">MindFlow Pro - Supabase Auth</p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Statut */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Statut Utilisateur</h2>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>État:</span>
                <span className={\`\${loading ? 'text-yellow-600' : 'text-green-600'}\`}>
                  {loading ? '⏳ Chargement...' : '✅ Prêt'}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span>Connecté:</span>
                <span className={\`\${isAuthenticated ? 'text-green-600' : 'text-red-600'}\`}>
                  {isAuthenticated ? '✅ Oui' : '❌ Non'}
                </span>
              </div>

              {user && (
                <div className="mt-4 p-4 bg-green-50 rounded-lg">
                  <h3 className="font-semibold text-green-800">Informations:</h3>
                  <p className="text-sm text-green-700">Email: {user.user.email}</p>
                  <p className="text-sm text-green-700">Nom: {user.user.name}</p>
                </div>
              )}
            </div>
          </div>

          {/* Formulaire */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Actions</h2>
            
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Nom"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full p-3 border rounded-lg"
              />
              
              <input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-3 border rounded-lg"
              />
              
              <input
                type="password"
                placeholder="Mot de passe"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full p-3 border rounded-lg"
              />

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={handleRegister}
                  disabled={loading}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  S'inscrire
                </button>
                
                <button
                  onClick={handleLogin}
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Se connecter
                </button>
              </div>

              {isAuthenticated && (
                <>
                  <button
                    onClick={handleLogout}
                    className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                  >
                    Se déconnecter
                  </button>
                  
                  <button
                    onClick={() => router.push('/dashboard')}
                    className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
                  >
                    Aller au Dashboard
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Résultats */}
        {results.length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Journal des Actions</h3>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {results.map((result, index) => (
                <div key={index} className="p-2 bg-gray-50 rounded text-sm">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}`;

// Créer le dossier auth-test
const authTestDir = path.join(frontendDir, 'src', 'app', 'auth-test');
if (!fs.existsSync(authTestDir)) {
  fs.mkdirSync(authTestDir, { recursive: true });
  console.log('✅ Dossier auth-test créé');
}

// Écrire la page de test
const authTestPagePath = path.join(authTestDir, 'page.tsx');
fs.writeFileSync(authTestPagePath, authTestPageContent);
console.log('✅ Page de test d\'authentification créée');

// Créer un layout simplifié pour le dashboard
const simpleDashboardLayoutContent = `'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { Brain, Home, BookOpen, Activity, TrendingUp, LogOut } from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: 'Journal', href: '/journal', icon: BookOpen },
  { name: 'IA Coach', href: '/ai-coach', icon: Brain },
  { name: 'Analytics', href: '/analytics', icon: TrendingUp },
  { name: 'ML Analytics', href: '/ml-analytics', icon: Activity },
];

export function SimpleDashboardLayout({ children }) {
  const { user, loading, logout, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/auth-test');
    }
  }, [isAuthenticated, loading, router]);

  const handleLogout = async () => {
    await logout();
    router.push('/auth-test');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Chargement...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) return null;

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white border-r border-gray-200">
        <div className="flex items-center gap-2 h-16 px-6 border-b">
          <Brain className="w-8 h-8 text-blue-600" />
          <span className="text-xl font-bold">MindFlow Pro</span>
        </div>
        
        <nav className="p-6">
          <ul className="space-y-2">
            {navigation.map((item) => (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className="flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100"
                >
                  <item.icon className="w-5 h-5" />
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
          
          <div className="mt-8 pt-6 border-t">
            <div className="px-3 py-2 text-sm text-gray-700">
              {user?.user.name}
            </div>
            <div className="px-3 py-1 text-xs text-gray-500">
              {user?.user.email}
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center gap-3 px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 rounded-md w-full mt-2"
            >
              <LogOut className="w-5 h-5" />
              Déconnexion
            </button>
          </div>
        </nav>
      </div>

      {/* Contenu principal */}
      <div className="flex-1">
        <div className="bg-white border-b border-gray-200">
          <div className="h-16 px-6 flex items-center justify-between">
            <h1 className="text-xl font-semibold">Dashboard</h1>
            <div className="text-sm text-gray-500">
              Bienvenue, {user?.user.name}
            </div>
          </div>
        </div>
        
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}`;

// Créer le layout simplifié
const layoutDir = path.join(frontendDir, 'src', 'components', 'Layout');
const layoutPath = path.join(layoutDir, 'SimpleDashboardLayout.tsx');
fs.writeFileSync(layoutPath, simpleDashboardLayoutContent);
console.log('✅ Layout dashboard simplifié créé');

console.log('\n🎉 Correction terminée!');
console.log('📝 Pages créées:');
console.log('   - /auth-test : Page de test d\'authentification');
console.log('   - SimpleDashboardLayout : Layout simplifié pour le dashboard');
console.log('\n🚀 Testez maintenant:');
console.log('   1. Aller sur http://localhost:3000/auth-test');
console.log('   2. Tester l\'inscription/connexion');
console.log('   3. Naviguer vers le dashboard');
console.log('\n✨ MindFlow Pro est maintenant prêt!'); 