# 🚀 Guide de Migration Supabase - MindFlow Pro

## 📊 État Actuel
✅ **Architecture Supabase** : 100% complète  
✅ **Composants UI** : Corrigés et fonctionnels  
✅ **Stores et Providers** : Opérationnels  
✅ **Migration System** : Prêt  

## 🎯 MIGRATION IMMÉDIATE - 10 MINUTES

### Étape 1: <PERSON><PERSON>er le Projet Supabase (2 min)
1. **Ouvrir** : https://supabase.com
2. **Cliquer** : "New project"
3. **Nom** : `mindflow-pro`
4. **Password** : Générer un mot de passe fort
5. **Région** : West EU (Ireland)
6. **Cliquer** : "Create new project"

### Étape 2: Récupérer les Clés (1 min)
1. **Aller à** : Settings > API
2. **Copier** :
   - Project URL : `https://xxx.supabase.co`
   - anon public key
   - service_role key (secret)

### Étape 3: Configuration Locale (2 min)
```bash
# Créer frontend/.env.local avec:
NEXT_PUBLIC_SUPABASE_URL=https://votre-projet.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ...
SUPABASE_SERVICE_ROLE_KEY=eyJ...

# Feature Flags - Commencer par:
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true
```

### Étape 4: Initialiser le Schéma (2 min)
1. **Supabase** > SQL Editor > New Query
2. **Copier le contenu de** `supabase-schema.sql`
3. **Exécuter** le script

### Étape 5: Configurer Authentication (1 min)
1. **Authentication** > URL Configuration
2. **Site URL** : `http://localhost:3000`
3. **Redirect URLs** : `http://localhost:3000/auth/callback`

### Étape 6: Test de Connexion (2 min)
```bash
cd frontend
npm run dev
# Vérifier dans la console : Connexion Supabase OK
```

## 🔄 Migration Progressive

### Phase 1: Mode Dual (Parallèle)
```bash
# Dans .env.local:
NEXT_PUBLIC_DUAL_DATABASE_MODE=true
NEXT_PUBLIC_MIGRATE_USER_DATA=true
```

### Phase 2: Migration Graduelle
```bash
# Activer progressivement:
NEXT_PUBLIC_MIGRATE_MOOD_TRACKING=true
NEXT_PUBLIC_MIGRATE_JOURNAL_ENTRIES=true
```

### Phase 3: Basculement Final
```bash
# Quand tout fonctionne:
NEXT_PUBLIC_USE_SUPABASE_DATABASE=true
NEXT_PUBLIC_USE_SUPABASE_AUTH=true
NEXT_PUBLIC_ENABLE_REAL_TIME=true
```

## 🛠️ Scripts Disponibles

### Configuration Interactive
```bash
node setup-supabase.js
```

### Validation Système
```bash
node validate-mindflow-system.js
```

### Tests de Migration
```bash
cd frontend
npm test -- tests/supabase-migration.spec.ts
```

## 📁 Fichiers Clés

- `frontend/src/lib/supabase/client.ts` - Client Supabase
- `frontend/src/lib/database/supabase-adapter.ts` - Adaptateur DB
- `frontend/src/lib/migration/data-migrator.ts` - Migration service
- `supabase-schema.sql` - Schéma complet
- `frontend/src/lib/config/feature-flags.ts` - Feature flags

## 🚨 Points d'Attention

1. **Ne pas activer USE_SUPABASE_AUTH** tant que les données ne sont pas migrées
2. **Tester chaque feature flag** individuellement
3. **Garder DUAL_DATABASE_MODE=true** pendant la transition
4. **Surveiller les logs** pour détecter les erreurs

## ✅ Checklist de Migration

- [ ] Projet Supabase créé
- [ ] Variables d'environnement configurées
- [ ] Schéma SQL exécuté
- [ ] Authentication URLs configurées
- [ ] Mode dual activé
- [ ] Tests passent
- [ ] Migration des utilisateurs
- [ ] Migration mood tracking
- [ ] Migration journal entries
- [ ] Basculement auth Supabase
- [ ] Activation temps réel

## 🎯 Status: PRÊT POUR MIGRATION IMMÉDIATE! 