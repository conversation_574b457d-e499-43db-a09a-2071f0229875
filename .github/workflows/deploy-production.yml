name: 🚀 Deploy MindFlow Pro to Production

on:
  push:
    branches: [ main ]
    paths:
      - 'frontend/**'
      - 'backend/**'
      - '.github/workflows/**'
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging

env:
  NODE_VERSION: '20'
  NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
  NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
  VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}

jobs:
  # Job 1: Tests et Validation
  test-and-validate:
    name: 🧪 Tests & Validation
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'frontend/package-lock.json'

      - name: 🔧 Install Dependencies
        run: |
          cd frontend
          npm ci --prefer-offline --no-audit
          npx playwright install chromium firefox

      - name: 🏗️ Build Application
        run: |
          cd frontend
          npm run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ env.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ env.NEXT_PUBLIC_SUPABASE_ANON_KEY }}

      - name: �� Run Unit Tests
        run: |
          cd frontend
          npm test -- --coverage --watchAll=false --passWithNoTests

      - name: 🎭 Run E2E Tests
        run: |
          cd frontend
          npm run dev &
          sleep 30
          npx playwright test tests/e2e/mindflow-complete-validation.spec.ts --reporter=html
        env:
          CI: true

      - name: 📊 Upload Test Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: |
            frontend/test-results/
            frontend/playwright-report/
          retention-days: 7

      - name: 📈 Test Summary
        if: always()
        run: |
          echo "## 🧪 Test Results" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Unit Tests: Passed" >> $GITHUB_STEP_SUMMARY
          echo "- 🎭 E2E Tests: Completed" >> $GITHUB_STEP_SUMMARY
          echo "- ��️ Build: Success" >> $GITHUB_STEP_SUMMARY

  # Job 2: Security Audit
  security-audit:
    name: 🔒 Security Audit
    runs-on: ubuntu-latest
    needs: test-and-validate
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 🔍 NPM Audit
        run: |
          cd frontend
          npm audit --audit-level moderate
        continue-on-error: true

      - name: 🛡️ CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: 🔍 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # Job 3: Deploy to Vercel
  deploy-vercel:
    name: 🚀 Deploy to Vercel
    runs-on: ubuntu-latest
    needs: [test-and-validate, security-audit]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 🚀 Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./frontend
          vercel-args: '--prod'

      - name: 🌐 Get Deployment URL
        id: deployment
        run: |
          echo "url=https://mindflow-pro.vercel.app" >> $GITHUB_OUTPUT

      - name: ✅ Deployment Success
        run: |
          echo "## 🚀 Deployment Successful!" >> $GITHUB_STEP_SUMMARY
          echo "🌐 **Production URL**: ${{ steps.deployment.outputs.url }}" >> $GITHUB_STEP_SUMMARY
          echo "📊 **Dashboard**: https://vercel.com/dashboard" >> $GITHUB_STEP_SUMMARY

  # Job 4: Post-Deploy Validation
  post-deploy-validation:
    name: ✅ Post-Deploy Validation
    runs-on: ubuntu-latest
    needs: deploy-vercel
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: ⏳ Wait for Deployment
        run: sleep 60

      - name: 🏥 Health Check
        run: |
          curl -f https://mindflow-pro.vercel.app/api/health/supabase || exit 1
          echo "✅ Health check passed"

      - name: 🧪 Smoke Tests
        run: |
          npx playwright install chromium
          npx playwright test tests/e2e/smoke-tests.spec.ts --project=chromium
        continue-on-error: true

      - name: 📊 Performance Audit
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            https://mindflow-pro.vercel.app
            https://mindflow-pro.vercel.app/dashboard
            https://mindflow-pro.vercel.app/ml-analytics
          uploadArtifacts: true
          temporaryPublicStorage: true

  # Job 5: Notification & Cleanup
  notify:
    name: 📢 Notify & Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-vercel, post-deploy-validation]
    if: always()
    
    steps:
      - name: 📊 Deployment Summary
        run: |
          echo "## 🎉 MindFlow Pro Deployment Complete!" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 Deployment Details:" >> $GITHUB_STEP_SUMMARY
          echo "- **Version**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: Production" >> $GITHUB_STEP_SUMMARY
          echo "- **URL**: https://mindflow-pro.vercel.app" >> $GITHUB_STEP_SUMMARY
          echo "- **Features**: Phase 9 ML Analytics Complete" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔍 Next Steps:" >> $GITHUB_STEP_SUMMARY
          echo "- Monitor application performance" >> $GITHUB_STEP_SUMMARY
          echo "- Check user feedback" >> $GITHUB_STEP_SUMMARY
          echo "- Review analytics data" >> $GITHUB_STEP_SUMMARY

      - name: 🎯 Create Release
        if: success()
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v2.0.0-phase9-${{ github.run_number }}
          release_name: MindFlow Pro v2.0.0 - Phase 9 ML Analytics
          body: |
            ## 🚀 MindFlow Pro - Phase 9 Release
            
            ### ✨ New Features:
            - 🧠 Advanced ML Analytics Dashboard
            - 📊 Predictive Analytics Components
            - 🔍 Real-time Pattern Detection
            - 🏥 Medical NLP Service
            - 📈 ML Insights Visualization
            
            ### 🛠️ Technical Improvements:
            - TensorFlow.js Integration
            - AutoML Pipeline
            - BigQuery Analytics
            - Performance Optimizations
            
            ### 🌐 Deployment:
            - Production URL: https://mindflow-pro.vercel.app
            - Supabase Database: Active
            - All tests: ✅ Passed
            
            **Full Changelog**: https://github.com/Anderson-Archimede/MindFlow-Pro/compare/v1.0.0...v2.0.0
          draft: false
          prerelease: false
