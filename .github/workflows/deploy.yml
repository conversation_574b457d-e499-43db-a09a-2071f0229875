name: 🚀 Deploy MindFlow Pro to Vercel

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  # Job de tests
  test:
    name: 🧪 Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
          
      - name: 📋 Install dependencies
        run: |
          cd frontend
          npm ci
          
      - name: 🔍 Lint code
        run: |
          cd frontend
          npm run lint || echo "Lint warnings detected"
          
      - name: 🧪 Run tests
        run: |
          cd frontend
          npm test -- --passWithNoTests --coverage
          
      - name: 📊 Upload coverage
        uses: codecov/codecov-action@v3
        if: always()
        with:
          directory: frontend/coverage

  # Job de build
  build:
    name: 🏗️ Build
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
          
      - name: 📋 Install dependencies
        run: |
          cd frontend
          npm ci
          
      - name: 🏗️ Build application
        run: |
          cd frontend
          npm run build
          
      - name: 📤 Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: frontend/.next
          retention-days: 1

  # Job de déploiement Preview (PR)
  deploy-preview:
    name: 🔍 Deploy Preview
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.event_name == 'pull_request'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          
      - name: 📋 Install Vercel CLI
        run: npm install --global vercel@latest
        
      - name: 📤 Pull Vercel Environment
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: frontend
        
      - name: 🏗️ Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: frontend
        
      - name: 🚀 Deploy to Vercel (Preview)
        id: deploy-preview
        run: |
          url=$(vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }})
          echo "preview-url=$url" >> $GITHUB_OUTPUT
        working-directory: frontend
        
      - name: 💬 Comment PR
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **Preview déployé avec succès!**\n\n🔗 **URL Preview:** ${{ steps.deploy-preview.outputs.preview-url }}\n\n✅ Tests passés\n🏗️ Build réussi\n📱 Application prête pour review`
            })

  # Job de déploiement Production (main)
  deploy-production:
    name: 🌟 Deploy Production
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          
      - name: 📋 Install Vercel CLI
        run: npm install --global vercel@latest
        
      - name: 📤 Pull Vercel Environment
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: frontend
        
      - name: 🏗️ Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: frontend
        
      - name: 🚀 Deploy to Vercel (Production)
        id: deploy-production
        run: |
          url=$(vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }})
          echo "production-url=$url" >> $GITHUB_OUTPUT
        working-directory: frontend
        
      - name: 🔍 Health Check
        run: |
          sleep 30
          curl -f ${{ steps.deploy-production.outputs.production-url }} || exit 1
          
      - name: 📊 Performance Test
        run: |
          npx lighthouse ${{ steps.deploy-production.outputs.production-url }} --only-categories=performance --chrome-flags="--headless" --output=json --output-path=lighthouse.json || true
          
      - name: 📈 Upload Performance Report
        uses: actions/upload-artifact@v3
        with:
          name: lighthouse-report
          path: lighthouse.json

  # Job de notification
  notify:
    name: 📢 Notifications
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always()
    
    steps:
      - name: 📧 Send Success Notification
        if: needs.deploy-production.result == 'success'
        run: |
          echo "🎉 Déploiement réussi!"
          echo "🌟 Application en production: https://mindflow-jv4wmqi61-anderson-archimedes-projects.vercel.app"
          
      - name: 🚨 Send Failure Notification
        if: needs.deploy-production.result == 'failure'
        run: |
          echo "❌ Échec du déploiement!"
          echo "🔍 Vérifiez les logs GitHub Actions" 