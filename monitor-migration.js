#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function checkSupabaseSetup() {
  const envPath = path.join(__dirname, 'frontend', '.env.local');
  
  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    const hasSupabaseUrl = envContent.includes('NEXT_PUBLIC_SUPABASE_URL=https://');
    const hasAnonKey = envContent.includes('NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ');
    const hasServiceKey = envContent.includes('SUPABASE_SERVICE_ROLE_KEY=eyJ');
    
    console.log('🔍 STATUS CONFIGURATION SUPABASE');
    console.log('================================');
    console.log(`📂 Fichier .env.local: ${fs.existsSync(envPath) ? '✅' : '❌'}`);
    console.log(`🔗 URL Supabase: ${hasSupabaseUrl ? '✅' : '❌'}`);
    console.log(`🔑 Clé Publique: ${hasAnonKey ? '✅' : '❌'}`);
    console.log(`🔐 Clé Service: ${hasServiceKey ? '✅' : '❌'}`);
    
    const flags = envContent.match(/NEXT_PUBLIC_.*=true/g) || [];
    console.log('\n🏁 FLAGS MIGRATION ACTIFS:');
    if (flags.length === 0) {
      console.log('   Aucun flag activé');
    } else {
      flags.forEach(flag => console.log(`   ✅ ${flag}`));
    }
    
    return hasSupabaseUrl && hasAnonKey && hasServiceKey;
  } catch (error) {
    console.log('❌ Fichier .env.local introuvable');
    return false;
  }
}

function checkServers() {
  const { execSync } = require('child_process');
  
  try {
    const frontendPort = execSync('lsof -ti:3000', { encoding: 'utf8' }).trim();
    const backendPort = execSync('lsof -ti:4000', { encoding: 'utf8' }).trim();
    
    console.log('\n🌐 STATUS SERVEURS:');
    console.log(`Frontend (3000): ${frontendPort ? '✅ Actif' : '❌ Arrêté'}`);
    console.log(`Backend (4000): ${backendPort ? '✅ Actif' : '❌ Arrêté'}`);
    
    return frontendPort && backendPort;
  } catch (error) {
    console.log('\n🌐 STATUS SERVEURS:');
    console.log('Frontend (3000): ❌ Arrêté');
    console.log('Backend (4000): ❌ Arrêté');
    return false;
  }
}

function getRecommendations() {
  const supabaseOk = checkSupabaseSetup();
  const serversOk = checkServers();
  
  console.log('\n🎯 RECOMMANDATIONS:');
  
  if (!supabaseOk) {
    console.log('1. Exécuter: node setup-supabase.js');
    console.log('2. Configurer les clés Supabase');
  } else {
    console.log('✅ Configuration Supabase complète');
  }
  
  if (!serversOk) {
    console.log('3. Démarrer: npm run dev');
  } else {
    console.log('✅ Serveurs opérationnels');
  }
  
  if (supabaseOk && serversOk) {
    console.log('🚀 Prêt pour migration: node activate-migration.js');
  }
}

console.log('📊 MONITORING MIGRATION MINDFLOW PRO');
console.log('=====================================');

getRecommendations();

// Auto-refresh toutes les 10 secondes si lancé avec --watch
if (process.argv.includes('--watch')) {
  setInterval(() => {
    console.clear();
    console.log('📊 MONITORING MIGRATION MINDFLOW PRO (Auto-refresh)');
    console.log('====================================================');
    getRecommendations();
    console.log(`\n⏰ Dernière mise à jour: ${new Date().toLocaleTimeString()}`);
  }, 10000);
}
