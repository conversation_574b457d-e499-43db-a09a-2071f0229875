#!/bin/bash

# 🚀 TEST PHASE 2 - Activation Authentification Supabase
# =======================================================

echo "🚀 PHASE 2 - ACTIVATION AUTHENTIFICATION SUPABASE"
echo "=================================================="

# 1. Vérification fichier .env.local
echo "1. 📋 Vérification configuration..."
if [ -f "frontend/.env.local" ]; then
    echo "   ✅ frontend/.env.local existe"
    
    # Vérification des variables critiques
    if grep -q "NEXT_PUBLIC_USE_SUPABASE_AUTH=true" frontend/.env.local; then
        echo "   ✅ SUPABASE_AUTH activée"
    else
        echo "   ❌ SUPABASE_AUTH non configurée"
    fi
    
    if grep -q "NEXT_PUBLIC_USE_SUPABASE_DATABASE=false" frontend/.env.local; then
        echo "   ✅ SQLite maintenue"
    else
        echo "   ❌ Configuration DB non trouvée"
    fi
    
    if grep -q "NEXT_PUBLIC_DUAL_DATABASE_MODE=true" frontend/.env.local; then
        echo "   ✅ Mode dual activé"
    else
        echo "   ❌ Mode dual non configuré"
    fi
else
    echo "   ❌ frontend/.env.local manquant"
    echo ""
    echo "🚨 CRÉATION REQUISE - frontend/.env.local"
    echo "========================================="
    echo "Créez le fichier avec ce contenu:"
    echo ""
    cat << 'EOF'
# PHASE 2 SUPABASE
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4

NEXT_PUBLIC_USE_SUPABASE_AUTH=true
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true

NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:4000/api/v1
NEXT_PUBLIC_DEBUG_MODE=true
EOF
    echo ""
    echo "Puis relancez ce script."
    exit 1
fi

# 2. Test serveur Next.js
echo ""
echo "2. 🌐 Vérification serveur Next.js..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ 2>/dev/null || echo "000")
if [ "$response" = "200" ]; then
    echo "   ✅ Serveur opérationnel (port 3000)"
else
    echo "   ⚠️ Serveur non accessible"
    echo "   Vérifiez que 'npm run dev' fonctionne dans frontend/"
fi

# 3. Test pages d'authentification
echo ""
echo "3. 🔐 Test pages d'authentification..."

# Test page login
login_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/auth/login 2>/dev/null || echo "000")
if [ "$login_response" = "200" ]; then
    echo "   ✅ /auth/login accessible"
else
    echo "   ❌ /auth/login erreur ($login_response)"
fi

# Test page register  
register_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/auth/register 2>/dev/null || echo "000")
if [ "$register_response" = "200" ]; then
    echo "   ✅ /auth/register accessible"
else
    echo "   ❌ /auth/register erreur ($register_response)"
fi

# Test page test-auth
test_auth_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/test-auth 2>/dev/null || echo "000")
if [ "$test_auth_response" = "200" ]; then
    echo "   ✅ /test-auth accessible"
else
    echo "   ❌ /test-auth erreur ($test_auth_response)"
fi

# 4. Rapport final
echo ""
echo "🎯 RAPPORT PHASE 2"
echo "=================="

if [ -f "frontend/.env.local" ] && [ "$response" = "200" ]; then
    echo "🚀 PHASE 2 PRÊTE !"
    echo ""
    echo "✅ Configuration: OK"
    echo "✅ Serveur: Opérationnel"
    echo "✅ Mode hybride: SQLite + Supabase Auth"
    echo ""
    echo "📖 Prochaines étapes:"
    echo "1. Testez l'inscription: http://localhost:3000/auth/register"
    echo "2. Testez la connexion: http://localhost:3000/auth/login"
    echo "3. Diagnostics avancés: http://localhost:3000/test-auth"
    echo ""
    echo "🔄 Si vous modifiez .env.local, redémarrez le serveur:"
    echo "   cd frontend && npm run dev"
else
    echo "⚠️ CONFIGURATION INCOMPLÈTE"
    echo "Suivez les instructions ci-dessus pour finaliser"
fi 