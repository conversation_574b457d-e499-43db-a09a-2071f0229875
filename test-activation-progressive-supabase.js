#!/usr/bin/env node

/**
 * 🚀 TEST ET ACTIVATION PROGRESSIVE SUPABASE
 * Script automatisé pour MindFlow Pro
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 ACTIVATION PROGRESSIVE SUPABASE - MINDFLOW PRO');
console.log('==================================================\n');

// Configuration des phases
const phases = {
  1: {
    name: 'Phase 1: Tests de connectivité',
    description: 'Validation des nouvelles clés API et connectivité de base',
    env: {
      'NEXT_PUBLIC_USE_SUPABASE_AUTH': 'false',
      'NEXT_PUBLIC_USE_SUPABASE_DATABASE': 'false',
      'NEXT_PUBLIC_DUAL_DATABASE_MODE': 'true'
    }
  },
  2: {
    name: 'Phase 2: Activation authentification',
    description: 'Activation du système d\'authentification Supabase',
    env: {
      'NEXT_PUBLIC_USE_SUPABASE_AUTH': 'true',
      'NEXT_PUBLIC_USE_SUPABASE_DATABASE': 'false',
      'NEXT_PUBLIC_DUAL_DATABASE_MODE': 'true'
    }
  },
  3: {
    name: 'Phase 3: Activation complète',
    description: 'Migration complète vers Supabase',
    env: {
      'NEXT_PUBLIC_USE_SUPABASE_AUTH': 'true',
      'NEXT_PUBLIC_USE_SUPABASE_DATABASE': 'true',
      'NEXT_PUBLIC_DUAL_DATABASE_MODE': 'false'
    }
  }
};

function createEnvFile(phase) {
  const baseConfig = `# Configuration Supabase - MindFlow Pro
# URL et clés API (mises à jour décembre 2024)
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ

# Clé service role (pour administration backend)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4

# === ${phases[phase].name.toUpperCase()} ===
# ${phases[phase].description}
${Object.entries(phases[phase].env).map(([key, value]) => `${key}=${value}`).join('\n')}

NODE_ENV=development
`;

  const envPath = path.join(__dirname, 'frontend', '.env.local');
  fs.writeFileSync(envPath, baseConfig);
  console.log(`✅ Fichier .env.local créé pour ${phases[phase].name}`);
}

function runTests() {
  console.log('🧪 LANCEMENT DES TESTS DE CONNECTIVITÉ...\n');
  
  try {
    // Test connectivité HTTP simple
    console.log('📡 Test connectivité HTTP Supabase...');
    const testScript = `
      const https = require('https');
      const url = 'https://kvdrukmoxetoiojazukf.supabase.co/rest/v1/';
      const options = {
        headers: {
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
        }
      };
      
      const req = https.get(url, options, (res) => {
        console.log('   ✅ Status:', res.statusCode);
        console.log('   ✅ API Supabase accessible');
        process.exit(0);
      });
      
      req.on('error', (error) => {
        console.log('   ❌ Erreur:', error.message);
        process.exit(1);
      });
      
      req.setTimeout(5000, () => {
        console.log('   ❌ Timeout');
        req.destroy();
        process.exit(1);
      });
    `;
    
    execSync(`node -e "${testScript.replace(/\n/g, ' ')}"`, { stdio: 'inherit' });
    
  } catch (error) {
    console.log('   ❌ Erreur connectivité:', error.message);
  }
}

function displayInstructions(phase) {
  console.log('\n🎯 INSTRUCTIONS DE TEST :');
  console.log('=========================');
  
  console.log('1. 🖥️  Démarrez le serveur de développement :');
  console.log('   cd frontend && npm run dev');
  
  console.log('\n2. 🧪 Testez les pages suivantes :');
  console.log('   📊 http://localhost:3000/test-nouvelles-cles');
  console.log('   🚀 http://localhost:3000/test-activation-supabase'); 
  console.log('   🔍 http://localhost:3000/test-basic');
  console.log('   📡 http://localhost:3000/test-supabase-simple');
  
  console.log('\n3. ✅ Vérifications à effectuer :');
  console.log(`   • ${phases[phase].description}`);
  console.log('   • Toutes les clés API fonctionnent');
  console.log('   • Aucune erreur dans la console navigateur');
  
  if (phase < 3) {
    console.log(`\n4. 🔄 Pour passer à la ${phases[phase + 1].name} :`);
    console.log(`   node test-activation-progressive-supabase.js --phase=${phase + 1}`);
  } else {
    console.log('\n4. 🎉 Migration complète terminée !');
    console.log('   ✅ Supabase entièrement activé');
    console.log('   ✅ Prêt pour les tests de production');
  }
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  const phaseArg = args.find(arg => arg.startsWith('--phase='));
  const targetPhase = phaseArg ? parseInt(phaseArg.split('=')[1]) : 1;
  
  if (!phases[targetPhase]) {
    console.error('❌ Phase invalide. Utilisez --phase=1, --phase=2, ou --phase=3');
    process.exit(1);
  }
  
  console.log(`🎯 Configuration pour ${phases[targetPhase].name}`);
  console.log(`📋 ${phases[targetPhase].description}\n`);
  
  // Créer le fichier .env.local
  createEnvFile(targetPhase);
  
  // Lancer les tests
  runTests();
  
  // Afficher les instructions
  displayInstructions(targetPhase);
  
  console.log('\n🚀 Configuration terminée avec succès !');
  console.log('💡 Consultez les captures d\'écran des tests pour valider le fonctionnement.');
}

if (require.main === module) {
  main();
} 