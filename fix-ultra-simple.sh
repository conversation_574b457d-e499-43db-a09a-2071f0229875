#!/bin/bash

echo "🔧 CORRECTION COMPLÈTE - Page Ultra-Simple"
echo "=========================================="

# 1. Arrêter tous les serveurs Next.js
echo "1. 🛑 Arrêt des serveurs..."
pkill -f "next dev" 2>/dev/null || echo "   Aucun serveur actif"
sleep 2

# 2. Nettoyage complet
echo "2. 🧹 Nettoyage complet..."
cd frontend
rm -rf .next
rm -rf node_modules/.cache
rm -rf .next/cache
rm -rf .next/static

# 3. Vérifier la structure des pages
echo "3. 📁 Vérification structure pages..."
echo "   Pages dans app/:"
ls -la src/app/ | grep ultra-simple || echo "   ❌ Dossier ultra-simple manquant!"

# 4. Vérifier le contenu de la page
echo "4. 📄 Contenu page ultra-simple:"
if [ -f "src/app/ultra-simple/page.tsx" ]; then
    echo "   ✅ Fichier existe"
    head -5 src/app/ultra-simple/page.tsx
else
    echo "   ❌ Fichier manquant!"
fi

# 5. Créer/Recréer la page ultra-simple
echo "5. 🔨 Création page ultra-simple..."
mkdir -p src/app/ultra-simple
cat > src/app/ultra-simple/page.tsx << 'EOF'
export default function UltraSimple() {
  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      minHeight: '100vh',
      backgroundColor: '#f0f8ff'
    }}>
      <h1 style={{ color: '#2563eb', fontSize: '2em' }}>
        🚀 Ultra Simple - FIXED!
      </h1>
      
      <div style={{ 
        backgroundColor: '#dcfce7', 
        padding: '20px', 
        borderRadius: '8px',
        marginTop: '20px',
        border: '2px solid #22c55e'
      }}>
        <h2>✅ Page Ultra-Simple Fonctionnelle!</h2>
        <p>Cette page est maintenant corrigée et devrait s'afficher correctement.</p>
        <p><strong>URL:</strong> http://localhost:3000/ultra-simple</p>
        <p><strong>Statut:</strong> ✅ Opérationnelle</p>
      </div>

      <div style={{ 
        backgroundColor: '#fef3c7', 
        padding: '15px', 
        borderRadius: '8px',
        marginTop: '20px'
      }}>
        <h3>🔗 Navigation Test</h3>
        <ul>
          <li><a href="/" style={{ color: '#1d4ed8' }}>🏠 Accueil</a></li>
          <li><a href="/test-basic" style={{ color: '#1d4ed8' }}>🧪 Test Basic</a></li>
          <li><a href="/test-simple" style={{ color: '#1d4ed8' }}>🔍 Test Simple</a></li>
          <li><a href="/ultra-simple" style={{ color: '#1d4ed8' }}>⚡ Cette page</a></li>
        </ul>
      </div>

      <div style={{ 
        fontSize: '12px', 
        color: '#666',
        marginTop: '30px',
        borderTop: '1px solid #ddd',
        paddingTop: '15px',
        textAlign: 'center'
      }}>
        <p>🕒 Page générée le: {new Date().toLocaleString('fr-FR')}</p>
        <p>🔧 Problème résolu avec nettoyage cache complet</p>
      </div>
    </div>
  );
}
EOF

echo "   ✅ Page ultra-simple recréée"

# 6. Vérifier next.config.js
echo "6. ⚙️ Vérification next.config.js..."
if grep -q "appDir" next.config.js 2>/dev/null; then
    echo "   ⚠️ Configuration obsolète détectée, correction..."
    cp next.config.js next.config.js.backup
    cat > next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:4000/api/:path*',
      },
    ];
  },
};

module.exports = nextConfig;
EOF
    echo "   ✅ next.config.js corrigé"
else
    echo "   ✅ Configuration OK"
fi

# 7. Démarrage serveur
echo "7. 🚀 Démarrage serveur..."
echo "   Serveur démarre sur http://localhost:3000"
echo "   Testez: http://localhost:3000/ultra-simple"
echo ""
echo "⏰ Attendez 10-15 secondes pour la compilation..."

npm run dev 