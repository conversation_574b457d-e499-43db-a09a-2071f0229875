{"name": "mindflow-pro-production", "version": "1.0.0", "description": "MindFlow Pro - Application de santé mentale avec Supabase", "main": "frontend/.next/standalone/server.js", "scripts": {"start": "cd frontend && npm start", "build": "cd frontend && npm run build", "test": "node test-inscription-simple.js"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "next": "14.2.30", "react": "^18.2.0", "react-dom": "^18.2.0"}, "keywords": ["mindflow", "mental-health", "supabase", "nextjs"], "author": "MindFlow Pro Team", "license": "MIT"}