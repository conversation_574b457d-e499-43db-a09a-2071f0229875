#!/bin/bash

# Test Ultra-Simple pour MindFlow Pro
echo "🔧 TEST ULTRA-SIMPLE - MindFlow Pro"
echo "===================================="

cd frontend

# Nettoyage
echo "1. 🧹 Nettoyage..."
pkill -f "next" 2>/dev/null || true
rm -rf .next 2>/dev/null || true
rm -f next.config.ts 2>/dev/null || true

# Configuration
echo "2. ⚙️ Configuration..."
cat > next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  typescript: { ignoreBuildErrors: true },
  eslint: { ignoreDuringBuilds: true },
};
module.exports = nextConfig;
EOF

# Pages
echo "3. 📄 Vérification pages..."
mkdir -p src/app/ultra-simple

cat > src/app/page.tsx << 'EOF'
export default function Home() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>MindFlow Pro - Test</h1>
      <p>✅ Serveur opérationnel</p>
      <a href="/ultra-simple">Ultra Simple</a>
    </div>
  );
}
EOF

cat > src/app/ultra-simple/page.tsx << 'EOF'
export default function UltraSimple() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>🚀 Ultra Simple</h1>
      <p>✅ Page fonctionnelle</p>
      <a href="/">← Accueil</a>
    </div>
  );
}
EOF

echo "4. 🚀 Démarrage serveur sur http://localhost:3000"
npm run dev 