#!/usr/bin/env node

/**
 * 🗄️ CRÉATION AUTOMATIQUE DES TABLES SUPABASE VIA API
 * Alternative à l'exécution SQL - utilise l'API REST pour créer et peupler les tables
 */

const https = require('https');

// 🔧 Configuration Supabase
const SUPABASE_URL = 'https://kvdrukmoxetoiojazukf.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4';

console.log('🚀 CRÉATION TABLES SUPABASE VIA API - MINDFLOW PRO');
console.log('================================================');

// Fonction pour exécuter du SQL via l'API
function executeSQLRequest(sql) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      query: sql
    });

    const options = {
      hostname: 'kvdrukmoxetoiojazukf.supabase.co',
      port: 443,
      path: '/rest/v1/rpc/exec_sql',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(JSON.parse(data || '{}'));
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    req.write(postData);
    req.end();
  });
}

// Fonction pour insérer des données via API REST
function insertData(table, data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);

    const options = {
      hostname: 'kvdrukmoxetoiojazukf.supabase.co',
      port: 443,
      path: `/rest/v1/${table}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'Content-Length': Buffer.byteLength(postData),
        'Prefer': 'return=representation'
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(JSON.parse(responseData || '[]'));
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    req.write(postData);
    req.end();
  });
}

// Script principal
async function createSupabaseTables() {
  try {
    console.log('📋 ÉTAPE 1: Création des tables...');
    
    // SQL pour créer la table professionals
    const createProfessionalsSQL = `
      CREATE TABLE IF NOT EXISTS professionals (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        role TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        specialties TEXT[] DEFAULT '{}',
        price_per_session DECIMAL(10,2) DEFAULT 0,
        location TEXT,
        bio TEXT,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );
    `;

    console.log('  📝 Création table professionals...');
    try {
      await executeSQLRequest(createProfessionalsSQL);
      console.log('  ✅ Table professionals créée');
    } catch (error) {
      console.log('  ⚠️  Table professionals:', error.message);
    }

    // SQL pour créer la table appointments
    const createAppointmentsSQL = `
      CREATE TABLE IF NOT EXISTS appointments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        professional_id UUID REFERENCES professionals(id) ON DELETE CASCADE,
        professional_name TEXT NOT NULL,
        professional_role TEXT,
        client_id TEXT NOT NULL,
        client_name TEXT,
        appointment_date DATE NOT NULL,
        appointment_time TIME NOT NULL,
        duration_minutes INTEGER DEFAULT 60,
        type TEXT DEFAULT 'video',
        status TEXT DEFAULT 'scheduled',
        price DECIMAL(10,2),
        currency TEXT DEFAULT 'EUR',
        notes TEXT,
        meeting_link TEXT,
        reminder_sent BOOLEAN DEFAULT FALSE,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        feedback TEXT,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );
    `;

    console.log('  📝 Création table appointments...');
    try {
      await executeSQLRequest(createAppointmentsSQL);
      console.log('  ✅ Table appointments créée');
    } catch (error) {
      console.log('  ⚠️  Table appointments:', error.message);
    }

    // Créer les index
    const createIndexesSQL = [
      'CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_professional ON appointments(professional_id);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_client ON appointments(client_id);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);',
      'CREATE INDEX IF NOT EXISTS idx_professionals_email ON professionals(email);'
    ];

    console.log('  📝 Création des index...');
    for (const indexSQL of createIndexesSQL) {
      try {
        await executeSQLRequest(indexSQL);
        console.log('  ✅ Index créé');
      } catch (error) {
        console.log('  ⚠️  Index:', error.message);
      }
    }

    console.log('\n👥 ÉTAPE 2: Insertion des professionnels...');

    // Données des professionnels
    const professionals = [
      {
        name: 'Dr. Sophie Martin',
        role: 'Psychologue clinicienne',
        email: '<EMAIL>',
        specialties: ['Thérapie cognitive comportementale', 'Gestion du stress', 'Anxiété'],
        price_per_session: 85.00,
        location: '15 rue de la Paix, 75001 Paris',
        bio: 'Spécialisée en thérapie cognitive comportementale avec plus de 10 ans d\'expérience.'
      },
      {
        name: 'Dr. Jean Dupont',
        role: 'Psychiatre',
        email: '<EMAIL>',
        specialties: ['Psychiatrie générale', 'Troubles bipolaires', 'Dépression'],
        price_per_session: 120.00,
        location: '42 avenue Victor Hugo, 69003 Lyon',
        bio: 'Psychiatre expérimenté spécialisé dans les troubles de l\'humeur.'
      },
      {
        name: 'Marie Leblanc',
        role: 'Thérapeute comportementale',
        email: '<EMAIL>',
        specialties: ['Thérapie comportementale', 'Phobies', 'Addictions'],
        price_per_session: 75.00,
        location: '8 place Bellecour, 69002 Lyon',
        bio: 'Thérapeute spécialisée dans les troubles comportementaux.'
      },
      {
        name: 'Dr. Ahmed Benali',
        role: 'Psychothérapeute',
        email: '<EMAIL>',
        specialties: ['Psychothérapie humaniste', 'Thérapie de couple', 'Traumatismes'],
        price_per_session: 90.00,
        location: '25 cours Mirabeau, 13100 Aix-en-Provence',
        bio: 'Psychothérapeute avec une approche humaniste centrée sur la personne.'
      }
    ];

    try {
      const insertedProfessionals = await insertData('professionals', professionals);
      console.log(`  ✅ ${insertedProfessionals.length} professionnels insérés`);

      console.log('\n📅 ÉTAPE 3: Insertion des rendez-vous...');

      // Créer des rendez-vous pour chaque professionnel
      const appointments = [];
      const today = new Date();

      insertedProfessionals.forEach((prof, index) => {
        const appointmentDate = new Date(today);
        appointmentDate.setDate(today.getDate() + index + 1);

        appointments.push({
          professional_id: prof.id,
          professional_name: prof.name,
          professional_role: prof.role,
          client_id: `demo-user-${index + 1}`,
          client_name: `Client Démo ${index + 1}`,
          appointment_date: appointmentDate.toISOString().split('T')[0],
          appointment_time: '14:00',
          duration_minutes: 60,
          type: 'video',
          status: 'scheduled',
          price: prof.price_per_session,
          currency: 'EUR',
          notes: `Rendez-vous de test avec ${prof.name}`,
          meeting_link: `https://meet.mindflow.com/session-${prof.id}`,
          reminder_sent: false
        });

        // Ajouter un deuxième rendez-vous avec un statut différent
        const completedDate = new Date(today);
        completedDate.setDate(today.getDate() - (index + 1));

        appointments.push({
          professional_id: prof.id,
          professional_name: prof.name,
          professional_role: prof.role,
          client_id: `demo-user-${index + 10}`,
          client_name: `Client Expérience ${index + 10}`,
          appointment_date: completedDate.toISOString().split('T')[0],
          appointment_time: '10:00',
          duration_minutes: 60,
          type: index % 2 === 0 ? 'in-person' : 'video',
          status: index === 0 ? 'completed' : 'cancelled',
          price: prof.price_per_session,
          currency: 'EUR',
          notes: index === 0 ? 'Séance terminée avec succès' : 'Annulé par le client',
          rating: index === 0 ? 5 : null,
          feedback: index === 0 ? 'Excellente séance, très professionnel!' : null,
          reminder_sent: true
        });
      });

      const insertedAppointments = await insertData('appointments', appointments);
      console.log(`  ✅ ${insertedAppointments.length} rendez-vous insérés`);

      console.log('\n🔍 ÉTAPE 4: Validation des données...');
      await validateSupabaseData();

      console.log('\n🎉 CRÉATION TERMINÉE AVEC SUCCÈS !');
      console.log('📊 Résumé:');
      console.log(`   👥 ${insertedProfessionals.length} professionnels créés`);
      console.log(`   📅 ${insertedAppointments.length} rendez-vous créés`);
      console.log('\n🌐 Testez maintenant:');
      console.log('   • Page de test: http://localhost:3001/test-appointments-supabase');
      console.log('   • Dashboard Supabase: https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf');

    } catch (error) {
      console.error('❌ Erreur lors de l\'insertion des données:', error.message);
    }

  } catch (error) {
    console.error('💥 Erreur fatale:', error.message);
    process.exit(1);
  }
}

// Validation des données
async function validateSupabaseData() {
  try {
    // Test de connexion avec le client Supabase
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

    // Vérifier les professionnels
    const { data: professionals, error: profError } = await supabase
      .from('professionals')
      .select('*');
    
    if (profError) {
      console.log('❌ Erreur professionnels:', profError.message);
    } else {
      console.log(`  ✅ Professionnels: ${professionals.length} enregistrements`);
    }

    // Vérifier les rendez-vous
    const { data: appointments, error: apptError } = await supabase
      .from('appointments')
      .select('*');
    
    if (apptError) {
      console.log('❌ Erreur rendez-vous:', apptError.message);
    } else {
      console.log(`  ✅ Rendez-vous: ${appointments.length} enregistrements`);
    }

    // Test de jointure
    const { data: joined, error: joinError } = await supabase
      .from('appointments')
      .select(`
        id,
        appointment_date,
        appointment_time,
        status,
        professional:professionals(name, role)
      `)
      .limit(3);
    
    if (joinError) {
      console.log('❌ Erreur jointure:', joinError.message);
    } else {
      console.log(`  ✅ Jointure: ${joined.length} enregistrements liés`);
    }

  } catch (error) {
    console.log('❌ Erreur de validation:', error.message);
  }
}

// Lancement du script
if (require.main === module) {
  createSupabaseTables();
}

module.exports = { createSupabaseTables }; 