# CONTEXTE :
Tu as une application “MindFlow Pro” fonctionnelle, front-end en React/Tailwind, back-end en Node.js/Express, data et auth sur Supabase, déployée jusqu’à présent sur Vercel (mais avec des erreurs de déploiement).

# OBJECTIF :
Migrer l’hébergement de Vercel vers AWS (Lightsail, EC2 ou Serverless via Lambda/API Gateway), sans toucher à la logique Supabase (DB, Auth, Storage).

# CONTRAINTES :
- Conserver toutes les routes API et la structure du code existant.
- Préserver la connexion Supabase (client JS/TypeScript).
- Ajouter uniquement la configuration et les scripts nécessaires pour AWS.
- Garder l’UX/UI et les composants React inchangés.
- Minimiser la période de refactoring pour ne pas casser la CI/CD GitHub.

# TÂCHES À ACCOMPLIR :
1. ANALYSE  
   - Scanner tous les points d’entrée Express (server.js/app.js, routes).  
   - Relever les variables d’environnement (SUPABASE_URL, SUPABASE_ANON_KEY, etc.).

2. CONFIGURATION AWS  
   - Créer un fichier `aws-config.js` ou `config/aws.js` pour gérer les credentiels (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, REGION).  
   - Proposer deux scénarios de déploiement :  
     • **Lightsail/EC2** : Dockerfile + script de déploiement (user-data).  
     • **Serverless** : `serverless.yml` ou `template.yaml` (AWS SAM) pour packager Express en Lambda + API Gateway.  

3. SCRIPTS DE BUILD & DEPLOY  
   - Adapter `package.json` pour inclure :  
     • `deploy:ec2` (ssh → git pull → npm install → pm2 restart).  
     • `deploy:lambda` (serverless deploy ou sam deploy).  
   - Mettre à jour les workflows GitHub Actions :  
     • Remplacer Vercel Action par AWS CLI / Serverless Action.  
     • Variables secrètes GITHUB : AWS_CREDENTIALS, REGION.

4. INFRASTRUCTURE AS CODE (IAC)  
   - Si possible, proposer un exemple Terraform/CloudFormation minimal :  
     • Création d’une instance EC2 (t2.micro), sécurité, balancers.  
     • Définition d’une fonction Lambda + API Gateway.

5. TESTS & VERIFICATIONS  
   - Vérifier en local avec `serverless offline` ou via SSH sur Lightsail.  
   - S’assurer que les requêtes vers Supabase (CRUD, Auth) fonctionnent sans changement.

6. DOCUMENTATION  
   - Mettre à jour `README.md` :  
     • Nouvelle section “Déploiement AWS”.  
     • Commandes `npm run deploy:…`.  
     • Variables d’environnement AWS + Supabase.

# FICHIERS CIBLÉS :
- `server.js` / `app.js`  
- `routes/**/*.js` ou `controllers/**/*.ts`  
- `package.json`  
- `.github/workflows/*.yml`  
- `services/supabase.js` ou `lib/supabaseClient.ts`  
- Tout Dockerfile existant

# OPTIONNEL :
- Exemple de `Dockerfile` et `docker-compose.yml` pour AWS Lightsail.  
- Script bash de mise à jour continue (CI/CD).  
- Configuration SSL via ACM (Certificate Manager) et Route 53.

 
