#!/bin/bash

# 🎯 LANCEMENT ARCHITECTURE PRINCIPALE MINDFLOW PRO
# 📅 28 Décembre 2024
# 🚀 Setup automatisé de la base de données centralisée

echo "🎯 LANCEMENT ARCHITECTURE PRINCIPALE MINDFLOW PRO"
echo "🚀 Setup automatisé de la base de données centralisée"
echo "============================================================"

# Vérification des prérequis
echo "📋 1. Vérification des prérequis..."

if [ ! -f "schema-principal.sql" ]; then
    echo "❌ Fichier schema-principal.sql manquant"
    exit 1
fi

if [ ! -f "frontend/src/hooks/usePrincipalDatabase.ts" ]; then
    echo "❌ Hook principal manquant"
    exit 1
fi

echo "✅ Fichiers requis présents"

# Configuration des variables d'environnement
echo ""
echo "🔧 2. Configuration des variables d'environnement..."

cat > frontend/.env.local << EOF
# 🗄️ CONFIGURATION BASE DE DONNÉES PRINCIPALE
# 📅 Mise à jour automatique $(date '+%d/%m/%Y')

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ

# Database Configuration
NEXT_PUBLIC_USE_SUPABASE_DATABASE=true
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=false

# Principal Database Mode
NEXT_PUBLIC_PRINCIPAL_DATABASE_MODE=true
NEXT_PUBLIC_AUTO_SYNC_ENABLED=true
NEXT_PUBLIC_REAL_TIME_ENABLED=true
EOF

echo "✅ Variables d'environnement configurées"

# Installation des dépendances Supabase si nécessaire
echo ""
echo "📦 3. Vérification des dépendances..."

cd frontend
if ! npm list @supabase/supabase-js > /dev/null 2>&1; then
    echo "📦 Installation de @supabase/supabase-js..."
    npm install @supabase/supabase-js
fi
echo "✅ Dépendances vérifiées"

cd ..

# Validation du schéma
echo ""
echo "🧪 4. Validation du schéma..."
node valider-schema.js

# Création de la page de test
echo ""
echo "🧪 5. Création de la page de test..."

mkdir -p frontend/src/app/test-architecture-principale

cat > frontend/src/app/test-architecture-principale/page.tsx << 'EOF'
'use client';

import { useState, useEffect } from 'react';
import { usePrincipalDatabase, useProfessionals, useAppointments } from '@/hooks/usePrincipalDatabase';

export default function TestArchitecturePrincipale() {
    const { status, error, testConnexion } = usePrincipalDatabase();
    const { getProfessionals } = useProfessionals();
    const { getAppointments } = useAppointments();
    
    const [professionals, setProfessionals] = useState([]);
    const [appointments, setAppointments] = useState([]);
    const [testResults, setTestResults] = useState([]);

    const addTestResult = (name: string, status: 'success' | 'error', message: string) => {
        setTestResults(prev => [...prev, { name, status, message, timestamp: new Date() }]);
    };

    const runTests = async () => {
        setTestResults([]);
        
        // Test 1: Connexion
        addTestResult('Connexion', 'success', 'Test de connexion en cours...');
        const connexionOk = await testConnexion();
        addTestResult('Connexion', connexionOk ? 'success' : 'error', 
            connexionOk ? 'Connexion établie' : 'Échec de connexion');

        // Test 2: Professionnels
        try {
            const { data, error } = await getProfessionals();
            if (error) throw error;
            setProfessionals(data || []);
            addTestResult('Professionnels', 'success', `${(data || []).length} professionnels chargés`);
        } catch (err) {
            addTestResult('Professionnels', 'error', err.message);
        }

        // Test 3: Rendez-vous
        try {
            const { data, error } = await getAppointments();
            if (error) throw error;
            setAppointments(data || []);
            addTestResult('Rendez-vous', 'success', `${(data || []).length} rendez-vous chargés`);
        } catch (err) {
            addTestResult('Rendez-vous', 'error', err.message);
        }
    };

    useEffect(() => {
        runTests();
    }, []);

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-6xl mx-auto">
                <div className="bg-white rounded-lg shadow-lg p-6">
                    <h1 className="text-3xl font-bold text-center mb-8">
                        🎯 Test Architecture Principale MindFlow Pro
                    </h1>

                    {/* Statut de connexion */}
                    <div className="mb-8 p-4 rounded-lg border">
                        <h2 className="text-xl font-semibold mb-4">📊 Statut de la base de données</h2>
                        <div className="flex items-center space-x-4">
                            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                                status === 'connected' ? 'bg-green-100 text-green-800' :
                                status === 'error' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                            }`}>
                                {status === 'connected' ? '✅ Connecté' :
                                 status === 'error' ? '❌ Erreur' : '🔄 Initialisation'}
                            </div>
                            {error && (
                                <span className="text-red-600 text-sm">{error}</span>
                            )}
                        </div>
                    </div>

                    {/* Résultats des tests */}
                    <div className="mb-8">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-xl font-semibold">🧪 Résultats des tests</h2>
                            <button 
                                onClick={runTests}
                                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                            >
                                🔄 Relancer les tests
                            </button>
                        </div>
                        
                        <div className="space-y-2">
                            {testResults.map((result, index) => (
                                <div key={index} className={`p-3 rounded-lg border ${
                                    result.status === 'success' ? 'bg-green-50 border-green-200' :
                                    'bg-red-50 border-red-200'
                                }`}>
                                    <div className="flex justify-between items-center">
                                        <span className="font-medium">
                                            {result.status === 'success' ? '✅' : '❌'} {result.name}
                                        </span>
                                        <span className="text-sm text-gray-500">
                                            {result.timestamp.toLocaleTimeString()}
                                        </span>
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Données chargées */}
                    <div className="grid md:grid-cols-2 gap-6">
                        <div className="border rounded-lg p-4">
                            <h3 className="text-lg font-semibold mb-3">👨‍⚕️ Professionnels ({professionals.length})</h3>
                            <div className="space-y-2 max-h-60 overflow-y-auto">
                                {professionals.map((pro, index) => (
                                    <div key={index} className="p-2 bg-gray-50 rounded">
                                        <div className="font-medium">{pro.name}</div>
                                        <div className="text-sm text-gray-600">{pro.role}</div>
                                        <div className="text-sm text-blue-600">{pro.email}</div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="border rounded-lg p-4">
                            <h3 className="text-lg font-semibold mb-3">📅 Rendez-vous ({appointments.length})</h3>
                            <div className="space-y-2 max-h-60 overflow-y-auto">
                                {appointments.map((apt, index) => (
                                    <div key={index} className="p-2 bg-gray-50 rounded">
                                        <div className="font-medium">{apt.client_name}</div>
                                        <div className="text-sm text-gray-600">
                                            {apt.appointment_date} à {apt.appointment_time}
                                        </div>
                                        <div className="text-sm text-green-600">{apt.status}</div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Instructions */}
                    <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                        <h3 className="text-lg font-semibold mb-2">📋 Instructions</h3>
                        <ul className="text-sm space-y-1">
                            <li>✅ Base de données principale configurée et opérationnelle</li>
                            <li>🔄 Hooks unifiés disponibles pour toutes les fonctionnalités</li>
                            <li>🚀 Architecture centralisée prête pour production</li>
                            <li>📊 Tests automatiques validant l'intégrité du système</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
}
EOF

echo "✅ Page de test créée: /test-architecture-principale"

# Finalisation
echo ""
echo "🎉 ARCHITECTURE PRINCIPALE CONFIGURÉE !"
echo "✅ Base de données centralisée opérationnelle"
echo "🚀 Hooks unifiés prêts"
echo "📊 Page de test disponible"
echo ""
echo "🔗 Accès:"
echo "   Frontend: http://localhost:3000"
echo "   Test: http://localhost:3000/test-architecture-principale"
echo "   Supabase: https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf"
echo ""
echo "🚀 Prochaines étapes:"
echo "   1. Tester l'architecture: npm run dev"
echo "   2. Valider les données: node valider-schema.js"
echo "   3. Déployer en production: npm run build" 