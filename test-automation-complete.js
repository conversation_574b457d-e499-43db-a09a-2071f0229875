#!/usr/bin/env node

/**
 * 🧪 SCRIPT DE TESTS AUTOMATIQUES - MINDFLOW PRO
 * Suite complète de tests pour validation pré-déploiement
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const TEST_CONFIG = {
  FRONTEND_URL: 'http://localhost:3000',
  TEST_TIMEOUT: 300000, // 5 minutes
  PARALLEL_TESTS: false,
  BROWSERS: ['chromium', 'firefox'],
  VIEWPORTS: ['desktop', 'tablet', 'mobile']
};

// Couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  reset: '\x1b[0m'
};

const log = (msg, color = colors.cyan) => {
  console.log(`${color}[${new Date().toLocaleTimeString()}] ${msg}${colors.reset}`);
};

const success = (msg) => log(`✅ ${msg}`, colors.green);
const error = (msg) => log(`❌ ${msg}`, colors.red);
const info = (msg) => log(`ℹ️ ${msg}`, colors.blue);
const warning = (msg) => log(`⚠️ ${msg}`, colors.yellow);

/**
 * Résultats des tests
 */
let testResults = {
  timestamp: new Date().toISOString(),
  environment: {
    frontend: { status: 'pending', url: TEST_CONFIG.FRONTEND_URL },
    supabase: { status: 'pending', connection: false }
  },
  tests: {
    playwright: { status: 'pending', passed: 0, failed: 0 },
    unit: { status: 'pending', passed: 0, failed: 0 },
    integration: { status: 'pending', passed: 0, failed: 0 }
  },
  features: {
    dashboard: { status: 'pending' },
    journal: { status: 'pending' },
    aiCoach: { status: 'pending' },
    analytics: { status: 'pending' },
    telemedicine: { status: 'pending' },
    compliance: { status: 'pending' },
    integrations: { status: 'pending' },
    mlAnalytics: { status: 'pending' }
  },
  performance: {
    loadTime: 0,
    buildTime: 0,
    testDuration: 0
  },
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0,
    coverage: 0
  }
};

/**
 * Exécuter une commande
 */
function runCommand(command, options = {}) {
  try {
    const result = execSync(command, { 
      encoding: 'utf8',
      stdio: options.silent ? 'pipe' : 'inherit',
      timeout: options.timeout || 60000,
      ...options 
    });
    return { success: true, output: result };
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      output: error.stdout || error.stderr || ''
    };
  }
}

/**
 * Vérifier l'environnement
 */
async function checkEnvironment() {
  info('🔍 Vérification de l\'environnement...');
  
  // Vérifier Node.js et NPM
  const nodeCheck = runCommand('node --version', { silent: true });
  const npmCheck = runCommand('npm --version', { silent: true });
  
  if (!nodeCheck.success || !npmCheck.success) {
    error('Node.js ou NPM non installé');
    return false;
  }
  
  success(`Node.js: ${nodeCheck.output.trim()}`);
  success(`NPM: ${npmCheck.output.trim()}`);
  
  // Vérifier que le serveur frontend fonctionne
  try {
    const response = await fetch(TEST_CONFIG.FRONTEND_URL);
    if (response.ok || response.status === 404) {
      testResults.environment.frontend.status = 'running';
      success('Serveur frontend accessible');
    } else {
      throw new Error(`Status: ${response.status}`);
    }
  } catch (error) {
    testResults.environment.frontend.status = 'down';
    warning(`Serveur frontend non accessible: ${error.message}`);
    info('Tentative de démarrage du serveur...');
    return await startFrontendServer();
  }
  
  // Vérifier Supabase
  try {
    const supabaseResponse = await fetch(`${TEST_CONFIG.FRONTEND_URL}/api/health/supabase`);
    if (supabaseResponse.ok) {
      testResults.environment.supabase.status = 'connected';
      testResults.environment.supabase.connection = true;
      success('Connexion Supabase active');
    }
  } catch (error) {
    testResults.environment.supabase.status = 'disconnected';
    warning('Connexion Supabase non vérifiée');
  }
  
  return true;
}

/**
 * Démarrer le serveur frontend
 */
async function startFrontendServer() {
  info('🚀 Démarrage du serveur frontend...');
  
  return new Promise((resolve) => {
    const frontendProcess = spawn('npm', ['run', 'dev'], {
      cwd: 'frontend',
      stdio: 'pipe'
    });
    
    let serverReady = false;
    
    frontendProcess.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Ready') || output.includes('localhost:3000')) {
        if (!serverReady) {
          serverReady = true;
          success('Serveur frontend démarré');
          testResults.environment.frontend.status = 'running';
          resolve(true);
        }
      }
    });
    
    // Timeout après 60 secondes
    setTimeout(() => {
      if (!serverReady) {
        error('Timeout: Serveur frontend non démarré');
        frontendProcess.kill();
        resolve(false);
      }
    }, 60000);
  });
}

/**
 * Installer les dépendances de test
 */
async function installTestDependencies() {
  info('📦 Installation des dépendances de test...');
  
  // Installer Playwright si nécessaire
  const playwrightCheck = runCommand('npx playwright --version', { silent: true });
  if (!playwrightCheck.success) {
    info('Installation de Playwright...');
    const installResult = runCommand('npm install @playwright/test', { 
      timeout: 120000 
    });
    if (!installResult.success) {
      error('Installation de Playwright échouée');
      return false;
    }
  }
  
  // Installer les navigateurs Playwright
  info('Installation des navigateurs...');
  const browsersResult = runCommand('npx playwright install chromium firefox', { 
    timeout: 180000,
    silent: true 
  });
  
  if (browsersResult.success) {
    success('Navigateurs installés');
  } else {
    warning('Installation navigateurs partielle');
  }
  
  return true;
}

/**
 * Exécuter les tests Playwright
 */
async function runPlaywrightTests() {
  info('🎭 Exécution des tests Playwright...');
  
  const startTime = Date.now();
  
  // Commande de test avec reporter JSON
  const testCommand = 'npx playwright test tests/e2e/mindflow-complete-validation.spec.ts --reporter=json --output-dir=test-results/playwright';
  
  const testResult = runCommand(testCommand, { 
    timeout: TEST_CONFIG.TEST_TIMEOUT 
  });
  
  const testDuration = Date.now() - startTime;
  testResults.performance.testDuration = testDuration;
  
  if (testResult.success) {
    testResults.tests.playwright.status = 'passed';
    success(`Tests Playwright terminés en ${Math.round(testDuration/1000)}s`);
    
    // Parser les résultats JSON si disponible
    try {
      if (fs.existsSync('test-results/playwright/results.json')) {
        const playwrightResults = JSON.parse(fs.readFileSync('test-results/playwright/results.json', 'utf8'));
        testResults.tests.playwright.passed = playwrightResults.stats?.passed || 0;
        testResults.tests.playwright.failed = playwrightResults.stats?.failed || 0;
      }
    } catch (error) {
      info('Résultats détaillés non disponibles');
    }
    
    return true;
  } else {
    testResults.tests.playwright.status = 'failed';
    warning('Tests Playwright avec erreurs');
    console.log(testResult.output);
    return false;
  }
}

/**
 * Exécuter les tests unitaires
 */
async function runUnitTests() {
  info('🔬 Exécution des tests unitaires...');
  
  // Aller dans le dossier frontend
  const testCommand = 'cd frontend && npm test -- --passWithNoTests --coverage --watchAll=false';
  
  const testResult = runCommand(testCommand, { 
    timeout: 120000,
    silent: true 
  });
  
  if (testResult.success) {
    testResults.tests.unit.status = 'passed';
    success('Tests unitaires réussis');
    return true;
  } else {
    testResults.tests.unit.status = 'failed';
    warning('Tests unitaires avec erreurs');
    return false;
  }
}

/**
 * Tests de performance
 */
async function runPerformanceTests() {
  info('⚡ Tests de performance...');
  
  const performanceTests = [
    {
      name: 'Dashboard Load Time',
      url: '/dashboard',
      maxTime: 5000
    },
    {
      name: 'ML Analytics Load Time',
      url: '/ml-analytics',
      maxTime: 8000
    },
    {
      name: 'Journal Load Time',
      url: '/journal',
      maxTime: 4000
    }
  ];
  
  let allPassed = true;
  
  for (const test of performanceTests) {
    try {
      const startTime = Date.now();
      const response = await fetch(`${TEST_CONFIG.FRONTEND_URL}${test.url}`);
      const loadTime = Date.now() - startTime;
      
      if (loadTime < test.maxTime) {
        success(`${test.name}: ${loadTime}ms (< ${test.maxTime}ms)`);
      } else {
        warning(`${test.name}: ${loadTime}ms (> ${test.maxTime}ms)`);
        allPassed = false;
      }
    } catch (error) {
      error(`${test.name}: Erreur de chargement`);
      allPassed = false;
    }
  }
  
  return allPassed;
}

/**
 * Validation des fonctionnalités
 */
async function validateFeatures() {
  info('🔍 Validation des fonctionnalités...');
  
  const features = [
    { name: 'dashboard', url: '/dashboard', keyword: 'Dashboard' },
    { name: 'journal', url: '/journal', keyword: 'Journal' },
    { name: 'aiCoach', url: '/ai-coach', keyword: 'IA' },
    { name: 'analytics', url: '/analytics', keyword: 'Analytics' },
    { name: 'mlAnalytics', url: '/ml-analytics', keyword: 'Prédictifs' },
    { name: 'telemedicine', url: '/telemedicine', keyword: 'Télémédecine' },
    { name: 'compliance', url: '/compliance', keyword: 'Conformité' },
    { name: 'integrations', url: '/integrations-b2b', keyword: 'Intégrations' }
  ];
  
  for (const feature of features) {
    try {
      const response = await fetch(`${TEST_CONFIG.FRONTEND_URL}${feature.url}`);
      if (response.ok) {
        testResults.features[feature.name].status = 'available';
        success(`${feature.name}: Disponible`);
      } else {
        testResults.features[feature.name].status = 'error';
        warning(`${feature.name}: Erreur ${response.status}`);
      }
    } catch (error) {
      testResults.features[feature.name].status = 'unavailable';
      error(`${feature.name}: Non accessible`);
    }
  }
}

/**
 * Générer le rapport de tests
 */
async function generateTestReport() {
  info('📊 Génération du rapport de tests...');
  
  // Calculer le résumé
  const playwrightTests = testResults.tests.playwright.passed + testResults.tests.playwright.failed;
  const unitTests = testResults.tests.unit.passed + testResults.tests.unit.failed;
  
  testResults.summary.total = playwrightTests + unitTests;
  testResults.summary.passed = testResults.tests.playwright.passed + testResults.tests.unit.passed;
  testResults.summary.failed = testResults.tests.playwright.failed + testResults.tests.unit.failed;
  
  // Calculer le score de couverture
  const availableFeatures = Object.values(testResults.features)
    .filter(f => f.status === 'available').length;
  const totalFeatures = Object.keys(testResults.features).length;
  
  testResults.summary.coverage = Math.round((availableFeatures / totalFeatures) * 100);
  
  // Sauvegarder le rapport
  const reportFile = `test-report-${Date.now()}.json`;
  fs.writeFileSync(reportFile, JSON.stringify(testResults, null, 2));
  
  // Afficher le résumé
  console.log(`
📊 RAPPORT DE TESTS - MINDFLOW PRO
=================================

🏃‍♂️ Tests Playwright:     ${testResults.tests.playwright.status}
🔬 Tests Unitaires:      ${testResults.tests.unit.status}
⚡ Performance:          ${testResults.performance.testDuration}ms
🎯 Couverture:           ${testResults.summary.coverage}%

📋 Fonctionnalités:
${Object.entries(testResults.features).map(([name, feature]) => 
  `   ${feature.status === 'available' ? '✅' : '❌'} ${name}: ${feature.status}`
).join('\n')}

🌐 Environnement:
   Frontend: ${testResults.environment.frontend.status}
   Supabase: ${testResults.environment.supabase.status}

📄 Rapport détaillé: ${reportFile}
`);
  
  success('Rapport de tests généré');
  return testResults;
}

/**
 * Script principal
 */
async function main() {
  console.log(`
🧪 TESTS AUTOMATIQUES MINDFLOW PRO
==================================
Phase 9 - Analytics Prédictifs ML
Validation complète pré-déploiement
`);
  
  const startTime = Date.now();
  
  try {
    // 1. Vérification environnement
    if (!await checkEnvironment()) {
      process.exit(1);
    }
    
    // 2. Installation dépendances
    if (!await installTestDependencies()) {
      process.exit(1);
    }
    
    // 3. Validation des fonctionnalités
    await validateFeatures();
    
    // 4. Tests de performance
    await runPerformanceTests();
    
    // 5. Tests unitaires
    await runUnitTests();
    
    // 6. Tests Playwright
    await runPlaywrightTests();
    
    // 7. Génération du rapport
    const report = await generateTestReport();
    
    const totalTime = Date.now() - startTime;
    
    // 8. Résumé final
    console.log(`
✅ TESTS TERMINÉS!
==================
Durée totale: ${Math.round(totalTime/1000)}s
Score global: ${report.summary.coverage}%

${report.summary.coverage >= 80 ? '🎉 PRÊT POUR DÉPLOIEMENT!' : '⚠️ CORRECTIONS NÉCESSAIRES'}
`);
    
    success('Tests automatiques terminés');
    
    // Code de sortie basé sur le score
    process.exit(report.summary.coverage >= 70 ? 0 : 1);
    
  } catch (error) {
    console.error(`Erreur durant les tests: ${error.message}`);
    process.exit(1);
  }
}

// Gestion des signaux
process.on('SIGINT', () => {
  warning('Tests interrompus par l\'utilisateur');
  process.exit(1);
});

// Exécution
if (require.main === module) {
  main();
}

module.exports = { main, testResults };
