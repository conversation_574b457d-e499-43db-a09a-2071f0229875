#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

console.log('🚀 SETUP SUPABASE SIMPLE - MINDFLOW PRO');
console.log('=====================================');

async function setupSupabase() {
  try {
    console.log('📡 Connexion à Supabase...');
    const supabase = createClient(
      'https://kvdrukmoxetoiojazukf.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4'
    );
    
    console.log('🔍 Test des tables existantes...');
    
    // Test si les tables existent
    const { data: testProf, error: testProfError } = await supabase
      .from('professionals')
      .select('id')
      .limit(1);
    
    if (testProfError && testProfError.message.includes('does not exist')) {
      console.log('❌ Tables non créées. Affichage du SQL à exécuter...');
      console.log('\n🔗 Allez sur: https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf/sql');
      console.log('📋 Copiez-collez le script suivant et cliquez "Run":\n');
      
      console.log('--- DÉBUT DU SCRIPT SQL ---');
      console.log(`
-- Tables pour MindFlow Pro
CREATE TABLE IF NOT EXISTS professionals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    specialties TEXT[] DEFAULT '{}',
    price_per_session DECIMAL(10,2) DEFAULT 0,
    location TEXT,
    bio TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    professional_id UUID REFERENCES professionals(id) ON DELETE CASCADE,
    professional_name TEXT NOT NULL,
    professional_role TEXT,
    client_id TEXT NOT NULL,
    client_name TEXT,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    type TEXT DEFAULT 'video',
    status TEXT DEFAULT 'scheduled',
    price DECIMAL(10,2),
    currency TEXT DEFAULT 'EUR',
    notes TEXT,
    meeting_link TEXT,
    reminder_sent BOOLEAN DEFAULT FALSE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index pour performances
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_professional ON appointments(professional_id);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);

-- Permissions publiques pour tests
ALTER TABLE professionals ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public access" ON professionals FOR ALL USING (true);
CREATE POLICY "Public access" ON appointments FOR ALL USING (true);
      `);
      console.log('--- FIN DU SCRIPT SQL ---\n');
      console.log('⚡ Après avoir exécuté le SQL, relancez: node setup-supabase-fixed.js');
      return;
    }
    
    console.log('✅ Tables trouvées! Insertion des données...');
    
    // Données des professionnels
    const professionals = [
      {
        name: 'Dr. Sophie Martin',
        role: 'Psychologue clinicienne',
        email: '<EMAIL>',
        specialties: ['Thérapie cognitive comportementale', 'Gestion du stress'],
        price_per_session: 85.00,
        location: 'Paris',
        bio: 'Spécialisée en thérapie cognitive comportementale.'
      },
      {
        name: 'Dr. Jean Dupont',
        role: 'Psychiatre',
        email: '<EMAIL>',
        specialties: ['Psychiatrie générale', 'Troubles bipolaires'],
        price_per_session: 120.00,
        location: 'Lyon',
        bio: 'Psychiatre expérimenté spécialisé dans les troubles de l\'humeur.'
      },
      {
        name: 'Marie Leblanc',
        role: 'Thérapeute',
        email: '<EMAIL>',
        specialties: ['Thérapie comportementale', 'Phobies'],
        price_per_session: 75.00,
        location: 'Lyon',
        bio: 'Thérapeute spécialisée dans les troubles comportementaux.'
      }
    ];

    // Insérer les professionnels (avec upsert pour éviter les doublons)
    const { data: insertedProfs, error: profError } = await supabase
      .from('professionals')
      .upsert(professionals, { onConflict: 'email' })
      .select();

    if (profError) {
      console.log('❌ Erreur professionnels:', profError.message);
      throw profError;
    }

    console.log('✅ ' + insertedProfs.length + ' professionnels traités');

    // Créer des rendez-vous
    const appointments = [];
    const today = new Date();

    insertedProfs.forEach((prof, index) => {
      const futureDate = new Date(today);
      futureDate.setDate(today.getDate() + index + 1);

      appointments.push({
        professional_id: prof.id,
        professional_name: prof.name,
        professional_role: prof.role,
        client_id: 'demo-user-' + (index + 1),
        client_name: 'Client Démo ' + (index + 1),
        appointment_date: futureDate.toISOString().split('T')[0],
        appointment_time: '14:00',
        duration_minutes: 60,
        type: 'video',
        status: 'scheduled',
        price: prof.price_per_session,
        currency: 'EUR',
        notes: 'Rendez-vous avec ' + prof.name,
        meeting_link: 'https://meet.mindflow.com/' + prof.id,
        reminder_sent: false
      });
    });

    // Supprimer les anciens rendez-vous de démonstration
    await supabase
      .from('appointments')
      .delete()
      .like('client_id', 'demo-user-%');

    // Insérer les nouveaux rendez-vous
    const { data: insertedApts, error: aptError } = await supabase
      .from('appointments')
      .insert(appointments)
      .select();

    if (aptError) {
      console.log('❌ Erreur rendez-vous:', aptError.message);
      throw aptError;
    }

    console.log('✅ ' + insertedApts.length + ' rendez-vous insérés');

    // Validation finale
    const { data: validation, error: valError } = await supabase
      .from('appointments')
      .select('id, appointment_date, professional_name, status')
      .limit(5);

    if (valError) {
      console.log('❌ Erreur de validation:', valError.message);
    } else {
      console.log('✅ Validation: ' + validation.length + ' rendez-vous trouvés');
    }

    console.log('\n🎉 SETUP TERMINÉ AVEC SUCCÈS !');
    console.log('🌐 Testez sur: http://localhost:3001/test-appointments-supabase');
    console.log('�� Dashboard: https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf');

  } catch (error) {
    console.error('💥 Erreur:', error.message);
    process.exit(1);
  }
}

setupSupabase();
