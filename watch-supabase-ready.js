#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

console.log('👀 SURVEILLANCE SUPABASE - MINDFLOW PRO');
console.log('========================================');
console.log('🔍 Surveillance automatique de la création des tables...');
console.log('⏰ Vérification toutes les 3 secondes');
console.log('🛑 Ctrl+C pour arrêter');
console.log('');

let attempts = 0;
const maxAttempts = 20; // 1 minute maximum

async function checkSupabaseReady() {
  try {
    attempts++;
    const supabase = createClient(
      'https://kvdrukmoxetoiojazukf.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
    );

    process.stdout.write('🔍 Tentative ' + attempts + '/' + maxAttempts + '... ');

    const { data, error } = await supabase
      .from('professionals')
      .select('id')
      .limit(1);

    if (error && error.message.includes('does not exist')) {
      console.log('❌ Tables non trouvées');
      
      if (attempts >= maxAttempts) {
        console.log('\n⏰ Timeout atteint. Arrêt de la surveillance.');
        console.log('🔧 Exécutez manuellement: node automation-complete.js');
        process.exit(0);
      }
      
      setTimeout(checkSupabaseReady, 3000);
    } else if (error) {
      console.log('❌ Erreur:', error.message);
      setTimeout(checkSupabaseReady, 3000);
    } else {
      console.log('✅ TABLES DÉTECTÉES !');
      console.log('\n🎉 SQL exécuté avec succès !');
      console.log('⚡ Lancement automatique de l\'insertion des données...\n');
      
      // Lancer automatiquement l'insertion
      const { exec } = require('child_process');
      exec('node automation-complete.js', (error, stdout, stderr) => {
        if (error) {
          console.error('❌ Erreur lors de l\'automatisation:', error.message);
        } else {
          console.log(stdout);
          console.log('\n✅ AUTOMATISATION TERMINÉE !');
          console.log('🌐 Testez maintenant: http://localhost:3001/test-appointments-supabase');
        }
      });
    }

  } catch (e) {
    console.log('❌ Erreur de connexion:', e.message);
    setTimeout(checkSupabaseReady, 3000);
  }
}

// Démarrer la surveillance
checkSupabaseReady();
