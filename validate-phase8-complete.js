#!/usr/bin/env node

/**
 * 🧪 VALIDATION COMPLÈTE PHASE 8 - MINDFLOW PRO
 * Test automatisé de toutes les fonctionnalités avant déploiement production
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');

class Phase8Validator {
    constructor() {
        this.startTime = Date.now();
        this.testResults = [];
        this.frontendPort = 3003;
        this.backendPort = 4000;
        console.log('🧪 VALIDATION PHASE 8 - MINDFLOW PRO');
        console.log('🎯 Objectif: Valider toutes les fonctionnalités avant production');
        console.log('=' .repeat(60));
    }

    async execute() {
        try {
            await this.cleanup();
            await this.checkServices();
            await this.runTests();
            await this.generateReport();
            
            if (this.allTestsPassed()) {
                await this.deployToProduction();
                console.log('✅ VALIDATION RÉUSSIE - DÉPLOIEMENT PRODUCTION LANCÉ');
            } else {
                console.log('❌ VALIDATION ÉCHOUÉE - CORRECTION NÉCESSAIRE');
            }
            
        } catch (error) {
            console.error('❌ Erreur validation:', error.message);
        }
    }

    async cleanup() {
        console.log('🧹 Nettoyage...');
        try {
            execSync(`lsof -ti:${this.frontendPort} | xargs kill -9 2>/dev/null || true`, { stdio: 'ignore' });
            execSync(`lsof -ti:${this.backendPort} | xargs kill -9 2>/dev/null || true`, { stdio: 'ignore' });
            execSync('cd frontend && rm -rf .next', { stdio: 'ignore' });
        } catch (e) {}
    }

    async checkServices() {
        console.log('🔍 Vérification des services...');
        
        // Vérifier si les services sont déjà en cours d'exécution
        try {
            const frontendResponse = await fetch(`http://localhost:${this.frontendPort}`);
            const backendResponse = await fetch(`http://localhost:${this.backendPort}/api/v1/health`);
            
            if (frontendResponse.ok && backendResponse.ok) {
                console.log('✅ Services déjà actifs !');
                return;
            }
        } catch (e) {
            console.log('⚠️ Services non actifs, démarrage nécessaire');
        }
    }

    async runTests() {
        console.log('🧪 Exécution des tests...');
        
        const pages = [
            { url: '/', name: 'Accueil' },
            { url: '/dashboard', name: 'Dashboard' },
            { url: '/appointments', name: 'Rendez-vous' },
            { url: '/ai-coach', name: 'AI Coach' },
            { url: '/analytics', name: 'Analytics' },
            { url: '/professionals', name: 'Professionnels' },
            { url: '/compliance', name: 'Conformité' },
            { url: '/integrations-b2b', name: 'Intégrations B2B' },
            { url: '/telemedicine-advanced', name: 'Télémédecine' },
            { url: '/monitoring-dashboard', name: 'Monitoring' }
        ];

        for (const page of pages) {
            await this.testPage(page.url, page.name);
        }
    }

    async testPage(url, name) {
        try {
            const fullUrl = `http://localhost:${this.frontendPort}${url}`;
            const response = await fetch(fullUrl);
            
            const result = {
                test: name,
                url: url,
                status: response.ok ? 'PASS' : 'FAIL',
                statusCode: response.status,
                timestamp: new Date().toISOString()
            };

            this.testResults.push(result);
            
            const emoji = response.ok ? '✅' : '❌';
            console.log(`  ${emoji} ${name} (${response.status})`);
            
        } catch (error) {
            const result = {
                test: name,
                url: url,
                status: 'ERROR',
                error: error.message,
                timestamp: new Date().toISOString()
            };
            
            this.testResults.push(result);
            console.log(`  ❌ ${name} (ERROR: ${error.message})`);
        }
    }

    async generateReport() {
        console.log('\n📊 GÉNÉRATION DU RAPPORT...');
        
        const passedTests = this.testResults.filter(r => r.status === 'PASS').length;
        const failedTests = this.testResults.filter(r => r.status === 'FAIL').length;
        const errorTests = this.testResults.filter(r => r.status === 'ERROR').length;
        const totalTests = this.testResults.length;
        const successRate = Math.round((passedTests / totalTests) * 100);

        console.log('\n' + '='.repeat(60));
        console.log('📊 RAPPORT DE VALIDATION PHASE 8');
        console.log('='.repeat(60));
        console.log(`✅ Tests réussis: ${passedTests}/${totalTests} (${successRate}%)`);
        console.log(`❌ Tests échoués: ${failedTests}`);
        console.log(`⚠️  Erreurs: ${errorTests}`);

        if (failedTests > 0 || errorTests > 0) {
            console.log('\n❌ DÉTAILS DES ÉCHECS:');
            this.testResults
                .filter(r => r.status !== 'PASS')
                .forEach(r => {
                    console.log(`  - ${r.test}: ${r.status} ${r.error || r.statusCode || ''}`);
                });
        }
    }

    allTestsPassed() {
        const successRate = (this.testResults.filter(r => r.status === 'PASS').length / this.testResults.length) * 100;
        return successRate >= 80; // 80% minimum pour déploiement
    }

    async deployToProduction() {
        console.log('\n🚀 DÉPLOIEMENT PRODUCTION...');
        
        try {
            execSync('git add .', { stdio: 'inherit' });
            execSync('git commit -m "✅ Phase 8 Validated - Ready for Production" --allow-empty', { stdio: 'inherit' });
            execSync('git push origin main', { stdio: 'inherit' });
            execSync('cd frontend && npx vercel --prod --yes', { stdio: 'inherit' });
            
            console.log('✅ DÉPLOIEMENT PRODUCTION RÉUSSI !');
            
        } catch (error) {
            console.error('❌ Erreur déploiement:', error.message);
        }
    }
}

// Exécution
if (require.main === module) {
    const validator = new Phase8Validator();
    validator.execute().catch(console.error);
}

module.exports = Phase8Validator; 