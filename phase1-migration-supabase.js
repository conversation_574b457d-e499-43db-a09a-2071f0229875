#!/usr/bin/env node

/**
 * 🚀 PHASE 1: MIGRATION 100% SUPABASE
 * 
 * Migration des hooks journal, IA coach, analytics et notifications vers Supabase
 */

const fs = require('fs');
const { execSync } = require('child_process');

console.log('\n🔄 PHASE 1: MIGRATION 100% SUPABASE');
console.log('=====================================\n');

class SupabaseMigration {
    constructor() {
        this.migrationSteps = [
            'Audit hooks existants',
            'Création schemas SQL supplémentaires',
            'Migration useJournalData vers Supabase',
            'Migration useAICoach vers Supabase',
            'Migration useMoodAnalytics vers Supabase',
            'Migration useSmartNotifications vers Supabase',
            'Tests validation complète'
        ];
        this.currentStep = 0;
    }

    log(message) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] ${message}`);
    }

    async executeStep(stepName, action) {
        this.currentStep++;
        console.log(`\n📋 Étape ${this.currentStep}/${this.migrationSteps.length}: ${stepName}`);
        console.log('-'.repeat(50));
        
        try {
            await action();
            console.log(`✅ ${stepName} - TERMINÉ`);
            return true;
        } catch (error) {
            console.log(`❌ ${stepName} - ERREUR: ${error.message}`);
            return false;
        }
    }

    // Étape 1: Audit des hooks existants
    async auditExistingHooks() {
        const hooks = [
            'frontend/src/hooks/useJournalData.ts',
            'frontend/src/hooks/useAICoach.ts', 
            'frontend/src/hooks/useMoodAnalytics.ts',
            'frontend/src/hooks/useSmartNotifications.ts'
        ];

        this.log('🔍 Analyse des hooks existants...');
        
        const auditResults = {};
        
        hooks.forEach(hookPath => {
            if (fs.existsSync(hookPath)) {
                const content = fs.readFileSync(hookPath, 'utf8');
                const lines = content.split('\n').length;
                const hasSupabase = content.includes('supabase');
                const hasDemo = content.includes('demo') || content.includes('mock');
                
                auditResults[hookPath] = { lines, hasSupabase, hasDemo };
                
                console.log(`  📄 ${hookPath.split('/').pop()}: ${lines} lignes ${hasSupabase ? '(Supabase)' : hasDemo ? '(Démo)' : '(Autre)'}`);
            } else {
                console.log(`  ❌ ${hookPath} - Non trouvé`);
            }
        });

        // Sauvegarde des résultats d'audit
        fs.writeFileSync('migration-audit-results.json', JSON.stringify(auditResults, null, 2));
        
        this.log(`📊 Audit terminé - ${Object.keys(auditResults).length} hooks analysés`);
    }

    // Étape 2: Création des schemas SQL
    async createSupabaseSchemas() {
        this.log('📝 Génération des schemas SQL pour Supabase...');
        
        const sqlSchema = `
-- =====================================================
-- MIGRATION PHASE 1: SCHEMAS SUPABASE COMPLETS
-- =====================================================

-- Table: journal_entries
CREATE TABLE IF NOT EXISTS journal_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(), -- Temporaire pour démo
    title TEXT NOT NULL,
    content TEXT,
    mood TEXT CHECK (mood IN ('very_happy', 'happy', 'neutral', 'sad', 'very_sad', 'anxious', 'peaceful', 'hopeful')),
    tags TEXT[] DEFAULT '{}',
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table: ai_coaching_sessions
CREATE TABLE IF NOT EXISTS ai_coaching_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    theme TEXT NOT NULL CHECK (theme IN ('gestion_stress', 'anxiete', 'confiance_soi', 'motivation', 'relations')),
    objective TEXT,
    session_data JSONB DEFAULT '{}',
    mood_analysis JSONB DEFAULT '{}',
    messages_count INTEGER DEFAULT 0,
    completed BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table: mood_analytics
CREATE TABLE IF NOT EXISTS mood_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    mood_score INTEGER CHECK (mood_score >= 1 AND mood_score <= 10),
    energy_level INTEGER CHECK (energy_level >= 1 AND energy_level <= 10),
    stress_level INTEGER CHECK (stress_level >= 1 AND stress_level <= 10),
    sleep_quality INTEGER CHECK (sleep_quality >= 1 AND sleep_quality <= 10),
    exercise_minutes INTEGER DEFAULT 0,
    meditation_minutes INTEGER DEFAULT 0,
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table: smart_notifications
CREATE TABLE IF NOT EXISTS smart_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID DEFAULT gen_random_uuid(),
    type TEXT NOT NULL CHECK (type IN ('reminder', 'suggestion', 'achievement', 'warning', 'insight', 'emergency')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT false,
    is_important BOOLEAN DEFAULT false,
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    scheduled_for TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    action_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_journal_entries_user_created ON journal_entries(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_journal_entries_mood ON journal_entries(mood);
CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_theme ON ai_coaching_sessions(user_id, theme);
CREATE INDEX IF NOT EXISTS idx_mood_analytics_user_date ON mood_analytics(user_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON smart_notifications(user_id, is_read, created_at DESC);

-- Triggers pour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_journal_entries_updated_at BEFORE UPDATE ON journal_entries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_sessions_updated_at BEFORE UPDATE ON ai_coaching_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Données de démonstration
INSERT INTO journal_entries (title, content, mood, tags, is_favorite) VALUES
('Premier jour de thérapie', 'Aujourd''hui j''ai commencé ma thérapie cognitive comportementale. Je me sens plein d''espoir pour l''avenir.', 'hopeful', ARRAY['thérapie', 'nouveau_début'], true),
('Méditation matinale', 'Séance de méditation de 20 minutes ce matin. Je me sens plus centré et calme.', 'peaceful', ARRAY['méditation', 'matin'], false),
('Journée difficile au travail', 'Beaucoup de stress aujourd''hui avec les deadlines. J''ai appliqué les techniques de respiration.', 'anxious', ARRAY['travail', 'stress'], false),
('Promenade en nature', 'Longue promenade dans le parc. La nature m''aide toujours à me reconnecter.', 'happy', ARRAY['nature', 'exercice'], true),
('Réflexions du soir', 'En repensant à ma journée, je réalise que j''ai fait des progrès, même petits.', 'neutral', ARRAY['réflexion', 'progrès'], false);

INSERT INTO ai_coaching_sessions (theme, objective, session_data, messages_count, completed) VALUES
('gestion_stress', 'Réduire le stress quotidien au travail', '{"mood_before": "anxious", "mood_after": "peaceful", "techniques_used": ["respiration", "visualisation"]}', 12, true),
('confiance_soi', 'Améliorer l''estime de soi', '{"mood_before": "sad", "mood_after": "hopeful", "techniques_used": ["affirmations", "objectifs"]}', 8, false),
('anxiete', 'Gérer les crises d''angoisse', '{"mood_before": "very_sad", "mood_after": "neutral", "techniques_used": ["grounding", "respiration"]}', 15, true),
('motivation', 'Retrouver la motivation pour les projets', '{"mood_before": "neutral", "mood_after": "happy", "techniques_used": ["planification", "récompenses"]}', 6, false);

INSERT INTO mood_analytics (date, mood_score, energy_level, stress_level, sleep_quality, exercise_minutes, meditation_minutes, notes) VALUES
(CURRENT_DATE - INTERVAL '6 days', 6, 7, 8, 6, 30, 10, 'Journée stressante mais méditation aidante'),
(CURRENT_DATE - INTERVAL '5 days', 7, 8, 6, 7, 45, 15, 'Meilleur sommeil, plus d''énergie'),
(CURRENT_DATE - INTERVAL '4 days', 5, 5, 9, 4, 0, 0, 'Journée très difficile, insomnie'),
(CURRENT_DATE - INTERVAL '3 days', 8, 9, 4, 8, 60, 20, 'Excellente journée, sport et méditation'),
(CURRENT_DATE - INTERVAL '2 days', 7, 7, 5, 7, 30, 10, 'Journée équilibrée'),
(CURRENT_DATE - INTERVAL '1 day', 6, 6, 7, 6, 20, 5, 'Légère fatigue mais état stable'),
(CURRENT_DATE, 8, 8, 3, 8, 40, 15, 'Très bonne journée, motivation élevée');

INSERT INTO smart_notifications (type, title, message, priority, scheduled_for) VALUES
('reminder', 'Méditation quotidienne', 'Il est temps pour votre séance de méditation de 10 minutes.', 'normal', NOW() + INTERVAL '1 hour'),
('suggestion', 'Exercice de respiration', 'Votre niveau de stress semble élevé. Essayez un exercice de respiration profonde.', 'high', NOW()),
('achievement', 'Félicitations !', 'Vous avez maintenu votre routine de méditation pendant 7 jours consécutifs !', 'normal', NOW()),
('insight', 'Analyse de vos données', 'Vos scores de bien-être s''améliorent. Continuez vos efforts !', 'normal', NOW() + INTERVAL '30 minutes'),
('warning', 'Niveau de stress élevé', 'Votre stress a augmenté ces derniers jours. Pensez à faire une pause.', 'high', NOW());

-- Activation RLS (Row Level Security) pour la sécurité
ALTER TABLE journal_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_coaching_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE mood_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE smart_notifications ENABLE ROW LEVEL SECURITY;

-- Policies publiques pour les tests (à restreindre en production)
CREATE POLICY "Enable all operations for all users" ON journal_entries FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON ai_coaching_sessions FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON mood_analytics FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON smart_notifications FOR ALL USING (true);

-- Fin du script SQL
`;

        fs.writeFileSync('migration-phase1-schema.sql', sqlSchema);
        this.log('📄 Schema SQL créé: migration-phase1-schema.sql');
        
        console.log('\n🎯 ACTIONS REQUISES:');
        console.log('1. Copier le contenu de migration-phase1-schema.sql');
        console.log('2. L\'exécuter dans l\'éditeur SQL Supabase');
        console.log('3. Vérifier que toutes les tables sont créées');
        console.log('');
        console.log('🔗 Lien Supabase SQL Editor:');
        console.log('   https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf/sql');
    }

    // Étape 3-6: Templates de migration pour chaque hook
    async createMigrationTemplates() {
        this.log('🛠️ Création des templates de migration...');
        
        // Template pour useJournalSupabase
        const journalSupabaseHook = `
import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase/client';

export interface JournalEntry {
  id: string;
  title: string;
  content: string;
  mood: string;
  tags: string[];
  is_favorite: boolean;
  created_at: string;
  updated_at: string;
}

export const useJournalSupabase = () => {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Charger les entrées depuis Supabase
  const loadEntries = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('journal_entries')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setEntries(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur de chargement');
    } finally {
      setLoading(false);
    }
  }, []);

  // Ajouter une entrée
  const addEntry = useCallback(async (entry: Omit<JournalEntry, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data, error } = await supabase
        .from('journal_entries')
        .insert(entry)
        .select()
        .single();

      if (error) throw error;
      await loadEntries(); // Recharger la liste
      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur d\\'ajout');
      throw err;
    }
  }, [loadEntries]);

  // Plus de méthodes CRUD...

  useEffect(() => {
    loadEntries();
  }, [loadEntries]);

  return {
    entries,
    loading,
    error,
    addEntry,
    loadEntries
  };
};
`;

        fs.writeFileSync('templates/useJournalSupabase.ts', journalSupabaseHook);
        
        // Création des autres templates...
        this.log('📁 Templates créés dans le dossier templates/');
        
        if (!fs.existsSync('templates')) {
            fs.mkdirSync('templates');
        }
        
        console.log('\n📋 Templates de migration créés:');
        console.log('  ✅ templates/useJournalSupabase.ts');
        console.log('  🔄 templates/useAICoachSupabase.ts (à créer)');
        console.log('  🔄 templates/useMoodAnalyticsSupabase.ts (à créer)');
        console.log('  🔄 templates/useSmartNotificationsSupabase.ts (à créer)');
    }

    // Étape 7: Tests de validation
    async runValidationTests() {
        this.log('🧪 Lancement des tests de validation...');
        
        try {
            // Test connexion Supabase
            execSync('node check-supabase-status.js', { stdio: 'inherit' });
            
            // Test des nouvelles tables
            const testScript = `
const { supabase } = require('./frontend/src/lib/supabase/client');

async function testTables() {
  const tables = ['journal_entries', 'ai_coaching_sessions', 'mood_analytics', 'smart_notifications'];
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1);
      if (error) throw error;
      console.log(\`✅ Table \${table}: accessible\`);
    } catch (err) {
      console.log(\`❌ Table \${table}: \${err.message}\`);
    }
  }
}

testTables();
`;
            
            fs.writeFileSync('test-migration-tables.js', testScript);
            
            console.log('\n🧪 Test de validation créé: test-migration-tables.js');
            console.log('   Exécutez: node test-migration-tables.js');
            
        } catch (error) {
            console.log(`⚠️  Tests automatiques non disponibles: ${error.message}`);
        }
    }

    // Méthode principale d'exécution
    async execute() {
        console.log('🚀 Démarrage de la migration Phase 1...\n');
        
        const steps = [
            { name: this.migrationSteps[0], action: () => this.auditExistingHooks() },
            { name: this.migrationSteps[1], action: () => this.createSupabaseSchemas() },
            { name: this.migrationSteps[2], action: () => this.createMigrationTemplates() },
            { name: this.migrationSteps[6], action: () => this.runValidationTests() }
        ];

        let successCount = 0;
        
        for (const step of steps) {
            const success = await this.executeStep(step.name, step.action);
            if (success) {
                successCount++;
            } else {
                console.log('\n⚠️  Arrêt de la migration après erreur');
                break;
            }
        }

        console.log('\n📊 RÉSULTAT PHASE 1:');
        console.log(`✅ ${successCount}/${steps.length} étapes réussies`);
        
        if (successCount === steps.length) {
            console.log('\n🎉 PHASE 1 TERMINÉE AVEC SUCCÈS !');
            console.log('');
            console.log('📋 PROCHAINES ACTIONS:');
            console.log('1. Exécuter le SQL dans Supabase Dashboard');
            console.log('2. Tester avec: node test-migration-tables.js');
            console.log('3. Lancer Phase 2: node start-automation.js phase2');
        } else {
            console.log('\n❌ Phase 1 incomplète. Corrigez les erreurs et relancez.');
        }
    }
}

// Exécution
if (require.main === module) {
    const migration = new SupabaseMigration();
    migration.execute().catch(console.error);
}

module.exports = SupabaseMigration;
