#!/usr/bin/env node

/**
 * 🚀 DÉPLOIEMENT AUTOMATIQUE - MINDFLOW PRO
 * Script complet de déploiement vers production
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  projectName: 'MindFlow Pro',
  frontendDir: './frontend',
  backendDir: './backend',
  requiredEnvVars: [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ]
};

// Couleurs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = (msg, color = colors.reset) => console.log(`${color}${msg}${colors.reset}`);
const success = (msg) => log(`✅ ${msg}`, colors.green);
const error = (msg) => log(`❌ ${msg}`, colors.red);
const info = (msg) => log(`ℹ️  ${msg}`, colors.blue);
const warning = (msg) => log(`⚠️  ${msg}`, colors.yellow);

// Étapes de déploiement
const deploymentSteps = [
  {
    name: 'Vérification de l\'environnement',
    action: checkEnvironment
  },
  {
    name: 'Tests automatiques',
    action: runTests
  },
  {
    name: 'Build Frontend',
    action: buildFrontend
  },
  {
    name: 'Optimisation des assets',
    action: optimizeAssets
  },
  {
    name: 'Création du bundle de production',
    action: createProductionBundle
  },
  {
    name: 'Déploiement sur Vercel',
    action: deployToVercel
  },
  {
    name: 'Vérification post-déploiement',
    action: postDeploymentCheck
  }
];

// Vérifier l'environnement
async function checkEnvironment() {
  info('Vérification de l\'environnement...');
  
  // Vérifier Node.js
  const nodeVersion = process.version;
  if (parseInt(nodeVersion.split('.')[0].substring(1)) < 18) {
    throw new Error('Node.js 18+ requis');
  }
  success(`Node.js ${nodeVersion} détecté`);
  
  // Vérifier les variables d'environnement
  const envPath = path.join(CONFIG.frontendDir, '.env.local');
  if (!fs.existsSync(envPath)) {
    throw new Error('.env.local manquant dans frontend/');
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  for (const varName of CONFIG.requiredEnvVars) {
    if (!envContent.includes(varName)) {
      throw new Error(`Variable ${varName} manquante`);
    }
  }
  success('Variables d\'environnement vérifiées');
  
  return true;
}

// Exécuter les tests
async function runTests() {
  info('Exécution des tests...');
  
  return new Promise((resolve, reject) => {
    exec('node test-automatique-complet.js', (error, stdout, stderr) => {
      if (error) {
        error(`Tests échoués: ${error.message}`);
        reject(error);
        return;
      }
      
      // Vérifier le taux de réussite
      const successMatch = stdout.match(/Taux de réussite: ([\d.]+)%/);
      if (successMatch) {
        const successRate = parseFloat(successMatch[1]);
        if (successRate < 80) {
          reject(new Error(`Taux de réussite insuffisant: ${successRate}%`));
          return;
        }
        success(`Tests réussis avec ${successRate}% de réussite`);
      }
      
      resolve(true);
    });
  });
}

// Build Frontend
async function buildFrontend() {
  info('Build du frontend...');
  
  return new Promise((resolve, reject) => {
    exec('cd frontend && npm run build', (error, stdout, stderr) => {
      if (error) {
        error(`Build échoué: ${error.message}`);
        reject(error);
        return;
      }
      
      // Vérifier la taille du build
      const buildDir = path.join(CONFIG.frontendDir, '.next');
      if (fs.existsSync(buildDir)) {
        success('Build frontend terminé');
        resolve(true);
      } else {
        reject(new Error('Dossier de build introuvable'));
      }
    });
  });
}

// Optimiser les assets
async function optimizeAssets() {
  info('Optimisation des assets...');
  
  // Créer un fichier de configuration pour l'optimisation
  const optimizationConfig = {
    images: {
      quality: 85,
      formats: ['webp', 'avif']
    },
    css: {
      minify: true,
      purge: true
    },
    js: {
      minify: true,
      treeshake: true
    }
  };
  
  fs.writeFileSync('optimization-config.json', JSON.stringify(optimizationConfig, null, 2));
  success('Configuration d\'optimisation créée');
  
  return true;
}

// Créer le bundle de production
async function createProductionBundle() {
  info('Création du bundle de production...');
  
  // Créer un manifeste de déploiement
  const manifest = {
    name: CONFIG.projectName,
    version: '1.0.0',
    buildDate: new Date().toISOString(),
    environment: 'production',
    features: {
      supabase: true,
      realtime: true,
      auth: true,
      monitoring: true
    }
  };
  
  fs.writeFileSync('deployment-manifest.json', JSON.stringify(manifest, null, 2));
  success('Manifeste de déploiement créé');
  
  return true;
}

// Déployer sur Vercel
async function deployToVercel() {
  info('Déploiement sur Vercel...');
  
  // Vérifier si Vercel CLI est installé
  return new Promise((resolve, reject) => {
    exec('vercel --version', (error) => {
      if (error) {
        warning('Vercel CLI non installé');
        info('Instructions de déploiement manuel:');
        console.log('\n1. Installer Vercel CLI: npm i -g vercel');
        console.log('2. Se connecter: vercel login');
        console.log('3. Déployer: vercel --prod\n');
        resolve(true);
      } else {
        // Déployer avec Vercel
        exec('cd frontend && vercel --prod --yes', (error, stdout) => {
          if (error) {
            reject(error);
          } else {
            success('Déploiement Vercel réussi');
            console.log(stdout);
            resolve(true);
          }
        });
      }
    });
  });
}

// Vérification post-déploiement
async function postDeploymentCheck() {
  info('Vérification post-déploiement...');
  
  // Créer un rapport de déploiement
  const report = {
    deploymentDate: new Date().toISOString(),
    status: 'success',
    steps: deploymentSteps.map(s => s.name),
    nextSteps: [
      'Vérifier l\'application en production',
      'Configurer le monitoring',
      'Activer les alertes',
      'Tester les fonctionnalités critiques'
    ]
  };
  
  fs.writeFileSync('deployment-report.json', JSON.stringify(report, null, 2));
  success('Rapport de déploiement créé');
  
  return true;
}

// Fonction principale
async function deploy() {
  log('\n🚀 DÉPLOIEMENT AUTOMATIQUE - MINDFLOW PRO\n', colors.blue);
  
  const startTime = Date.now();
  let currentStep = 0;
  
  try {
    for (const step of deploymentSteps) {
      currentStep++;
      log(`\n[${currentStep}/${deploymentSteps.length}] ${step.name}`, colors.yellow);
      await step.action();
    }
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    
    log('\n' + '='.repeat(60), colors.green);
    log('🎉 DÉPLOIEMENT TERMINÉ AVEC SUCCÈS !', colors.green);
    log('='.repeat(60), colors.green);
    log(`\nDurée totale: ${duration} secondes`);
    
    // Instructions finales
    log('\n📋 PROCHAINES ÉTAPES:', colors.blue);
    console.log('\n1. Vérifier l\'application en production');
    console.log('2. Tester toutes les fonctionnalités critiques');
    console.log('3. Configurer le monitoring (voir /monitoring-dashboard)');
    console.log('4. Activer les notifications d\'alerte');
    console.log('5. Partager l\'URL de production avec l\'équipe\n');
    
  } catch (err) {
    error(`\nÉchec à l'étape ${currentStep}: ${err.message}`);
    log('\n💡 Consultez les logs pour plus de détails', colors.yellow);
    process.exit(1);
  }
}

// Lancer le déploiement
if (require.main === module) {
  deploy().catch(err => {
    error(`Erreur fatale: ${err.message}`);
    process.exit(1);
  });
}

module.exports = { deploy }; 