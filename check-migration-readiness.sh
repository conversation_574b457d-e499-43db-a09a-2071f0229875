#!/bin/bash

echo "🔍 Vérification Finale - MindFlow Pro Migration Supabase"
echo "================================================"

# Vérifier les fichiers essentiels
echo "📁 Fichiers de migration:"
ls -la setup-supabase.js supabase-schema.sql PROCHAINES_ETAPES_SUPABASE.md 2>/dev/null && echo "✅ Scripts disponibles" || echo "❌ Scripts manquants"

# Vérifier architecture Supabase
echo -e "
🏗️  Architecture Supabase:"
ls -la frontend/src/lib/supabase/ frontend/src/lib/database/ frontend/src/lib/migration/ 2>/dev/null && echo "✅ Architecture complète" || echo "❌ Architecture incomplète"

# Vérifier dépendances
echo -e "
📦 Dépendances Supabase:"
cd frontend && npm list @supabase/supabase-js @supabase/ssr 2>/dev/null | grep -E "@supabase" && echo "✅ Dépendances installées" || echo "❌ Dépendances manquantes"

# Status serveurs
echo -e "
🌐 Status Serveurs:"
lsof -i :3000 >/dev/null 2>&1 && echo "✅ Frontend (port 3000): En cours" || echo "⚠️  Frontend (port 3000): Arrêté"
lsof -i :4000 >/dev/null 2>&1 && echo "✅ Backend (port 4000): En cours" || echo "⚠️  Backend (port 4000): Arrêté"

echo -e "
🎯 PROCHAINES ÉTAPES:"
echo "1. node setup-supabase.js"
echo "2. Exécuter supabase-schema.sql"
echo "3. Configurer Authentication URLs"
echo "4. Activer DUAL_DATABASE_MODE=true"
echo "5. node validate-mindflow-system.js"

echo -e "
✨ SYSTÈME PRÊT POUR MIGRATION SUPABASE !"

