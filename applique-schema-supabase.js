#!/usr/bin/env node

/**
 * 🚀 APPLICATION AUTOMATIQUE DU SCHÉMA EN SUPABASE
 * 📅 28 Décembre 2024
 * 🎯 Déploiement de l'architecture centralisée
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 APPLICATION DU SCHÉMA PRINCIPAL EN SUPABASE');
console.log('🎯 Déploiement de l\'architecture centralisée');
console.log('=' .repeat(60));

class DeploymentSupabase {
    constructor() {
        this.supabaseUrl = 'https://kvdrukmoxetoiojazukf.supabase.co';
        this.projectId = 'kvdrukmoxetoiojazukf';
    }

    async deployer() {
        console.log('📊 1. Lecture du schéma principal...');
        const schema = fs.readFileSync('schema-principal.sql', 'utf8');
        console.log('✅ Schéma chargé (' + schema.split('\n').length + ' lignes)');

        console.log('\n🔗 2. Configuration Supabase...');
        console.log('   URL: ' + this.supabaseUrl);
        console.log('   Projet: ' + this.projectId);

        console.log('\n📝 3. Instructions d\'application:');
        console.log('   a) Ouvrir Supabase Dashboard:');
        console.log('      https://supabase.com/dashboard/project/' + this.projectId + '/sql');
        console.log('   b) Coller le contenu de schema-principal.sql');
        console.log('   c) Exécuter le script SQL');

        console.log('\n🔄 4. Création du script de validation...');
        this.creerScriptValidation();

        console.log('\n🎯 5. Mise à jour des variables d\'environnement...');
        this.mettreAJourEnv();

        console.log('\n✅ DÉPLOIEMENT CONFIGURÉ !');
        console.log('🚀 Prochaine étape: Appliquer le schéma en Supabase');
    }

    creerScriptValidation() {
        const scriptValidation = `#!/usr/bin/env node

/**
 * 🧪 VALIDATION SCHEMA SUPABASE
 * 📅 Script de validation automatique
 */

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://kvdrukmoxetoiojazukf.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function validerSchema() {
    console.log('🧪 VALIDATION DU SCHÉMA SUPABASE');
    console.log('=' .repeat(50));

    const tables = [
        'users',
        'professionals', 
        'appointments',
        'journal_entries',
        'ai_coach_sessions',
        'mood_analytics',
        'smart_notifications'
    ];

    let tablesValides = 0;

    for (const table of tables) {
        try {
            const { data, error } = await supabase
                .from(table)
                .select('*')
                .limit(1);

            if (error) {
                console.log('❌ Table ' + table + ': ' + error.message);
            } else {
                console.log('✅ Table ' + table + ': OK');
                tablesValides++;
            }
        } catch (err) {
            console.log('❌ Table ' + table + ': Erreur de connexion');
        }
    }

    console.log('\\n📊 RÉSULTAT:');
    console.log('   Tables validées: ' + tablesValides + '/' + tables.length);
    
    if (tablesValides === tables.length) {
        console.log('🎉 SCHÉMA APPLIQUÉ AVEC SUCCÈS !');
        console.log('✅ Architecture centralisée opérationnelle');
    } else {
        console.log('⚠️  Certaines tables manquent');
        console.log('🔧 Vérifier l\'application du schéma en Supabase');
    }
}

validerSchema().catch(console.error);
`;

        fs.writeFileSync('valider-schema.js', scriptValidation);
        console.log('   ✅ Script de validation créé: valider-schema.js');
    }

    mettreAJourEnv() {
        const envContent = `# 🗄️ CONFIGURATION BASE DE DONNÉES PRINCIPALE
# 📅 Mise à jour automatique ${new Date().toLocaleDateString('fr-FR')}

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ

# Database Configuration
NEXT_PUBLIC_USE_SUPABASE_DATABASE=true
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=false

# Principal Database Mode
NEXT_PUBLIC_PRINCIPAL_DATABASE_MODE=true
NEXT_PUBLIC_AUTO_SYNC_ENABLED=true
NEXT_PUBLIC_REAL_TIME_ENABLED=true
`;

        fs.writeFileSync('frontend/.env.local', envContent);
        console.log('   ✅ Variables d\'environnement mises à jour');
    }
}

new DeploymentSupabase().deployer();
