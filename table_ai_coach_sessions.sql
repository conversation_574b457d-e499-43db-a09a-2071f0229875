
                CREATE TABLE IF NOT EXISTS ai_coach_sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    session_type TEXT NOT NULL,
                    objective TEXT,
                    messages JSONB DEFAULT '[]',
                    sentiment_analysis JSONB DEFAULT '{}',
                    mood_before INTEGER CHECK (mood_before >= 1 AND mood_before <= 10),
                    mood_after INTEGER CHECK (mood_after >= 1 AND mood_after <= 10),
                    duration_minutes INTEGER DEFAULT 0,
                    status TEXT CHECK (status IN ('active', 'completed', 'paused')) DEFAULT 'active',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                