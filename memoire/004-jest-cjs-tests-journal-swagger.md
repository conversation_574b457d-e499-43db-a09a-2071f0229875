# Mémoire 004 – Corrections Jest, isolation tests backend, Swagger journal, tests unitaires

## Corrections Jest & TypeScript
- Vérification du champ `type` dans `package.json` backend (pas de "module").
- Ajout/forçage de `"module": "commonjs"` dans `tsconfig.json`.
- Isolation des tests backend dans `jest.config.js` (`testMatch`, `testPathIgnorePatterns` pour ignorer frontend/e2e).

## Enrichissement Swagger
- Ajout d'annotations Swagger sur les endpoints du journal (`src/routes/journal.ts`).
- Ajout des schémas `JournalEntry` et `JournalEntryInput` dans `swagger.ts`.

## Tests unitaires
- Ajout d'un test Jest pour `JournalService` (structure, promesse, mock DB simple).

## Prochaines étapes
- Corriger les linter errors sur les tests si besoin (méthodes manquantes ou à mocker).
- Continuer l'enrichissement Swagger (appointments, etc.).
- Ajouter des tests unitaires plus poussés (mock DB, cas d'erreur, etc.). 