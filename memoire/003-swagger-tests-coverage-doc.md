# Mémoire 003 – Swagger enrichi, tests unitaires, couverture, documentation

## Fonctionnalités implémentées

- **Enrichissement de la documentation Swagger** :
  - Ajout d'annotations Swagger sur les endpoints d'authentification (`auth.routes.ts`).
  - Définition des schémas principaux (User, UserRegister, UserLogin, AuthResponse, UserResponse) dans la config OpenAPI (`swagger.ts`).
  - Documentation interactive accessible sur `/api-docs`.

- **Ajout de tests unitaires sur les services principaux** :
  - Création d'un test Jest pour `AuthService` (hashPassword, comparePassword, structure register/login).

- **Génération de la couverture de tests** :
  - Lancement de la commande `npx jest --coverage` (échec partiel à cause de la config Jest/Node v22, à corriger pour la CI).
  - Rapport de couverture généré (0% car erreurs de parsing TypeScript, à corriger dans la config Jest).

- **Documentation technique** :
  - Complétion du `README.md` backend (démarrage, structure, endpoints, tests, Swagger, sécurité, TODO).

## Points à améliorer / TODO
- Corriger la configuration Jest pour supporter TypeScript (voir erreurs de parsing et modules ES).
- Ajouter des tests unitaires pour tous les services et middlewares.
- Continuer l'enrichissement de la doc Swagger (autres modèles/endpoints).
- Générer la couverture de tests régulièrement et viser >80%. 