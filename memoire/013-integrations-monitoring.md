# Étape : Monitoring, alertes et statistiques pour les paiements + WebSocket

## Fichiers modifiés
- backend/src/controllers/PaymentController.ts
- backend/src/routes/payments.ts

## Résumé des modifications
- Ajout de logs (info, error) lors de la création de paiement, des webhooks et des abonnements.
- Ajout d’une route GET `/payments/stats` pour obtenir des statistiques sur les paiements (total, somme, par statut).
- Emission d’un événement WebSocket `monitoring_alert` lors de la création d’un paiement ou d’une erreur critique, pour un monitoring temps réel côté frontend.

## Exemple d’usage / endpoints

### Créer un paiement
POST `/api/v1/payments/create`
```json
{
  "userId": 1,
  "amount": 49.99,
  "type": "abonnement"
}
```

### Statistiques paiements
GET `/api/v1/payments/stats`
Réponse :
```json
{
  "total": 10,
  "sum": 499.90,
  "byStatus": [
    { "status": "succeeded", "count": 8 },
    { "status": "failed", "count": 2 }
  ]
}
```

### WebSocket (événement monitoring_alert)
À chaque création de paiement ou erreur critique, un événement est émis :
```json
{
  "type": "paiement",
  "message": "Nouveau paiement créé",
  "userId": 1,
  "amount": 49.99,
  "paymentIntentId": "pi_123..."
}
```
Ou en cas d’erreur :
```json
{
  "type": "paiement",
  "message": "Erreur lors de la création du paiement",
  "error": "..."
}
``` 