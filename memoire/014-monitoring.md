# Étape : Monitoring global (logs, alertes, stats, audit, WebSocket, instance globale)

## Fichiers modifiés
- backend/src/controllers/MonitoringController.ts
- backend/src/routes/monitoring.ts
- backend/src/routes/index.ts
- backend/src/services/websocketInstance.ts (nouveau)
- backend/src/server.ts
- backend/src/middleware/errorHandler.ts

## Résumé des modifications
- Création d'un contrôleur et de routes pour le monitoring global :
  - GET `/monitoring/logs` (logs mockés)
  - GET `/monitoring/audit` (audit trail mocké)
  - GET `/monitoring/stats` (stats globales mockées)
- Ajout d'une instance globale de WebSocketService accessible dans tout le backend.
- Emission d'un événement WebSocket `monitoring_alert` sur chaque erreur serveur critique (middleware d'erreur).
- Intégration des routes de monitoring dans le routeur principal.

## Exemple d'usage / endpoints

### Logs globaux
GET `/api/v1/monitoring/logs`
Réponse :
```json
{
  "logs": [
    { "level": "info", "message": "Serveur démarré", "timestamp": "..." },
    { "level": "error", "message": "Erreur critique", "timestamp": "..." }
  ]
}
```

### Audit trail
GET `/api/v1/monitoring/audit`
Réponse :
```json
{
  "audit": [
    { "action": "login", "userId": 1, "timestamp": "..." },
    { "action": "paiement", "userId": 2, "timestamp": "..." }
  ]
}
```

### Statistiques globales
GET `/api/v1/monitoring/stats`
Réponse :
```json
{
  "users": 100,
  "paiements": 25,
  "erreurs": 2,
  "uptime": "24h"
}
```

### WebSocket (événement monitoring_alert)
À chaque erreur serveur critique, un événement est émis :
```json
{
  "type": "global",
  "message": "Erreur serveur",
  "error": "...",
  "url": "/api/v1/endpoint",
  "method": "POST",
  "timestamp": "..."
}
``` 