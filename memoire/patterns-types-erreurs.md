# Guide de référence : Typage des entités et gestion d'erreur (Frontend)

## 1. Centralisation des types métiers

- Tous les types d'entités métiers (User, JournalEntry, Appointment, etc.) sont définis dans `frontend/src/types/`.
- Cha<PERSON> entité a son propre fichier (ex : `User.ts`, `Appointment.ts`), exportant une interface principale.
- Le fichier `index.ts` centralise tous les exports pour simplifier l'importation dans le reste du projet.

**Exemple d'import :**
```ts
import type { User, Appointment } from '@/types';
```

## 2. Typage des réponses API

- Utiliser le type générique `ApiResponse<T>` pour tous les retours de services API.
- Ce type garantit la présence d'un booléen `success`, d'un message optionnel, et de la donnée typée.

**Exemple :**
```ts
export interface ApiResponse<T = unknown> {
  success: boolean;
  message?: string;
  data?: T;
}
```

## 3. Pattern de gestion d'erreur dans les services

- Toujours vérifier la présence de la donnée avant d'y accéder.
- Retourner un objet d'erreur standardisé si la donnée est absente ou invalide.

**Exemple de méthode API :**
```ts
async getUser(id: string): Promise<ApiResponse<User>> {
  const response = await this.api.get<ApiResponse<User>>(`/users/${id}`);
  const data = response.data;
  if (data && data.success) {
    return data;
  }
  return {
    success: false,
    message: 'Réponse du serveur invalide.',
    data: undefined,
  };
}
```

## 4. Gestion des erreurs Axios

- Utiliser la méthode utilitaire `extractErrorMessage` pour obtenir un message lisible à partir d'une erreur Axios.

**Exemple :**
```ts
static extractErrorMessage(error: unknown): string {
  if (axios.isAxiosError(error)) {
    return (
      error.response?.data?.message ||
      error.response?.data?.error ||
      error.message ||
      'Erreur inconnue.'
    );
  }
  if (error instanceof Error) {
    return error.message;
  }
  return 'Erreur inconnue.';
}
```

## 5. Linter et typage strict

- La règle ESLint `@typescript-eslint/no-unnecessary-condition` est assouplie pour ne pas signaler d'erreur sur les conditions déjà vérifiées.
- Préférer la désactivation locale de la règle pour les cas spécifiques.
- Toujours garder le typage strict TypeScript pour éviter les erreurs réelles ailleurs dans le code.

---

**Ce guide doit être complété et adapté au fil de l'évolution du projet.** 