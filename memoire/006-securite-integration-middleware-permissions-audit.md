# Mémoire 006 – Sécurité : intégration middleware permissions & audit, corrections, prochaines étapes

## Fonctionnalités intégrées
- **Middlewares d’audit (`auditTrail`)** ajoutés sur toutes les routes sensibles (auth, user, journal, professionnels, IA coach, modules bien-être).
- **Contrôle d’accès par permissions (`requirePermission`)** ajouté sur toutes les opérations critiques (CRUD, actions sensibles), basé sur les permissions du rôle utilisateur (`user.role.permissions`).
- **Correction des linter errors** :
  - Signature des middlewares Express (toujours `void`).
  - Utilisation du type `AuthenticatedRequest` pour accéder à `req.user`.
  - Vérification des permissions via `user.role.permissions` (tableau d’objets Permission avec `.name`).

## Points validés
- Les routes critiques sont maintenant protégées par l’authentification, le contrôle d’accès fin et l’audit trail.
- Le code est conforme aux types TypeScript et à la logique de sécurité attendue.

## À faire / TODO
- Compléter la couverture de tests (tests d’intégration pour les nouveaux contrôles d’accès et l’audit).
- Enrichir la documentation Swagger (security schemes, permissions, exemples d’erreurs 403/401, audit).
- Poursuivre avec les modules suivants : notifications, WebSocket, IA coach (fonctionnalités avancées), modules bien-être enrichis, intégrations tierces (Stripe, etc.), monitoring.

## Importance
Ce point d’étape garantit la robustesse de la sécurité applicative et la traçabilité des actions. Il sert de base solide pour la suite du développement.

# Étape : Patch massif Express/TypeScript strict – Handlers, typage, req.user

## Fichiers modifiés
- `src/controllers/AuthController.ts`
- `src/middlewares/auth.ts`
- `src/routes/auth.routes.ts`
- `src/types/express/index.d.ts`

## Résumé
- Suppression de tout `return` dans les handlers critiques (enable2FA, verify2FA, disable2FA, etc.)
- Application stricte du typage `RequestHandler` sur tous les exports de handlers
- Correction de l'import du modèle User et de la sauvegarde via TypeORM
- Ajout d'une déclaration de type pour `req.user` dans Express
- Correction du middleware d'authentification pour charger l'utilisateur complet et ne jamais retourner de valeur
- Correction des routes pour n'utiliser que des handlers typés
- Correction des appels à qrcode et speakeasy pour garantir des arguments typés string
- Correction de l'appel à refreshToken (utilisation de la bonne méthode)

## Exemples avant/après

**Avant**
```ts
export const enable2FA = authController.enable2FA.bind(authController); // type implicite
// ...
return res.json(...);
```

**Après**
```ts
export const enable2FA: RequestHandler = authController.enable2FA.bind(authController);
// ...
res.json(...); // sans return
```

**Avant (middleware)**
```ts
export function authMiddleware(req, res, next) {
  // ...
  return res.status(401).json(...);
}
```

**Après**
```ts
export async function authMiddleware(req, res, next) {
  // ...
  res.status(401).json(...);
  return;
}
```

**Avant (req.user)**
```ts
const user = req.user;
user.twoFASecret = ...;
await user.save();
```

**Après**
```ts
const user = req.user;
if (!user) { res.status(401).json(...); return; }
user.twoFASecret = ...;
await getRepository(User).save(user);
```

---

**Compilation relancée :**
- Les erreurs sur les handlers critiques sont corrigées.
- Il reste des erreurs sur d'autres modules (Notification, Payment, etc.) à traiter dans les étapes suivantes. 