# Mémoire 005 – Sécurité avancée : 2FA, permissions, audit

## 2FA (Two-Factor Authentication)
- Champs `is2FAEnabled`, `twoFASecret` ajoutés au modèle User.
- Endpoints `/auth/enable-2fa`, `/auth/verify-2fa`, `/auth/disable-2fa` (QR code, TOTP, speakeasy).
- Documentation Swagger complète.
- Tests unitaires (TOTP) et d’intégration (flow 2FA).

## Permissions
- Middleware `requirePermission` pour contrôle d’accès granulaire.
- Tests unitaires du middleware (cas success/erreur).

## Audit trail
- Middleware `auditTrail` pour loguer les actions sensibles.
- Tests unitaires du middleware (vérification du log et du next).

## À faire/valider
- Intégrer le middleware d’audit sur les routes sensibles.
- Étendre le contrôle d’accès à tous les modules critiques.
- Continuer la couverture de tests et la documentation. 