{"name": "mindflow-pro", "version": "1.0.0", "description": "MindFlow Pro - Full Stack Application", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "test:server-comm": "playwright test tests/server-communication.spec.ts", "test:dashboard": "playwright test tests/dashboard-functionality.spec.ts", "test:api": "playwright test tests/api-integration.spec.ts", "test:e2e": "playwright test tests/end-to-end.spec.ts", "test:all": "playwright test --workers=1", "start:servers": "concurrently \"cd backend && npm run dev\" \"cd frontend && npm run dev\"", "stop:servers": "pkill -f 'node.*enhanced-server' && pkill -f 'next dev'"}, "devDependencies": {"@playwright/test": "^1.53.1", "@types/jest": "^30.0.0", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "concurrently": "^8.2.2", "jest": "^30.0.3", "supertest": "^7.1.1", "ts-jest": "^29.4.0"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "next": "^15.4.0-canary.100", "node-fetch": "^2.7.0", "playwright": "^1.53.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "engines": {"node": ">=18.0.0"}, "packageManager": "yarn@4.9.2+sha512.1fc009bc09d13cfd0e19efa44cbfc2b9cf6ca61482725eb35bbc5e257e093ebf4130db6dfe15d604ff4b79efd8e1e8e99b25fa7d0a6197c9f9826358d4d65c3c"}