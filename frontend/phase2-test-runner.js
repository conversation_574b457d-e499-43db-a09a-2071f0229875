#!/usr/bin/env node

/**
 * 🚀 PHASE 2A - RUNNER DE TESTS SIMPLIFIÉS MINDFLOW PRO
 */

const { exec, spawn } = require('child_process');
const fs = require('fs');

function log(msg, type = 'info') {
  const colors = { info: '\x1b[36m', success: '\x1b[32m', error: '\x1b[31m', reset: '\x1b[0m' };
  console.log(`${colors[type]}[PHASE2] ${msg}${colors.reset}`);
}

async function checkServer() {
  return new Promise((resolve) => {
    exec('curl -s http://localhost:3000', (error) => {
      resolve(!error);
    });
  });
}

async function runSimpleTests() {
  log('🔍 Lancement tests simplifiés...', 'info');
  
  return new Promise((resolve) => {
    const testProcess = spawn('npx', [
      'playwright', 'test', 
      'tests/e2e/critical-user-flows.spec.ts',
      '--project=desktop-chrome',
      '--timeout=30000',
      '--reporter=line'
    ], {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    testProcess.on('close', (code) => {
      resolve(code === 0);
    });
  });
}

async function main() {
  log('🚀 Démarrage Phase 2A Tests', 'info');
  
  // Vérifier serveur
  const serverReady = await checkServer();
  if (!serverReady) {
    log('❌ Serveur non accessible - Démarrez avec: npm run dev', 'error');
    process.exit(1);
  }
  
  log('✅ Serveur détecté', 'success');
  
  // Tests simplifiés
  const testsOk = await runSimpleTests();
  
  if (testsOk) {
    log('✅ Tests Phase 2A réussis', 'success');
    process.exit(0);
  } else {
    log('⚠️ Tests avec erreurs', 'error');
    process.exit(1);
  }
}

main().catch(console.error); 