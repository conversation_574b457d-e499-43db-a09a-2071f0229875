-- 🗄️ SCRIPT SQL COMPLET POUR SUPABASE
-- Copier-coller ce script entier dans l'éditeur SQL Supabase

-- =====================================================
-- ÉTAPE 1: SUPPRESSION DES TABLES EXISTANTES (OPTIONNEL)
-- =====================================================

DROP TABLE IF EXISTS appointments CASCADE;
DROP TABLE IF EXISTS professionals CASCADE;

-- =====================================================
-- ÉTAPE 2: CRÉATION DES TABLES
-- =====================================================

-- Table des professionnels
CREATE TABLE professionals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    specialties TEXT[] DEFAULT '{}',
    price_per_session DECIMAL(10,2) DEFAULT 0,
    location TEXT,
    bio TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table des rendez-vous
CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    professional_id UUID REFERENCES professionals(id) ON DELETE CASCADE,
    professional_name TEXT NOT NULL,
    professional_role TEXT,
    client_id TEXT NOT NULL,
    client_name TEXT,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    type TEXT DEFAULT 'video',
    status TEXT DEFAULT 'scheduled',
    price DECIMAL(10,2),
    currency TEXT DEFAULT 'EUR',
    notes TEXT,
    meeting_link TEXT,
    reminder_sent BOOLEAN DEFAULT FALSE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- ÉTAPE 3: CRÉATION DES INDEX
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_professional ON appointments(professional_id);
CREATE INDEX IF NOT EXISTS idx_appointments_client ON appointments(client_id);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_professionals_email ON professionals(email);

-- =====================================================
-- ÉTAPE 4: FONCTION ET TRIGGERS POUR updated_at
-- =====================================================

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers
CREATE TRIGGER update_professionals_updated_at 
    BEFORE UPDATE ON professionals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at 
    BEFORE UPDATE ON appointments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ÉTAPE 5: INSERTION DES DONNÉES DE TEST
-- =====================================================

-- Insertion des professionnels
INSERT INTO professionals (name, role, email, specialties, price_per_session, location, bio) VALUES
('Dr. Sophie Martin', 'Psychologue clinicienne', '<EMAIL>', 
 ARRAY['Thérapie cognitive comportementale', 'Gestion du stress', 'Anxiété'], 
 85.00, '15 rue de la Paix, 75001 Paris', 
 'Spécialisée en thérapie cognitive comportementale avec plus de 10 ans d''expérience dans le traitement des troubles anxieux.'),

('Dr. Jean Dupont', 'Psychiatre', '<EMAIL>', 
 ARRAY['Psychiatrie générale', 'Troubles bipolaires', 'Dépression'], 
 120.00, '42 avenue Victor Hugo, 69003 Lyon', 
 'Psychiatre expérimenté spécialisé dans les troubles de l''humeur et la prise en charge des troubles bipolaires.'),

('Marie Leblanc', 'Thérapeute comportementale', '<EMAIL>', 
 ARRAY['Thérapie comportementale', 'Phobies', 'Addictions'], 
 75.00, '8 place Bellecour, 69002 Lyon', 
 'Thérapeute spécialisée dans les troubles comportementaux et le traitement des phobies spécifiques.'),

('Dr. Ahmed Benali', 'Psychothérapeute', '<EMAIL>', 
 ARRAY['Psychothérapie humaniste', 'Thérapie de couple', 'Traumatismes'], 
 90.00, '25 cours Mirabeau, 13100 Aix-en-Provence', 
 'Psychothérapeute avec une approche humaniste centrée sur la personne et spécialisé en thérapie de couple.');

-- Insertion des rendez-vous de test
-- Récupérer les IDs des professionnels pour les rendez-vous
WITH prof_ids AS (
  SELECT id, name, role, price_per_session FROM professionals
)
INSERT INTO appointments (
  professional_id, professional_name, professional_role, client_id, client_name,
  appointment_date, appointment_time, duration_minutes, type, status, price, currency, notes, meeting_link, reminder_sent, rating, feedback
)
SELECT 
  p.id,
  p.name,
  p.role,
  'demo-user-' || ROW_NUMBER() OVER (ORDER BY p.name),
  'Client Démo ' || ROW_NUMBER() OVER (ORDER BY p.name),
  CURRENT_DATE + (ROW_NUMBER() OVER (ORDER BY p.name) || ' days')::interval,
  '14:00'::time,
  60,
  'video',
  'scheduled',
  p.price_per_session,
  'EUR',
  'Rendez-vous de test avec ' || p.name,
  'https://meet.mindflow.com/session-' || p.id,
  false,
  NULL,
  NULL
FROM prof_ids p;

-- Ajouter quelques rendez-vous supplémentaires avec différents statuts
WITH prof_data AS (
  SELECT id, name, role, price_per_session, 
         ROW_NUMBER() OVER (ORDER BY name) as rn 
  FROM professionals
)
INSERT INTO appointments (
  professional_id, professional_name, professional_role, client_id, client_name,
  appointment_date, appointment_time, duration_minutes, type, status, price, currency, notes, rating, feedback
)
SELECT 
  p.id,
  p.name,
  p.role,
  'demo-user-' || (p.rn + 10),
  'Client Expérience ' || (p.rn + 10),
  CURRENT_DATE + (p.rn + 7 || ' days')::interval,
  CASE p.rn 
    WHEN 1 THEN '09:00'::time
    WHEN 2 THEN '16:00'::time
    WHEN 3 THEN '11:30'::time
    ELSE '15:00'::time
  END,
  60,
  CASE p.rn 
    WHEN 1 THEN 'in-person'
    WHEN 2 THEN 'phone'
    ELSE 'video'
  END,
  CASE p.rn 
    WHEN 1 THEN 'completed'
    WHEN 2 THEN 'cancelled'
    ELSE 'scheduled'
  END,
  p.price_per_session,
  'EUR',
  'Séance ' || CASE p.rn WHEN 1 THEN 'terminée' WHEN 2 THEN 'annulée' ELSE 'programmée' END,
  CASE p.rn WHEN 1 THEN 5 ELSE NULL END,
  CASE p.rn WHEN 1 THEN 'Excellente séance, très professionnel et à l''écoute.' ELSE NULL END
FROM prof_data p;

-- =====================================================
-- ÉTAPE 6: VUES UTILES (OPTIONNEL)
-- =====================================================

-- Vue des statistiques des rendez-vous
CREATE OR REPLACE VIEW appointments_stats AS
SELECT 
  COUNT(*) as total_appointments,
  COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as scheduled,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
  COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled,
  COUNT(CASE WHEN appointment_date >= CURRENT_DATE THEN 1 END) as upcoming,
  ROUND(AVG(rating), 2) as avg_rating
FROM appointments;

-- Vue des rendez-vous avec professionnels
CREATE OR REPLACE VIEW appointments_with_professionals AS
SELECT 
  a.id,
  a.appointment_date,
  a.appointment_time,
  a.duration_minutes,
  a.type,
  a.status,
  a.price,
  a.currency,
  a.notes,
  a.rating,
  a.feedback,
  p.name as professional_name,
  p.role as professional_role,
  p.email as professional_email,
  p.specialties,
  p.location,
  a.client_id,
  a.client_name,
  a.meeting_link,
  a.reminder_sent,
  a.created_at,
  a.updated_at
FROM appointments a
JOIN professionals p ON a.professional_id = p.id;

-- =====================================================
-- ÉTAPE 7: CONFIGURATION DES PERMISSIONS (RLS)
-- =====================================================

-- Activer Row Level Security
ALTER TABLE professionals ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre la lecture publique (pour les tests)
CREATE POLICY "Public read access" ON professionals
FOR SELECT USING (true);

CREATE POLICY "Public read access" ON appointments
FOR SELECT USING (true);

-- Politique pour permettre l'insertion publique (pour les tests)
CREATE POLICY "Public insert access" ON professionals
FOR INSERT WITH CHECK (true);

CREATE POLICY "Public insert access" ON appointments
FOR INSERT WITH CHECK (true);

-- Politique pour permettre la mise à jour publique (pour les tests)
CREATE POLICY "Public update access" ON professionals
FOR UPDATE USING (true);

CREATE POLICY "Public update access" ON appointments
FOR UPDATE USING (true);

-- Politique pour permettre la suppression publique (pour les tests)
CREATE POLICY "Public delete access" ON professionals
FOR DELETE USING (true);

CREATE POLICY "Public delete access" ON appointments
FOR DELETE USING (true);

-- =====================================================
-- ÉTAPE 8: REQUÊTES DE VALIDATION
-- =====================================================

-- Vérifier les données insérées
SELECT 'Professionnels insérés:' as info, COUNT(*) as count FROM professionals;
SELECT 'Rendez-vous insérés:' as info, COUNT(*) as count FROM appointments;

-- Afficher un échantillon des données
SELECT 'Échantillon professionnels:' as info;
SELECT name, role, email, price_per_session FROM professionals LIMIT 3;

SELECT 'Échantillon rendez-vous:' as info;
SELECT professional_name, appointment_date, appointment_time, status, type FROM appointments LIMIT 5;

-- Afficher les statistiques
SELECT * FROM appointments_stats;

-- Test de requête complexe
SELECT 'Test jointure:' as info;
SELECT 
  a.appointment_date,
  a.appointment_time,
  p.name as professionnel,
  a.status
FROM appointments a
JOIN professionals p ON a.professional_id = p.id
WHERE a.appointment_date >= CURRENT_DATE
ORDER BY a.appointment_date
LIMIT 3;

-- =====================================================
-- SUCCÈS ! 🎉
-- =====================================================
-- Si vous voyez ce message, le script s'est exécuté avec succès !
-- 
-- Prochaines étapes:
-- 1. Tester l'interface: http://localhost:3000/test-appointments-supabase
-- 2. Vérifier les données dans l'onglet "Table Editor"
-- 3. Intégrer dans votre application React
-- ===================================================== 