import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Configure redirects for old pages router routes
  async redirects() {
    return [
      {
        source: '/login',
        destination: '/auth/login',
        permanent: true,
      },
      {
        source: '/register',
        destination: '/auth/register',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
