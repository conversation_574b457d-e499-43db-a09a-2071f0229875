#!/usr/bin/env node

/**
 * 🚀 ACTIVATION PROGRESSIVE SUPABASE - PHASE 1
 * Script d'activation et test de Supabase pour MindFlow Pro
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 ACTIVATION SUPABASE - PHASE 1');
console.log('================================\n');

// Configuration .env.local
const envContent = `# Configuration Supabase - MindFlow Pro (Phase 1)
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ

# Clé service role
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4

# === PHASE 1: TESTS DE CONNECTIVITÉ ===
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true

NODE_ENV=development
`;

try {
  // 1. Créer .env.local
  fs.writeFileSync('.env.local', envContent);
  console.log('✅ 1. Fichier .env.local créé avec configuration Phase 1');

  // 2. Vérification structure
  const pages = [
    'src/app/test-nouvelles-cles',
    'src/app/test-supabase-simple', 
    'src/app/test-basic',
    'src/app/ultra-simple'
  ];

  console.log('✅ 2. Vérification pages de test :');
  pages.forEach(page => {
    if (fs.existsSync(page)) {
      console.log(`   ✅ ${page.split('/').pop()}`);
    } else {
      console.log(`   ❌ ${page.split('/').pop()} - MANQUANTE`);
    }
  });

  // 3. Instructions de test
  console.log('\n🧪 TESTS À EFFECTUER :');
  console.log('========================');
  console.log('1. Redémarrez le serveur : npm run dev');
  console.log('2. Testez les pages :');
  console.log('   📊 http://localhost:3000/test-nouvelles-cles');
  console.log('   🔍 http://localhost:3000/test-basic');
  console.log('   🚀 http://localhost:3000/ultra-simple');
  console.log('   📡 http://localhost:3000/test-supabase-simple');

  console.log('\n🎯 PROCHAINES ÉTAPES :');
  console.log('======================');
  console.log('Phase 2: Activer NEXT_PUBLIC_USE_SUPABASE_AUTH=true');
  console.log('Phase 3: Activer NEXT_PUBLIC_USE_SUPABASE_DATABASE=true');
  console.log('Phase 4: Migration données SQLite → Supabase');

} catch (error) {
  console.error('❌ Erreur:', error.message);
  process.exit(1);
} 