#!/usr/bin/env node

/**
 * 🚀 PHASE 2A - TESTS E2E AUTOMATISÉS MINDFLOW PRO
 * Script d'exécution des tests end-to-end avec gestion automatique du serveur
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  SERVER_PORT: 3000,
  SERVER_TIMEOUT: 60000, // 60 secondes
  TEST_TIMEOUT: 300000,  // 5 minutes
  RETRY_COUNT: 3
};

let serverProcess = null;
let isServerReady = false;

/**
 * Logger avec couleurs
 */
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Vert
    warning: '\x1b[33m', // Jaune
    error: '\x1b[31m',   // Rouge
    reset: '\x1b[0m'
  };
  
  const timestamp = new Date().toISOString().substr(11, 8);
  console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
}

/**
 * Vérifier si le port est libre
 */
function checkPort(port) {
  return new Promise((resolve) => {
    const { exec } = require('child_process');
    exec(`lsof -ti:${port}`, (error, stdout) => {
      resolve(!stdout.trim());
    });
  });
}

/**
 * Attendre que le serveur soit prêt
 */
function waitForServer() {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const checkInterval = 2000;
    
    const check = async () => {
      try {
        const response = await fetch(`http://localhost:${CONFIG.SERVER_PORT}`);
        if (response.ok || response.status === 404) {
          log('✅ Serveur Next.js prêt', 'success');
          isServerReady = true;
          resolve();
          return;
        }
      } catch (error) {
        // Server not ready yet
      }
      
      if (Date.now() - startTime > CONFIG.SERVER_TIMEOUT) {
        reject(new Error('Timeout: Serveur non accessible'));
        return;
      }
      
      setTimeout(check, checkInterval);
    };
    
    check();
  });
}

/**
 * Démarrer le serveur Next.js
 */
async function startServer() {
  log('🚀 Démarrage du serveur Next.js...', 'info');
  
  // Vérifier si le port est libre
  const isPortFree = await checkPort(CONFIG.SERVER_PORT);
  if (!isPortFree) {
    log(`⚠️ Port ${CONFIG.SERVER_PORT} déjà utilisé, tentative d'arrêt...`, 'warning');
    exec(`lsof -ti:${CONFIG.SERVER_PORT} | xargs kill -9`);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Démarrer le serveur
  serverProcess = spawn('npm', ['run', 'dev'], {
    stdio: ['ignore', 'pipe', 'pipe'],
    cwd: process.cwd()
  });
  
  serverProcess.stdout.on('data', (data) => {
    const output = data.toString();
    if (output.includes('Local:') || output.includes('Ready')) {
      log('📡 Next.js démarré', 'info');
    }
  });
  
  serverProcess.stderr.on('data', (data) => {
    const error = data.toString();
    if (!error.includes('warn') && !error.includes('Warning')) {
      log(`Erreur serveur: ${error}`, 'error');
    }
  });
  
  // Attendre que le serveur soit prêt
  try {
    await waitForServer();
    return true;
  } catch (error) {
    log(`❌ Échec démarrage serveur: ${error.message}`, 'error');
    return false;
  }
}

/**
 * Arrêter le serveur
 */
function stopServer() {
  if (serverProcess) {
    log('🛑 Arrêt du serveur Next.js...', 'info');
    serverProcess.kill('SIGTERM');
    
    setTimeout(() => {
      if (serverProcess && !serverProcess.killed) {
        serverProcess.kill('SIGKILL');
      }
    }, 5000);
  }
}

/**
 * Exécuter les tests Playwright
 */
function runPlaywrightTests() {
  return new Promise((resolve, reject) => {
    log('🎭 Lancement des tests Playwright...', 'info');
    
    const testCommand = [
      'npx', 'playwright', 'test',
      '--reporter=html',
      '--reporter=line',
      '--output-dir=test-results/phase2',
      '--project=desktop-chrome'
    ];
    
    const testProcess = spawn(testCommand[0], testCommand.slice(1), {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    testProcess.on('close', (code) => {
      if (code === 0) {
        log('✅ Tests Playwright terminés avec succès', 'success');
        resolve({ success: true, code });
      } else {
        log(`⚠️ Tests terminés avec code: ${code}`, 'warning');
        resolve({ success: false, code });
      }
    });
    
    testProcess.on('error', (error) => {
      log(`❌ Erreur tests: ${error.message}`, 'error');
      reject(error);
    });
    
    // Timeout de sécurité
    setTimeout(() => {
      testProcess.kill('SIGTERM');
      reject(new Error('Timeout des tests'));
    }, CONFIG.TEST_TIMEOUT);
  });
}

/**
 * Exécuter tests ciblés sur nouvelles fonctionnalités
 */
function runTargetedTests() {
  return new Promise((resolve, reject) => {
    log('🎯 Tests ciblés nouvelles fonctionnalités...', 'info');
    
    const testFiles = [
      'tests/e2e/critical-user-flows.spec.ts',
      'tests/e2e/performance-tests.spec.ts'
    ];
    
    const testCommand = [
      'npx', 'playwright', 'test',
      ...testFiles,
      '--reporter=line',
      '--project=desktop-chrome',
      '--timeout=60000'
    ];
    
    const testProcess = spawn(testCommand[0], testCommand.slice(1), {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    testProcess.on('close', (code) => {
      resolve({ success: code === 0, code });
    });
    
    testProcess.on('error', reject);
  });
}

/**
 * Générer rapport final
 */
function generateReport(results) {
  const report = {
    timestamp: new Date().toISOString(),
    phase: '2A - Tests E2E Automatisés',
    results: results,
    summary: {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      success_rate: Math.round((results.filter(r => r.success).length / results.length) * 100)
    }
  };
  
  fs.writeFileSync('test-results/phase2a-report.json', JSON.stringify(report, null, 2));
  
  log('\n📊 RAPPORT PHASE 2A - TESTS E2E:', 'info');
  log(`   Total: ${report.summary.total}`, 'info');
  log(`   Réussis: ${report.summary.passed}`, 'success');
  log(`   Échoués: ${report.summary.failed}`, report.summary.failed > 0 ? 'warning' : 'info');
  log(`   Taux de réussite: ${report.summary.success_rate}%`, 'info');
  
  return report;
}

/**
 * Fonction principale
 */
async function main() {
  log('🚀 DÉMARRAGE PHASE 2A - TESTS E2E MINDFLOW PRO', 'info');
  
  const results = [];
  
  try {
    // 1. Démarrer le serveur
    const serverStarted = await startServer();
    if (!serverStarted) {
      throw new Error('Impossible de démarrer le serveur');
    }
    
    // Attendre stabilisation
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 2. Tests ciblés d'abord (plus rapides)
    log('\n📋 Phase 1: Tests Ciblés', 'info');
    try {
      const targetedResult = await runTargetedTests();
      results.push({ test: 'Tests Ciblés', ...targetedResult });
      
      if (targetedResult.success) {
        log('✅ Tests ciblés réussis, lancement tests complets', 'success');
        
        // 3. Tests complets si tests ciblés OK
        log('\n📋 Phase 2: Tests Complets', 'info');
        const fullResult = await runPlaywrightTests();
        results.push({ test: 'Tests Complets', ...fullResult });
      } else {
        log('⚠️ Tests ciblés avec erreurs, skip tests complets', 'warning');
      }
    } catch (error) {
      log(`❌ Erreur tests: ${error.message}`, 'error');
      results.push({ test: 'Tests', success: false, error: error.message });
    }
    
  } catch (error) {
    log(`❌ Erreur critique: ${error.message}`, 'error');
    results.push({ test: 'Setup', success: false, error: error.message });
  } finally {
    // 4. Nettoyage
    stopServer();
    
    // 5. Générer rapport
    const report = generateReport(results);
    
    log('\n🎉 PHASE 2A TERMINÉE', 'info');
    log(`📊 Rapport sauvé: test-results/phase2a-report.json`, 'info');
    
    if (fs.existsSync('playwright-report/index.html')) {
      log(`📈 Rapport HTML: playwright-report/index.html`, 'info');
    }
    
    process.exit(report.summary.success_rate >= 50 ? 0 : 1);
  }
}

// Gestion des signaux pour nettoyage
process.on('SIGINT', () => {
  log('\n⚠️ Interruption détectée', 'warning');
  stopServer();
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('\n⚠️ Arrêt demandé', 'warning');
  stopServer();
  process.exit(1);
});

// Polyfill fetch pour Node.js < 18
if (!global.fetch) {
  global.fetch = require('node-fetch');
}

main().catch((error) => {
  log(`💥 Erreur fatale: ${error.message}`, 'error');
  stopServer();
  process.exit(1);
}); 