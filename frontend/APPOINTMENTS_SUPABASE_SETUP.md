# Configuration du Système de Rendez-vous avec Supabase

## 🎯 Objectif

Intégrer le système de rendez-vous de MindFlow Pro avec Supabase pour remplacer les données simulées par une vraie base de données.

## 📋 Prérequis

- ✅ Supabase configuré et fonctionnel
- ✅ Variables d'environnement Supabase définies
- ✅ Accès à l'interface Supabase SQL Editor

## 🚀 Étapes d'Installation

### 1. <PERSON><PERSON>er les Tables Supabase

Exécutez le script SQL suivant dans **Supabase SQL Editor** :

```sql
-- Script disponible dans: frontend/src/sql/appointments-schema.sql
-- À copier-coller dans l'interface Supabase
```

### 2. Vérifier l'Installation

Testez la page de vérification :
```bash
# Démarrer l'application
cd frontend
npm run dev

# Accéder à la page de test
http://localhost:3000/test-appointments-supabase
```

### 3. Basculer vers Supabase

Remplacez le hook existant :

```typescript
// Dans vos composants, remplacer :
import { useAppointmentsData } from '@/hooks/useAppointmentsData';

// Par :
import { useAppointmentsSupabase as useAppointmentsData } from '@/hooks/useAppointmentsSupabase';
```

## 📊 Structure des Tables

### Table `professionals`
- `id` : UUID (clé primaire)
- `name` : Nom du professionnel
- `role` : Rôle/spécialité
- `email` : Email de contact
- `price_per_session` : Tarif par séance
- `location` : Adresse du cabinet
- `specialties` : Array des spécialités

### Table `appointments`
- `id` : UUID (clé primaire)
- `professional_id` : Référence au professionnel
- `client_id` : ID du client
- `appointment_date` : Date du rendez-vous
- `appointment_time` : Heure du rendez-vous
- `duration_minutes` : Durée en minutes
- `type` : Type (video, in-person, chat)
- `status` : Statut (scheduled, confirmed, completed, cancelled, no-show)
- `price` : Prix de la consultation
- `notes` : Notes du rendez-vous

## 🔧 Hook useAppointmentsSupabase

Le nouveau hook offre les mêmes fonctionnalités que l'ancien :

```typescript
const {
  appointments,       // Liste des rendez-vous
  loading,           // État de chargement
  error,             // Erreurs éventuelles
  refreshAppointments // Fonction pour rafraîchir
} = useAppointmentsSupabase();
```

## 🧪 Tests

### Test de Connexion
```bash
node frontend/setup-appointments-supabase.js
```

### Test d'Interface
Visitez : `http://localhost:3000/test-appointments-supabase`

## 🔄 Migration Progressive

1. **Phase 1** : Installation et test (✅ Actuel)
2. **Phase 2** : Intégration dans les pages existantes
3. **Phase 3** : Fonctionnalités CRUD complètes
4. **Phase 4** : Optimisations et cache

## 📝 Fonctionnalités Prêtes

- ✅ Affichage des rendez-vous depuis Supabase
- ✅ Fallback vers données de démonstration
- ✅ Gestion des erreurs
- ✅ Interface de test
- ⏳ Création de nouveaux rendez-vous
- ⏳ Modification/annulation
- ⏳ Notifications temps réel

## 🐛 Résolution de Problèmes

### Erreur "Table does not exist"
1. Vérifiez que le SQL a été exécuté dans Supabase
2. Contrôlez les permissions RLS
3. Vérifiez les variables d'environnement

### Données vides
1. Le hook charge automatiquement des données de démonstration
2. Utilisez la page de test pour créer des données
3. Vérifiez les permissions Supabase

### Erreurs de connexion
1. Vérifiez `NEXT_PUBLIC_SUPABASE_URL`
2. Vérifiez `NEXT_PUBLIC_SUPABASE_ANON_KEY`
3. Testez la connexion Supabase basique

## 🎉 Prochaines Étapes

1. Valider le test d'intégration
2. Implémenter les fonctionnalités CRUD
3. Intégrer dans la page `/appointments`
4. Ajouter les notifications temps réel
5. Optimiser les performances

---

**Status**: ✅ Installation complète - Prêt pour les tests 

MIGRATION ULTRA-FIX TERMINÉE - Tables recréées avec succès !

journal_entries: 5 records
ai_coaching_sessions: 3 records  
mood_analytics: 7 records
smart_notifications: 5 records 