#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');

console.log('🚀 Configuration du système de rendez-vous Supabase');

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Variables d\'environnement Supabase manquantes');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testConnection() {
  try {
    const { data, error } = await supabase.from('users').select('id').limit(1);
    console.log('✅ Connexion Supabase OK');
    return true;
  } catch (error) {
    console.error('❌ Erreur connexion:', error);
    return false;
  }
}

async function checkExistingTables() {
  console.log('🔍 Vérification des tables existantes...');
  
  try {
    const { data: tables, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['professionals', 'appointments']);
    
    if (error) throw error;
    
    const existingTables = tables.map(t => t.table_name);
    console.log('📋 Tables existantes:', existingTables);
    
    return existingTables;
  } catch (error) {
    console.error('❌ Erreur vérification tables:', error.message);
    return [];
  }
}

async function createTablesDirectly() {
  console.log('🔧 Création des tables via méthodes directes...');
  
  try {
    // Créer la table professionals
    const { error: profError } = await supabase
      .from('professionals')
      .select('id')
      .limit(1);
    
    if (profError && profError.message.includes('does not exist')) {
      console.log('⚠️  Table professionals n\'existe pas');
      console.log('📝 Veuillez exécuter le SQL suivant dans l\'interface Supabase:');
      
      const sqlContent = await fs.readFile(
        path.join(__dirname, 'src/sql/appointments-schema.sql'),
        'utf8'
      );
      console.log('\n' + '='.repeat(50));
      console.log(sqlContent);
      console.log('='.repeat(50) + '\n');
      
      return false;
    }
    
    console.log('✅ Tables déjà présentes ou accessibles');
    return true;
  } catch (error) {
    console.error('❌ Erreur création tables:', error.message);
    return false;
  }
}

async function insertTestData() {
  console.log('📊 Insertion des données de test...');
  
  try {
    // Vérifier si des professionnels existent
    const { data: existingProfs, error: checkError } = await supabase
      .from('professionals')
      .select('id')
      .limit(1);
    
    if (checkError) throw checkError;
    
    if (existingProfs && existingProfs.length > 0) {
      console.log('ℹ️  Données de test déjà présentes');
      return true;
    }
    
    // Insérer des professionnels de test
    const { error: profError } = await supabase
      .from('professionals')
      .insert([
        {
          name: 'Dr. Sophie Martin',
          role: 'Psychologue clinicienne',
          email: '<EMAIL>',
          specialties: ['Thérapie cognitive comportementale', 'Gestion du stress'],
          price_per_session: 85.00,
          location: '15 rue de la Paix, Paris',
          bio: 'Spécialisée dans la thérapie cognitive comportementale.'
        },
        {
          name: 'Dr. Jean Dupont',
          role: 'Psychiatre',
          email: '<EMAIL>',
          specialties: ['Psychiatrie générale', 'Troubles bipolaires'],
          price_per_session: 120.00,
          location: '42 avenue Victor Hugo, Lyon',
          bio: 'Psychiatre expérimenté spécialisé dans les troubles de l\'humeur.'
        }
      ]);
    
    if (profError) throw profError;
    
    // Créer quelques rendez-vous de test
    const { data: professionals } = await supabase
      .from('professionals')
      .select('id, name, role, price_per_session')
      .limit(2);
    
    if (professionals && professionals.length > 0) {
      const appointments = [];
      
      for (let i = 0; i < professionals.length; i++) {
        const prof = professionals[i];
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + i + 1);
        
        appointments.push({
          professional_id: prof.id,
          professional_name: prof.name,
          professional_role: prof.role,
          client_id: 'demo-user-1',
          client_name: 'Utilisateur Démo',
          appointment_date: futureDate.toISOString().split('T')[0],
          appointment_time: '14:00',
          duration_minutes: 60,
          type: 'video',
          status: 'scheduled',
          price: prof.price_per_session || 80,
          currency: 'EUR',
          notes: `Rendez-vous de test avec ${prof.name}`,
          meeting_link: `https://meet.mindflow.com/session-${prof.id}`
        });
      }
      
      const { error: aptError } = await supabase
        .from('appointments')
        .insert(appointments);
      
      if (aptError) throw aptError;
    }
    
    console.log('✅ Données de test insérées avec succès');
    return true;
  } catch (error) {
    console.error('❌ Erreur insertion données:', error.message);
    return false;
  }
}

async function verifySetup() {
  console.log('🔍 Vérification finale...');
  
  try {
    const { count: profCount } = await supabase
      .from('professionals')
      .select('*', { count: 'exact', head: true });
    
    const { count: aptCount } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true });
    
    console.log(`📊 ${profCount || 0} professionnels dans la base`);
    console.log(`📅 ${aptCount || 0} rendez-vous dans la base`);
    
    if ((profCount || 0) > 0 && (aptCount || 0) > 0) {
      console.log('✅ Configuration complète et opérationnelle!');
      return true;
    } else {
      console.log('⚠️  Configuration incomplète');
      return false;
    }
  } catch (error) {
    console.error('❌ Erreur vérification:', error.message);
    return false;
  }
}

async function main() {
  const connected = await testConnection();
  if (connected) {
    console.log('🎉 Prêt à configurer les tables!');
  }
}

main(); 