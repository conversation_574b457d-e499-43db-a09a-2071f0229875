#!/usr/bin/env node

/**
 * Script de nettoyage et redémarrage pour MindFlow Pro
 * Corrige les erreurs webpack et les problèmes de cache Next.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧹 Nettoyage et redémarrage de MindFlow Pro...\n');

const frontendDir = path.join(__dirname);

// Fonction pour supprimer un dossier récursivement
function removeDirRecursive(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    console.log(`✅ Supprimé: ${dirPath}`);
    return true;
  }
  return false;
}

// Fonction pour tuer les processus Next.js
function killNextProcesses() {
  try {
    console.log('🔪 Arrêt des processus Next.js...');
    
    // Tuer tous les processus next sur macOS/Linux
    if (process.platform === 'darwin' || process.platform === 'linux') {
      try {
        execSync('pkill -f "next"', { stdio: 'ignore' });
        console.log('✅ Processus Next.js arrêtés');
      } catch (error) {
        console.log('ℹ️  Aucun processus Next.js à arrêter');
      }
    }
    
    // Tuer les processus sur les ports spécifiques
    const ports = [3000, 3001, 3002, 3010];
    ports.forEach(port => {
      try {
        execSync(`lsof -ti:${port} | xargs kill -9`, { stdio: 'ignore' });
        console.log(`✅ Port ${port} libéré`);
      } catch (error) {
        // Port déjà libre
      }
    });
    
  } catch (error) {
    console.log('ℹ️  Erreur lors de l\'arrêt des processus (normal si aucun processus actif)');
  }
}

// Étape 1: Arrêter les processus
killNextProcesses();

// Étape 2: Nettoyer les caches et fichiers temporaires
console.log('\n🧽 Nettoyage des caches...');

const pathsToClean = [
  '.next',
  'node_modules/.cache',
  '.vercel',
  'dist',
  'build'
];

let cleaned = 0;
pathsToClean.forEach(pathToClean => {
  const fullPath = path.join(frontendDir, pathToClean);
  if (removeDirRecursive(fullPath)) {
    cleaned++;
  }
});

console.log(`\n✨ ${cleaned} dossiers nettoyés`);

// Étape 3: Vérifier les dépendances
console.log('\n📦 Vérification des dépendances...');

try {
  // Vérifier package.json
  const packagePath = path.join(frontendDir, 'package.json');
  if (!fs.existsSync(packagePath)) {
    console.error('❌ package.json non trouvé!');
    process.exit(1);
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  console.log(`✅ Package: ${packageJson.name} v${packageJson.version}`);
  
  // Réinstaller les dépendances
  console.log('\n📥 Réinstallation des dépendances...');
  execSync('npm install', { 
    stdio: 'inherit', 
    cwd: frontendDir 
  });
  
  console.log('✅ Dépendances installées');
  
} catch (error) {
  console.error('❌ Erreur lors de l\'installation des dépendances:', error.message);
  process.exit(1);
}

// Étape 4: Vérifier la configuration Next.js
console.log('\n⚙️  Vérification de la configuration...');

const nextConfigPath = path.join(frontendDir, 'next.config.js');
if (fs.existsSync(nextConfigPath)) {
  console.log('✅ next.config.js trouvé');
} else {
  console.log('⚠️  next.config.js non trouvé - création d\'une configuration par défaut');
  const defaultConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  experimental: {
    appDir: true,
  },
};

module.exports = nextConfig;
`;
  fs.writeFileSync(nextConfigPath, defaultConfig);
  console.log('✅ Configuration Next.js créée');
}

// Étape 5: Lancer l'application
console.log('\n🚀 Démarrage de l\'application...');

try {
  console.log('📡 Démarrage sur http://localhost:3000');
  console.log('⏹️  Appuyez sur Ctrl+C pour arrêter\n');
  
  // Lancer Next.js en mode développement
  execSync('npm run dev', { 
    stdio: 'inherit', 
    cwd: frontendDir 
  });
  
} catch (error) {
  if (error.signal === 'SIGINT') {
    console.log('\n👋 Application arrêtée par l\'utilisateur');
  } else {
    console.error('❌ Erreur lors du démarrage:', error.message);
    process.exit(1);
  }
}

console.log('\n✅ Nettoyage et redémarrage terminés!'); 