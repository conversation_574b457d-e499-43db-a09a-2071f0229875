'use client';

import { useState } from 'react';
import { createBrowserClient } from '@supabase/ssr';

export default function TestCompletSupabase() {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [testResults, setTestResults] = useState<Record<string, string>>({});
  const [user, setUser] = useState<any>(null);

  // Client Supabase direct
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // Test de connexion basique
  const testConnection = async () => {
    setLoading(true);
    setMessage('Test de connexion en cours...');
    
    try {
      const { data, error } = await supabase.from('users').select('count');
      
      if (error && error.code === '42P01') {
        setMessage('✅ Connexion Supabase OK mais tables non créées');
        setTestResults(prev => ({ ...prev, connection: 'ok_no_tables' }));
      } else if (error) {
        setMessage(`❌ Erreur: ${error.message}`);
        setTestResults(prev => ({ ...prev, connection: 'error' }));
      } else {
        setMessage('✅ Connexion Supabase OK !');
        setTestResults(prev => ({ ...prev, connection: 'ok' }));
      }
    } catch (err) {
      setMessage(`❌ Erreur: ${err}`);
      setTestResults(prev => ({ ...prev, connection: 'error' }));
    }
    
    setLoading(false);
  };

  // Inscription simplifiée
  const testSignUp = async () => {
    setLoading(true);
    setMessage('Inscription en cours...');
    
    const email = `test${Date.now()}@mindflow.pro`;
    const password = 'Test123456!';
    
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: 'Test User'
          }
        }
      });
      
      if (error) {
        setMessage(`❌ Erreur inscription: ${error.message}`);
        setTestResults(prev => ({ ...prev, signup: 'error' }));
      } else {
        setMessage(`✅ Inscription réussie ! Email: ${email}`);
        setTestResults(prev => ({ ...prev, signup: 'ok', email }));
        setUser(data.user);
      }
    } catch (err) {
      setMessage(`❌ Erreur: ${err}`);
      setTestResults(prev => ({ ...prev, signup: 'error' }));
    }
    
    setLoading(false);
  };

  // Connexion avec credentials fixes
  const testLogin = async () => {
    setLoading(true);
    setMessage('Connexion en cours...');
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'Test123456!'
      });
      
      if (error) {
        setMessage(`❌ Erreur connexion: ${error.message}`);
        setTestResults(prev => ({ ...prev, login: 'error' }));
      } else {
        setMessage('✅ Connexion réussie !');
        setTestResults(prev => ({ ...prev, login: 'ok' }));
        setUser(data.user);
      }
    } catch (err) {
      setMessage(`❌ Erreur: ${err}`);
      setTestResults(prev => ({ ...prev, login: 'error' }));
    }
    
    setLoading(false);
  };

  // Création de tables
  const createTables = async () => {
    setLoading(true);
    setMessage('Création des tables...');
    
    try {
      // Créer une entrée mood
      const { error: moodError } = await supabase
        .from('mood_entries')
        .insert({
          mood_score: 8,
          mood_note: 'Test mood entry',
          user_id: user?.id || 'test-user-id'
        });
      
      if (moodError && moodError.code === '42P01') {
        setMessage('⚠️ Tables non existantes. Exécutez le schéma SQL dans Supabase Dashboard');
        setTestResults(prev => ({ ...prev, tables: 'not_exists' }));
      } else if (moodError) {
        setMessage(`❌ Erreur: ${moodError.message}`);
        setTestResults(prev => ({ ...prev, tables: 'error' }));
      } else {
        setMessage('✅ Tables fonctionnelles !');
        setTestResults(prev => ({ ...prev, tables: 'ok' }));
      }
    } catch (err) {
      setMessage(`❌ Erreur: ${err}`);
      setTestResults(prev => ({ ...prev, tables: 'error' }));
    }
    
    setLoading(false);
  };

  // Test complet automatique
  const runAllTests = async () => {
    setTestResults({});
    await testConnection();
    await new Promise(resolve => setTimeout(resolve, 1000));
    await testSignUp();
    await new Promise(resolve => setTimeout(resolve, 1000));
    if (testResults.signup === 'ok') {
      await createTables();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🧪 Test Complet Supabase - MindFlow Pro</h1>
        
        {/* Message Status */}
        <div className={`mb-6 p-4 rounded-lg ${
          message.includes('✅') ? 'bg-green-100 text-green-800' :
          message.includes('❌') ? 'bg-red-100 text-red-800' :
          message.includes('⚠️') ? 'bg-yellow-100 text-yellow-800' :
          'bg-blue-100 text-blue-800'
        }`}>
          {message || 'Prêt pour les tests'}
        </div>

        {/* Boutons de test */}
        <div className="grid grid-cols-2 gap-4 mb-8">
          <button
            onClick={testConnection}
            disabled={loading}
            className="p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            🔗 Tester Connexion
          </button>
          
          <button
            onClick={testSignUp}
            disabled={loading}
            className="p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            ✍️ Tester Inscription
          </button>
          
          <button
            onClick={testLogin}
            disabled={loading}
            className="p-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
          >
            🔐 Tester Connexion
          </button>
          
          <button
            onClick={createTables}
            disabled={loading || !user}
            className="p-4 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50"
          >
            📊 Tester Tables
          </button>
        </div>

        {/* Bouton test complet */}
        <button
          onClick={runAllTests}
          disabled={loading}
          className="w-full p-4 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 font-bold text-lg"
        >
          🚀 LANCER TOUS LES TESTS
        </button>

        {/* Résultats */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-bold mb-4">📊 Résultats des Tests</h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Connexion Supabase:</span>
              <span className={testResults.connection === 'ok' ? 'text-green-600' : 'text-gray-400'}>
                {testResults.connection === 'ok' ? '✅ OK' : 
                 testResults.connection === 'ok_no_tables' ? '⚠️ OK (sans tables)' :
                 testResults.connection === 'error' ? '❌ Erreur' : '⏳ En attente'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Inscription:</span>
              <span className={testResults.signup === 'ok' ? 'text-green-600' : 'text-gray-400'}>
                {testResults.signup === 'ok' ? '✅ OK' : 
                 testResults.signup === 'error' ? '❌ Erreur' : '⏳ En attente'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Connexion:</span>
              <span className={testResults.login === 'ok' ? 'text-green-600' : 'text-gray-400'}>
                {testResults.login === 'ok' ? '✅ OK' : 
                 testResults.login === 'error' ? '❌ Erreur' : '⏳ En attente'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Tables:</span>
              <span className={testResults.tables === 'ok' ? 'text-green-600' : 'text-gray-400'}>
                {testResults.tables === 'ok' ? '✅ OK' : 
                 testResults.tables === 'not_exists' ? '⚠️ Non créées' :
                 testResults.tables === 'error' ? '❌ Erreur' : '⏳ En attente'}
              </span>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-yellow-50 p-6 rounded-lg">
          <h3 className="font-bold mb-2">⚠️ Si les tables ne sont pas créées:</h3>
          <ol className="list-decimal list-inside space-y-1">
            <li>Allez sur <a href="https://app.supabase.com" className="text-blue-600 underline" target="_blank">app.supabase.com</a></li>
            <li>Connectez-vous à votre projet</li>
            <li>Allez dans SQL Editor</li>
            <li>Copiez le schéma depuis <a href="/test-supabase-schema" className="text-blue-600 underline" target="_blank">/test-supabase-schema</a></li>
            <li>Exécutez le SQL</li>
          </ol>
        </div>

        {/* Info utilisateur */}
        {user && (
          <div className="mt-4 bg-gray-100 p-4 rounded">
            <p className="text-sm">Utilisateur connecté: {user.email}</p>
          </div>
        )}
      </div>
    </div>
  );
} 