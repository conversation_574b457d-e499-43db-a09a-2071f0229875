'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Brain, CheckCircle, XCircle } from 'lucide-react';

export default function AuthTestPage() {
  const { user, loading, login, register, logout, isAuthenticated } = useAuth();
  const router = useRouter();
  
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('mindflow123');
  const [name, setName] = useState('Anderson Kouassi');
  const [results, setResults] = useState<string[]>([]);

  const addResult = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    setResults(prev => [`${emoji} ${message}`, ...prev.slice(0, 9)]);
  };

  const handleLogin = async () => {
    try {
      addResult('Tentative de connexion...');
      await login(email, password);
      addResult('Connexion réussie! 🎉', 'success');
    } catch (error: any) {
      addResult(`Erreur: ${error.message}`, 'error');
    }
  };

  const handleRegister = async () => {
    try {
      addResult('Tentative d\'inscription...');
      await register(name, email, password);
      addResult('Inscription réussie! 🎉', 'success');
    } catch (error: any) {
      addResult(`Erreur: ${error.message}`, 'error');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      addResult('Déconnexion réussie!', 'success');
    } catch (error: any) {
      addResult(`Erreur: ${error.message}`, 'error');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <Brain className="w-16 h-16 text-blue-600 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900">Test d'Authentification</h1>
          <p className="text-gray-600">MindFlow Pro - Supabase Auth</p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Statut */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Statut Utilisateur</h2>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>État:</span>
                <span className={`${loading ? 'text-yellow-600' : 'text-green-600'}`}>
                  {loading ? '⏳ Chargement...' : '✅ Prêt'}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span>Connecté:</span>
                <span className={`${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                  {isAuthenticated ? '✅ Oui' : '❌ Non'}
                </span>
              </div>

              {user && (
                <div className="mt-4 p-4 bg-green-50 rounded-lg">
                  <h3 className="font-semibold text-green-800">Informations:</h3>
                  <p className="text-sm text-green-700">Email: {user.user.email}</p>
                  <p className="text-sm text-green-700">Nom: {user.user.name}</p>
                </div>
              )}
            </div>
          </div>

          {/* Formulaire */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Actions</h2>
            
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Nom"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              
              <input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              
              <input
                type="password"
                placeholder="Mot de passe"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={handleRegister}
                  disabled={loading}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  S'inscrire
                </button>
                
                <button
                  onClick={handleLogin}
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  Se connecter
                </button>
              </div>

              {isAuthenticated && (
                <>
                  <button
                    onClick={handleLogout}
                    className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                  >
                    Se déconnecter
                  </button>
                  
                  <button
                    onClick={() => router.push('/dashboard')}
                    className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
                  >
                    Aller au Dashboard
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Résultats */}
        {results.length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Journal des Actions</h3>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {results.map((result, index) => (
                <div key={index} className="p-2 bg-gray-50 rounded text-sm font-mono">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">Comment tester:</h3>
          <ol className="text-blue-700 space-y-1">
            <li>1. Remplissez les champs (données de test pré-remplies)</li>
            <li>2. Cliquez sur "S'inscrire" pour créer un nouveau compte</li>
            <li>3. Ou cliquez sur "Se connecter" si le compte existe déjà</li>
            <li>4. Une fois connecté, naviguez vers le Dashboard</li>
            <li>5. Testez la déconnexion</li>
          </ol>
        </div>
      </div>
    </div>
  );
} 