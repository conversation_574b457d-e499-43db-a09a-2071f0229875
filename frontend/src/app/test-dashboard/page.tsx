'use client';

import { useState, useEffect } from 'react';

const mockWellnessStats = {
  currentMood: 8,
  energyLevel: 7,
  journalStreak: 7,
  completedPrograms: 3,
};

const mockRecentActivities = [
  {
    id: '1',
    type: 'mood' as const,
    title: 'Suivi d\'humeur',
    description: 'Humeur enregistrée : Bien (8/10)',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: '2',
    type: 'journal' as const,
    title: 'Entrée de journal',
    description: 'Nouvelle entrée : "Réflexions du matin"',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
  },
];

export default function TestDashboardPage() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1500);
    return () => clearTimeout(timer);
  }, []);

  const refreshData = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 1000);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto py-6 px-4">
          <div className="space-y-6 animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
              <div className="lg:col-span-8 h-40 bg-gray-200 rounded-lg"></div>
              <div className="lg:col-span-4 h-40 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4">
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">🧠 Dashboard MindFlow Pro</h1>
              <p className="text-gray-600 mt-1">Version de test - Données simulées</p>
            </div>
            <button
              onClick={refreshData}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              🔄 Actualiser
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
            <div className="lg:col-span-8">
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques de bien-être</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{mockWellnessStats.currentMood}/10</div>
                    <div className="text-sm text-gray-600">Humeur actuelle</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{mockWellnessStats.energyLevel}/10</div>
                    <div className="text-sm text-gray-600">Énergie</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{mockWellnessStats.journalStreak}</div>
                    <div className="text-sm text-gray-600">Jours consécutifs</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">{mockWellnessStats.completedPrograms}</div>
                    <div className="text-sm text-gray-600">Programmes terminés</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="lg:col-span-4">
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h3>
                <div className="space-y-3">
                  <button className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-left">
                    📝 Nouvelle entrée de journal
                  </button>
                  <button className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-left">
                    😊 Enregistrer mon humeur
                  </button>
                  <button className="w-full px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-left">
                    🧘 Session de méditation
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité récente</h3>
            <div className="space-y-4">
              {mockRecentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3 p-3 hover:bg-gray-50 rounded-lg">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    activity.type === 'mood' ? 'bg-blue-500' : 'bg-green-500'
                  }`}>
                    {activity.type === 'mood' ? '😊' : '📝'}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{activity.title}</h4>
                    <p className="text-sm text-gray-600">{activity.description}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      {new Date(activity.timestamp).toLocaleString('fr-FR')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">🎉 Dashboard MindFlow Pro fonctionnel !</h3>
              <p className="text-gray-600 mb-4">
                Cette version de test montre les fonctionnalités du dashboard avec des données simulées.
              </p>
              <div className="flex justify-center gap-4 text-sm">
                <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full">✅ Données en temps réel</span>
                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full">✅ Interface responsive</span>
                <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full">✅ Interactions fluides</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
