export default function UltraSimple() {
  const timestamp = new Date().toLocaleString('fr-FR');
  
  return (
    <div style={{ 
      fontFamily: 'Arial, sans-serif', 
      padding: '20px',
      minHeight: '100vh',
      backgroundColor: '#f0f8ff'
    }}>
      <h1 style={{ color: '#2563eb', fontSize: '2.5em' }}>
        🚀 Ultra Simple - FIXED! ✅
      </h1>
      <p style={{ fontSize: '18px', color: '#333' }}>
        Cette page utilise zéro dépendance externe et devrait maintenant fonctionner !
      </p>
      
      <div style={{ 
        backgroundColor: '#dcfce7', 
        padding: '20px', 
        borderRadius: '12px',
        marginTop: '20px',
        border: '2px solid #22c55e'
      }}>
        <h2>✅ Status - CORRIGÉ!</h2>
        <p><strong>Si vous voyez cette page, le problème est résolu ! 🎉</strong></p>
        <p>Cache nettoyé et page recompilée</p>
      </div>

      <div style={{ 
        backgroundColor: '#e0f2fe', 
        padding: '20px', 
        borderRadius: '12px',
        marginTop: '20px',
        border: '2px solid #0288d1'
      }}>
        <h2>🔧 Corrections Appliquées</h2>
        <ul style={{ fontSize: '16px' }}>
          <li>✅ Cache Next.js nettoyé</li>
          <li>✅ Serveurs multiples arrêtés</li>
          <li>✅ Page recompilée</li>
          <li>✅ Timestamp ajouté: {timestamp}</li>
        </ul>
      </div>

      <div style={{ 
        backgroundColor: '#fef3c7', 
        padding: '15px', 
        borderRadius: '8px',
        marginTop: '20px'
      }}>
        <h2>🔗 Navigation Test</h2>
        <ul>
          <li><a href="/" style={{ color: '#1d4ed8' }}>🏠 Accueil</a></li>
          <li><a href="/test-basic" style={{ color: '#1d4ed8' }}>🧪 Test Basic</a></li>
          <li><a href="/test-simple" style={{ color: '#1d4ed8' }}>🔍 Test Simple</a></li>
          <li><a href="/ultra-simple" style={{ color: '#1d4ed8' }}>⚡ Cette page (rechargez si nécessaire)</a></li>
        </ul>
      </div>

      <div style={{ 
        fontSize: '14px', 
        color: '#666',
        marginTop: '30px',
        borderTop: '2px solid #ddd',
        paddingTop: '15px',
        textAlign: 'center',
        backgroundColor: '#f8f9fa',
        padding: '15px',
        borderRadius: '8px'
      }}>
        <p><strong>🔧 Debug Info:</strong></p>
        <p>URL: /ultra-simple</p>
        <p>Port: localhost:3000 (ou autre port disponible)</p>
        <p>Statut: ✅ CORRIGÉ ET FONCTIONNEL</p>
        <p>Timestamp: {timestamp}</p>
        <p style={{ color: '#22c55e', fontWeight: 'bold' }}>
          🎯 Si vous voyez ce message, le problème est résolu !
        </p>
      </div>
    </div>
  );
} 