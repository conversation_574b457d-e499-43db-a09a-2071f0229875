'use client';

import { useState } from 'react';
import { createSupabaseClient } from '@/lib/supabase/browser-client';

export default function TestSupabaseVerification() {
  const [results, setResults] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const supabase = createSupabaseClient();

  const verifyTables = async () => {
    setLoading(true);
    const testResults: Record<string, any> = {};

    try {
      // Test 1: Vérifier la table users
      try {
        const { data: usersData, error: usersError } = await supabase
          .from('users')
          .select('count(*)')
          .limit(1);
        
        testResults.users = {
          exists: !usersError,
          error: usersError?.message,
          count: usersData?.[0]?.count || 0
        };
      } catch (error) {
        testResults.users = { exists: false, error: 'Table users non trouvée' };
      }

      // Test 2: Vérifier la table mood_entries
      try {
        const { data: moodData, error: moodError } = await supabase
          .from('mood_entries')
          .select('count(*)')
          .limit(1);
        
        testResults.mood_entries = {
          exists: !moodError,
          error: moodError?.message,
          count: moodData?.[0]?.count || 0
        };
      } catch (error) {
        testResults.mood_entries = { exists: false, error: 'Table mood_entries non trouvée' };
      }

      // Test 3: Vérifier la table journal_entries
      try {
        const { data: journalData, error: journalError } = await supabase
          .from('journal_entries')
          .select('count(*)')
          .limit(1);
        
        testResults.journal_entries = {
          exists: !journalError,
          error: journalError?.message,
          count: journalData?.[0]?.count || 0
        };
      } catch (error) {
        testResults.journal_entries = { exists: false, error: 'Table journal_entries non trouvée' };
      }

      // Test 4: Vérifier la connexion générale
      try {
        const { data: healthData, error: healthError } = await supabase
          .from('users')
          .select('id')
          .limit(1);
        
        testResults.connection = {
          working: !healthError,
          error: healthError?.message
        };
      } catch (error) {
        testResults.connection = { working: false, error: 'Connexion échoue' };
      }

      setResults(testResults);
    } catch (error) {
      console.error('Erreur vérification:', error);
      setResults({ global_error: error });
    } finally {
      setLoading(false);
    }
  };

  const allTablesExist = results.users?.exists && results.mood_entries?.exists && results.journal_entries?.exists;

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-center text-gray-900 mb-6">
            🔍 Vérification Tables Supabase
          </h1>
          
          <div className="text-center mb-8">
            <div className="text-lg mb-4">
              {allTablesExist ? 
                '✅ Tables Supabase OPÉRATIONNELLES !' : 
                'Vérification du statut des tables...'}
            </div>
          </div>

          <div className="space-y-6">
            <div className="text-center">
              <button
                onClick={verifyTables}
                disabled={loading}
                className="px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 text-lg font-semibold"
              >
                {loading ? '⏳ Vérification en cours...' : '🔍 Vérifier les Tables'}
              </button>
            </div>

            {Object.keys(results).length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Table Users */}
                <div className={`p-4 rounded-lg border ${results.users?.exists ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                  <h3 className="font-semibold mb-2">
                    {results.users?.exists ? '✅' : '❌'} Table users
                  </h3>
                  <div className="text-sm">
                    <p><strong>Statut:</strong> {results.users?.exists ? 'Existe' : 'Manquante'}</p>
                    {results.users?.count !== undefined && (
                      <p><strong>Entrées:</strong> {results.users.count}</p>
                    )}
                    {results.users?.error && (
                      <p className="text-red-600"><strong>Erreur:</strong> {results.users.error}</p>
                    )}
                  </div>
                </div>

                {/* Table Mood Entries */}
                <div className={`p-4 rounded-lg border ${results.mood_entries?.exists ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                  <h3 className="font-semibold mb-2">
                    {results.mood_entries?.exists ? '✅' : '❌'} Table mood_entries
                  </h3>
                  <div className="text-sm">
                    <p><strong>Statut:</strong> {results.mood_entries?.exists ? 'Existe' : 'Manquante'}</p>
                    {results.mood_entries?.count !== undefined && (
                      <p><strong>Entrées:</strong> {results.mood_entries.count}</p>
                    )}
                    {results.mood_entries?.error && (
                      <p className="text-red-600"><strong>Erreur:</strong> {results.mood_entries.error}</p>
                    )}
                  </div>
                </div>

                {/* Table Journal Entries */}
                <div className={`p-4 rounded-lg border ${results.journal_entries?.exists ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                  <h3 className="font-semibold mb-2">
                    {results.journal_entries?.exists ? '✅' : '❌'} Table journal_entries
                  </h3>
                  <div className="text-sm">
                    <p><strong>Statut:</strong> {results.journal_entries?.exists ? 'Existe' : 'Manquante'}</p>
                    {results.journal_entries?.count !== undefined && (
                      <p><strong>Entrées:</strong> {results.journal_entries.count}</p>
                    )}
                    {results.journal_entries?.error && (
                      <p className="text-red-600"><strong>Erreur:</strong> {results.journal_entries.error}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Connexion Status */}
            {results.connection && (
              <div className={`p-4 rounded-lg border ${results.connection.working ? 'bg-blue-50 border-blue-200' : 'bg-red-50 border-red-200'}`}>
                <h3 className="font-semibold mb-2">
                  {results.connection.working ? '✅' : '❌'} Connexion Supabase
                </h3>
                <div className="text-sm">
                  <p><strong>Statut:</strong> {results.connection.working ? 'Fonctionnelle' : 'Problème'}</p>
                  {results.connection.error && (
                    <p className="text-red-600"><strong>Erreur:</strong> {results.connection.error}</p>
                  )}
                </div>
              </div>
            )}

            {allTablesExist && (
              <div className="text-center space-y-4 mt-8">
                <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
                  <h3 className="text-lg font-semibold text-green-900 mb-2">
                    🎉 PARFAIT ! Base de données prête !
                  </h3>
                  <p className="text-green-800 mb-4">
                    Toutes les tables Supabase sont créées et fonctionnelles. 
                    Vous pouvez maintenant tester l'application complète !
                  </p>
                  <div className="space-x-4">
                    <a 
                      href="/test-phase4-supabase" 
                      className="inline-block px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      🚀 Tester l'Application
                    </a>
                    <a 
                      href="/auth/register" 
                      className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      👤 Créer un Compte
                    </a>
                  </div>
                </div>
              </div>
            )}

            {Object.keys(results).length > 0 && !allTablesExist && (
              <div className="text-center">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h3 className="font-semibold text-yellow-900 mb-2">
                    ⚠️ Configuration incomplète
                  </h3>
                  <p className="text-yellow-800 text-sm">
                    Certaines tables sont manquantes. Le schéma SQL doit être exécuté.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 