'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  FileText, 
  Lock, 
  Globe,
  Activity,
  BarChart3,
  Settings
} from 'lucide-react';

export default function CompliancePage() {
  const complianceStandards = [
    {
      id: 'hds',
      name: 'HDS - Hébergement Données de Santé',
      description: 'Certification française pour hébergement sécurisé données de santé',
      status: 'in_progress',
      progress: 85,
      icon: Shield
    },
    {
      id: 'iso27001', 
      name: 'ISO 27001 - Management Sécurité',
      description: 'Standard international de management de la sécurité',
      status: 'in_progress',
      progress: 75,
      icon: Globe
    },
    {
      id: 'hipaa',
      name: 'HIPAA - Health Insurance Portability', 
      description: 'Conformité américaine protection données santé',
      status: 'pending',
      progress: 60,
      icon: FileText
    },
    {
      id: 'soc2',
      name: 'SOC 2 Type II - Contrôles Sécurité',
      description: 'Certification contrôles sécurité opérationnels', 
      status: 'pending',
      progress: 45,
      icon: Lock
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'certified': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-yellow-600 bg-yellow-100';
      case 'pending': return 'text-orange-600 bg-orange-100';
      case 'expired': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              🛡️ Conformité & Sécurité - Phase 3
            </h1>
            <p className="text-gray-600">
              Module avancé de conformité médicale et sécurité entreprise
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Paramètres
            </Button>
            <Button>
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Conformité Globale</p>
                <p className="text-2xl font-bold text-green-600">78%</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </Card>
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Certifications</p>
                <p className="text-2xl font-bold text-blue-600">4/4</p>
              </div>
              <Shield className="h-8 w-8 text-blue-600" />
            </div>
          </Card>
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Incidents</p>
                <p className="text-2xl font-bold text-orange-600">3</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </Card>
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Uptime</p>
                <p className="text-2xl font-bold text-green-600">99.9%</p>
              </div>
              <Activity className="h-8 w-8 text-green-600" />
            </div>
          </Card>
        </div>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">📋 Status Certifications Phase 3</h3>
          <div className="space-y-4">
            {complianceStandards.map((standard) => {
              const IconComponent = standard.icon;
              return (
                <div key={standard.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <div>
                      <h4 className="font-medium">{standard.name}</h4>
                      <p className="text-sm text-gray-600">{standard.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <Badge className={getStatusColor(standard.status)}>
                        {standard.status}
                      </Badge>
                      <p className="text-sm text-gray-600 mt-1">{standard.progress}%</p>
                    </div>
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          standard.progress >= 80 ? 'bg-green-500' : 
                          standard.progress >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${standard.progress}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </Card>

        <Card className="p-6 mt-6 bg-blue-50 border-blue-200">
          <div className="text-center">
            <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              🛡️ Phase 3 : Conformité & Sécurité Médicale
            </h3>
            <p className="text-blue-700 mb-4">
              Transformation en plateforme de santé certifiée enterprise avec conformité internationale complète
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="text-left">
                <h4 className="font-medium text-blue-900 mb-2">🎯 Certifications en cours :</h4>
                <ul className="space-y-1 text-blue-700">
                  <li>✅ HDS France (85% - Janvier 2025)</li>
                  <li>🔄 ISO 27001 (75% - Février 2025)</li>
                  <li>🔄 HIPAA USA (60% - Mars 2025)</li>
                  <li>🔄 SOC 2 Type II (45% - Avril 2025)</li>
                </ul>
              </div>
              <div className="text-left">
                <h4 className="font-medium text-blue-900 mb-2">💰 Impact business :</h4>
                <ul className="space-y-1 text-blue-700">
                  <li>🌍 Marché français +100%</li>
                  <li>🏢 Contrats B2B +300%</li>
                  <li>🇺🇸 Marché US accessible</li>
                  <li>💼 Enterprise sales +500%</li>
                </ul>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
