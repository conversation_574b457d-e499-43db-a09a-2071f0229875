'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Brain, CheckCircle, XCircle, User, Mail, Key, LogIn, LogOut } from 'lucide-react';

export default function TestAuthFixPage() {
  const { user, loading, login, register, logout, isAuthenticated } = useAuth();
  const router = useRouter();
  
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('testpassword123');
  const [name, setName] = useState('Test User');
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string, type: 'success' | 'error' = 'success') => {
    const emoji = type === 'success' ? '✅' : '❌';
    setTestResults(prev => [`${emoji} ${message}`, ...prev]);
  };

  const handleTestLogin = async () => {
    try {
      addResult('Tentative de connexion...');
      await login(email, password);
      addResult('Connexion réussie!', 'success');
    } catch (error: any) {
      addResult(`Erreur de connexion: ${error.message}`, 'error');
    }
  };

  const handleTestRegister = async () => {
    try {
      addResult('Tentative d\'inscription...');
      await register(name, email, password);
      addResult('Inscription réussie!', 'success');
    } catch (error: any) {
      addResult(`Erreur d\'inscription: ${error.message}`, 'error');
    }
  };

  const handleTestLogout = async () => {
    try {
      addResult('Tentative de déconnexion...');
      await logout();
      addResult('Déconnexion réussie!', 'success');
    } catch (error: any) {
      addResult(`Erreur de déconnexion: ${error.message}`, 'error');
    }
  };

  const goToDashboard = () => {
    router.push('/dashboard');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <Brain className="w-16 h-16 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Test d'Authentification MindFlow Pro
          </h1>
          <p className="text-lg text-gray-600">
            Vérification du système d'authentification Supabase
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Statut d'authentification */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <User className="w-5 h-5 text-blue-600" />
              Statut d'Authentification
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">État de chargement:</span>
                <span className={`flex items-center gap-2 ${loading ? 'text-yellow-600' : 'text-green-600'}`}>
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
                      Chargement...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4" />
                      Chargé
                    </>
                  )}
                </span>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">Authentifié:</span>
                <span className={`flex items-center gap-2 ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                  {isAuthenticated ? (
                    <>
                      <CheckCircle className="w-4 h-4" />
                      Oui
                    </>
                  ) : (
                    <>
                      <XCircle className="w-4 h-4" />
                      Non
                    </>
                  )}
                </span>
              </div>

              {user && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <h3 className="font-medium text-green-800 mb-2">Informations utilisateur:</h3>
                  <div className="space-y-1 text-sm text-green-700">
                    <div><strong>ID:</strong> {user.user.id}</div>
                    <div><strong>Email:</strong> {user.user.email}</div>
                    <div><strong>Nom:</strong> {user.user.name}</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Formulaire de test */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Key className="w-5 h-5 text-blue-600" />
              Tests d'Authentification
            </h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom
                </label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nom complet"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mot de passe
                </label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="••••••••"
                />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={handleTestRegister}
                  disabled={loading}
                  className="flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <User className="w-4 h-4" />
                  S'inscrire
                </button>

                <button
                  onClick={handleTestLogin}
                  disabled={loading}
                  className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <LogIn className="w-4 h-4" />
                  Se connecter
                </button>
              </div>

              {isAuthenticated && (
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={handleTestLogout}
                    disabled={loading}
                    className="flex items-center justify-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <LogOut className="w-4 h-4" />
                    Se déconnecter
                  </button>

                  <button
                    onClick={goToDashboard}
                    className="flex items-center justify-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                  >
                    <Brain className="w-4 h-4" />
                    Dashboard
                  </button>
                </div>
              )}

              {testResults.length > 0 && (
                <button
                  onClick={clearResults}
                  className="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                >
                  Effacer les résultats
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Résultats des tests */}
        {testResults.length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Résultats des Tests</h2>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className="p-2 bg-gray-50 rounded text-sm font-mono"
                >
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">Instructions de test:</h3>
          <ul className="text-blue-700 space-y-1">
            <li>1. Remplissez les champs email, mot de passe et nom</li>
            <li>2. Cliquez sur "S'inscrire" pour créer un nouveau compte</li>
            <li>3. Si le compte existe déjà, utilisez "Se connecter"</li>
            <li>4. Une fois connecté, testez la navigation vers le Dashboard</li>
            <li>5. Testez la déconnexion</li>
          </ul>
        </div>
      </div>
    </div>
  );
} 