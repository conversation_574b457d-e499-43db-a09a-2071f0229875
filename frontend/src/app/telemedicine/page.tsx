'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON>,
  Mic,
  MicOff,
  <PERSON>Off,
  Phone,
  MessageSquare,
  Share,
  UserPlus,
  Settings,
  Clock,
  Activity,
  FileText,
  Send,
  Plus,
  CircleDot,
  Pause,
  Monitor,
  Stethoscope,
  Eye,
  Brain,
  Wifi,
  WifiOff,
  Download,
  Upload,
  Maximize,
  Minimize,
  Volume2,
  VolumeX,
  Camera,
  CameraOff,
  Shield,
  Zap,
  PlayCircle,
  StopCircle,
  RotateCcw,
  Share2
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Interfaces étendues pour la télémédecine avancée
interface TelemedicineSessionExtended {
  id: string;
  patientName: string;
  patientAge: number;
  appointmentTime: string;
  duration: number;
  type: 'video' | 'audio' | 'chat';
  status: 'waiting' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'routine' | 'urgent' | 'emergency';
  chiefComplaint: string;
  vitalSigns?: {
    bloodPressure: string;
    heartRate: number;
    temperature: number;
    oxygenSat: number;
    respiratoryRate: number;
  };
  notes: string;
  
  // Nouvelles fonctionnalités Phase 2
  videoQuality: '720p' | '1080p' | 'adaptive';
  diagnosticTools: DiagnosticTool[];
  aiAnalysis: AIAnalysis;
  recording: RecordingSettings;
  connectionQuality: ConnectionQuality;
}

interface DiagnosticTool {
  id: string;
  name: string;
  type: 'stethoscope' | 'dermascope' | 'vision_test' | 'posture_analysis' | 'vital_signs';
  isActive: boolean;
  status: 'available' | 'in_use' | 'completed' | 'error';
  icon: React.ReactNode;
  description: string;
  measurements: Measurement[];
}

interface Measurement {
  timestamp: string;
  value: any;
  unit?: string;
  confidence?: number;
  interpretation?: string;
}

interface AIAnalysis {
  transcription: string;
  extractedSymptoms: string[];
  suggestedDiagnosis: Array<{
    condition: string;
    confidence: number;
    reasoning: string;
  }>;
  urgencyLevel: 'low' | 'medium' | 'high' | 'emergency';
  keyInsights: string[];
}

interface RecordingSettings {
  isRecording: boolean;
  recordingType: 'video+audio' | 'audio_only' | 'screen_only';
  duration: number;
  fileSize: string;
  consentGiven: boolean;
}

interface ConnectionQuality {
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'disconnected';
  bandwidth: number;
  latency: number;
  packetLoss: number;
  videoResolution: string;
}

interface ChatMessage {
  id: string;
  sender: 'doctor' | 'patient' | 'system' | 'ai';
  message: string;
  timestamp: string;
  type: 'text' | 'image' | 'file' | 'ai_insight';
  priority?: 'normal' | 'high' | 'urgent';
}

export default function AdvancedTelemedicinePage() {
  // États de base
  const [isVideoOn, setIsVideoOn] = useState(true);
  const [isAudioOn, setIsAudioOn] = useState(true);
  const [isRecording, setIsRecording] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [chatMessage, setChatMessage] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // Nouveaux états Phase 2
  const [selectedDiagnosticTool, setSelectedDiagnosticTool] = useState<string | null>(null);
  const [isAITranscribing, setIsAITranscribing] = useState(true);
  const [sessionTime, setSessionTime] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'reconnecting' | 'disconnected'>('connected');
  const [videoQuality, setVideoQuality] = useState<'720p' | '1080p' | 'adaptive'>('adaptive');
  
  // Session simulée avancée
  const [session] = useState<TelemedicineSessionExtended>({
    id: 'tel-001',
    patientName: 'Marie Dubois',
    patientAge: 34,
    appointmentTime: '14:30',
    duration: 45,
    type: 'video',
    status: 'in-progress',
    priority: 'routine',
    chiefComplaint: 'Douleurs abdominales persistantes depuis 3 jours',
    vitalSigns: {
      bloodPressure: '125/80',
      heartRate: 78,
      temperature: 37.2,
      oxygenSat: 98,
      respiratoryRate: 16
    },
    notes: 'Consultation de suivi post-opératoire',
    videoQuality: 'adaptive',
    diagnosticTools: [],
    aiAnalysis: {
      transcription: '',
      extractedSymptoms: ['douleur abdominale', 'nausées légères', 'fatigue'],
      suggestedDiagnosis: [
        { condition: 'Gastro-entérite', confidence: 75, reasoning: 'Symptômes compatibles avec infection digestive' },
        { condition: 'Syndrome du côlon irritable', confidence: 60, reasoning: 'Douleurs récurrentes sans fièvre' }
      ],
      urgencyLevel: 'medium',
      keyInsights: ['Patient stable', 'Surveillance recommandée', 'Hydratation importante']
    },
    recording: {
      isRecording: false,
      recordingType: 'video+audio',
      duration: 0,
      fileSize: '0 MB',
      consentGiven: true
    },
    connectionQuality: {
      status: 'excellent',
      bandwidth: 25.6,
      latency: 45,
      packetLoss: 0.1,
      videoResolution: '1080p'
    }
  });

  // Outils diagnostiques virtuels avancés
  const diagnosticTools: DiagnosticTool[] = [
    {
      id: 'stethoscope',
      name: 'Stéthoscope Numérique',
      type: 'stethoscope',
      isActive: false,
      status: 'available',
      icon: <Stethoscope className="h-5 w-5" />,
      description: 'Auscultation cardiaque et pulmonaire assistée par IA',
      measurements: []
    },
    {
      id: 'dermascope',
      name: 'Analyse Dermatologique',
      type: 'dermascope',
      isActive: false,
      status: 'available',
      icon: <Eye className="h-5 w-5" />,
      description: 'Analyse IA des lésions cutanées et grains de beauté',
      measurements: []
    },
    {
      id: 'vision_test',
      name: 'Test de Vision',
      type: 'vision_test',
      isActive: false,
      status: 'available',
      icon: <Monitor className="h-5 w-5" />,
      description: 'Évaluation acuité visuelle et champ visuel',
      measurements: []
    },
    {
      id: 'posture_analysis',
      name: 'Analyse Posturale',
      type: 'posture_analysis',
      isActive: false,
      status: 'available',
      icon: <Activity className="h-5 w-5" />,
      description: 'Évaluation posture et mobilité par vision IA',
      measurements: []
    },
    {
      id: 'vital_signs',
      name: 'Signes Vitaux IA',
      type: 'vital_signs',
      isActive: true,
      status: 'in_use',
      icon: <Brain className="h-5 w-5" />,
      description: 'Monitoring automatique par analyse vidéo',
      measurements: [
        { timestamp: '14:32', value: 76, unit: 'bpm', confidence: 94, interpretation: 'Rythme cardiaque normal' },
        { timestamp: '14:33', value: 16, unit: '/min', confidence: 89, interpretation: 'Fréquence respiratoire normale' }
      ]
    }
  ];

  // Messages de chat améliorés
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      sender: 'system',
      message: 'Consultation démarrée. Connexion sécurisée établie.',
      timestamp: '14:30',
      type: 'text'
    },
    {
      id: '2',
      sender: 'ai',
      message: '🤖 IA Médicale : Transcription automatique activée. Analyse des symptômes en cours.',
      timestamp: '14:31',
      type: 'ai_insight',
      priority: 'normal'
    },
    {
      id: '3',
      sender: 'patient',
      message: 'Bonjour Docteur, je ressens toujours ces douleurs abdominales.',
      timestamp: '14:31',
      type: 'text'
    },
    {
      id: '4',
      sender: 'doctor',
      message: 'Bonjour Marie. Pouvez-vous me décrire plus précisément la localisation de la douleur ?',
      timestamp: '14:32',
      type: 'text'
    },
    {
      id: '5',
      sender: 'ai',
      message: '📊 Analyse IA : Symptômes compatibles avec gastro-entérite (75% confiance). Recommande questions sur hydratation.',
      timestamp: '14:33',
      type: 'ai_insight',
      priority: 'high'
    }
  ]);

  // Timer de session
  useEffect(() => {
    const timer = setInterval(() => {
      setSessionTime(prev => prev + 1);
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const sendMessage = () => {
    if (chatMessage.trim()) {
      const newMessage: ChatMessage = {
        id: Date.now().toString(),
        sender: 'doctor',
        message: chatMessage,
        timestamp: new Date().toLocaleTimeString().slice(0, 5),
        type: 'text'
      };
      setChatMessages(prev => [...prev, newMessage]);
      setChatMessage('');
      
      // Simulation analyse IA du message
      setTimeout(() => {
        if (chatMessage.toLowerCase().includes('douleur') || chatMessage.toLowerCase().includes('symptôme')) {
          const aiMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            sender: 'ai',
            message: `🧠 IA Analyse : Nouveau symptôme détecté - "${chatMessage}". Mise à jour du diagnostic en cours.`,
            timestamp: new Date().toLocaleTimeString().slice(0, 5),
            type: 'ai_insight',
            priority: 'normal'
          };
          setChatMessages(prev => [...prev, aiMessage]);
        }
      }, 2000);
    }
  };

  const activateDiagnosticTool = (toolId: string) => {
    setSelectedDiagnosticTool(toolId);
    // Simulation activation outil
    setTimeout(() => {
      const successMessage: ChatMessage = {
        id: Date.now().toString(),
        sender: 'system',
        message: `🔧 Outil ${diagnosticTools.find(t => t.id === toolId)?.name} activé avec succès.`,
        timestamp: new Date().toLocaleTimeString().slice(0, 5),
        type: 'text'
      };
      setChatMessages(prev => [...prev, successMessage]);
    }, 1500);
  };

  const getConnectionStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'fair': return 'text-yellow-600';
      case 'poor': return 'text-orange-600';
      case 'disconnected': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'normal': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header avec informations de session */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Téléconsultation Avancée</h1>
            <div className="flex items-center space-x-4 mt-2">
              <p className="text-gray-600">Patient: {session.patientName}</p>
              <Badge className="bg-blue-100 text-blue-800">
                <Clock className="h-3 w-3 mr-1" />
                {formatTime(sessionTime)}
              </Badge>
              <Badge className={getConnectionStatusColor(session.connectionQuality.status)}>
                {session.connectionQuality.status === 'excellent' ? <Wifi className="h-3 w-3 mr-1" /> : <WifiOff className="h-3 w-3 mr-1" />}
                {session.connectionQuality.status}
              </Badge>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={isRecording ? "destructive" : "outline"}
              size="sm"
              onClick={() => setIsRecording(!isRecording)}
              className="flex items-center space-x-2"
            >
              {isRecording ? <StopCircle className="h-4 w-4" /> : <PlayCircle className="h-4 w-4" />}
              <span>{isRecording ? 'Arrêter' : 'Enregistrer'}</span>
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Exporter
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Zone vidéo principale */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <div className="relative">
                {/* Simulateur de vidéo HD */}
                <div className="relative bg-gradient-to-br from-gray-900 to-gray-700 rounded-lg aspect-video mb-4 overflow-hidden">
                  {isVideoOn ? (
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                      <div className="text-center text-white">
                        <Camera className="h-16 w-16 mx-auto mb-4" />
                        <p className="text-lg font-semibold">{session.patientName}</p>
                        <p className="text-sm opacity-75">Vidéo HD 1080p</p>
                      </div>
                    </div>
                  ) : (
                    <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
                      <div className="text-center text-gray-400">
                        <CameraOff className="h-16 w-16 mx-auto mb-4" />
                        <p>Caméra désactivée</p>
                      </div>
                    </div>
                  )}
                  
                  {/* Overlays d'information */}
                  <div className="absolute top-4 left-4 flex space-x-2">
                    {isRecording && (
                      <Badge className="bg-red-600 text-white animate-pulse">
                        <CircleDot className="h-3 w-3 mr-1" />
                        REC
                      </Badge>
                    )}
                    <Badge className="bg-black/50 text-white">
                      {session.connectionQuality.videoResolution}
                    </Badge>
                  </div>
                  
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-black/50 text-white">
                      {session.connectionQuality.latency}ms
                    </Badge>
                  </div>
                  
                  {/* Indicateurs de qualité de connexion */}
                  <div className="absolute bottom-4 left-4 flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${session.connectionQuality.status === 'excellent' ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                    <span className="text-white text-xs">{session.connectionQuality.bandwidth} Mbps</span>
                  </div>
                </div>

                {/* Contrôles vidéo avancés */}
                <div className="flex justify-center space-x-3">
                  <Button
                    variant={isVideoOn ? "default" : "destructive"}
                    size="lg"
                    onClick={() => setIsVideoOn(!isVideoOn)}
                    className="rounded-full w-12 h-12"
                  >
                    {isVideoOn ? <Video className="h-5 w-5" /> : <VideoOff className="h-5 w-5" />}
                  </Button>

                  <Button
                    variant={isAudioOn ? "default" : "destructive"}
                    size="lg"
                    onClick={() => setIsAudioOn(!isAudioOn)}
                    className="rounded-full w-12 h-12"
                  >
                    {isAudioOn ? <Mic className="h-5 w-5" /> : <MicOff className="h-5 w-5" />}
                  </Button>

                  <Button
                    variant={isScreenSharing ? "secondary" : "outline"}
                    size="lg"
                    onClick={() => setIsScreenSharing(!isScreenSharing)}
                    className="rounded-full w-12 h-12"
                  >
                    <Share2 className="h-5 w-5" />
                  </Button>

                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => setIsFullscreen(!isFullscreen)}
                    className="rounded-full w-12 h-12"
                  >
                    {isFullscreen ? <Minimize className="h-5 w-5" /> : <Maximize className="h-5 w-5" />}
                  </Button>

                  <Button
                    variant="destructive"
                    size="lg"
                    className="rounded-full w-12 h-12"
                  >
                    <Phone className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </Card>

            {/* Outils diagnostiques virtuels */}
            <Card className="p-6 mt-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Stethoscope className="h-5 w-5 mr-2 text-blue-600" />
                Outils Diagnostiques Virtuels
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {diagnosticTools.map((tool) => (
                  <Button
                    key={tool.id}
                    variant={tool.isActive ? "default" : "outline"}
                    onClick={() => activateDiagnosticTool(tool.id)}
                    className="h-auto p-3 flex flex-col items-center space-y-2"
                    disabled={tool.status === 'error'}
                  >
                    <div className={`p-2 rounded-lg ${tool.isActive ? 'bg-white text-blue-600' : 'bg-blue-100 text-blue-600'}`}>
                      {tool.icon}
                    </div>
                    <div className="text-center">
                      <p className="text-xs font-medium">{tool.name}</p>
                      <Badge 
                        className={`text-xs ${
                          tool.status === 'available' ? 'bg-green-100 text-green-800' :
                          tool.status === 'in_use' ? 'bg-blue-100 text-blue-800' :
                          tool.status === 'completed' ? 'bg-purple-100 text-purple-800' :
                          'bg-red-100 text-red-800'
                        }`}
                      >
                        {tool.status}
                      </Badge>
                    </div>
                  </Button>
                ))}
              </div>
              
              {selectedDiagnosticTool && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <Zap className="h-4 w-4 inline mr-1" />
                    Outil {diagnosticTools.find(t => t.id === selectedDiagnosticTool)?.name} en cours d'activation...
                  </p>
                </div>
              )}
            </Card>
          </div>

          {/* Panneau latéral droit */}
          <div className="lg:col-span-2 space-y-6">
            {/* Signes vitaux et IA */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Activity className="h-5 w-5 mr-2 text-red-600" />
                Monitoring Temps Réel
              </h3>
              
              {session.vitalSigns && (
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-red-50 rounded-lg">
                    <p className="text-xs text-gray-600">Tension</p>
                    <p className="text-lg font-bold text-red-600">{session.vitalSigns.bloodPressure}</p>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <p className="text-xs text-gray-600">FC</p>
                    <p className="text-lg font-bold text-blue-600">{session.vitalSigns.heartRate} bpm</p>
                  </div>
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <p className="text-xs text-gray-600">Temp</p>
                    <p className="text-lg font-bold text-orange-600">{session.vitalSigns.temperature}°C</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <p className="text-xs text-gray-600">SpO2</p>
                    <p className="text-lg font-bold text-green-600">{session.vitalSigns.oxygenSat}%</p>
                  </div>
                </div>
              )}

              {/* Analyse IA en temps réel */}
              <div className="bg-purple-50 rounded-lg p-4">
                <h4 className="font-medium text-purple-900 mb-2 flex items-center">
                  <Brain className="h-4 w-4 mr-1" />
                  Analyse IA Médicale
                </h4>
                <div className="space-y-2">
                  <div>
                    <p className="text-xs text-purple-700">Symptômes détectés:</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {session.aiAnalysis.extractedSymptoms.map((symptom, index) => (
                        <Badge key={index} className="text-xs bg-purple-100 text-purple-800">
                          {symptom}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-purple-700">Diagnostic suggéré:</p>
                    {session.aiAnalysis.suggestedDiagnosis.map((diagnosis, index) => (
                      <div key={index} className="text-xs text-purple-800 mt-1">
                        • {diagnosis.condition} ({diagnosis.confidence}%)
                      </div>
                    ))}
                  </div>
                  <Badge className={`text-xs ${
                    session.aiAnalysis.urgencyLevel === 'emergency' ? 'bg-red-100 text-red-800' :
                    session.aiAnalysis.urgencyLevel === 'high' ? 'bg-orange-100 text-orange-800' :
                    session.aiAnalysis.urgencyLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    Urgence: {session.aiAnalysis.urgencyLevel}
                  </Badge>
                </div>
              </div>
            </Card>

            {/* Chat amélioré */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <MessageSquare className="h-5 w-5 mr-2 text-blue-600" />
                Chat + Transcription IA
                {isAITranscribing && (
                  <Badge className="ml-2 bg-green-100 text-green-800 animate-pulse">
                    <Mic className="h-3 w-3 mr-1" />
                    Écoute
                  </Badge>
                )}
              </h3>

              <div className="h-64 overflow-y-auto space-y-3 mb-4 p-3 bg-gray-50 rounded-lg">
                {chatMessages.map((message) => (
                  <div key={message.id} className={`flex ${message.sender === 'doctor' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-xs p-3 rounded-lg ${
                      message.sender === 'doctor' ? 'bg-blue-600 text-white' :
                      message.sender === 'ai' ? 'bg-purple-100 text-purple-900 border border-purple-200' :
                      message.sender === 'system' ? 'bg-gray-200 text-gray-800' :
                      'bg-white text-gray-900 border'
                    }`}>
                      <div className="flex items-start justify-between">
                        <p className="text-sm">{message.message}</p>
                        {message.priority && message.priority !== 'normal' && (
                          <Badge className={`ml-2 text-xs ${getPriorityColor(message.priority)}`}>
                            {message.priority}
                          </Badge>
                        )}
                      </div>
                      <p className={`text-xs mt-1 ${
                        message.sender === 'doctor' ? 'text-blue-200' : 'text-gray-500'
                      }`}>
                        {message.timestamp}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex space-x-2">
                <input
                  type="text"
                  value={chatMessage}
                  onChange={(e) => setChatMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  placeholder="Tapez votre message..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <Button onClick={sendMessage} className="px-4">
                  <Send className="h-4 w-4" />
                </Button>
              </div>

              <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
                <span>🔒 Chiffrement end-to-end activé</span>
                <span>⚡ Transcription automatique IA</span>
              </div>
            </Card>

            {/* Actions rapides */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Actions Rapides</h3>
              <div className="grid grid-cols-2 gap-3">
                <Button variant="outline" className="flex items-center justify-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>Notes</span>
                </Button>
                <Button variant="outline" className="flex items-center justify-center space-x-2">
                  <Download className="h-4 w-4" />
                  <span>Ordonnance</span>
                </Button>
                <Button variant="outline" className="flex items-center justify-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>Prochain RDV</span>
                </Button>
                <Button variant="outline" className="flex items-center justify-center space-x-2">
                  <Share className="h-4 w-4" />
                  <span>Partager</span>
                </Button>
              </div>
            </Card>
          </div>
        </div>

        {/* Barre de statut de session */}
        <Card className="mt-6 p-4 bg-green-50 border-green-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Badge className="bg-green-100 text-green-800">
                <Shield className="h-3 w-3 mr-1" />
                Session sécurisée
              </Badge>
              <span className="text-sm text-green-700">
                Consultation en cours - Durée: {formatTime(sessionTime)}
              </span>
              <span className="text-sm text-green-700">
                Qualité: {session.connectionQuality.status} ({session.connectionQuality.bandwidth} Mbps)
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Paramètres
              </Button>
              <Button variant="default" size="sm">
                <FileText className="h-4 w-4 mr-2" />
                Terminer & Résumer
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
} 