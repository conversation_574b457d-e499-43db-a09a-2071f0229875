'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getDataService, MoodEntry, JournalEntry } from '@/services/data-supabase';
import { FEATURE_FLAGS } from '@/lib/config/feature-flags';

export default function TestPhase4Supabase() {
  const { user, isAuthenticated } = useAuth();
  const [status, setStatus] = useState('Loading...');
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [moodEntries, setMoodEntries] = useState<MoodEntry[]>([]);
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
  const dataService = getDataService();

  useEffect(() => {
    const checkPhase4 = () => {
      const authEnabled = FEATURE_FLAGS.USE_SUPABASE_AUTH;
      const dbEnabled = FEATURE_FLAGS.USE_SUPABASE_DATABASE;
      const realTimeEnabled = FEATURE_FLAGS.ENABLE_REAL_TIME;
      const dualModeDisabled = !FEATURE_FLAGS.DUAL_DATABASE_MODE;
      
      if (authEnabled && dbEnabled && realTimeEnabled && dualModeDisabled) {
        setStatus('✅ Phase 4 ACTIVE - Basculement complet Supabase');
      } else {
        setStatus('❌ Phase 4 NON ACTIVE - Configuration requise');
      }
    };

    checkPhase4();
  }, []);

  const runTests = async () => {
    if (!isAuthenticated || !user || !dataService) {
      setTestResults({ auth: false });
      return;
    }

    const results: Record<string, boolean> = {};

    try {
      // Test 1: Connexion Supabase Auth
      results.auth = isAuthenticated && !!user;

      // Test 2: Récupération mood entries
      try {
        const moods = await dataService.getMoodEntries(user.id, 10);
        results.moodRead = true;
        setMoodEntries(moods);
      } catch (error) {
        results.moodRead = false;
        console.error('Test mood read:', error);
      }

      // Test 3: Création mood entry
      try {
        const newMood: Omit<MoodEntry, 'id' | 'created_at' | 'updated_at'> = {
          user_id: user.id,
          mood_level: 7,
          energy_level: 6,
          stress_level: 4,
          notes: 'Test Phase 4',
          date: new Date().toISOString().split('T')[0],
        };
        await dataService.createMoodEntry(newMood);
        results.moodCreate = true;
      } catch (error) {
        results.moodCreate = false;
        console.error('Test mood create:', error);
      }

      // Test 4: Récupération journal entries
      try {
        const journals = await dataService.getJournalEntries(user.id, 10);
        results.journalRead = true;
        setJournalEntries(journals);
      } catch (error) {
        results.journalRead = false;
        console.error('Test journal read:', error);
      }

      // Test 5: Création journal entry
      try {
        const newJournal: Omit<JournalEntry, 'id' | 'created_at' | 'updated_at'> = {
          user_id: user.id,
          title: 'Test Phase 4 Journal',
          content: 'Contenu de test pour valider le fonctionnement de Supabase en Phase 4',
          mood_before: 6,
          mood_after: 8,
          tags: ['test', 'phase4'],
          is_private: true,
        };
        await dataService.createJournalEntry(newJournal);
        results.journalCreate = true;
      } catch (error) {
        results.journalCreate = false;
        console.error('Test journal create:', error);
      }

      // Test 6: Stats mood
      try {
        const stats = await dataService.getMoodStats(user.id, 30);
        results.stats = true;
      } catch (error) {
        results.stats = false;
        console.error('Test stats:', error);
      }

      setTestResults(results);
    } catch (error) {
      console.error('Erreur tests Phase 4:', error);
    }
  };

  const createSampleData = async () => {
    if (!isAuthenticated || !user || !dataService) return;

    try {
      // Créer plusieurs mood entries
      for (let i = 0; i < 5; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        
        await dataService.createMoodEntry({
          user_id: user.id,
          mood_level: Math.floor(Math.random() * 10) + 1,
          energy_level: Math.floor(Math.random() * 10) + 1,
          stress_level: Math.floor(Math.random() * 10) + 1,
          notes: `Note de test ${i + 1}`,
          date: date.toISOString().split('T')[0],
        });
      }

      // Créer plusieurs journal entries
      for (let i = 0; i < 3; i++) {
        await dataService.createJournalEntry({
          user_id: user.id,
          title: `Journal Test ${i + 1}`,
          content: `Contenu du journal de test numéro ${i + 1}. Ceci est un test de la Phase 4 avec Supabase.`,
          mood_before: Math.floor(Math.random() * 10) + 1,
          mood_after: Math.floor(Math.random() * 10) + 1,
          tags: ['test', 'demo'],
          is_private: false,
        });
      }

      alert('Données de test créées avec succès !');
      await runTests(); // Relancer les tests
    } catch (error) {
      console.error('Erreur création données test:', error);
      alert('Erreur lors de la création des données de test');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-center text-gray-900 mb-6">
            🚀 Test Phase 4 - Basculement Complet Supabase
          </h1>
          
          <div className="text-center mb-8">
            <div className="text-lg mb-4">{status}</div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Configuration Phase 4 :</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Auth Supabase: {FEATURE_FLAGS.USE_SUPABASE_AUTH ? '✅' : '❌'}</li>
                <li>• DB Supabase: {FEATURE_FLAGS.USE_SUPABASE_DATABASE ? '✅' : '❌'}</li>
                <li>• Real-time: {FEATURE_FLAGS.ENABLE_REAL_TIME ? '✅' : '❌'}</li>
                <li>• Mode Dual: {FEATURE_FLAGS.DUAL_DATABASE_MODE ? '⚠️ ACTIVÉ' : '✅ DÉSACTIVÉ'}</li>
              </ul>
            </div>

            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">Migration des données :</h3>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• User Data: {FEATURE_FLAGS.MIGRATE_USER_DATA ? '✅' : '❌'}</li>
                <li>• Mood Tracking: {FEATURE_FLAGS.MIGRATE_MOOD_TRACKING ? '✅' : '❌'}</li>
                <li>• Journal Entries: {FEATURE_FLAGS.MIGRATE_JOURNAL_ENTRIES ? '✅' : '❌'}</li>
                <li>• AI Chat: {FEATURE_FLAGS.MIGRATE_AI_CHAT ? '✅' : '❌'}</li>
              </ul>
            </div>
          </div>

          {isAuthenticated ? (
            <div className="space-y-6">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-semibold text-green-900 mb-2">✅ Utilisateur connecté :</h3>
                <p className="text-sm text-green-800">
                  {user?.email} (ID: {user?.id?.slice(0, 8)}...)
                </p>
              </div>

              <div className="flex flex-wrap gap-4">
                <button 
                  onClick={runTests}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  🧪 Lancer Tests Supabase
                </button>
                <button 
                  onClick={createSampleData}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  📝 Créer Données Test
                </button>
              </div>

              {Object.keys(testResults).length > 0 && (
                <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <h3 className="font-semibold text-gray-900 mb-2">Résultats des tests :</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                    <div>Auth: {testResults.auth ? '✅' : '❌'}</div>
                    <div>Mood Read: {testResults.moodRead ? '✅' : '❌'}</div>
                    <div>Mood Create: {testResults.moodCreate ? '✅' : '❌'}</div>
                    <div>Journal Read: {testResults.journalRead ? '✅' : '❌'}</div>
                    <div>Journal Create: {testResults.journalCreate ? '✅' : '❌'}</div>
                    <div>Stats: {testResults.stats ? '✅' : '❌'}</div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                  <h3 className="font-semibold text-purple-900 mb-2">
                    Mood Entries ({moodEntries.length})
                  </h3>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {moodEntries.slice(0, 5).map((mood, index) => (
                      <div key={mood.id || index} className="text-sm text-purple-800">
                        {mood.date}: Humeur {mood.mood_level}/10
                      </div>
                    ))}
                  </div>
                </div>

                <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <h3 className="font-semibold text-orange-900 mb-2">
                    Journal Entries ({journalEntries.length})
                  </h3>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {journalEntries.slice(0, 3).map((journal, index) => (
                      <div key={journal.id || index} className="text-sm text-orange-800">
                        <div className="font-medium">{journal.title}</div>
                        <div className="truncate">{journal.content?.slice(0, 50)}...</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center space-y-4">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-800">Vous devez être connecté pour tester les fonctionnalités.</p>
              </div>
              <a 
                href="/auth/login" 
                className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                🔑 Se connecter
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 