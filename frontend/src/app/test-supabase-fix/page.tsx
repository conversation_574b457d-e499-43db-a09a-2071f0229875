'use client';

import React, { useState, useEffect } from 'react';

export default function TestSupabaseFix() {
  const [results, setResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (name: string, status: string, message: string, details?: any) => {
    const result = {
      name,
      status,
      message,
      details,
      timestamp: new Date().toLocaleTimeString()
    };
    setResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const runDiagnostic = async () => {
    setIsRunning(true);
    clearResults();

    addResult('Diagnostic', 'info', '🔍 Début du diagnostic Supabase...');

    // Test 1: Variables d'environnement
    try {
      const envVars = {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        keyPreview: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20) + '...'
      };

      addResult(
        'Variables d\'environnement',
        envVars.url && envVars.hasKey ? 'success' : 'warning',
        envVars.url && envVars.hasKey ? 
          'Variables d\'environnement trouvées' : 
          'Variables d\'environnement manquantes - utilisation config par défaut',
        envVars
      );
    } catch (error) {
      addResult('Variables d\'environnement', 'error', `Erreur: ${error}`, error);
    }

    // Test 2: Configuration par défaut Supabase
    try {
      const defaultConfig = {
        url: 'https://kvdrukmoxetoiojazukf.supabase.co',
        project_id: 'kvdrukmoxetoiojazukf',
        hasKey: true
      };

      addResult(
        'Configuration par défaut',
        'success',
        'Configuration Supabase par défaut disponible',
        defaultConfig
      );
    } catch (error) {
      addResult('Configuration par défaut', 'error', `Erreur: ${error}`, error);
    }

    // Test 3: Test de connectivité HTTP basique
    try {
      const testUrl = 'https://kvdrukmoxetoiojazukf.supabase.co';
      
      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      addResult(
        'Connectivité HTTP',
        response.ok ? 'success' : 'warning',
        response.ok ? 
          `Connexion réussie (${response.status})` : 
          `Connexion partielle (${response.status})`,
        {
          status: response.status,
          statusText: response.statusText,
          url: testUrl
        }
      );
    } catch (error) {
      addResult('Connectivité HTTP', 'error', `Erreur de connexion: ${error}`, error);
    }

    // Test 4: Test API REST
    try {
      const apiUrl = 'https://kvdrukmoxetoiojazukf.supabase.co/rest/v1/';
      const apiKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzODE0ODEsImV4cCI6MjA1MDk1NzQ4MX0.Mu8Wao-8lGO2PkrTHQgPIhzQNHJ9Dtu4bhALCRNq6bw';

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'apikey': apiKey,
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }

      const isValidResponse = response.status === 200 || 
                             response.status === 404 || 
                             (response.status === 401 && !responseText.includes('Invalid API key'));

      addResult(
        'API REST Supabase',
        isValidResponse ? 'success' : 'error',
        isValidResponse ? 
          `API accessible (${response.status})` : 
          `Clé API invalide (${response.status})`,
        {
          status: response.status,
          response: responseData,
          keyUsed: apiKey.substring(0, 20) + '...'
        }
      );
    } catch (error) {
      addResult('API REST Supabase', 'error', `Erreur API: ${error}`, error);
    }

    // Test 5: Test client Supabase
    try {
      // Import dynamique pour éviter les erreurs
      const { createBrowserClient } = await import('@supabase/ssr');
      
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://kvdrukmoxetoiojazukf.supabase.co';
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzODE0ODEsImV4cCI6MjA1MDk1NzQ4MX0.Mu8Wao-8lGO2PkrTHQgPIhzQNHJ9Dtu4bhALCRNq6bw';

      const client = createBrowserClient(supabaseUrl, supabaseKey);

      // Test auth session
      const { data, error } = await client.auth.getSession();

      addResult(
        'Client Supabase',
        !error || error.message.includes('session') ? 'success' : 'error',
        !error || error.message.includes('session') ? 
          'Client Supabase fonctionnel' : 
          `Erreur client: ${error.message}`,
        {
          hasClient: !!client,
          hasAuth: !!client.auth,
          sessionError: error?.message,
          session: data?.session
        }
      );
    } catch (error) {
      addResult('Client Supabase', 'error', `Erreur création client: ${error}`, error);
    }

    addResult('Diagnostic', 'info', '✅ Diagnostic terminé');
    setIsRunning(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return '#10b981';
      case 'error': return '#ef4444';
      case 'warning': return '#f59e0b';
      case 'info': return '#3b82f6';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '⚫';
    }
  };

  useEffect(() => {
    // Lancer automatiquement au chargement
    runDiagnostic();
  }, []);

  return (
    <div style={{ 
      fontFamily: 'Arial, sans-serif', 
      padding: '20px',
      maxWidth: '1000px',
      margin: '0 auto',
      backgroundColor: '#f8fafc',
      minHeight: '100vh'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '30px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}>
        <h1 style={{ 
          color: '#1e293b', 
          marginBottom: '8px',
          fontSize: '28px',
          fontWeight: 'bold'
        }}>
          🔧 Diagnostic et Correction Supabase
        </h1>
        
        <p style={{ 
          color: '#64748b', 
          marginBottom: '30px',
          fontSize: '16px'
        }}>
          Test automatique de connectivité et diagnostic des problèmes Supabase pour MindFlow Pro
        </p>

        <div style={{ marginBottom: '30px' }}>
          <button
            onClick={runDiagnostic}
            disabled={isRunning}
            style={{
              backgroundColor: isRunning ? '#9ca3af' : '#2563eb',
              color: 'white',
              border: 'none',
              padding: '14px 28px',
              borderRadius: '8px',
              cursor: isRunning ? 'not-allowed' : 'pointer',
              marginRight: '12px',
              fontSize: '16px',
              fontWeight: '600',
              transition: 'all 0.2s'
            }}
          >
            {isRunning ? '🔄 Diagnostic en cours...' : '🚀 Relancer le diagnostic'}
          </button>

          <button
            onClick={clearResults}
            style={{
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              padding: '14px 28px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: '600'
            }}
          >
            🧹 Effacer
          </button>
        </div>

        {results.length > 0 && (
          <div>
            <h2 style={{ 
              color: '#1e293b', 
              marginBottom: '20px',
              fontSize: '22px',
              fontWeight: '600'
            }}>
              📋 Résultats du diagnostic:
            </h2>
            
            {results.map((result, index) => (
              <div key={index} style={{
                backgroundColor: '#f8fafc',
                border: `2px solid ${getStatusColor(result.status)}`,
                borderRadius: '8px',
                padding: '20px',
                marginBottom: '15px',
                transition: 'all 0.2s'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '10px'
                }}>
                  <span style={{ marginRight: '12px', fontSize: '20px' }}>
                    {getStatusIcon(result.status)}
                  </span>
                  <strong style={{ 
                    color: '#1e293b',
                    fontSize: '18px',
                    flex: 1
                  }}>
                    {result.name}
                  </strong>
                  <span style={{ 
                    fontSize: '13px', 
                    color: '#6b7280',
                    backgroundColor: '#e2e8f0',
                    padding: '4px 8px',
                    borderRadius: '4px'
                  }}>
                    {result.timestamp}
                  </span>
                </div>
                
                <p style={{ 
                  margin: '10px 0', 
                  color: '#374151',
                  fontSize: '15px',
                  lineHeight: '1.6'
                }}>
                  {result.message}
                </p>
                
                {result.details && (
                  <details style={{ marginTop: '15px' }}>
                    <summary style={{ 
                      cursor: 'pointer', 
                      color: '#6366f1',
                      fontSize: '14px',
                      fontWeight: '500',
                      padding: '8px 0'
                    }}>
                      📄 Afficher les détails techniques
                    </summary>
                    <pre style={{
                      backgroundColor: '#1e293b',
                      color: '#e2e8f0',
                      padding: '15px',
                      borderRadius: '6px',
                      fontSize: '12px',
                      overflow: 'auto',
                      marginTop: '10px',
                      border: '1px solid #334155',
                      lineHeight: '1.4'
                    }}>
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}

        <div style={{
          marginTop: '40px',
          padding: '20px',
          backgroundColor: '#fef3c7',
          border: '1px solid #f59e0b',
          borderRadius: '8px'
        }}>
          <h3 style={{ 
            margin: '0 0 15px 0', 
            color: '#92400e',
            fontSize: '18px',
            fontWeight: '600'
          }}>
            💡 Solutions aux problèmes courants:
          </h3>
          <ul style={{ 
            margin: 0, 
            paddingLeft: '20px', 
            color: '#92400e',
            lineHeight: '1.6'
          }}>
            <li><strong>Clé API invalide:</strong> Vérifiez votre dashboard Supabase et régénérez les clés si nécessaire</li>
            <li><strong>Variables d'environnement:</strong> Créez le fichier .env.local avec les bonnes valeurs</li>
            <li><strong>Erreur de réseau:</strong> Vérifiez votre connexion internet et les paramètres proxy</li>
            <li><strong>CORS:</strong> Ajoutez votre domaine dans les paramètres Supabase si nécessaire</li>
          </ul>
        </div>
      </div>
    </div>
  );
}