'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthStore } from '@/stores/authStore';

interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  dateOfBirth?: string;
  bio?: string;
  profilePicture?: string;
  preferences?: {
    notifications?: {
      email: boolean;
      push: boolean;
      sms: boolean;
      journalReminders: boolean;
      aiCoachSuggestions: boolean;
      weeklyReports: boolean;
    };
    privacy?: {
      shareDataForResearch: boolean;
      allowAnalytics: boolean;
      publicProfile: boolean;
    };
    wellness?: {
      dailyCheckInTime?: string;
      weeklyGoalReminder?: boolean;
      moodTrackingFrequency: 'daily' | 'weekly' | 'as_needed';
    };
  };
}

export default function ProfilePage() {
  const { user, isAuthenticated } = useAuthStore();
  const router = useRouter();
  
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'profile' | 'notifications' | 'privacy' | 'wellness'>('profile');

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  // Load user profile
  useEffect(() => {
    const loadProfile = async () => {
      if (!isAuthenticated || !user) return;

      try {
        setIsLoading(true);
        setError(null);

        // For now, we'll use the user data from auth store
        // In a real app, you'd have a separate profile endpoint
        const profileData: UserProfile = {
          ...user,
          preferences: {
            notifications: {
              email: user.preferences?.notifications?.email ?? true,
              push: user.preferences?.notifications?.push ?? true,
              sms: user.preferences?.notifications?.sms ?? false,
              journalReminders: true,
              aiCoachSuggestions: true,
              weeklyReports: true,
            },
            privacy: {
              shareDataForResearch: user.preferences?.privacy?.dataSharing ?? false,
              allowAnalytics: true,
              publicProfile: user.preferences?.privacy?.profileVisible ?? false,
            },
            wellness: {
              dailyCheckInTime: '09:00',
              weeklyGoalReminder: true,
              moodTrackingFrequency: 'daily',
            },
          },
        };

        setProfile(profileData);
      } catch (err: unknown) {
        const errorMessage = (err as { message?: string })?.message || 'Failed to load profile';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    loadProfile();
  }, [isAuthenticated, user]);

  const handleProfileUpdate = async (field: string, value: unknown) => {
    if (!profile) return;

    setProfile(prev => prev ? { ...prev, [field]: value } : null);
  };

  const handlePreferenceUpdate = (category: string, field: string, value: unknown) => {
    if (!profile) return;

    setProfile(prev => prev ? {
      ...prev,
      preferences: {
        ...prev.preferences,
        [category]: {
          ...prev.preferences?.[category as keyof typeof prev.preferences],
          [field]: value,
        },
      },
    } : null);
  };

  const handleSave = async () => {
    if (!profile) return;

    setIsSaving(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // In a real app, you'd call an API to update the profile
      // await apiService.updateProfile(profile);
      
      // For now, we'll simulate a successful save
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccessMessage('Profile updated successfully!');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err: unknown) {
      const errorMessage = (err as { message?: string })?.message || 'Failed to update profile';
      setError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  if (!isAuthenticated || !user) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your profile...</p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">Failed to load profile</p>
          <Button onClick={() => router.push('/dashboard')} className="mt-4">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => router.push('/dashboard')}
                className="text-gray-600 hover:text-gray-900"
              >
                ← Back to Dashboard
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">Profile & Settings</h1>
            </div>
            <Button 
              onClick={handleSave} 
              disabled={isSaving}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Success/Error Messages */}
        {successMessage && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md mb-6">
            {successMessage}
          </div>
        )}
        
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'profile', label: 'Profile', icon: '👤' },
              { id: 'notifications', label: 'Notifications', icon: '🔔' },
              { id: 'privacy', label: 'Privacy', icon: '🔒' },
              { id: 'wellness', label: 'Wellness', icon: '🧘' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as typeof activeTab)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
                <CardDescription>Update your personal details and contact information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      First Name
                    </label>
                    <Input
                      type="text"
                      value={profile.firstName}
                      onChange={(e) => handleProfileUpdate('firstName', e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Last Name
                    </label>
                    <Input
                      type="text"
                      value={profile.lastName}
                      onChange={(e) => handleProfileUpdate('lastName', e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <Input
                    type="email"
                    value={profile.email}
                    onChange={(e) => handleProfileUpdate('email', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <Input
                    type="tel"
                    value={profile.phone || ''}
                    onChange={(e) => handleProfileUpdate('phone', e.target.value)}
                    placeholder="(*************"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date of Birth
                  </label>
                  <Input
                    type="date"
                    value={profile.dateOfBirth || ''}
                    onChange={(e) => handleProfileUpdate('dateOfBirth', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bio
                  </label>
                  <textarea
                    value={profile.bio || ''}
                    onChange={(e) => handleProfileUpdate('bio', e.target.value)}
                    rows={4}
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
                    placeholder="Tell us a bit about yourself and your wellness journey..."
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Notifications Tab */}
        {activeTab === 'notifications' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Notification Preferences</CardTitle>
                <CardDescription>Choose how you&apos;d like to receive updates and reminders</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Communication Channels</h4>
                  <div className="space-y-3">
                    {[
                      { key: 'email', label: 'Email Notifications', description: 'Receive updates via email' },
                      { key: 'push', label: 'Push Notifications', description: 'Browser and mobile push notifications' },
                      { key: 'sms', label: 'SMS Notifications', description: 'Text message notifications (charges may apply)' },
                    ].map((channel) => (
                      <div key={channel.key} className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">{channel.label}</div>
                          <div className="text-sm text-gray-500">{channel.description}</div>
                        </div>
                        <input
                          type="checkbox"
                          checked={profile.preferences?.notifications?.[channel.key as keyof typeof profile.preferences.notifications] || false}
                          onChange={(e) => handlePreferenceUpdate('notifications', channel.key, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Content Preferences</h4>
                  <div className="space-y-3">
                    {[
                      { key: 'journalReminders', label: 'Journal Reminders', description: 'Daily reminders to write in your journal' },
                      { key: 'aiCoachSuggestions', label: 'AI Coach Suggestions', description: 'Personalized wellness tips and check-ins' },
                      { key: 'weeklyReports', label: 'Weekly Progress Reports', description: 'Summary of your wellness journey' },
                    ].map((content) => (
                      <div key={content.key} className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">{content.label}</div>
                          <div className="text-sm text-gray-500">{content.description}</div>
                        </div>
                        <input
                          type="checkbox"
                          checked={profile.preferences?.notifications?.[content.key as keyof typeof profile.preferences.notifications] || false}
                          onChange={(e) => handlePreferenceUpdate('notifications', content.key, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Privacy Tab */}
        {activeTab === 'privacy' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Privacy Settings</CardTitle>
                <CardDescription>Control how your data is used and shared</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  {[
                    { 
                      key: 'shareDataForResearch', 
                      label: 'Share Data for Research', 
                      description: 'Help improve mental health research by sharing anonymized data',
                      warning: 'All data is anonymized and cannot be traced back to you'
                    },
                    { 
                      key: 'allowAnalytics', 
                      label: 'Allow Analytics', 
                      description: 'Help us improve the platform with usage analytics',
                      warning: 'No personal health data is included in analytics'
                    },
                    { 
                      key: 'publicProfile', 
                      label: 'Public Profile', 
                      description: 'Allow other users to see your basic profile information',
                      warning: 'Only your name and bio will be visible to other users'
                    },
                  ].map((setting) => (
                    <div key={setting.key} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium text-gray-900">{setting.label}</div>
                        <input
                          type="checkbox"
                          checked={profile.preferences?.privacy?.[setting.key as keyof typeof profile.preferences.privacy] || false}
                          onChange={(e) => handlePreferenceUpdate('privacy', setting.key, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                      <div className="text-sm text-gray-600 mb-1">{setting.description}</div>
                      <div className="text-xs text-gray-500 italic">{setting.warning}</div>
                    </div>
                  ))}
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h5 className="font-medium text-blue-800 mb-2">Data Protection</h5>
                  <p className="text-sm text-blue-700">
                    Your mental health data is encrypted and stored securely. We never share personal health information 
                    with third parties without your explicit consent. You can request data deletion at any time.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Wellness Tab */}
        {activeTab === 'wellness' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Wellness Preferences</CardTitle>
                <CardDescription>Customize your wellness tracking and reminder settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Daily Check-in Time
                  </label>
                  <Input
                    type="time"
                    value={profile.preferences?.wellness?.dailyCheckInTime || '09:00'}
                    onChange={(e) => handlePreferenceUpdate('wellness', 'dailyCheckInTime', e.target.value)}
                    className="w-48"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    When would you like to receive daily wellness check-in reminders?
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mood Tracking Frequency
                  </label>
                  <select
                    value={profile.preferences?.wellness?.moodTrackingFrequency || 'daily'}
                    onChange={(e) => handlePreferenceUpdate('wellness', 'moodTrackingFrequency', e.target.value)}
                    className="w-48 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="as_needed">As Needed</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    How often would you like to track your mood?
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-900">Weekly Goal Reminders</div>
                    <div className="text-sm text-gray-500">Receive reminders to set and review weekly wellness goals</div>
                  </div>
                  <input
                    type="checkbox"
                    checked={profile.preferences?.wellness?.weeklyGoalReminder || false}
                    onChange={(e) => handlePreferenceUpdate('wellness', 'weeklyGoalReminder', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Professional Disclaimer */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <span className="text-blue-600 text-lg">ℹ️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Privacy & Professional Disclaimer
              </h3>
              <p className="mt-1 text-sm text-blue-700">
                Your privacy is our priority. All personal and health data is encrypted and stored securely. 
                MindFlow Pro is designed to supplement, not replace, professional mental health care. 
                If you&apos;re experiencing a mental health crisis, please contact a professional immediately.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
