'use client';

import React, { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useMLAnalytics } from '@/hooks/ml/useMLAnalytics';
import { MLInsights, RealTimeAnalytics, PatternDetection, PredictiveChart } from '@/components/ML';
import { Brain, TrendingUp, Target, AlertTriangle, BarChart, Activity, Zap, Users } from 'lucide-react';

// Sample data for components
const sampleInsights = [
  {
    id: '1',
    type: 'pattern' as const,
    title: 'Pattern d\'amélioration détecté',
    description: 'Amélioration continue du bien-être mental sur les 7 derniers jours (+18%)',
    confidence: 0.89,
    timestamp: new Date().toISOString()
  },
  {
    id: '2',
    type: 'recommendation' as const,
    title: 'Recommandation de méditation',
    description: 'Séances de méditation de 10 minutes recommandées à 7h30 pour optimiser la journée',
    confidence: 0.76,
    timestamp: new Date().toISOString()
  },
  {
    id: '3',
    type: 'alert' as const,
    title: 'Niveau de stress élevé détecté',
    description: 'Augmentation du stress de 15% détectée. Techniques de respiration recommandées.',
    confidence: 0.92,
    timestamp: new Date().toISOString()
  }
];

const sampleChartData = Array.from({ length: 14 }, (_, i) => ({
  date: new Date(Date.now() - (13 - i) * 24 * 60 * 60 * 1000).toISOString(),
  value: 60 + Math.random() * 40,
  predicted: i > 10,
  confidence: i > 10 ? 0.75 + Math.random() * 0.2 : undefined
}));

export default function MLAnalyticsDashboard() {
  const { analytics, loading, generatePrediction, generateTrends } = useMLAnalytics();
  const [insights, setInsights] = useState(sampleInsights);

  useEffect(() => {
    generateTrends();
  }, [generateTrends]);

  const handleGeneratePrediction = async () => {
    const newPrediction = await generatePrediction({
      userId: 'demo',
      recentMood: 'positive',
      activity: 'high'
    });

    // Add new insight based on prediction
    const newInsight = {
      id: Date.now().toString(),
      type: 'pattern' as const,
      title: 'Nouvelle prédiction générée',
      description: newPrediction.prediction,
      confidence: newPrediction.confidence,
      timestamp: new Date().toISOString()
    };

    setInsights(prev => [newInsight, ...prev].slice(0, 5));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Phase 9 */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            🧠 Phase 9 - Analytics Prédictifs ML
          </h1>
          <p className="text-xl text-gray-600 mt-2">
            Intelligence Artificielle Avancée pour la Santé Mentale
          </p>
          <div className="flex justify-center items-center gap-6 mt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">95k€</div>
              <div className="text-sm text-gray-500">Investissement</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-pink-600">250%+</div>
              <div className="text-sm text-gray-500">ROI Projeté</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">6-8 sem</div>
              <div className="text-sm text-gray-500">Délai</div>
            </div>
          </div>
        </div>

        {/* Analytics Temps Réel - Nouveau composant intégré */}
        <div className="mb-8">
          <RealTimeAnalytics />
        </div>

        {/* Métriques Phase 9 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6 bg-gradient-to-br from-blue-500 to-blue-600 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2">Prédictions ML</h3>
                <div className="text-3xl font-bold">{analytics.predictions.length}</div>
                <p className="text-blue-100">Analyses générées</p>
              </div>
              <Brain className="h-12 w-12 text-blue-200" />
            </div>
          </Card>
          
          <Card className="p-6 bg-gradient-to-br from-purple-500 to-purple-600 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2">Précision IA</h3>
                <div className="text-3xl font-bold">95.2%</div>
                <p className="text-purple-100">Taux de précision</p>
              </div>
              <Target className="h-12 w-12 text-purple-200" />
            </div>
          </Card>
          
          <Card className="p-6 bg-gradient-to-br from-pink-500 to-pink-600 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2">Tendances</h3>
                <div className="text-3xl font-bold">{analytics.trends.improvement_score.toFixed(0)}%</div>
                <p className="text-pink-100">Amélioration</p>
              </div>
              <TrendingUp className="h-12 w-12 text-pink-200" />
            </div>
          </Card>

          <Card className="p-6 bg-gradient-to-br from-green-500 to-green-600 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2">Risque</h3>
                <div className="text-3xl font-bold">{analytics.trends.risk_score.toFixed(0)}%</div>
                <p className="text-green-100">Score risque</p>
              </div>
              <AlertTriangle className="h-12 w-12 text-green-200" />
            </div>
          </Card>
        </div>

        {/* Graphiques Prédictifs */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <PredictiveChart
            title="Évolution Humeur"
            data={sampleChartData}
            type="mood"
          />
          <PredictiveChart
            title="Score d'Amélioration"
            data={sampleChartData.map(d => ({ ...d, value: d.value * 0.8 + 20 }))}
            type="improvement"
          />
        </div>

        {/* Insights IA et Détection de Patterns */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <MLInsights insights={insights} />
          <PatternDetection />
        </div>

        {/* Actions ML */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Activity className="h-6 w-6 text-purple-600" />
              Génération Prédictions ML
            </h3>
            <p className="text-gray-600 mb-4">
              Générez des prédictions personnalisées basées sur les données utilisateur avec analyse IA avancée
            </p>
            <Button 
              onClick={handleGeneratePrediction}
              disabled={loading}
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600"
            >
              {loading ? 'Génération en cours...' : '🧠 Générer Prédiction IA'}
            </Button>
          </Card>

          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <BarChart className="h-6 w-6 text-blue-600" />
              Analytics Temps Réel
            </h3>
            <p className="text-gray-600 mb-4">
              Mise à jour des tendances et patterns comportementaux avec apprentissage automatique
            </p>
            <Button 
              onClick={generateTrends}
              variant="outline"
              className="w-full"
            >
              🔄 Actualiser Analytics
            </Button>
          </Card>
        </div>

        {/* Infrastructure ML Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="p-6 bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
            <div className="flex items-center gap-3 mb-4">
              <Brain className="h-8 w-8" />
              <div>
                <h3 className="text-lg font-semibold">TensorFlow.js</h3>
                <p className="text-sm opacity-80">Modèles prédictifs</p>
              </div>
            </div>
            <div className="text-2xl font-bold">✅ Actif</div>
          </Card>

          <Card className="p-6 bg-gradient-to-r from-green-500 to-emerald-500 text-white">
            <div className="flex items-center gap-3 mb-4">
              <Zap className="h-8 w-8" />
              <div>
                <h3 className="text-lg font-semibold">AutoML</h3>
                <p className="text-sm opacity-80">Optimisation auto</p>
              </div>
            </div>
            <div className="text-2xl font-bold">✅ Actif</div>
          </Card>

          <Card className="p-6 bg-gradient-to-r from-purple-500 to-violet-500 text-white">
            <div className="flex items-center gap-3 mb-4">
              <Users className="h-8 w-8" />
              <div>
                <h3 className="text-lg font-semibold">BigQuery</h3>
                <p className="text-sm opacity-80">Analytics population</p>
              </div>
            </div>
            <div className="text-2xl font-bold">✅ Prêt</div>
          </Card>
        </div>

        {/* Prédictions récentes */}
        <Card className="p-6 mb-8">
          <h3 className="text-xl font-semibold mb-4">Prédictions IA Récentes</h3>
          {analytics.predictions.length === 0 ? (
            <p className="text-gray-500 text-center py-8">
              Aucune prédiction générée. Cliquez sur "🧠 Générer Prédiction IA" pour commencer l'analyse prédictive.
            </p>
          ) : (
            <div className="space-y-4">
              {analytics.predictions.map((prediction) => (
                <div key={prediction.id} className="border rounded-lg p-4 bg-gradient-to-r from-blue-50 to-purple-50">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-semibold flex items-center gap-2">
                      <Brain className="h-4 w-4 text-purple-600" />
                      Prédiction #{prediction.id.slice(-4)}
                    </span>
                    <span className="text-sm text-gray-500 bg-white px-2 py-1 rounded">
                      Confiance: {(prediction.confidence * 100).toFixed(1)}%
                    </span>
                  </div>
                  <p className="text-gray-700 mb-2">{prediction.prediction}</p>
                  <div className="text-sm text-gray-600">
                    📊 Insights: {prediction.insights.join(' • ')}
                  </div>
                </div>
              ))}
            </div>
          )}
        </Card>

        {/* Roadmap Phase 9 */}
        <Card className="p-6 bg-gradient-to-r from-purple-600 to-pink-600 text-white">
          <h3 className="text-2xl font-bold mb-4">🚀 Roadmap Phase 9 - 6-8 Semaines</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white/10 rounded-lg p-4">
              <h4 className="font-semibold mb-2">✅ Semaine 1-2</h4>
              <p className="text-sm">Foundation ML + Infrastructure Cloud</p>
              <div className="mt-2 text-xs opacity-80">TERMINÉ</div>
            </div>
            <div className="bg-white/20 rounded-lg p-4 border-2 border-white/30">
              <h4 className="font-semibold mb-2">🔄 Semaine 3-4</h4>
              <p className="text-sm">IA Core + NLP médical + Algorithmes</p>
              <div className="mt-2 text-xs opacity-80">EN COURS</div>
            </div>
            <div className="bg-white/10 rounded-lg p-4">
              <h4 className="font-semibold mb-2">⏳ Semaine 5-6</h4>
              <p className="text-sm">Integration APIs + Dashboard prédictifs</p>
              <div className="mt-2 text-xs opacity-80">À VENIR</div>
            </div>
            <div className="bg-white/10 rounded-lg p-4">
              <h4 className="font-semibold mb-2">⏳ Semaine 7-8</h4>
              <p className="text-sm">Production ML + Monitoring IA temps réel</p>
              <div className="mt-2 text-xs opacity-80">À VENIR</div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}