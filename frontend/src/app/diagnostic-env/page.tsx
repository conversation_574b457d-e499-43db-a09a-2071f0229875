'use client';

import React from 'react';

export default function DiagnosticEnv() {
  const envVars = {
    'NEXT_PUBLIC_SUPABASE_URL': process.env.NEXT_PUBLIC_SUPABASE_URL,
    'NEXT_PUBLIC_SUPABASE_ANON_KEY': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Configurée (masquée)' : 'MANQUANTE',
    'NEXT_PUBLIC_DUAL_DATABASE_MODE': process.env.NEXT_PUBLIC_DUAL_DATABASE_MODE,
    'NEXT_PUBLIC_USE_SUPABASE_AUTH': process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH,
    'NEXT_PUBLIC_USE_SUPABASE_DATABASE': process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE,
    'NEXT_PUBLIC_MIGRATE_USER_DATA': process.env.NEXT_PUBLIC_MIGRATE_USER_DATA,
    'NEXT_PUBLIC_DEBUG_MIGRATION': process.env.NEXT_PUBLIC_DEBUG_MIGRATION,
    'NODE_ENV': process.env.NODE_ENV,
  };

  const missingVars = Object.entries(envVars).filter(([key, value]) => !value);
  const configuredVars = Object.entries(envVars).filter(([key, value]) => !!value);

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace',
      backgroundColor: '#1a1a1a',
      color: '#00ff00',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#00ffff', marginBottom: '30px' }}>
        🔍 DIAGNOSTIC VARIABLES D'ENVIRONNEMENT
      </h1>
      
      <div style={{ marginBottom: '30px' }}>
        <h2 style={{ color: '#ffff00', marginBottom: '15px' }}>
          ✅ VARIABLES CONFIGURÉES ({configuredVars.length})
        </h2>
        {configuredVars.length > 0 ? (
          <div style={{ backgroundColor: '#003300', padding: '15px', borderRadius: '5px' }}>
            {configuredVars.map(([key, value]) => (
              <div key={key} style={{ margin: '5px 0', fontSize: '14px' }}>
                <strong style={{ color: '#00ff00' }}>{key}:</strong> 
                <span style={{ color: '#ffffff', marginLeft: '10px' }}>{String(value)}</span>
              </div>
            ))}
          </div>
        ) : (
          <p style={{ color: '#ff6666' }}>Aucune variable configurée !</p>
        )}
      </div>

      <div style={{ marginBottom: '30px' }}>
        <h2 style={{ color: '#ff6666', marginBottom: '15px' }}>
          ❌ VARIABLES MANQUANTES ({missingVars.length})
        </h2>
        {missingVars.length > 0 ? (
          <div style={{ backgroundColor: '#330000', padding: '15px', borderRadius: '5px' }}>
            {missingVars.map(([key]) => (
              <div key={key} style={{ margin: '5px 0', fontSize: '14px', color: '#ff6666' }}>
                ❌ {key}
              </div>
            ))}
          </div>
        ) : (
          <p style={{ color: '#00ff00' }}>Toutes les variables sont configurées ! ✅</p>
        )}
      </div>

      <div style={{ 
        backgroundColor: '#000033', 
        padding: '20px', 
        borderRadius: '5px',
        border: '2px solid #0066ff'
      }}>
        <h3 style={{ color: '#00ffff', marginBottom: '15px' }}>
          📋 CONFIGURATION RECOMMANDÉE .env.local
        </h3>
        <pre style={{ 
          color: '#cccccc', 
          fontSize: '12px',
          whiteSpace: 'pre-wrap',
          lineHeight: '1.4'
        }}>
{`# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUwNDY0ODQsImV4cCI6MjA1MDYyMjQ4NH0.A0HBpJZKqQlUQLGnQK5p3FsHxJKqGZHrUHOEh_Bs2gg

# Feature Flags
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_DUAL_DATABASE_MODE=true
NEXT_PUBLIC_MIGRATE_USER_DATA=true
NEXT_PUBLIC_DEBUG_MIGRATION=true

# Development
NODE_ENV=development`}
        </pre>
      </div>

      <div style={{ 
        marginTop: '30px',
        padding: '15px',
        backgroundColor: '#003366',
        borderRadius: '5px'
      }}>
        <h3 style={{ color: '#00ffff' }}>🛠️ INSTRUCTIONS</h3>
        <ol style={{ color: '#ffffff', fontSize: '14px' }}>
          <li>Arrêter le serveur Next.js (Ctrl+C)</li>
          <li>Créer/éditer le fichier <code style={{ color: '#ffff00' }}>frontend/.env.local</code></li>
          <li>Copier la configuration ci-dessus</li>
          <li>Redémarrer le serveur avec <code style={{ color: '#ffff00' }}>npm run dev</code></li>
          <li>Recharger cette page pour voir les changements</li>
        </ol>
      </div>

      <div style={{ 
        marginTop: '20px',
        fontSize: '12px',
        color: '#888888',
        textAlign: 'center'
      }}>
        Page générée le: {new Date().toLocaleString('fr-FR')} | 
        URL: http://localhost:3000/diagnostic-env
      </div>
    </div>
  );
} 