'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import { 
  ArrowLeft,
  Star, 
  Clock, 
  DollarSign, 
  Video, 
  Phone, 
  MessageCircle, 
  Users,
  Award,
  Calendar,
  MapPin,
  Globe,
  GraduationCap,
  Shield,
  CheckCircle,
  AlertTriangle,
  User
} from 'lucide-react';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext2';
import Navigation from '@/components/Layout/Navigation';

interface Professional {
  id: string;
  user: {
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  professionalType: string;
  specializations: string[];
  treatmentApproaches: string[];
  languagesSpoken: string[];
  yearsOfExperience: number;
  professionalBio: string;
  education: Array<{
    degree: string;
    institution: string;
    year: number;
    field: string;
  }>;
  certifications: Array<{
    name: string;
    issuingOrganization: string;
    issueDate: string;
    expiryDate?: string;
    credentialId?: string;
  }>;
  hourlyRate?: number;
  currency: string;
  acceptingNewClients: boolean;
  averageRating: number;
  totalReviews: number;
  totalAppointments: number;
  consultationTypes: Array<{
    type: 'video' | 'phone' | 'chat' | 'in_person';
    duration: number;
    price: number;
    currency: string;
  }>;
  verificationStatus: string;
  verifiedAt?: string;
  licensingBoard: string;
  licensingState: string;
  licenseNumber: string;
  insuranceAccepted: string[];
}

const ProfessionalProfilePage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;
  const { user } = useAuth();
  const [professional, setProfessional] = useState<Professional | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedConsultationType, setSelectedConsultationType] = useState<string>('');

  useEffect(() => {
    if (id) {
      fetchProfessional();
    }
  }, [id]);

  const fetchProfessional = async () => {
    try {
      setLoading(true);
      const response = await apiService.getProfessional(id as string);
      setProfessional(response.data.professional);
      
      // Set default consultation type
      if (response.data.professional.consultationTypes.length > 0) {
        setSelectedConsultationType(response.data.professional.consultationTypes[0].type);
      }
    } catch (error) {
      console.error('Error fetching professional:', error);
      router.push('/professionals');
    } finally {
      setLoading(false);
    }
  };

  const formatProfessionalType = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  };

  const getConsultationTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="w-5 h-5" />;
      case 'phone': return <Phone className="w-5 h-5" />;
      case 'chat': return <MessageCircle className="w-5 h-5" />;
      case 'in_person': return <Users className="w-5 h-5" />;
      default: return <Video className="w-5 h-5" />;
    }
  };

  const handleBookAppointment = () => {
    if (!user) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/professionals/${id}`));
      return;
    }
    
    const selectedType = professional?.consultationTypes.find(ct => ct.type === selectedConsultationType);
    if (selectedType) {
      router.push(`/appointments/book?professionalId=${id}&type=${selectedConsultationType}&price=${selectedType.price}&duration=${selectedType.duration}`);
    }
  };

  if (loading) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </>
    );
  }

  if (!professional) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Professional Not Found</h2>
            <p className="text-gray-600 mb-4">The professional you're looking for doesn't exist.</p>
            <Link href="/professionals" className="text-blue-600 hover:text-blue-800">
              ← Back to Directory
            </Link>
          </div>
        </div>
      </>
    );
  }

  const selectedConsultation = professional.consultationTypes.find(ct => ct.type === selectedConsultationType);

  return (
    <>
      <Head>
        <title>{professional.user.firstName} {professional.user.lastName} - MindFlow Pro</title>
        <meta name="description" content={professional.professionalBio} />
      </Head>

      <Navigation />
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <Link 
              href="/professionals"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Directory
            </Link>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Professional Header */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-start space-x-6">
                  <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    {professional.user.profilePicture ? (
                      <Image
                        src={professional.user.profilePicture}
                        alt={`${professional.user.firstName} ${professional.user.lastName}`}
                        width={96}
                        height={96}
                        className="w-24 h-24 rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-12 h-12 text-blue-600" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h1 className="text-3xl font-bold text-gray-900">
                        {professional.user.firstName} {professional.user.lastName}
                      </h1>
                      {professional.verificationStatus === 'verified' && (
                        <div className="flex items-center space-x-1 bg-green-100 px-2 py-1 rounded-full">
                          <Shield className="w-4 h-4 text-green-600" />
                          <span className="text-sm text-green-800 font-medium">Verified</span>
                        </div>
                      )}
                    </div>
                    
                    <p className="text-xl text-gray-600 mb-3">
                      {formatProfessionalType(professional.professionalType)}
                    </p>
                    
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="flex items-center">
                        <Star className="w-5 h-5 text-yellow-400 fill-current" />
                        <span className="ml-1 text-lg font-semibold text-gray-900">
                          {professional.averageRating.toFixed(1)}
                        </span>
                        <span className="ml-1 text-gray-600">
                          ({professional.totalReviews} reviews)
                        </span>
                      </div>
                      
                      <div className="flex items-center text-gray-600">
                        <Calendar className="w-5 h-5 mr-1" />
                        {professional.totalAppointments} sessions completed
                      </div>
                      
                      <div className="flex items-center text-gray-600">
                        <Clock className="w-5 h-5 mr-1" />
                        {professional.yearsOfExperience} years experience
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${
                        professional.acceptingNewClients ? 'bg-green-500' : 'bg-red-500'
                      }`} />
                      <span className={`font-medium ${
                        professional.acceptingNewClients ? 'text-green-700' : 'text-red-700'
                      }`}>
                        {professional.acceptingNewClients ? 'Accepting new clients' : 'Not accepting new clients'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* About */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">About</h2>
                <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                  {professional.professionalBio}
                </p>
              </div>

              {/* Specializations & Approaches */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Specializations & Approaches</h2>

                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Specializations</h3>
                    <div className="flex flex-wrap gap-2">
                      {professional.specializations.map((spec, index) => (
                        <span
                          key={index}
                          className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                        >
                          {spec}
                        </span>
                      ))}
                    </div>
                  </div>

                  {professional.treatmentApproaches.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Treatment Approaches</h3>
                      <div className="flex flex-wrap gap-2">
                        {professional.treatmentApproaches.map((approach, index) => (
                          <span
                            key={index}
                            className="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                          >
                            {approach}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Languages</h3>
                    <div className="flex flex-wrap gap-2">
                      {professional.languagesSpoken.map((language, index) => (
                        <span
                          key={index}
                          className="inline-block px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm"
                        >
                          {language}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Education & Credentials */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Education & Credentials</h2>

                <div className="space-y-6">
                  {/* Education */}
                  {professional.education.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                        <GraduationCap className="w-5 h-5 mr-2" />
                        Education
                      </h3>
                      <div className="space-y-3">
                        {professional.education.map((edu, index) => (
                          <div key={index} className="border-l-4 border-blue-200 pl-4">
                            <h4 className="font-medium text-gray-900">{edu.degree}</h4>
                            <p className="text-gray-600">{edu.institution}</p>
                            <p className="text-sm text-gray-500">{edu.year} • {edu.field}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Certifications */}
                  {professional.certifications.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                        <Award className="w-5 h-5 mr-2" />
                        Certifications
                      </h3>
                      <div className="space-y-3">
                        {professional.certifications.map((cert, index) => (
                          <div key={index} className="border-l-4 border-green-200 pl-4">
                            <h4 className="font-medium text-gray-900">{cert.name}</h4>
                            <p className="text-gray-600">{cert.issuingOrganization}</p>
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span>Issued: {new Date(cert.issueDate).getFullYear()}</span>
                              {cert.expiryDate && (
                                <span>Expires: {new Date(cert.expiryDate).getFullYear()}</span>
                              )}
                              {cert.credentialId && (
                                <span>ID: {cert.credentialId}</span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* License Information */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                      <Shield className="w-5 h-5 mr-2" />
                      License Information
                    </h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <span className="text-sm font-medium text-gray-700">License Number:</span>
                          <p className="text-gray-900">{professional.licenseNumber}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-700">Licensing Board:</span>
                          <p className="text-gray-900">{professional.licensingBoard}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-700">State:</span>
                          <p className="text-gray-900">{professional.licensingState}</p>
                        </div>
                        {professional.verifiedAt && (
                          <div>
                            <span className="text-sm font-medium text-gray-700">Verified:</span>
                            <p className="text-gray-900 flex items-center">
                              <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
                              {new Date(professional.verifiedAt).toLocaleDateString()}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Insurance */}
              {professional.insuranceAccepted.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Insurance Accepted</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {professional.insuranceAccepted.map((insurance, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="text-gray-700">{insurance}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Booking Card */}
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Book a Session</h3>

                {professional.acceptingNewClients ? (
                  <div className="space-y-4">
                    {/* Consultation Type Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Session Type
                      </label>
                      <div className="space-y-2">
                        {professional.consultationTypes.map((type) => (
                          <label key={type.type} className="flex items-center space-x-3 cursor-pointer">
                            <input
                              type="radio"
                              name="consultationType"
                              value={type.type}
                              checked={selectedConsultationType === type.type}
                              onChange={(e) => setSelectedConsultationType(e.target.value)}
                              className="text-blue-600 focus:ring-blue-500"
                            />
                            <div className="flex items-center space-x-2 flex-1">
                              {getConsultationTypeIcon(type.type)}
                              <span className="text-sm font-medium capitalize">
                                {type.type.replace('_', ' ')}
                              </span>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-semibold text-gray-900">
                                ${type.price}
                              </div>
                              <div className="text-xs text-gray-500">
                                {type.duration} min
                              </div>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>

                    {selectedConsultation && (
                      <div className="bg-blue-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-blue-900">Session Details</span>
                        </div>
                        <div className="space-y-1 text-sm text-blue-800">
                          <div className="flex justify-between">
                            <span>Duration:</span>
                            <span>{selectedConsultation.duration} minutes</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Price:</span>
                            <span>${selectedConsultation.price} {selectedConsultation.currency}</span>
                          </div>
                        </div>
                      </div>
                    )}

                    <button
                      onClick={handleBookAppointment}
                      className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                    >
                      Book Appointment
                    </button>

                    <p className="text-xs text-gray-500 text-center">
                      You'll be able to select your preferred date and time on the next page
                    </p>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <AlertTriangle className="w-8 h-8 text-amber-500 mx-auto mb-2" />
                    <p className="text-gray-600 mb-2">Not accepting new clients</p>
                    <p className="text-sm text-gray-500">
                      This professional is currently at capacity
                    </p>
                  </div>
                )}
              </div>

              {/* Professional Disclaimer */}
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-amber-800">
                      Professional Verification Notice
                    </h3>
                    <p className="mt-1 text-sm text-amber-700">
                      This professional has been verified for credentials and licensing.
                      However, this platform is for informational purposes only and does not replace
                      professional medical advice, diagnosis, or treatment.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProfessionalProfilePage;
