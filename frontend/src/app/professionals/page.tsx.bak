'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Head from 'next/head';
import Link from 'next/link';
import { 
  Search, 
  Filter, 
  MapPin, 
  Star, 
  Clock, 
  DollarSign, 
  Video, 
  Phone, 
  MessageCircle, 
  Users,
  Award,
  Calendar,
  ChevronRight,
  User
} from 'lucide-react';
import { apiService } from '@/services/api';
import Navigation from '@/components/Layout/Navigation';

export const dynamic = 'force-dynamic';

interface Professional {
  id: string;
  user: {
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  professionalType: string;
  specializations: string[];
  languagesSpoken: string[];
  yearsOfExperience: number;
  professionalBio: string;
  hourlyRate?: number;
  currency: string;
  acceptingNewClients: boolean;
  averageRating: number;
  totalReviews: number;
  totalAppointments: number;
  consultationTypes: Array<{
    type: 'video' | 'phone' | 'chat' | 'in_person';
    duration: number;
    price: number;
    currency: string;
  }>;
  verificationStatus: string;
}

interface ProfessionalStats {
  totalProfessionals: number;
  verifiedProfessionals: number;
  professionalsByType: Array<{ type: string; count: number }>;
  averageRating: number;
}

const ProfessionalsPage: React.FC = () => {
  const router = useRouter();
  const [professionals, setProfessionals] = useState<Professional[]>([]);
  const [stats, setStats] = useState<ProfessionalStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    professionalType: '',
    specializations: [] as string[],
    acceptingNewClients: true,
    minRating: 0,
    maxHourlyRate: 500,
    consultationType: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchProfessionals();
    fetchStats();
  }, [currentPage, searchTerm, filters]);

  const fetchProfessionals = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
      });

      if (searchTerm) {
        const searchResponse = await apiService.searchProfessionals(searchTerm, {
          page: currentPage,
          limit: 12,
          professionalType: filters.professionalType || undefined,
          acceptingNewClients: filters.acceptingNewClients,
          specializations: filters.specializations.length > 0 ? filters.specializations : undefined,
        });
        setProfessionals(searchResponse.data.professionals);
        setTotalPages(searchResponse.data.pagination.totalPages);
      } else {
        const response = await apiService.getProfessionals({
          page: currentPage,
          limit: 12,
          professionalType: filters.professionalType || undefined,
          acceptingNewClients: filters.acceptingNewClients,
          minRating: filters.minRating > 0 ? filters.minRating : undefined,
          maxHourlyRate: filters.maxHourlyRate < 500 ? filters.maxHourlyRate : undefined,
          consultationType: filters.consultationType || undefined,
          specializations: filters.specializations.length > 0 ? filters.specializations : undefined,
        });
        setProfessionals(response.data.professionals);
        setTotalPages(response.data.pagination.totalPages);
      }
    } catch (error) {
      console.error('Error fetching professionals:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await apiService.getProfessionalStats();
      setStats(response.data.stats);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchProfessionals();
  };

  const clearFilters = () => {
    setFilters({
      professionalType: '',
      specializations: [],
      acceptingNewClients: true,
      minRating: 0,
      maxHourlyRate: 500,
      consultationType: '',
    });
    setCurrentPage(1);
  };

  const formatProfessionalType = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  };

  const getConsultationTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="w-4 h-4" />;
      case 'phone': return <Phone className="w-4 h-4" />;
      case 'chat': return <MessageCircle className="w-4 h-4" />;
      case 'in_person': return <Users className="w-4 h-4" />;
      default: return <Video className="w-4 h-4" />;
    }
  };

  return (
    <>
      <Head>
        <title>Professional Directory - MindFlow Pro</title>
        <meta name="description" content="Find qualified mental health professionals on MindFlow Pro" />
      </Head>

      <Navigation />
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Professional Directory</h1>
                <p className="mt-2 text-gray-600">
                  Connect with verified mental health professionals
                </p>
              </div>
              
              {stats && (
                <div className="mt-4 lg:mt-0 flex flex-wrap gap-4">
                  <div className="bg-blue-50 px-4 py-2 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{stats.verifiedProfessionals}</div>
                    <div className="text-sm text-blue-600">Verified Professionals</div>
                  </div>
                  <div className="bg-green-50 px-4 py-2 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{stats.averageRating.toFixed(1)}</div>
                    <div className="text-sm text-green-600">Average Rating</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <form onSubmit={handleSearch} className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search by name, specialization, or treatment approach..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <button
                  type="submit"
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Search
                </button>
                <button
                  type="button"
                  onClick={() => setShowFilters(!showFilters)}
                  className="px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                >
                  <Filter className="w-4 h-4" />
                  Filters
                </button>
              </div>
            </form>

            {showFilters && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Professional Type
                    </label>
                    <select
                      value={filters.professionalType}
                      onChange={(e) => setFilters(prev => ({ ...prev, professionalType: e.target.value }))}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">All Types</option>
                      <option value="psychologist">Psychologist</option>
                      <option value="psychiatrist">Psychiatrist</option>
                      <option value="therapist">Therapist</option>
                      <option value="counselor">Counselor</option>
                      <option value="social_worker">Social Worker</option>
                      <option value="life_coach">Life Coach</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Consultation Type
                    </label>
                    <select
                      value={filters.consultationType}
                      onChange={(e) => setFilters(prev => ({ ...prev, consultationType: e.target.value }))}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">All Types</option>
                      <option value="video">Video Call</option>
                      <option value="phone">Phone Call</option>
                      <option value="chat">Chat</option>
                      <option value="in_person">In Person</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Rating
                    </label>
                    <select
                      value={filters.minRating}
                      onChange={(e) => setFilters(prev => ({ ...prev, minRating: Number(e.target.value) }))}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value={0}>Any Rating</option>
                      <option value={3}>3+ Stars</option>
                      <option value={4}>4+ Stars</option>
                      <option value={4.5}>4.5+ Stars</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Max Hourly Rate ($)
                    </label>
                    <input
                      type="range"
                      min="50"
                      max="500"
                      step="25"
                      value={filters.maxHourlyRate}
                      onChange={(e) => setFilters(prev => ({ ...prev, maxHourlyRate: Number(e.target.value) }))}
                      className="w-full"
                    />
                    <div className="text-sm text-gray-600 mt-1">
                      Up to ${filters.maxHourlyRate}
                    </div>
                  </div>
                </div>

                <div className="mt-4 flex justify-end">
                  <button
                    onClick={clearFilters}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Clear Filters
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Professional Disclaimer */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <Award className="h-5 w-5 text-amber-600 mt-0.5" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-amber-800">
                  Professional Verification Notice
                </h3>
                <p className="mt-1 text-sm text-amber-700">
                  All professionals listed have been verified for their credentials and licensing.
                  However, this platform is for informational purposes only and does not replace
                  professional medical advice, diagnosis, or treatment.
                </p>
              </div>
            </div>
          </div>

          {/* Professionals Grid */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg shadow-sm p-6 animate-pulse">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-16 h-16 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 rounded"></div>
                    <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                    <div className="h-3 bg-gray-200 rounded w-4/6"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : professionals.length === 0 ? (
            <div className="text-center py-12">
              <User className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No professionals found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or filters.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {professionals.map((professional) => (
                <div key={professional.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <div className="p-6">
                    {/* Professional Header */}
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                        {professional.user.profilePicture ? (
                          <img
                            src={professional.user.profilePicture}
                            alt={`${professional.user.firstName} ${professional.user.lastName}`}
                            className="w-16 h-16 rounded-full object-cover"
                          />
                        ) : (
                          <User className="w-8 h-8 text-blue-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {professional.user.firstName} {professional.user.lastName}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {formatProfessionalType(professional.professionalType)}
                        </p>
                        <div className="flex items-center mt-1">
                          <div className="flex items-center">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="ml-1 text-sm text-gray-600">
                              {professional.averageRating.toFixed(1)} ({professional.totalReviews} reviews)
                            </span>
                          </div>
                          {professional.verificationStatus === 'verified' && (
                            <Award className="w-4 h-4 text-green-500 ml-2" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Specializations */}
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {professional.specializations.slice(0, 3).map((spec, index) => (
                          <span
                            key={index}
                            className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full"
                          >
                            {spec}
                          </span>
                        ))}
                        {professional.specializations.length > 3 && (
                          <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                            +{professional.specializations.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Bio Preview */}
                    <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                      {professional.professionalBio}
                    </p>

                    {/* Details */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="w-4 h-4 mr-2" />
                        {professional.yearsOfExperience} years experience
                      </div>
                      {professional.hourlyRate && (
                        <div className="flex items-center text-sm text-gray-600">
                          <DollarSign className="w-4 h-4 mr-2" />
                          From ${professional.hourlyRate}/{professional.currency}
                        </div>
                      )}
                      <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="w-4 h-4 mr-2" />
                        {professional.totalAppointments} sessions completed
                      </div>
                    </div>

                    {/* Consultation Types */}
                    <div className="flex items-center space-x-2 mb-4">
                      {professional.consultationTypes.map((type, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-1 text-xs bg-gray-100 px-2 py-1 rounded"
                          title={`${type.type} - $${type.price}`}
                        >
                          {getConsultationTypeIcon(type.type)}
                          <span className="capitalize">{type.type.replace('_', ' ')}</span>
                        </div>
                      ))}
                    </div>

                    {/* Status */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`w-2 h-2 rounded-full mr-2 ${
                          professional.acceptingNewClients ? 'bg-green-500' : 'bg-red-500'
                        }`} />
                        <span className="text-sm text-gray-600">
                          {professional.acceptingNewClients ? 'Accepting new clients' : 'Not accepting new clients'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Action Button */}
                  <div className="px-6 py-4 bg-gray-50 rounded-b-lg">
                    <Link
                      href={`/professionals/${professional.id}`}
                      className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                    >
                      View Profile
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-8 flex justify-center">
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>

                {[...Array(Math.min(5, totalPages))].map((_, i) => {
                  const page = i + 1;
                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`px-4 py-2 border rounded-lg ${
                        currentPage === page
                          ? 'bg-blue-600 text-white border-blue-600'
                          : 'border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}

                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ProfessionalsPage;
