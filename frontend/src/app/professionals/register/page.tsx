'use client';

import React, { useState } from 'react';
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  Upload, 
  Calendar,
  MapPin,
  Shield,
  Stethoscope,
  Building,
  CreditCard,
  Clock,
  FileText,
  Award,
  Globe,
  Zap,
  MessageCircle
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

interface RegisterFormData {
  // Informations personnelles
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: string;
    profilePhoto?: File;
  };
  
  // Informations professionnelles
  professionalInfo: {
    professionalType: string;
    licenseNumber: string;
    medicalLicenseNumber?: string;
    licenseExpiryDate: string;
    licensingBoard: string;
    licensingState: string;
    yearsOfExperience: number;
    specializations: string[];
    treatmentApproaches: string[];
    languagesSpoken: string[];
    professionalBio: string;
  };
  
  // Éducation et certifications
  education: {
    medicalSchoolDegree?: string;
    education: Array<{
      degree: string;
      institution: string;
      year: number;
      field: string;
    }>;
    medicalCertifications: Array<{
      name: string;
      boardName: string;
      issueDate: string;
      expiryDate?: string;
      certificateNumber: string;
      isActive: boolean;
    }>;
    continuingEducation: Array<{
      courseName: string;
      provider: string;
      completionDate: string;
      creditHours: number;
      category: string;
    }>;
  };
  
  // Paramètres de pratique
  practiceSettings: {
    clinicalSettings: Array<{
      type: 'hospital' | 'clinic' | 'private_practice' | 'telehealth_only';
      name: string;
      address?: {
        street: string;
        city: string;
        state: string;
        zipCode: string;
        country: string;
      };
      isPrimary: boolean;
    }>;
    hospitalAffiliations: Array<{
      hospitalName: string;
      department: string;
      position: string;
      startDate: string;
      isActive: boolean;
    }>;
    consultationTypes: Array<{
      type: 'video' | 'phone' | 'chat' | 'in_person';
      duration: number;
      price: number;
      currency: string;
    }>;
    availability: {
      timezone: string;
      schedule: Record<string, Array<{
        startTime: string;
        endTime: string;
      }>>;
    };
  };
  
  // Télémédecine
  telemedicine: {
    hasEquipment: boolean;
    platforms: string[];
    equipmentDetails?: string;
    internetSpeedMbps?: number;
    backupConnection: boolean;
  };
  
  // Facturation et paiement
  billing: {
    taxId: string;
    billingAddress: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
    acceptedPaymentMethods: string[];
    insuranceAccepted: string[];
  };
  
  // Conformité
  compliance: {
    complianceCertifications: Array<{
      name: string;
      issueDate: string;
      expiryDate?: string;
      certificateId?: string;
      isValid: boolean;
    }>;
    prescriptionAuthority?: {
      hasAuthority: boolean;
      authorityType: 'full' | 'limited' | 'none';
      restrictions?: string[];
    };
  };
}

const STEPS = [
  { id: 1, title: 'Informations personnelles', icon: <FileText className="h-5 w-5" /> },
  { id: 2, title: 'Qualifications professionnelles', icon: <Stethoscope className="h-5 w-5" /> },
  { id: 3, title: 'Éducation & Certifications', icon: <Award className="h-5 w-5" /> },
  { id: 4, title: 'Paramètres de pratique', icon: <Building className="h-5 w-5" /> },
  { id: 5, title: 'Télémédecine', icon: <Globe className="h-5 w-5" /> },
  { id: 6, title: 'Facturation', icon: <CreditCard className="h-5 w-5" /> },
  { id: 7, title: 'Conformité', icon: <Shield className="h-5 w-5" /> },
  { id: 8, title: 'Révision finale', icon: <CheckCircle className="h-5 w-5" /> },
];

const PROFESSIONAL_TYPES = [
  // Médecine générale
  { value: 'general_practitioner', label: 'Médecin généraliste', category: 'Médecine générale' },
  { value: 'family_doctor', label: 'Médecin de famille', category: 'Médecine générale' },
  { value: 'internist', label: 'Interniste', category: 'Médecine générale' },
  
  // Spécialités médicales
  { value: 'cardiologist', label: 'Cardiologue', category: 'Spécialités médicales' },
  { value: 'neurologist', label: 'Neurologue', category: 'Spécialités médicales' },
  { value: 'dermatologist', label: 'Dermatologue', category: 'Spécialités médicales' },
  { value: 'endocrinologist', label: 'Endocrinologue', category: 'Spécialités médicales' },
  { value: 'gynecologist', label: 'Gynécologue', category: 'Spécialités médicales' },
  { value: 'pediatrician', label: 'Pédiatre', category: 'Spécialités médicales' },
  { value: 'oncologist', label: 'Oncologue', category: 'Spécialités médicales' },
  
  // Chirurgie
  { value: 'surgeon', label: 'Chirurgien', category: 'Chirurgie' },
  { value: 'orthopedic_surgeon', label: 'Chirurgien orthopédique', category: 'Chirurgie' },
  { value: 'plastic_surgeon', label: 'Chirurgien plastique', category: 'Chirurgie' },
  
  // Santé mentale
  { value: 'psychiatrist', label: 'Psychiatre', category: 'Santé mentale' },
  { value: 'psychologist', label: 'Psychologue', category: 'Santé mentale' },
  { value: 'neuropsychologist', label: 'Neuropsychologue', category: 'Santé mentale' },
  { value: 'child_psychologist', label: 'Psychologue pour enfants', category: 'Santé mentale' },
  { value: 'addiction_specialist', label: 'Spécialiste en addictologie', category: 'Santé mentale' },
  
  // Personnel médical
  { value: 'nurse', label: 'Infirmier(e)', category: 'Personnel médical' },
  { value: 'nurse_practitioner', label: 'Infirmier(e) praticien(ne)', category: 'Personnel médical' },
  { value: 'physician_assistant', label: 'Assistant médical', category: 'Personnel médical' },
  { value: 'pharmacist', label: 'Pharmacien', category: 'Personnel médical' },
  
  // Thérapies
  { value: 'physical_therapist', label: 'Kinésithérapeute', category: 'Thérapies' },
  { value: 'occupational_therapist', label: 'Ergothérapeute', category: 'Thérapies' },
  { value: 'speech_therapist', label: 'Orthophoniste', category: 'Thérapies' },
  { value: 'nutritionist', label: 'Nutritionniste', category: 'Thérapies' },
];

const SPECIALIZATIONS = [
  'Médecine générale', 'Cardiologie', 'Neurologie', 'Dermatologie', 'Endocrinologie',
  'Gynécologie', 'Pédiatrie', 'Oncologie', 'Chirurgie', 'Orthopédie', 'Plastique',
  'Psychiatrie', 'Psychologie', 'Addictologie', 'Soins infirmiers', 'Pharmacie',
  'Kinésithérapie', 'Ergothérapie', 'Orthophonie', 'Nutrition', 'Médecine d\'urgence',
  'Radiologie', 'Anesthésie', 'Médecine du travail', 'Médecine sportive',
  'Gériatrie', 'Pneumologie', 'Gastroentérologie', 'Néphrologie', 'Rhumatologie',
  'Urologie', 'Ophtalmologie', 'ORL', 'Allergologie', 'Pathologie'
];

const LANGUAGES = [
  'Français', 'Anglais', 'Espagnol', 'Allemand', 'Italien', 'Portugais',
  'Arabe', 'Chinois', 'Japonais', 'Russe', 'Néerlandais', 'Polonais'
];

const TELEMEDICINE_PLATFORMS = [
  'Zoom', 'Microsoft Teams', 'Google Meet', 'Cisco Webex', 'BlueJeans',
  'Jitsi Meet', 'Plateforme personnalisée', 'Doxy.me', 'VSee', 'Amwell'
];

const COMPLIANCE_CERTIFICATIONS = [
  'HIPAA', 'GDPR', 'ISO 27001', 'SOC 2', 'HITECH', 'FDA', 'CE Marking',
  'Joint Commission', 'AAAHC', 'CARF', 'ISO 13485', 'MDR'
];

export default function ProfessionalRegisterPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<RegisterFormData>({
    personalInfo: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: '',
    },
    professionalInfo: {
      professionalType: '',
      licenseNumber: '',
      licenseExpiryDate: '',
      licensingBoard: '',
      licensingState: '',
      yearsOfExperience: 0,
      specializations: [],
      treatmentApproaches: [],
      languagesSpoken: ['Français'],
      professionalBio: '',
    },
    education: {
      education: [],
      medicalCertifications: [],
      continuingEducation: [],
    },
    practiceSettings: {
      clinicalSettings: [],
      hospitalAffiliations: [],
      consultationTypes: [
        { type: 'video', duration: 30, price: 80, currency: 'EUR' },
        { type: 'in_person', duration: 45, price: 100, currency: 'EUR' }
      ],
      availability: {
        timezone: 'Europe/Paris',
        schedule: {
          monday: [{ startTime: '09:00', endTime: '17:00' }],
          tuesday: [{ startTime: '09:00', endTime: '17:00' }],
          wednesday: [{ startTime: '09:00', endTime: '17:00' }],
          thursday: [{ startTime: '09:00', endTime: '17:00' }],
          friday: [{ startTime: '09:00', endTime: '17:00' }],
        },
      },
    },
    telemedicine: {
      hasEquipment: false,
      platforms: [],
      backupConnection: false,
    },
    billing: {
      taxId: '',
      billingAddress: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'France',
      },
      acceptedPaymentMethods: [],
      insuranceAccepted: [],
    },
    compliance: {
      complianceCertifications: [],
    },
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const nextStep = () => {
    if (validateCurrentStep()) {
      setCurrentStep(prev => Math.min(prev + 1, STEPS.length));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const validateCurrentStep = () => {
    const newErrors: Record<string, string> = {};
    
    switch (currentStep) {
      case 1:
        if (!formData.personalInfo.firstName) newErrors.firstName = 'Prénom requis';
        if (!formData.personalInfo.lastName) newErrors.lastName = 'Nom requis';
        if (!formData.personalInfo.email) newErrors.email = 'Email requis';
        if (!formData.personalInfo.phone) newErrors.phone = 'Téléphone requis';
        break;
      case 2:
        if (!formData.professionalInfo.professionalType) newErrors.professionalType = 'Type de professionnel requis';
        if (!formData.professionalInfo.licenseNumber) newErrors.licenseNumber = 'Numéro de licence requis';
        if (formData.professionalInfo.specializations.length === 0) newErrors.specializations = 'Au moins une spécialisation requise';
        break;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Ici on enverrait les données au backend
      console.log('Soumission du formulaire:', formData);
      // Simulation d'une requête API
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('Inscription réussie ! Votre profil est en cours de vérification.');
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      alert('Erreur lors de l\'inscription. Veuillez réessayer.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <PersonalInfoStep formData={formData} setFormData={setFormData} errors={errors} />;
      case 2:
        return <ProfessionalInfoStep formData={formData} setFormData={setFormData} errors={errors} />;
      case 3:
        return <EducationStep formData={formData} setFormData={setFormData} errors={errors} />;
      case 4:
        return <PracticeSettingsStep formData={formData} setFormData={setFormData} errors={errors} />;
      case 5:
        return <TelemedicineStep formData={formData} setFormData={setFormData} errors={errors} />;
      case 6:
        return <BillingStep formData={formData} setFormData={setFormData} errors={errors} />;
      case 7:
        return <ComplianceStep formData={formData} setFormData={setFormData} errors={errors} />;
      case 8:
        return <ReviewStep formData={formData} />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Inscription Professionnelle MindFlow Pro
          </h1>
          <p className="text-gray-600">
            Rejoignez notre écosystème de soins de santé révolutionnaire
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8 overflow-x-auto">
          <div className="flex items-center justify-between min-w-max px-4">
            {STEPS.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all ${
                      currentStep > step.id
                        ? 'bg-green-500 border-green-500 text-white'
                        : currentStep === step.id
                        ? 'bg-blue-600 border-blue-600 text-white'
                        : 'bg-white border-gray-300 text-gray-400'
                    }`}
                  >
                    {currentStep > step.id ? <CheckCircle className="h-6 w-6" /> : step.icon}
                  </div>
                  <div className="mt-2 text-center">
                    <p className={`text-sm font-medium ${
                      currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </p>
                  </div>
                </div>
                {index < STEPS.length - 1 && (
                  <div
                    className={`h-1 w-24 mx-4 transition-all ${
                      currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <Card className="p-8 shadow-lg">
          {renderStepContent()}
        </Card>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Précédent
          </Button>

          {currentStep < STEPS.length ? (
            <Button
              onClick={nextStep}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
            >
              Suivant
              <ArrowRight className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Soumission...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4" />
                  Soumettre l'inscription
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

// Components pour chaque étape
function PersonalInfoStep({ formData, setFormData, errors }: any) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Informations personnelles</h2>
        <p className="text-gray-600 mb-6">
          Commençons par vos informations de base. Ces données seront utilisées pour créer votre profil professionnel.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Prénom *
          </label>
          <Input
            type="text"
            value={formData.personalInfo.firstName}
            onChange={(e) => setFormData((prev: any) => ({
              ...prev,
              personalInfo: { ...prev.personalInfo, firstName: e.target.value }
            }))}
            className={errors.firstName ? 'border-red-500' : ''}
            placeholder="Votre prénom"
          />
          {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nom *
          </label>
          <Input
            type="text"
            value={formData.personalInfo.lastName}
            onChange={(e) => setFormData((prev: any) => ({
              ...prev,
              personalInfo: { ...prev.personalInfo, lastName: e.target.value }
            }))}
            className={errors.lastName ? 'border-red-500' : ''}
            placeholder="Votre nom"
          />
          {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email professionnel *
          </label>
          <Input
            type="email"
            value={formData.personalInfo.email}
            onChange={(e) => setFormData((prev: any) => ({
              ...prev,
              personalInfo: { ...prev.personalInfo, email: e.target.value }
            }))}
            className={errors.email ? 'border-red-500' : ''}
            placeholder="<EMAIL>"
          />
          {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Téléphone *
          </label>
          <Input
            type="tel"
            value={formData.personalInfo.phone}
            onChange={(e) => setFormData((prev: any) => ({
              ...prev,
              personalInfo: { ...prev.personalInfo, phone: e.target.value }
            }))}
            className={errors.phone ? 'border-red-500' : ''}
            placeholder="+33 1 23 45 67 89"
          />
          {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Date de naissance
          </label>
          <Input
            type="date"
            value={formData.personalInfo.dateOfBirth}
            onChange={(e) => setFormData((prev: any) => ({
              ...prev,
              personalInfo: { ...prev.personalInfo, dateOfBirth: e.target.value }
            }))}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Photo de profil
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-500 transition-colors">
            <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600">
              Cliquez pour télécharger ou glissez-déposez
            </p>
            <p className="text-xs text-gray-500 mt-1">
              PNG, JPG jusqu'à 5MB
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function ProfessionalInfoStep({ formData, setFormData, errors }: any) {
  const [selectedCategory, setSelectedCategory] = useState('');
  
  const categorizedTypes = PROFESSIONAL_TYPES.reduce((acc, type) => {
    if (!acc[type.category]) acc[type.category] = [];
    acc[type.category].push(type);
    return acc;
  }, {} as Record<string, typeof PROFESSIONAL_TYPES>);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Qualifications professionnelles</h2>
        <p className="text-gray-600 mb-6">
          Renseignez vos qualifications et informations professionnelles pour la vérification.
        </p>
      </div>

      <div className="space-y-6">
        {/* Type de professionnel */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Type de professionnel *
          </label>
          
          {/* Sélection par catégorie */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
            {Object.keys(categorizedTypes).map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(selectedCategory === category ? '' : category)}
                className="justify-start"
              >
                {category}
              </Button>
            ))}
          </div>

          {/* Types par catégorie */}
          {selectedCategory && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 p-4 bg-gray-50 rounded-lg">
              {categorizedTypes[selectedCategory].map((type) => (
                <label key={type.value} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="professionalType"
                    value={type.value}
                    checked={formData.professionalInfo.professionalType === type.value}
                    onChange={(e) => setFormData((prev: any) => ({
                      ...prev,
                      professionalInfo: { ...prev.professionalInfo, professionalType: e.target.value }
                    }))}
                    className="text-blue-600"
                  />
                  <span className="text-sm">{type.label}</span>
                </label>
              ))}
            </div>
          )}
          {errors.professionalType && <p className="text-red-500 text-sm mt-1">{errors.professionalType}</p>}
        </div>

        {/* Licences */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Numéro de licence professionnelle *
            </label>
            <Input
              type="text"
              value={formData.professionalInfo.licenseNumber}
              onChange={(e) => setFormData((prev: any) => ({
                ...prev,
                professionalInfo: { ...prev.professionalInfo, licenseNumber: e.target.value }
              }))}
              className={errors.licenseNumber ? 'border-red-500' : ''}
              placeholder="Ex: PSY123456"
            />
            {errors.licenseNumber && <p className="text-red-500 text-sm mt-1">{errors.licenseNumber}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Numéro RPPS/ADELI (si applicable)
            </label>
            <Input
              type="text"
              value={formData.professionalInfo.medicalLicenseNumber || ''}
              onChange={(e) => setFormData((prev: any) => ({
                ...prev,
                professionalInfo: { ...prev.professionalInfo, medicalLicenseNumber: e.target.value }
              }))}
              placeholder="Ex: 10001234567"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date d'expiration de licence
            </label>
            <Input
              type="date"
              value={formData.professionalInfo.licenseExpiryDate}
              onChange={(e) => setFormData((prev: any) => ({
                ...prev,
                professionalInfo: { ...prev.professionalInfo, licenseExpiryDate: e.target.value }
              }))}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Années d'expérience
            </label>
            <Input
              type="number"
              min="0"
              max="50"
              value={formData.professionalInfo.yearsOfExperience}
              onChange={(e) => setFormData((prev: any) => ({
                ...prev,
                professionalInfo: { ...prev.professionalInfo, yearsOfExperience: parseInt(e.target.value) || 0 }
              }))}
            />
          </div>
        </div>

        {/* Spécialisations */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Spécialisations *
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {SPECIALIZATIONS.map((spec) => (
              <label key={spec} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.professionalInfo.specializations.includes(spec)}
                  onChange={(e) => {
                    const newSpecs = e.target.checked
                      ? [...formData.professionalInfo.specializations, spec]
                      : formData.professionalInfo.specializations.filter((s: string) => s !== spec);
                    
                    setFormData((prev: any) => ({
                      ...prev,
                      professionalInfo: { ...prev.professionalInfo, specializations: newSpecs }
                    }));
                  }}
                  className="text-blue-600"
                />
                <span className="text-sm">{spec}</span>
              </label>
            ))}
          </div>
          {errors.specializations && <p className="text-red-500 text-sm mt-1">{errors.specializations}</p>}
        </div>

        {/* Langues */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Langues parlées
          </label>
          <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
            {LANGUAGES.map((lang) => (
              <label key={lang} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.professionalInfo.languagesSpoken.includes(lang)}
                  onChange={(e) => {
                    const newLangs = e.target.checked
                      ? [...formData.professionalInfo.languagesSpoken, lang]
                      : formData.professionalInfo.languagesSpoken.filter((l: string) => l !== lang);
                    
                    setFormData((prev: any) => ({
                      ...prev,
                      professionalInfo: { ...prev.professionalInfo, languagesSpoken: newLangs }
                    }));
                  }}
                  className="text-blue-600"
                />
                <span className="text-sm">{lang}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Bio professionnelle */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Bio professionnelle
          </label>
          <textarea
            value={formData.professionalInfo.professionalBio}
            onChange={(e) => setFormData((prev: any) => ({
              ...prev,
              professionalInfo: { ...prev.professionalInfo, professionalBio: e.target.value }
            }))}
            rows={4}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Décrivez votre approche thérapeutique, votre expérience et ce qui vous distingue..."
          />
          <p className="text-sm text-gray-500 mt-1">
            {formData.professionalInfo.professionalBio.length}/500 caractères minimum 50
          </p>
        </div>
      </div>
    </div>
  );
}

// Autres composants d'étapes simplifiés pour le moment
function EducationStep({ formData, setFormData, errors }: any) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Éducation & Certifications</h2>
        <p className="text-gray-600 mb-6">
          Ajoutez vos diplômes, certifications et formations continues.
        </p>
      </div>
      <div className="text-center py-12 text-gray-500">
        <Award className="h-16 w-16 mx-auto mb-4 text-gray-300" />
        <p>Étape en cours de développement...</p>
      </div>
    </div>
  );
}

function PracticeSettingsStep({ formData, setFormData, errors }: any) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Paramètres de pratique</h2>
        <p className="text-gray-600 mb-6">
          Configurez vos lieux d'exercice, horaires et types de consultation.
        </p>
      </div>
      <div className="text-center py-12 text-gray-500">
        <Building className="h-16 w-16 mx-auto mb-4 text-gray-300" />
        <p>Étape en cours de développement...</p>
      </div>
    </div>
  );
}

function TelemedicineStep({ formData, setFormData, errors }: any) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Capacités de télémédecine</h2>
        <p className="text-gray-600 mb-6">
          Configurez vos équipements et plateformes pour les consultations à distance.
        </p>
      </div>
      <div className="text-center py-12 text-gray-500">
        <Globe className="h-16 w-16 mx-auto mb-4 text-gray-300" />
        <p>Étape en cours de développement...</p>
      </div>
    </div>
  );
}

function BillingStep({ formData, setFormData, errors }: any) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Facturation & Paiement</h2>
        <p className="text-gray-600 mb-6">
          Configurez vos informations de facturation et méthodes de paiement acceptées.
        </p>
      </div>
      <div className="text-center py-12 text-gray-500">
        <CreditCard className="h-16 w-16 mx-auto mb-4 text-gray-300" />
        <p>Étape en cours de développement...</p>
      </div>
    </div>
  );
}

function ComplianceStep({ formData, setFormData, errors }: any) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Conformité & Sécurité</h2>
        <p className="text-gray-600 mb-6">
          Vérifiez votre conformité aux réglementations de santé.
        </p>
      </div>
      <div className="text-center py-12 text-gray-500">
        <Shield className="h-16 w-16 mx-auto mb-4 text-gray-300" />
        <p>Étape en cours de développement...</p>
      </div>
    </div>
  );
}

function ReviewStep({ formData }: any) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Révision finale</h2>
        <p className="text-gray-600 mb-6">
          Vérifiez vos informations avant soumission. Votre profil sera examiné par notre équipe.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-4">
          <h3 className="font-semibold mb-2">Informations personnelles</h3>
          <p className="text-sm text-gray-600">
            {formData.personalInfo.firstName} {formData.personalInfo.lastName}
          </p>
          <p className="text-sm text-gray-600">{formData.personalInfo.email}</p>
          <p className="text-sm text-gray-600">{formData.personalInfo.phone}</p>
        </Card>

        <Card className="p-4">
          <h3 className="font-semibold mb-2">Qualifications</h3>
          <p className="text-sm text-gray-600">
            Type: {formData.professionalInfo.professionalType}
          </p>
          <p className="text-sm text-gray-600">
            Licence: {formData.professionalInfo.licenseNumber}
          </p>
          <p className="text-sm text-gray-600">
            Expérience: {formData.professionalInfo.yearsOfExperience} ans
          </p>
        </Card>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center">
          <Clock className="h-5 w-5 text-blue-600 mr-2" />
          <div>
            <h4 className="font-semibold text-blue-900">Délai de vérification</h4>
            <p className="text-sm text-blue-700">
              Votre profil sera examiné sous 24-48h ouvrables. Vous recevrez un email de confirmation.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 