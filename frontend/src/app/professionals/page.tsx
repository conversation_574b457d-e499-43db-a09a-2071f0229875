'use client';

import React, { useState } from 'react';
import { 
  Search,
  Filter,
  Star,
  MapPin,
  Video,
  Calendar,
  ChevronRight,
  Heart,
  Award,
  Clock,
  Users,
  MessageCircle
} from 'lucide-react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

interface Professional {
  id: string;
  name: string;
  role: string;
  specialties: string[];
  rating: number;
  reviewCount: number;
  experience: number;
  price: number;
  availability: string;
  location: string;
  consultationTypes: ('video' | 'in-person' | 'chat')[];
  avatar?: string;
  verified: boolean;
  languages: string[];
}

// Données simulées
const mockProfessionals: Professional[] = [
  {
    id: '1',
    name: 'Dr. <PERSON>',
    role: 'Psychologue clinicienne',
    specialties: ['Anxié<PERSON>', 'Dépression', 'Stress'],
    rating: 4.8,
    reviewCount: 127,
    experience: 12,
    price: 80,
    availability: 'Disponible aujourd\'hui',
    location: 'Paris 8ème',
    consultationTypes: ['video', 'in-person'],
    verified: true,
    languages: ['Français', 'Anglais']
  },
  {
    id: '2',
    name: 'Dr. Jean Dupont',
    role: 'Psychiatre',
    specialties: ['Troubles bipolaires', 'TDAH', 'Addictions'],
    rating: 4.9,
    reviewCount: 89,
    experience: 18,
    price: 120,
    availability: 'Prochaine disponibilité: Lundi',
    location: 'Lyon 3ème',
    consultationTypes: ['video', 'in-person'],
    verified: true,
    languages: ['Français']
  },
  {
    id: '3',
    name: 'Marie Leblanc',
    role: 'Thérapeute comportementale',
    specialties: ['Phobies', 'TOC', 'Gestion du stress'],
    rating: 4.7,
    reviewCount: 156,
    experience: 8,
    price: 70,
    availability: 'Disponible demain',
    location: 'Marseille 6ème',
    consultationTypes: ['video'],
    verified: true,
    languages: ['Français', 'Espagnol']
  },
  {
    id: '4',
    name: 'Dr. Ahmed Benali',
    role: 'Psychothérapeute',
    specialties: ['Couples', 'Famille', 'Adolescents'],
    rating: 4.6,
    reviewCount: 203,
    experience: 15,
    price: 90,
    availability: 'Disponible cette semaine',
    location: 'Lille Centre',
    consultationTypes: ['video', 'in-person', 'chat'],
    verified: true,
    languages: ['Français', 'Arabe', 'Anglais']
  }
];

export default function ProfessionalsPage() {
  const [professionals] = useState<Professional[]>(mockProfessionals);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecialty, setSelectedSpecialty] = useState('');
  const [selectedConsultationType, setSelectedConsultationType] = useState('');
  const [favorites, setFavorites] = useState<string[]>([]);

  // Extraire toutes les spécialités uniques
  const allSpecialties = Array.from(
    new Set(professionals.flatMap(p => p.specialties))
  ).sort();

  const filteredProfessionals = professionals.filter(pro => {
    const matchesSearch = pro.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pro.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pro.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesSpecialty = !selectedSpecialty || pro.specialties.includes(selectedSpecialty);
    
    const matchesConsultationType = !selectedConsultationType || 
                                   pro.consultationTypes.includes(selectedConsultationType as any);
    
    return matchesSearch && matchesSpecialty && matchesConsultationType;
  });

  const toggleFavorite = (id: string) => {
    setFavorites(prev => 
      prev.includes(id) 
        ? prev.filter(fId => fId !== id)
        : [...prev, id]
    );
  };

  const getConsultationTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="h-4 w-4" />;
      case 'in-person': return <Users className="h-4 w-4" />;
      case 'chat': return <MessageCircle className="h-4 w-4" />;
      default: return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Trouvez votre professionnel de santé mentale
          </h1>
          <p className="text-gray-600">
            Des experts qualifiés pour vous accompagner dans votre parcours de bien-être
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Rechercher par nom, spécialité ou trouble..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={selectedSpecialty}
              onChange={(e) => setSelectedSpecialty(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
            >
              <option value="">Toutes les spécialités</option>
              {allSpecialties.map(specialty => (
                <option key={specialty} value={specialty}>{specialty}</option>
              ))}
            </select>

            <select
              value={selectedConsultationType}
              onChange={(e) => setSelectedConsultationType(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
            >
              <option value="">Tous les types</option>
              <option value="video">Vidéo consultation</option>
              <option value="in-person">En cabinet</option>
              <option value="chat">Chat</option>
            </select>

            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Plus de filtres
            </Button>
          </div>
        </div>

        {/* Results count */}
        <div className="mb-4 text-gray-600">
          {filteredProfessionals.length} professionnel{filteredProfessionals.length > 1 ? 's' : ''} trouvé{filteredProfessionals.length > 1 ? 's' : ''}
        </div>

        {/* Professionals Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProfessionals.map((professional) => (
            <Card key={professional.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-xl">
                      {professional.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{professional.name}</h3>
                      <p className="text-sm text-gray-600">{professional.role}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => toggleFavorite(professional.id)}
                    className="text-gray-400 hover:text-red-500 transition-colors"
                  >
                    <Heart 
                      className={`h-5 w-5 ${favorites.includes(professional.id) ? 'fill-red-500 text-red-500' : ''}`} 
                    />
                  </button>
                </div>

                {/* Verified Badge */}
                {professional.verified && (
                  <div className="flex items-center gap-1 mb-3 text-green-600">
                    <Award className="h-4 w-4" />
                    <span className="text-sm font-medium">Professionnel vérifié</span>
                  </div>
                )}

                {/* Rating */}
                <div className="flex items-center gap-2 mb-3">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="ml-1 font-semibold">{professional.rating}</span>
                  </div>
                  <span className="text-sm text-gray-600">({professional.reviewCount} avis)</span>
                </div>

                {/* Specialties */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {professional.specialties.slice(0, 3).map((specialty) => (
                    <Badge key={specialty} variant="secondary" className="text-xs">
                      {specialty}
                    </Badge>
                  ))}
                  {professional.specialties.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{professional.specialties.length - 3}
                    </Badge>
                  )}
                </div>

                {/* Info */}
                <div className="space-y-2 mb-4 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>{professional.experience} ans d'expérience</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>{professional.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span className="text-green-600 font-medium">{professional.availability}</span>
                  </div>
                </div>

                {/* Consultation Types */}
                <div className="flex gap-2 mb-4">
                  {professional.consultationTypes.map((type) => (
                    <div key={type} className="flex items-center gap-1 text-gray-600">
                      {getConsultationTypeIcon(type)}
                    </div>
                  ))}
                </div>

                {/* Languages */}
                <div className="text-sm text-gray-600 mb-4">
                  Langues: {professional.languages.join(', ')}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div>
                    <span className="text-2xl font-bold text-gray-900">{professional.price}€</span>
                    <span className="text-sm text-gray-600">/séance</span>
                  </div>
                  <Link href={`/professionals/${professional.id}`}>
                    <Button className="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                      Voir le profil
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredProfessionals.length === 0 && (
          <Card className="p-12 text-center">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Aucun professionnel trouvé
            </h3>
            <p className="text-gray-600">
              Essayez de modifier vos critères de recherche
            </p>
          </Card>
        )}

        {/* CTA Section */}
        <div className="mt-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg p-8 text-white text-center">
          <h2 className="text-2xl font-bold mb-4">
            Vous êtes un professionnel de santé mentale ?
          </h2>
          <p className="mb-6 text-purple-100">
            Rejoignez notre plateforme et aidez des milliers de personnes à améliorer leur bien-être
          </p>
          <Button variant="secondary" size="lg">
            Devenir praticien sur MindFlow Pro
          </Button>
        </div>
      </div>
    </div>
  );
}
