'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>, Mic, Mic<PERSON>ff, <PERSON>Off, Phone, MessageSquare, Share2,
  Clock, Activity, FileText, Send, CircleDot, StopCircle, PlayCircle,
  Monitor, Stethoscope, Eye, Brain, Wifi, Download, Maximize,
  Camera, CameraOff, Shield, Zap, Settings, Calendar
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { VideoControls, WebRTCProvider, useWebRTC } from '@/components/Telemedicine';

// Interface pour session télémédecine avancée
interface AdvancedTelemedicineSession {
  id: string;
  patientName: string;
  patientAge: number;
  chiefComplaint: string;
  vitalSigns: {
    bloodPressure: string;
    heartRate: number;
    temperature: number;
    oxygenSat: number;
  };
  aiAnalysis: {
    extractedSymptoms: string[];
    suggestedDiagnosis: Array<{
      condition: string;
      confidence: number;
    }>;
    urgencyLevel: 'low' | 'medium' | 'high' | 'emergency';
  };
  connectionQuality: {
    status: 'excellent' | 'good' | 'fair' | 'poor';
    bandwidth: number;
    latency: number;
  };
}

interface DiagnosticTool {
  id: string;
  name: string;
  icon: React.ReactNode;
  status: 'available' | 'active' | 'completed';
  description: string;
}

interface ChatMessage {
  id: string;
  sender: 'doctor' | 'patient' | 'ai' | 'system';
  message: string;
  timestamp: string;
  type: 'text' | 'ai_insight';
  priority?: 'normal' | 'high';
}

export default function AdvancedTelemedicine() {
  const [isActive, setIsActive] = useState(true);
  const [isVideoOn, setIsVideoOn] = useState(true);
  const [isAudioOn, setIsAudioOn] = useState(true);
  const [isRecording, setIsRecording] = useState(false);
  const [sessionTime, setSessionTime] = useState(0);
  const [chatMessage, setChatMessage] = useState('');
  const [selectedTool, setSelectedTool] = useState<string | null>(null);

  // Session simulée avancée
  const session: AdvancedTelemedicineSession = {
    id: 'tel-adv-001',
    patientName: 'Marie Dubois',
    patientAge: 34,
    chiefComplaint: 'Douleurs abdominales persistantes depuis 3 jours',
    vitalSigns: {
      bloodPressure: '125/80',
      heartRate: 78,
      temperature: 37.2,
      oxygenSat: 98
    },
    aiAnalysis: {
      extractedSymptoms: ['douleur abdominale', 'nausées légères', 'fatigue'],
      suggestedDiagnosis: [
        { condition: 'Gastro-entérite', confidence: 75 },
        { condition: 'Syndrome du côlon irritable', confidence: 60 }
      ],
      urgencyLevel: 'medium'
    },
    connectionQuality: {
      status: 'excellent',
      bandwidth: 25.6,
      latency: 45
    }
  };

  // Outils diagnostiques virtuels
  const diagnosticTools: DiagnosticTool[] = [
    {
      id: 'stethoscope',
      name: 'Stéthoscope Digital',
      icon: <Stethoscope className="h-5 w-5" />,
      status: 'available',
      description: 'Auscultation cardiaque et pulmonaire assistée par IA'
    },
    {
      id: 'dermascope',
      name: 'Analyse Dermatologique',
      icon: <Eye className="h-5 w-5" />,
      status: 'available',
      description: 'Analyse IA des lésions cutanées'
    },
    {
      id: 'vision_test',
      name: 'Test de Vision',
      icon: <Monitor className="h-5 w-5" />,
      status: 'available',
      description: 'Évaluation acuité visuelle'
    },
    {
      id: 'vital_signs',
      name: 'Signes Vitaux IA',
      icon: <Brain className="h-5 w-5" />,
      status: 'active',
      description: 'Monitoring automatique par analyse vidéo'
    }
  ];

  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      sender: 'system',
      message: 'Consultation démarrée. Connexion sécurisée établie.',
      timestamp: '14:30',
      type: 'text'
    },
    {
      id: '2',
      sender: 'ai',
      message: '🤖 IA Médicale : Transcription automatique activée. Analyse des symptômes en cours.',
      timestamp: '14:31',
      type: 'ai_insight'
    },
    {
      id: '3',
      sender: 'patient',
      message: 'Bonjour Docteur, je ressens toujours ces douleurs abdominales.',
      timestamp: '14:31',
      type: 'text'
    },
    {
      id: '4',
      sender: 'ai',
      message: '📊 Analyse IA : Symptômes compatibles avec gastro-entérite (75% confiance).',
      timestamp: '14:33',
      type: 'ai_insight',
      priority: 'high'
    }
  ]);

  // Timer de session
  useEffect(() => {
    const timer = setInterval(() => {
      setSessionTime(prev => prev + 1);
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const sendMessage = () => {
    if (chatMessage.trim()) {
      const newMessage: ChatMessage = {
        id: Date.now().toString(),
        sender: 'doctor',
        message: chatMessage,
        timestamp: new Date().toLocaleTimeString().slice(0, 5),
        type: 'text'
      };
      setChatMessages(prev => [...prev, newMessage]);
      setChatMessage('');
    }
  };

  const activateTool = (toolId: string) => {
    setSelectedTool(toolId);
    setTimeout(() => {
      const toolMessage: ChatMessage = {
        id: Date.now().toString(),
        sender: 'system',
        message: `🔧 Outil ${diagnosticTools.find(t => t.id === toolId)?.name} activé avec succès.`,
        timestamp: new Date().toLocaleTimeString().slice(0, 5),
        type: 'text'
      };
      setChatMessages(prev => [...prev, toolMessage]);
    }, 1500);
  };

  const getConnectionColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'fair': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getUrgencyColor = (level: string) => {
    switch (level) {
      case 'emergency': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-green-100 text-green-800';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 p-4">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">
          🎥 Télémédecine Avancée - Phase 2
        </h1>
        
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">
            Nouvelles Fonctionnalités Implémentées
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">
                🎥 Vidéo HD Avancée
              </h3>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• Qualité 1080p adaptative</li>
                <li>• Latence &lt; 150ms</li>
                <li>• Chiffrement end-to-end</li>
                <li>• Enregistrement sécurisé</li>
              </ul>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-900 mb-2">
                🔧 Outils Diagnostiques
              </h3>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• Stéthoscope numérique</li>
                <li>• Analyse dermatologique IA</li>
                <li>• Tests de vision</li>
                <li>• Signes vitaux automatiques</li>
              </ul>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-medium text-purple-900 mb-2">
                🧠 Intelligence Artificielle
              </h3>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• Transcription automatique</li>
                <li>• Extraction symptômes</li>
                <li>• Suggestions diagnostic</li>
                <li>• Détection urgences</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-6">
            <Button 
              onClick={() => setIsActive(!isActive)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isActive ? 'Désactiver' : 'Activer'} Mode Avancé
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
} 