'use client';

export const dynamic = 'force-dynamic';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useAICoach, AIMessage, AISession } from '@/hooks/useAICoach';
import { DashboardLayout } from '@/components/Layout/DashboardLayout';
import { 
  MessageCircle, 
  Send, 
  Loader2, 
  Brain, 
  Heart, 
  TrendingUp, 
  Clock, 
  Target,
  BarChart3,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  Play,
  Pause,
  Square,
  Smile,
  Meh,
  Frown
} from 'lucide-react';

export default function AICoachPage() {
  const {
    currentSession,
    sessions,
    stats,
    isLoading,
    isTyping,
    startSession,
    sendMessage,
    endSession,
    getAnalysis
  } = useAICoach();

  const [messageInput, setMessageInput] = useState('');
  const [currentMood, setCurrentMood] = useState<number>(5);
  const [showSessionSetup, setShowSessionSetup] = useState(!currentSession);
  const [selectedTheme, setSelectedTheme] = useState('');
  const [sessionGoal, setSessionGoal] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Thèmes disponibles pour les sessions
  const themes = [
    { id: 'gestion_stress', name: 'Gestion du stress', icon: '🧘', color: 'bg-blue-100 text-blue-700' },
    { id: 'amélioration_humeur', name: 'Amélioration de l\'humeur', icon: '😊', color: 'bg-yellow-100 text-yellow-700' },
    { id: 'confiance_soi', name: 'Confiance en soi', icon: '💪', color: 'bg-green-100 text-green-700' },
    { id: 'anxiété', name: 'Gestion de l\'anxiété', icon: '🌸', color: 'bg-pink-100 text-pink-700' },
    { id: 'motivation', name: 'Motivation et énergie', icon: '⚡', color: 'bg-purple-100 text-purple-700' },
    { id: 'relations', name: 'Relations sociales', icon: '👥', color: 'bg-indigo-100 text-indigo-700' }
  ];

  // Scroll automatique vers le bas des messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentSession?.messages]);

  // Démarrer une nouvelle session
  const handleStartSession = async () => {
    if (!selectedTheme || !sessionGoal) return;
    
    try {
      await startSession(selectedTheme, sessionGoal);
      setShowSessionSetup(false);
    } catch (error) {
      console.error('Erreur démarrage session:', error);
    }
  };

  // Envoyer un message
  const handleSendMessage = async () => {
    if (!messageInput.trim()) return;

    const message = messageInput;
    setMessageInput('');
    
    try {
      await sendMessage(message, currentMood);
    } catch (error) {
      console.error('Erreur envoi message:', error);
    }
  };

  // Terminer la session
  const handleEndSession = async () => {
    try {
      await endSession();
      setShowSessionSetup(true);
      setSelectedTheme('');
      setSessionGoal('');
    } catch (error) {
      console.error('Erreur fin session:', error);
    }
  };

  // Obtenir l'emoji d'humeur
  const getMoodEmoji = (mood: number) => {
    if (mood >= 8) return '😄';
    if (mood >= 6) return '😊';
    if (mood >= 4) return '😐';
    if (mood >= 2) return '😟';
    return '😢';
  };

  // Obtenir la couleur du sentiment
  const getSentimentColor = (sentiment?: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-600 bg-green-100';
      case 'negative': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Interface de configuration de session
  if (showSessionSetup) {
    return (
      <DashboardLayout>
        <div className="max-w-4xl mx-auto space-y-6">
          {/* En-tête */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-0">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Brain className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Coach IA Personnel</h1>
                  <p className="text-gray-600">Votre compagnon de bien-être émotionnel</p>
                </div>
              </div>
              
              {stats && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{stats.totalSessions}</div>
                    <div className="text-sm text-gray-600">Sessions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{stats.moodImprovementRate}%</div>
                    <div className="text-sm text-gray-600">Amélioration</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{stats.averageSessionDuration.toFixed(0)}min</div>
                    <div className="text-sm text-gray-600">Durée moy.</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{stats.monthlyProgress.goalsAchieved}</div>
                    <div className="text-sm text-gray-600">Objectifs</div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Configuration de la session */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5" />
                <span>Nouvelle Session de Coaching</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Sélection du thème */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Sur quoi souhaitez-vous travailler aujourd'hui ?
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {themes.map((theme) => (
                    <button
                      key={theme.id}
                      onClick={() => setSelectedTheme(theme.id)}
                      className={`p-4 rounded-lg border-2 transition-all text-left ${
                        selectedTheme === theme.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{theme.icon}</span>
                        <div>
                          <div className="font-medium text-gray-900">{theme.name}</div>
                          <Badge className={`text-xs ${theme.color} border-0`}>
                            {theme.id.replace('_', ' ')}
                          </Badge>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Objectif de session */}
              <div>
                <label htmlFor="goal" className="block text-sm font-medium text-gray-700 mb-2">
                  Quel est votre objectif pour cette session ?
                </label>
                <Input
                  id="goal"
                  value={sessionGoal}
                  onChange={(e) => setSessionGoal(e.target.value)}
                  placeholder="Ex: Apprendre à gérer mon stress au travail"
                  className="w-full"
                />
              </div>

              {/* Humeur actuelle */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Comment vous sentez-vous maintenant ? ({currentMood}/10)
                </label>
                <div className="flex items-center space-x-4">
                  <Frown className="w-5 h-5 text-red-500" />
                  <input
                    type="range"
                    min="1"
                    max="10"
                    value={currentMood}
                    onChange={(e) => setCurrentMood(parseInt(e.target.value))}
                    className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <Smile className="w-5 h-5 text-green-500" />
                  <span className="text-2xl">{getMoodEmoji(currentMood)}</span>
                </div>
              </div>

              <Button
                onClick={handleStartSession}
                disabled={!selectedTheme || !sessionGoal || isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Démarrage...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Commencer la session
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Sessions récentes */}
          {sessions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="w-5 h-5" />
                  <span>Sessions récentes</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sessions.slice(0, 5).map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          session.status === 'completed' ? 'bg-green-500' : 
                          session.status === 'active' ? 'bg-blue-500' : 'bg-gray-500'
                        }`} />
                        <div>
                          <div className="font-medium text-sm">{session.theme.replace('_', ' ')}</div>
                          <div className="text-xs text-gray-500">
                            {session.startTime.toLocaleDateString('fr-FR')}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {session.stats?.duration || 0}min
                        </div>
                        <div className="text-xs text-gray-500">
                          {session.messages.length} messages
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DashboardLayout>
    );
  }

  // Interface de chat active
  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]">
        {/* Zone de chat principale */}
        <div className="lg:col-span-3 flex flex-col">
          <Card className="flex-1 flex flex-col">
            {/* En-tête de session */}
            <CardHeader className="border-b bg-gradient-to-r from-blue-50 to-purple-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Brain className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-semibold">Session de Coaching IA</div>
                    <div className="text-sm text-gray-600">
                      {currentSession?.theme.replace('_', ' ')} • 
                      Démarrée à {currentSession?.startTime.toLocaleTimeString('fr-FR', { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className="bg-green-100 text-green-700 border-0">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                    Active
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleEndSession}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Square className="w-4 h-4 mr-1" />
                    Terminer
                  </Button>
                </div>
              </div>
            </CardHeader>

            {/* Messages */}
            <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
              {currentSession?.messages.map((message) => (
                <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                    {/* Avatar */}
                    <div className={`flex items-end space-x-2 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
                        message.type === 'user' ? 'bg-blue-500' : 'bg-purple-500'
                      }`}>
                        {message.type === 'user' ? 'V' : 'IA'}
                      </div>
                      
                      <div className={`rounded-lg p-3 ${
                        message.type === 'user' 
                          ? 'bg-blue-500 text-white' 
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        <div className="text-sm">{message.content}</div>
                        
                        {/* Métadonnées du message */}
                        <div className={`flex items-center justify-between mt-2 text-xs ${
                          message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                        }`}>
                          <span>{message.timestamp.toLocaleTimeString('fr-FR', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}</span>
                          
                          {message.mood && (
                            <div className="flex items-center space-x-1">
                              <Heart className="w-3 h-3" />
                              <span>{message.mood}/10</span>
                            </div>
                          )}
                          
                          {message.sentiment && (
                            <Badge className={`text-xs ${getSentimentColor(message.sentiment)} border-0`}>
                              {message.sentiment}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions suggérées */}
                    {message.suggestedActions && message.suggestedActions.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {message.suggestedActions.map((action, index) => (
                          <button
                            key={index}
                            className="block w-full text-left text-xs p-2 bg-blue-50 text-blue-700 rounded border hover:bg-blue-100 transition-colors"
                          >
                            <Lightbulb className="w-3 h-3 inline mr-1" />
                            {action}
                          </button>
                        ))}
                      </div>
                    )}

                    {/* Indicateurs émotionnels */}
                    {message.emotionalIndicators && message.emotionalIndicators.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {message.emotionalIndicators.map((indicator, index) => (
                          <Badge key={index} className="text-xs bg-gray-200 text-gray-700 border-0">
                            {indicator}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {/* Indicateur de frappe */}
              {isTyping && (
                <div className="flex justify-start">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      IA
                    </div>
                    <div className="bg-gray-100 rounded-lg p-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </CardContent>

            {/* Zone de saisie */}
            <div className="border-t p-4">
              <div className="flex items-center space-x-3">
                {/* Sélecteur d'humeur rapide */}
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => setCurrentMood(Math.max(1, currentMood - 1))}
                    className="p-1 rounded hover:bg-gray-100"
                  >
                    <Frown className="w-4 h-4 text-red-500" />
                  </button>
                  <span className="text-sm font-medium w-8 text-center">{currentMood}</span>
                  <button
                    onClick={() => setCurrentMood(Math.min(10, currentMood + 1))}
                    className="p-1 rounded hover:bg-gray-100"
                  >
                    <Smile className="w-4 h-4 text-green-500" />
                  </button>
                </div>

                {/* Champ de saisie */}
                <Input
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  placeholder="Tapez votre message..."
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  className="flex-1"
                  disabled={isTyping}
                />

                <Button
                  onClick={handleSendMessage}
                  disabled={!messageInput.trim() || isTyping}
                  size="sm"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        </div>

        {/* Panneau d'analyse */}
        <div className="space-y-4">
          {/* Analyse de session */}
          {currentSession && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center space-x-2">
                  <BarChart3 className="w-4 h-4" />
                  <span>Analyse de Session</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Objectif */}
                <div>
                  <div className="text-xs text-gray-500 mb-1">Objectif</div>
                  <div className="text-sm">{currentSession.goal}</div>
                </div>

                {/* Progression d'humeur */}
                {currentSession.moodAnalysis && (
                  <div>
                    <div className="text-xs text-gray-500 mb-2">Évolution d'humeur</div>
                    <div className="flex items-center justify-between text-sm">
                      <div className="text-center">
                        <div className="text-lg">{getMoodEmoji(currentSession.moodAnalysis.initialMood)}</div>
                        <div className="text-xs text-gray-500">Début</div>
                      </div>
                      <TrendingUp className={`w-4 h-4 ${
                        currentSession.moodAnalysis.moodTrend === 'improving' ? 'text-green-500' :
                        currentSession.moodAnalysis.moodTrend === 'declining' ? 'text-red-500' :
                        'text-gray-500'
                      }`} />
                      <div className="text-center">
                        <div className="text-lg">{getMoodEmoji(currentSession.moodAnalysis.currentMood)}</div>
                        <div className="text-xs text-gray-500">Actuel</div>
                      </div>
                    </div>
                    <Badge className={`w-full justify-center mt-2 border-0 ${
                      currentSession.moodAnalysis.moodTrend === 'improving' ? 'bg-green-100 text-green-700' :
                      currentSession.moodAnalysis.moodTrend === 'declining' ? 'bg-red-100 text-red-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {currentSession.moodAnalysis.moodTrend === 'improving' ? 'En amélioration' :
                       currentSession.moodAnalysis.moodTrend === 'declining' ? 'En baisse' :
                       'Stable'}
                    </Badge>
                  </div>
                )}

                {/* Statistiques de session */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Messages</span>
                    <span>{currentSession.messages.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Durée</span>
                    <span>{Math.floor((Date.now() - currentSession.startTime.getTime()) / 60000)}min</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Ressources d'urgence */}
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="text-sm flex items-center space-x-2 text-red-600">
                <AlertTriangle className="w-4 h-4" />
                <span>Ressources d'urgence</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="text-xs text-gray-600">
                Si vous ressentez une détresse importante :
              </div>
              <div className="space-y-2">
                <button className="w-full text-left p-2 bg-red-50 text-red-700 rounded text-xs hover:bg-red-100">
                  📞 3114 - Ligne nationale d'écoute
                </button>
                <button className="w-full text-left p-2 bg-blue-50 text-blue-700 rounded text-xs hover:bg-blue-100">
                  🆘 SOS Amitié - 09 72 39 40 50
                </button>
                <button className="w-full text-left p-2 bg-green-50 text-green-700 rounded text-xs hover:bg-green-100">
                  🚨 Urgences - 15 ou 112
                </button>
              </div>
            </CardContent>
          </Card>

          {/* Statistiques globales */}
          {stats && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4" />
                  <span>Vos progrès</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{stats.moodImprovementRate}%</div>
                  <div className="text-xs text-gray-500">Amélioration d'humeur</div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span className="text-gray-500">Sessions ce mois</span>
                    <span>{stats.monthlyProgress.sessionsCompleted}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-gray-500">Objectifs atteints</span>
                    <span>{stats.monthlyProgress.goalsAchieved}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
