'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { useAppointmentsSupabase } from '@/hooks/useAppointmentsSupabase';

export default function TestAppointmentsSupabasePage() {
  const { appointments, loading, error } = useAppointmentsSupabase();

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-6">Test Rendez-vous Supabase</h1>
      
      <Card className="p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Statut</h2>
        <p>Loading: {loading ? 'Oui' : 'Non'}</p>
        <p>Error: {error || 'Aucune'}</p>
        <p>Nombre de rendez-vous: {appointments.length}</p>
      </Card>

             <div className="space-y-4">
         {appointments.map((appointment) => (
           <Card key={appointment.id} className="p-4">
             <h3 className="font-semibold">{appointment.professionalName}</h3>
             <p>{appointment.date} à {appointment.time}</p>
             <p>Status: {appointment.status}</p>
           </Card>
         ))}
      </div>
    </div>
  );
} 