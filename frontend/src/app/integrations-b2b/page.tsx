'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Globe, 
  Database, 
  Zap, 
  Building, 
  TestTube, 
  Pill, 
  Code, 
  CheckCircle,
  Clock,
  AlertCircle,
  Settings,
  Activity,
  Link,
  Server,
  Workflow,
  BarChart3
} from 'lucide-react';

export default function IntegrationsB2BPage() {
  const [activeTab, setActiveTab] = useState('overview');

  // Intégrations B2B
  const integrations = [
    {
      id: 'hospital_apis',
      name: 'APIs Systèmes Hospitaliers',
      description: 'Connecteurs Epic, Cerner, Meditech, SIH français',
      status: 'in_progress',
      progress: 70,
      icon: Building,
      category: 'Hospital Systems',
      endpoints: ['Epic MyChart', 'Cerner PowerChart', 'Meditech Expanse', 'SIH français'],
      connectivity: 'WebSocket + REST'
    },
    {
      id: 'hl7_fhir',
      name: 'HL7 FHIR Complet',
      description: 'Interopérabilité totale données médicales',
      status: 'in_progress', 
      progress: 85,
      icon: Globe,
      category: 'Standards',
      endpoints: ['Patient', 'Encounter', 'Observation', 'DiagnosticReport'],
      connectivity: 'FHIR R4 REST'
    },
    {
      id: 'laboratories',
      name: 'Connecteurs Laboratoires',
      description: 'CERBA, BIOMNIS, SYNLAB, EUROFINS',
      status: 'pending',
      progress: 45,
      icon: TestTube,
      category: 'Laboratories',
      endpoints: ['CERBA API', 'BIOMNIS Hub', 'SYNLAB Connect', 'EUROFINS Digital'],
      connectivity: 'HL7 + PDF'
    },
    {
      id: 'pharmacies',
      name: 'E-prescriptions + Pharmacies',
      description: 'PHARMAGEST, CERP ROUEN, DMP + tiers-payant',
      status: 'pending',
      progress: 30,
      icon: Pill,
      category: 'Pharmacies',
      endpoints: ['PHARMAGEST API', 'DMP Prescription', 'SESAM-Vitale', 'Tiers-payant'],
      connectivity: 'SOAP + REST'
    },
    {
      id: 'sdk_developers',
      name: 'SDK Développeurs Tiers',
      description: 'Écosystème applications tierces avec documentation complète',
      status: 'pending',
      progress: 25,
      icon: Code,
      category: 'Development',
      endpoints: ['MindFlow SDK', 'REST APIs', 'WebHooks', 'GraphQL'],
      connectivity: 'Multi-protocol'
    }
  ];

  // Métriques en temps réel
  const metrics = [
    { 
      label: 'Intégrations Actives', 
      value: '12/20', 
      trend: '+3 ce mois',
      icon: Link,
      color: 'text-blue-600'
    },
    { 
      label: 'Volume API/jour', 
      value: '847K', 
      trend: '+12% vs hier',
      icon: Activity,
      color: 'text-green-600'
    },
    { 
      label: 'Partenaires B2B', 
      value: '156', 
      trend: '+28 ce mois',
      icon: Building,
      color: 'text-purple-600'
    },
    { 
      label: 'Uptime Intégrations', 
      value: '99.97%', 
      trend: 'SLA respecté',
      icon: Server,
      color: 'text-green-600'
    }
  ];

  // Partenaires connectés
  const connectedPartners = [
    {
      name: 'CHU Bordeaux',
      type: 'Hôpital',
      system: 'Epic MyChart',
      status: 'connected',
      lastSync: '2024-12-28 14:30',
      volume: '2.4K/jour'
    },
    {
      name: 'CERBA HealthCare',
      type: 'Laboratoire',
      system: 'CERBA API',
      status: 'connected',
      lastSync: '2024-12-28 14:28',
      volume: '890/jour'
    },
    {
      name: 'Pharmacie Centrale',
      type: 'Pharmacie',
      system: 'PHARMAGEST',
      status: 'testing',
      lastSync: '2024-12-28 12:15',
      volume: '156/jour'
    },
    {
      name: 'Clinique Pasteur',
      type: 'Clinique',
      system: 'Meditech',
      status: 'setup',
      lastSync: 'N/A',
      volume: 'N/A'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-yellow-600 bg-yellow-100';
      case 'testing': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-orange-600 bg-orange-100';
      case 'setup': return 'text-gray-600 bg-gray-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {metrics.map((metric, index) => {
          const IconComponent = metric.icon;
          return (
            <Card key={index} className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{metric.label}</p>
                  <p className={`text-2xl font-bold ${metric.color}`}>{metric.value}</p>
                  <p className="text-xs text-gray-500 mt-1">{metric.trend}</p>
                </div>
                <IconComponent className={`h-8 w-8 ${metric.color}`} />
              </div>
            </Card>
          );
        })}
      </div>

      {/* Status intégrations */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">🔗 Status Intégrations B2B</h3>
        <div className="space-y-4">
          {integrations.map((integration) => {
            const IconComponent = integration.icon;
            return (
              <div key={integration.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                    <IconComponent className="h-6 w-6" />
                  </div>
                  <div>
                    <h4 className="font-medium">{integration.name}</h4>
                    <p className="text-sm text-gray-600">{integration.description}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className="text-xs bg-purple-100 text-purple-800">
                        {integration.category}
                      </Badge>
                      <span className="text-xs text-gray-500">{integration.connectivity}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <Badge className={getStatusColor(integration.status)}>
                      {integration.status}
                    </Badge>
                    <p className="text-sm text-gray-600 mt-1">{integration.progress}%</p>
                  </div>
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        integration.progress >= 80 ? 'bg-green-500' : 
                        integration.progress >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${integration.progress}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </Card>

      {/* Partenaires connectés */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">🏢 Partenaires Connectés</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {connectedPartners.map((partner, index) => (
            <div key={index} className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium">{partner.name}</h4>
                <Badge className={getStatusColor(partner.status)}>
                  {partner.status}
                </Badge>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Type:</span>
                  <span>{partner.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Système:</span>
                  <span>{partner.system}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Dernière sync:</span>
                  <span>{partner.lastSync}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Volume:</span>
                  <span className="font-medium">{partner.volume}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              🔗 Intégrations B2B - Phase 4
            </h1>
            <p className="text-gray-600">
              Écosystème complet d'intégrations avec systèmes de santé
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Configuration
            </Button>
            <Button>
              <BarChart3 className="h-4 w-4 mr-2" />
              Monitoring
            </Button>
          </div>
        </div>

        {/* Navigation tabs */}
        <div className="flex space-x-4 mb-6">
          {[
            { id: 'overview', label: 'Vue d\'ensemble', icon: <Activity className="h-4 w-4" /> },
            { id: 'hospitals', label: 'Hôpitaux', icon: <Building className="h-4 w-4" /> },
            { id: 'fhir', label: 'HL7 FHIR', icon: <Globe className="h-4 w-4" /> },
            { id: 'labs', label: 'Laboratoires', icon: <TestTube className="h-4 w-4" /> },
            { id: 'pharmacies', label: 'Pharmacies', icon: <Pill className="h-4 w-4" /> },
            { id: 'sdk', label: 'SDK', icon: <Code className="h-4 w-4" /> }
          ].map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? "default" : "outline"}
              onClick={() => setActiveTab(tab.id)}
              className="flex items-center space-x-2"
            >
              {tab.icon}
              <span>{tab.label}</span>
            </Button>
          ))}
        </div>

        {/* Content */}
        {activeTab === 'overview' && renderOverview()}
        {activeTab !== 'overview' && (
          <Card className="p-6">
            <div className="text-center py-12">
              <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <Code className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Module {activeTab} en développement
              </h3>
              <p className="text-gray-600">
                Cette section sera disponible dans la prochaine version de la Phase 4.
              </p>
            </div>
          </Card>
        )}

        {/* Roadmap Phase 4 */}
        <Card className="p-6 mt-6 bg-purple-50 border-purple-200">
          <div className="text-center">
            <Workflow className="h-12 w-12 text-purple-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-purple-900 mb-2">
              🔗 Phase 4 : Intégrations B2B & Écosystème
            </h3>
            <p className="text-purple-700 mb-4">
              Construction de l'écosystème ouvert permettant intégration avec tous systèmes médicaux
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="text-left">
                <h4 className="font-medium text-purple-900 mb-2">🎯 Intégrations planifiées :</h4>
                <ul className="space-y-1 text-purple-700">
                  <li>🏥 APIs hospitalières (70% - Mars 2025)</li>
                  <li>🌐 HL7 FHIR complet (85% - Février 2025)</li>
                  <li>🧪 Laboratoires (45% - Avril 2025)</li>
                  <li>💊 E-prescriptions (30% - Mai 2025)</li>
                  <li>⚙️ SDK développeurs (25% - Juin 2025)</li>
                </ul>
              </div>
              <div className="text-left">
                <h4 className="font-medium text-purple-900 mb-2">💰 Revenus projetés :</h4>
                <ul className="space-y-1 text-purple-700">
                  <li>🏥 Hôpitaux : 25k€/mois</li>
                  <li>🧪 Laboratoires : 20k€/mois</li>
                  <li>💊 Pharmacies : 30k€/mois</li>
                  <li>⚙️ SDK : 10k€/mois</li>
                  <li>📊 Total : 85k€/mois</li>
                </ul>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
