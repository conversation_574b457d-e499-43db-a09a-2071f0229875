'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { apiService } from '@/services/api';

const TestAuthPage: React.FC = () => {
  const { login, register, user, isAuthenticated, logout } = useAuth();
  const { showSuccess, showError, showInfo } = useToast();
  const [isTestingAPI, setIsTestingAPI] = useState(false);
  const [isTestingAuth, setIsTestingAuth] = useState(false);
  const [apiResult, setApiResult] = useState<string>('');
  const [authResult, setAuthResult] = useState<string>('');

  const testDirectAPI = async () => {
    setIsTestingAPI(true);
    setApiResult('');
    
    try {
      showInfo('Testing API', 'Testing direct API connection...');
      const response = await apiService.healthCheck();
      
      if (response.data) {
        setApiResult(`✅ API Connected: ${JSON.stringify(response.data, null, 2)}`);
        showSuccess('API Test Success', 'Backend API is responding correctly');
      } else {
        setApiResult('❌ API Error: No response data');
        showError('API Test Failed', 'No response data received');
      }
    } catch (error: any) {
      const errorMsg = error.response?.data?.message || error.message || 'Unknown error';
      setApiResult(`❌ API Error: ${errorMsg}`);
      showError('API Test Failed', errorMsg);
    } finally {
      setIsTestingAPI(false);
    }
  };

  const testAuthContext = async () => {
    setIsTestingAuth(true);
    setAuthResult('');
    
    try {
      showInfo('Testing Auth', 'Testing login with demo credentials...');
      
      await login('<EMAIL>', 'Password123');
      setAuthResult('✅ Login successful via AuthContext');
      showSuccess('Auth Test Success', 'Login successful!');
    } catch (error: any) {
      const errorMsg = error.response?.data?.message || error.message || 'Unknown error';
      setAuthResult(`❌ Auth Error: ${errorMsg}`);
      showError('Auth Test Failed', errorMsg);
    } finally {
      setIsTestingAuth(false);
    }
  };

  const testRegistration = async () => {
    setIsTestingAuth(true);
    setAuthResult('');
    
    try {
      showInfo('Testing Registration', 'Testing user registration...');
      
      const testUser = {
        firstName: 'Test',
        lastName: 'User',
        email: `test.user.${Date.now()}@example.com`,
        password: 'TestPassword123',
        confirmPassword: 'TestPassword123',
        phone: '+1234567890'
      };

      await register(`${testUser.firstName} ${testUser.lastName}`, testUser.email, testUser.password);
      setAuthResult('✅ Registration successful via AuthContext');
      showSuccess('Registration Test Success', 'New user registered successfully!');
    } catch (error: any) {
      const errorMsg = error.response?.data?.message || error.message || 'Unknown error';
      setAuthResult(`❌ Registration Error: ${errorMsg}`);
      showError('Registration Test Failed', errorMsg);
    } finally {
      setIsTestingAuth(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      setAuthResult('✅ Logout successful');
      showSuccess('Logout Success', 'Successfully logged out');
    } catch (error: any) {
      showError('Logout Failed', error.message);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            🧪 Authentication System Test
          </h1>
          <p className="text-gray-600">
            Test the authentication system and API connectivity
          </p>
        </div>

        {/* Current Auth Status */}
        <div className="mb-8 p-6 bg-white rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Current Authentication Status</h2>
          <div className="space-y-2">
            <p><strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}</p>
            {user && (
              <div>
                <p><strong>User:</strong> {user.user?.name || 'N/A'}</p>
                <p><strong>Email:</strong> {user.user?.email || 'N/A'}</p>
                <p><strong>ID:</strong> {user.user?.id || 'N/A'}</p>
                <button
                  onClick={handleLogout}
                  className="mt-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>

        {/* API Test */}
        <div className="mb-8 p-6 bg-white rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">API Connectivity Test</h2>
          <p className="text-gray-600 mb-4">
            Test direct connection to the backend API health endpoint.
          </p>
          
          <button
            onClick={testDirectAPI}
            disabled={isTestingAPI}
            className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors mb-4"
          >
            {isTestingAPI ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Testing API...
              </>
            ) : (
              <>
                🔗 Test API Connection
              </>
            )}
          </button>
          
          {apiResult && (
            <div className="mt-4 p-4 bg-gray-100 rounded-lg">
              <pre className="text-sm whitespace-pre-wrap">{apiResult}</pre>
            </div>
          )}
        </div>

        {/* Auth Test */}
        <div className="mb-8 p-6 bg-white rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Authentication Test</h2>
          <p className="text-gray-600 mb-4">
            Test the login and registration functionality through the AuthContext.
          </p>
          
          <div className="space-x-4">
            <button
              onClick={testAuthContext}
              disabled={isTestingAuth || isAuthenticated}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isTestingAuth ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                  Testing Login...
                </>
              ) : (
                <>
                  🔐 Test Login (Demo User)
                </>
              )}
            </button>

            <button
              onClick={testRegistration}
              disabled={isTestingAuth || isAuthenticated}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isTestingAuth ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                  Testing Registration...
                </>
              ) : (
                <>
                  📝 Test Registration (New User)
                </>
              )}
            </button>
          </div>
          
          {authResult && (
            <div className="mt-4 p-4 bg-gray-100 rounded-lg">
              <pre className="text-sm whitespace-pre-wrap">{authResult}</pre>
            </div>
          )}
        </div>

        {/* Demo Credentials */}
        <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="text-lg font-medium text-blue-800 mb-2">Demo Credentials</h3>
          <div className="text-sm text-blue-700 space-y-1">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> Password123</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestAuthPage;
