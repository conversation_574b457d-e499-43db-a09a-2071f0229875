'use client';

import React, { useState, useEffect } from 'react';

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'warning' | 'pending';
  message: string;
  details?: any;
}

export default function TestSupabaseSimple() {
  const [results, setResults] = useState<TestResult[]>([]);
  const [testing, setTesting] = useState(false);
  const [autoRun, setAutoRun] = useState(false);

  const addResult = (result: TestResult) => {
    setResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const runAllTests = async () => {
    setTesting(true);
    clearResults();

    // Test 1: Variables d'environnement
    try {
      const envVars = {
        SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
        SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        DUAL_MODE: process.env.NEXT_PUBLIC_DUAL_DATABASE_MODE,
        USE_SUPABASE_DB: process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE,
        USE_SUPABASE_AUTH: process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH,
      };

      const missing = Object.entries(envVars).filter(([key, value]) => !value);
      
      addResult({
        name: 'Variables d\'environnement',
        status: missing.length === 0 ? 'success' : 'warning',
        message: missing.length === 0 
          ? 'Toutes les variables configurées ✅' 
          : `${missing.length} variable(s) manquante(s)`,
        details: envVars
      });
    } catch (error) {
      addResult({
        name: 'Variables d\'environnement',
        status: 'error',
        message: 'Erreur lors de la vérification',
        details: String(error)
      });
    }

    // Test 2: Import Client Supabase
    try {
      const { createSupabaseClient, isSupabaseConfigured } = await import('@/lib/supabase/client');
      
      if (!isSupabaseConfigured()) {
        addResult({
          name: 'Configuration Supabase',
          status: 'error',
          message: 'Variables Supabase manquantes',
          details: 'NEXT_PUBLIC_SUPABASE_URL ou NEXT_PUBLIC_SUPABASE_ANON_KEY non configurés'
        });
      } else {
        const client = createSupabaseClient();
        
        addResult({
          name: 'Client Supabase',
          status: 'success',
          message: 'Client créé avec succès ✅',
          details: 'Import et création réussis'
        });

        // Test de connexion
        try {
          const { data, error } = await client.from('users').select('count').limit(1);
          
          addResult({
            name: 'Connexion Supabase',
            status: error ? 'error' : 'success',
            message: error ? `Erreur: ${error.message}` : 'Connexion réussie ✅',
            details: error || 'Base de données accessible'
          });
        } catch (connError) {
          addResult({
            name: 'Connexion Supabase',
            status: 'error',
            message: 'Erreur de connexion',
            details: String(connError)
          });
        }
      }
    } catch (importError) {
      addResult({
        name: 'Client Supabase',
        status: 'error',
        message: 'Erreur import client',
        details: String(importError)
      });
    }

    // Test 3: Feature Flags
    try {
      const { FEATURE_FLAGS, isInDualMode, canUseSupabase } = await import('@/lib/config/feature-flags');
      
      addResult({
        name: 'Feature Flags',
        status: 'success',
        message: `Dual: ${isInDualMode()}, Supabase: ${canUseSupabase()} ✅`,
        details: FEATURE_FLAGS
      });
    } catch (flagError) {
      addResult({
        name: 'Feature Flags',
        status: 'error',
        message: 'Erreur feature flags',
        details: String(flagError)
      });
    }

    // Test 4: Database Manager
    try {
      const { getDatabaseManager } = await import('@/lib/database');
      const manager = await getDatabaseManager();
      
      const isDual = manager.isDualModeAvailable();
      const hasSupabase = !!manager.getSupabaseAdapter();
      const hasSQLite = !!manager.getSQLiteAdapter();
      
      addResult({
        name: 'Database Manager',
        status: 'success',
        message: `Mode ${isDual ? 'dual' : 'simple'} ✅`,
        details: { isDual, hasSupabase, hasSQLite }
      });

      // Health check
      const adapter = manager.getAdapter();
      const health = await adapter.healthCheck();
      
      addResult({
        name: 'Health Check',
        status: 'success',
        message: `DB ${health.status} ✅`,
        details: health
      });
      
    } catch (dbError) {
      addResult({
        name: 'Database Manager',
        status: 'error',
        message: 'Erreur database manager',
        details: String(dbError)
      });
    }

    setTesting(false);
  };

  // Auto-run au chargement
  useEffect(() => {
    if (!autoRun) {
      setAutoRun(true);
      runAllTests();
    }
  }, [autoRun]);

  const getStatusEmoji = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      case 'pending': return '🔄';
      default: return '❓';
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '#4caf50';
      case 'warning': return '#ff9800';
      case 'error': return '#f44336';
      case 'pending': return '#2196f3';
      default: return '#757575';
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh',
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f5f5f5'
    }}>
      <div style={{ maxWidth: '900px', margin: '0 auto' }}>
        <div style={{
          backgroundColor: 'white',
          padding: '30px',
          borderRadius: '12px',
          boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
        }}>
          <h1 style={{ 
            fontSize: '28px',
            fontWeight: 'bold',
            marginBottom: '8px',
            color: '#333'
          }}>
            🧪 Test de Migration Supabase - Version Simple
          </h1>
          
          <p style={{ 
            color: '#666',
            marginBottom: '30px',
            fontSize: '16px'
          }}>
            Diagnostic complet de la configuration Supabase et du mode dual
          </p>

          {/* Contrôles */}
          <div style={{
            marginBottom: '30px',
            padding: '20px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            border: '1px solid #e9ecef'
          }}>
            <h3 style={{ margin: '0 0 15px 0', fontSize: '18px' }}>⚙️ Contrôles</h3>
            <button
              onClick={runAllTests}
              disabled={testing}
              style={{
                backgroundColor: testing ? '#6c757d' : '#007bff',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '6px',
                fontSize: '16px',
                fontWeight: '500',
                cursor: testing ? 'not-allowed' : 'pointer',
                marginRight: '15px'
              }}
            >
              {testing ? '🔄 Tests en cours...' : '🔄 Relancer tous les tests'}
            </button>
            
            <button
              onClick={clearResults}
              style={{
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '6px',
                fontSize: '16px',
                fontWeight: '500',
                cursor: 'pointer'
              }}
            >
              🗑️ Effacer les résultats
            </button>
          </div>

          {/* Résultats */}
          <div style={{
            marginBottom: '30px',
            padding: '20px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            border: '1px solid #e9ecef'
          }}>
            <h3 style={{ margin: '0 0 20px 0', fontSize: '18px' }}>
              📊 Résultats des tests ({results.length})
            </h3>
            
            {results.length === 0 ? (
              <p style={{ color: '#6c757d', fontStyle: 'italic' }}>
                {testing ? 'Tests en cours...' : 'Aucun test exécuté'}
              </p>
            ) : (
              <div>
                {results.map((result, index) => (
                  <div key={index} style={{
                    margin: '10px 0',
                    padding: '15px',
                    backgroundColor: 'white',
                    border: `2px solid ${getStatusColor(result.status)}`,
                    borderRadius: '8px'
                  }}>
                    <div style={{ 
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '8px'
                    }}>
                      <span style={{ fontSize: '20px', marginRight: '10px' }}>
                        {getStatusEmoji(result.status)}
                      </span>
                      <h4 style={{ margin: '0', fontSize: '16px', fontWeight: '600' }}>
                        {result.name}
                      </h4>
                    </div>
                    
                    <p style={{ 
                      margin: '0 0 10px 30px',
                      color: getStatusColor(result.status),
                      fontWeight: '500'
                    }}>
                      {result.message}
                    </p>
                    
                    {result.details && (
                      <details style={{ marginLeft: '30px' }}>
                        <summary style={{ 
                          fontSize: '14px',
                          color: '#666',
                          cursor: 'pointer',
                          marginBottom: '5px'
                        }}>
                          Voir les détails
                        </summary>
                        <pre style={{
                          fontSize: '12px',
                          backgroundColor: '#f1f3f4',
                          padding: '10px',
                          borderRadius: '4px',
                          overflow: 'auto',
                          color: '#333',
                          border: '1px solid #e0e0e0'
                        }}>
                          {JSON.stringify(result.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Résumé */}
          {results.length > 0 && (
            <div style={{
              padding: '20px',
              backgroundColor: '#e8f5e8',
              border: '2px solid #4caf50',
              borderRadius: '8px'
            }}>
              <h3 style={{ color: '#2e7d32', margin: '0 0 15px 0' }}>
                🎯 Résumé du Diagnostic
              </h3>
              <div style={{ 
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
                gap: '15px',
                textAlign: 'center'
              }}>
                <div>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#4caf50' }}>
                    {results.filter(r => r.status === 'success').length}
                  </div>
                  <div style={{ fontSize: '14px' }}>Réussis</div>
                </div>
                <div>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff9800' }}>
                    {results.filter(r => r.status === 'warning').length}
                  </div>
                  <div style={{ fontSize: '14px' }}>Avertissements</div>
                </div>
                <div>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f44336' }}>
                    {results.filter(r => r.status === 'error').length}
                  </div>
                  <div style={{ fontSize: '14px' }}>Erreurs</div>
                </div>
              </div>
            </div>
          )}

          <div style={{ 
            marginTop: '30px',
            padding: '15px',
            backgroundColor: '#e3f2fd',
            border: '1px solid #2196f3',
            borderRadius: '8px',
            fontSize: '12px',
            color: '#1565c0'
          }}>
            <strong>💡 Instructions :</strong> Cette page teste automatiquement votre configuration Supabase. 
            Assurez-vous d'avoir configuré vos variables d'environnement et exécuté le schéma SQL dans Supabase.
            <br />
            <strong>URL de test :</strong> http://localhost:3000/test-supabase-simple
          </div>
        </div>
      </div>
    </div>
  );
} 