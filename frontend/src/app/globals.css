@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Variables de couleur pour le mode clair */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    
    /* Variables personnalisées MindFlow */
    --mindflow-primary: 221.2 83.2% 53.3%;
    --mindflow-secondary: 158.1 64.4% 51.6%;
    --mindflow-accent: 0 84.2% 60.2%;
    --mindflow-neutral: 215 25% 27%;
  }

  .dark {
    /* Variables de couleur pour le mode sombre */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.0%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Amélioration de la typographie */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }
  
  h1 {
    @apply text-4xl lg:text-5xl;
  }
  
  h2 {
    @apply text-3xl lg:text-4xl;
  }
  
  h3 {
    @apply text-2xl lg:text-3xl;
  }
  
  h4 {
    @apply text-xl lg:text-2xl;
  }
  
  h5 {
    @apply text-lg lg:text-xl;
  }
  
  h6 {
    @apply text-base lg:text-lg;
  }
  
  p {
    @apply leading-7;
  }
  
  /* Styles pour les liens */
  a {
    @apply text-blue-600 hover:text-blue-800 transition-colors;
  }
  
  /* Amélioration du focus pour l'accessibilité */
  *:focus-visible {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }
  
  /* Styles de sélection personnalisés */
  ::selection {
    @apply bg-blue-100 text-blue-900;
  }
  
  /* Scrollbar personnalisée */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer components {
  /* Composants utilitaires */
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-12 lg:py-16;
  }
  
  .card-hover {
    @apply transition-all duration-200 hover:shadow-lg hover:scale-[1.02];
  }
  
  .gradient-primary {
    @apply bg-gradient-to-r from-blue-500 to-green-500;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent;
  }
  
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  .shadow-glow-lg {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
  }
  
  /* Animations personnalisées */
  .animate-fade-in {
    animation: fade-in 0.5s ease-out;
  }
  
  .animate-slide-in {
    animation: slide-in 0.3s ease-out;
  }
  
  .animate-pulse-glow {
    animation: pulse-glow 2s infinite;
  }
  
  /* États de chargement */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }
  
  /* Styles pour les graphiques */
  .chart-container {
    @apply w-full h-64 lg:h-80;
  }
  
  /* Styles responsifs spéciaux */
  .mobile-padding {
    @apply px-4 sm:px-6;
  }
  
  .desktop-padding {
    @apply lg:px-8 xl:px-12;
  }
  
  /* Styles d'impression */
  @media print {
    .no-print {
      @apply hidden;
    }
    
    .print-friendly {
      @apply text-black bg-white;
    }
  }
}

@layer utilities {
  /* Utilitaires de positionnement */
  .center-absolute {
    @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  }
  
  .center-flex {
    @apply flex items-center justify-center;
  }
  
  /* Utilitaires de texte */
  .text-balance {
    text-wrap: balance;
  }
  
  .text-pretty {
    text-wrap: pretty;
  }
  
  /* Utilitaires de débordement */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Utilitaires de sécurité */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Styles globaux pour les composants tiers */
.sonner-toaster {
  @apply font-sans;
}

.recharts-tooltip-wrapper {
  @apply rounded-lg border shadow-lg;
}

.recharts-tooltip-content {
  @apply bg-white text-gray-900 border-gray-200;
}

/* Styles pour React Big Calendar */
.rbc-calendar {
  @apply font-sans;
}

.rbc-header {
  @apply bg-gray-100 text-gray-700 font-medium;
}

.rbc-event {
  @apply bg-blue-500 text-white rounded;
}

.rbc-today {
  @apply bg-blue-50;
}

/* Améliorations de performance */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
}

/* Styles de mise au point pour les développeurs */
.debug-grid {
  background-image: 
    linear-gradient(rgba(255, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Mode high contrast pour l'accessibilité */
@media (prefers-contrast: high) {
  :root {
    --border: 0 0% 20%;
    --input: 0 0% 20%;
  }
  
  .dark {
    --border: 0 0% 80%;
    --input: 0 0% 80%;
  }
}

/* Réduction de mouvement pour l'accessibilité */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
