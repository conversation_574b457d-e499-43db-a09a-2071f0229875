'use client';

import { useState } from 'react';
import { createSupabaseClient } from '@/lib/supabase/browser-client';

export default function TestSupabaseSchema() {
  const [status, setStatus] = useState<string>('');
  const [isExecuting, setIsExecuting] = useState(false);
  const supabase = createSupabaseClient();

  const executeSchema = async () => {
    setIsExecuting(true);
    setStatus('Exécution du schéma en cours...');

    try {
      // Test de connexion
      const { data: testData, error: testError } = await supabase
        .from('users')
        .select('count(*)');

      if (testError && testError.code === '42P01') {
        // Table n'existe pas, c'est normal
        setStatus('Tables non créées. Vous devez exécuter le schéma SQL manuellement.');
      } else if (testError) {
        setStatus(`Erreur de connexion: ${testError.message}`);
      } else {
        setStatus('✅ Tables déjà créées et fonctionnelles !');
      }
    } catch (error) {
      setStatus(`Erreur: ${error}`);
    } finally {
      setIsExecuting(false);
    }
  };

  const schemaSQL = `-- Script de création du schéma Supabase pour MindFlow Pro
-- Exécutez ce script dans l'éditeur SQL de Supabase

-- Enable UUID extension
create extension if not exists "uuid-ossp";

-- Create users table with RLS
create table public.users (
  id uuid default uuid_generate_v4() primary key,
  email text unique not null,
  full_name text,
  avatar_url text,
  role text default 'user' check (role in ('user', 'professional', 'admin')),
  preferences jsonb default '{}'::jsonb,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create mood_entries table
create table public.mood_entries (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade not null,
  mood_level integer not null check (mood_level >= 1 and mood_level <= 10),
  energy_level integer check (energy_level >= 1 and energy_level <= 10),
  stress_level integer check (stress_level >= 1 and stress_level <= 10),
  notes text,
  triggers text[],
  activities text[],
  date date not null default current_date,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create journal_entries table
create table public.journal_entries (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade not null,
  title text not null,
  content text not null,
  mood_before integer check (mood_before >= 1 and mood_before <= 10),
  mood_after integer check (mood_after >= 1 and mood_after <= 10),
  tags text[],
  is_private boolean default true,
  ai_insights jsonb default '{}'::jsonb,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create updated_at triggers
create or replace function public.handle_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

create trigger handle_users_updated_at
  before update on public.users
  for each row execute function public.handle_updated_at();

create trigger handle_mood_entries_updated_at
  before update on public.mood_entries
  for each row execute function public.handle_updated_at();

create trigger handle_journal_entries_updated_at
  before update on public.journal_entries
  for each row execute function public.handle_updated_at();

-- Create indexes for better performance
create index mood_entries_user_id_date_idx on public.mood_entries(user_id, date desc);
create index journal_entries_user_id_created_idx on public.journal_entries(user_id, created_at desc);
create index users_email_idx on public.users(email);

-- Enable Row Level Security (RLS)
alter table public.users enable row level security;
alter table public.mood_entries enable row level security;
alter table public.journal_entries enable row level security;

-- Create RLS policies
create policy "Users can view own profile" on public.users
  for select using (auth.uid() = id);

create policy "Users can update own profile" on public.users
  for update using (auth.uid() = id);

-- Mood entries policies
create policy "Users can view own mood entries" on public.mood_entries
  for select using (auth.uid() = user_id);

create policy "Users can insert own mood entries" on public.mood_entries
  for insert with check (auth.uid() = user_id);

create policy "Users can update own mood entries" on public.mood_entries
  for update using (auth.uid() = user_id);

create policy "Users can delete own mood entries" on public.mood_entries
  for delete using (auth.uid() = user_id);

-- Journal entries policies  
create policy "Users can view own journal entries" on public.journal_entries
  for select using (auth.uid() = user_id);

create policy "Users can insert own journal entries" on public.journal_entries
  for insert with check (auth.uid() = user_id);

create policy "Users can update own journal entries" on public.journal_entries
  for update using (auth.uid() = user_id);

create policy "Users can delete own journal entries" on public.journal_entries
  for delete using (auth.uid() = user_id);

-- Success message
select 'Schema Supabase créé avec succès!' as message;`;

  const copyToClipboard = () => {
    navigator.clipboard.writeText(schemaSQL);
    alert('Schéma SQL copié dans le presse-papiers !');
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-center text-gray-900 mb-6">
            🗄️ Configuration Schéma Supabase
          </h1>
          
          <div className="space-y-6">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Instructions :</h3>
              <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                <li>Copiez le schéma SQL ci-dessous</li>
                <li>Allez sur <a href="https://supabase.com/dashboard" target="_blank" className="underline">votre dashboard Supabase</a></li>
                <li>Ouvrez l'éditeur SQL (SQL Editor)</li>
                <li>Collez et exécutez le script</li>
                <li>Testez la connexion ci-dessous</li>
              </ol>
            </div>

            <div className="text-center space-y-4">
              <button
                onClick={copyToClipboard}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mr-4"
              >
                📋 Copier le Schéma SQL
              </button>
              
              <button
                onClick={executeSchema}
                disabled={isExecuting}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                {isExecuting ? '⏳ Test en cours...' : '🧪 Tester la Connexion'}
              </button>
            </div>

            {status && (
              <div className={`p-4 rounded-lg ${
                status.includes('✅') ? 'bg-green-50 border-green-200 text-green-800' :
                status.includes('Erreur') ? 'bg-red-50 border-red-200 text-red-800' :
                'bg-yellow-50 border-yellow-200 text-yellow-800'
              }`}>
                <p className="text-sm font-medium">{status}</p>
              </div>
            )}

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">Schéma SQL :</h3>
              <div className="bg-black text-green-400 p-4 rounded text-xs overflow-auto max-h-96 font-mono">
                <pre>{schemaSQL}</pre>
              </div>
            </div>

            <div className="text-center">
              <a 
                href="/test-phase4-supabase" 
                className="inline-block px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                🚀 Aller aux Tests Phase 4
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 