'use client';

import React, { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { useWebSocket } from '@/hooks/useWebSocket';

const badgeColor = (level: string) => {
  switch (level) {
    case 'error': return 'bg-red-100 text-red-700 border-red-300';
    case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    case 'info': return 'bg-blue-100 text-blue-800 border-blue-300';
    case 'success': return 'bg-green-100 text-green-800 border-green-300';
    default: return 'bg-gray-100 text-gray-800 border-gray-300';
  }
};

const icon = (level: string) => {
  switch (level) {
    case 'error': return '⛔';
    case 'warning': return '⚠️';
    case 'info': return 'ℹ️';
    case 'success': return '✅';
    default: return '•';
  }
};

const MonitoringPage = () => {
  const [logs, setLogs] = useState([]);
  const [stats, setStats] = useState<any>({});
  const [audit, setAudit] = useState([]);
  const [alerts, setAlerts] = useState<any[]>([]);

  // Récupération des données API
  useEffect(() => {
    fetch('/api/v1/monitoring/logs').then(res => res.json()).then(data => setLogs(data.logs || []));
    fetch('/api/v1/monitoring/stats').then(res => res.json()).then(data => setStats(data));
    fetch('/api/v1/monitoring/audit').then(res => res.json()).then(data => setAudit(data.audit || []));
  }, []);

  // WebSocket : écoute des alertes monitoring
  const { isConnected } = useWebSocket();

  // Écouter les événements de monitoring via les événements personnalisés
  useEffect(() => {
    const handleMonitoringAlert = (event: CustomEvent) => {
      setAlerts(prev => [{...event.detail, _ts: Date.now()}, ...prev]);
    };

    window.addEventListener('monitoring-alert', handleMonitoringAlert as EventListener);

    return () => {
      window.removeEventListener('monitoring-alert', handleMonitoringAlert as EventListener);
    };
  }, []);

  return (
    <div className="p-4 md:p-8 max-w-5xl mx-auto space-y-8">
      <h1 className="text-3xl font-extrabold mb-2 text-gray-900">🩺 Monitoring global</h1>
      <div className="grid md:grid-cols-3 gap-6">
        <Card className="p-6 shadow-md border border-gray-200">
          <h2 className="font-bold text-lg mb-3 text-blue-900 flex items-center gap-2">📊 Statistiques</h2>
          <ul className="space-y-2">
            {Object.entries(stats).map(([k, v]) => (
              <li key={k} className="flex items-center gap-2">
                <span className="font-semibold capitalize text-gray-700">{k}:</span>
                <span className="bg-blue-50 text-blue-800 rounded px-2 py-0.5 text-xs font-mono">{String(v)}</span>
              </li>
            ))}
          </ul>
        </Card>
        <Card className="p-6 shadow-md border border-gray-200 md:col-span-2">
          <h2 className="font-bold text-lg mb-3 text-blue-900 flex items-center gap-2">📝 Logs récents</h2>
          <ul className="space-y-2 max-h-48 overflow-y-auto pr-2">
            {logs.map((log: any, i) => (
              <li key={i} className="flex items-center gap-2 text-xs">
                <span className={`inline-block rounded px-2 py-0.5 border font-bold ${badgeColor(log.level)}`}>{icon(log.level)} {log.level}</span>
                <span className="text-gray-500 font-mono">{log.timestamp}</span>
                <span className="text-gray-800">{log.message}</span>
              </li>
            ))}
          </ul>
        </Card>
      </div>
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="p-6 shadow-md border border-gray-200">
          <h2 className="font-bold text-lg mb-3 text-blue-900 flex items-center gap-2">🔍 Audit trail</h2>
          <ul className="space-y-2 max-h-40 overflow-y-auto pr-2">
            {audit.map((a: any, i) => (
              <li key={i} className="flex items-center gap-2 text-xs">
                <span className="bg-gray-100 text-gray-700 rounded px-2 py-0.5 border border-gray-300 font-mono">{a.timestamp}</span>
                <span className="text-gray-800">{a.action}</span>
                <span className="text-gray-500">(userId: {a.userId})</span>
              </li>
            ))}
          </ul>
        </Card>
        <Card className="p-6 shadow-md border border-gray-200">
          <h2 className="font-bold text-lg mb-3 text-blue-900 flex items-center gap-2">🚨 Alertes temps réel</h2>
          <ol className="relative border-l-2 border-blue-200 ml-2">
            {alerts.map((alert, i) => (
              <li key={alert._ts || i} className="mb-6 ml-4">
                <div className="absolute w-3 h-3 bg-blue-400 rounded-full -left-1.5 border-2 border-white"></div>
                <div className="flex items-center gap-2 mb-1">
                  <span className={`inline-block rounded px-2 py-0.5 border font-bold ${badgeColor(alert.type)}`}>{icon(alert.type)}</span>
                  <span className="text-xs text-gray-500 font-mono">{alert.timestamp || new Date(alert._ts).toLocaleTimeString()}</span>
                </div>
                <div className="text-sm text-gray-900 font-semibold">{alert.message}</div>
                {alert.error && <div className="text-xs text-red-600 mt-1">{alert.error}</div>}
                {alert.url && <div className="text-xs text-gray-400">{alert.url} ({alert.method})</div>}
              </li>
            ))}
            {alerts.length === 0 && <li className="text-xs text-gray-400 ml-4">Aucune alerte reçue.</li>}
          </ol>
        </Card>
      </div>
    </div>
  );
};

export default MonitoringPage; 