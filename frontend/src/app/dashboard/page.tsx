'use client';

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/Layout/DashboardLayout';
import { useDashboardData } from '@/hooks/useDashboardData';
import { useRouter } from 'next/navigation';
import { 
  Brain, 
  Heart, 
  Calendar, 
  TrendingUp, 
  Activity,
  Moon,
  Sun,
  Cloud,
  Target,
  Zap,
  Smile,
  Frown,
  Meh,
  AlertCircle,
  Clock,
  Award,
  Users,
  BookOpen
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Area,
  AreaChart,
  PieChart,
  Pie,
  Cell
} from 'recharts';

// Sélecteur d'humeur rapide avec design amélioré
const QuickMoodSelector = ({ onMoodSelect }: { onMoodSelect: (mood: number) => void }) => {
  const moods = [
    { value: 1, emoji: '😔', label: 'Très bas', color: 'from-red-500 to-red-600' },
    { value: 2, emoji: '😕', label: 'Bas', color: 'from-orange-500 to-orange-600' },
    { value: 3, emoji: '😐', label: 'Neutre', color: 'from-yellow-500 to-yellow-600' },
    { value: 4, emoji: '🙂', label: 'Bien', color: 'from-green-500 to-green-600' },
    { value: 5, emoji: '😊', label: 'Excellent', color: 'from-blue-500 to-blue-600' }
  ];

  return (
    <Card className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-xl">
      <CardHeader>
        <CardTitle className="text-2xl font-bold flex items-center gap-2">
          <Brain className="w-6 h-6" />
          Bonjour Alex 👋
        </CardTitle>
        <p className="text-base opacity-90">Comment vous sentez-vous aujourd'hui ?</p>
      </CardHeader>
      <CardContent>
        <div className="text-xl font-semibold mb-4">Vérification rapide de l'humeur</div>
        <div className="flex justify-between gap-2">
          {moods.map((mood) => (
            <button
              key={mood.value}
              onClick={() => onMoodSelect(mood.value)}
              className="flex flex-col items-center p-3 rounded-xl bg-white/20 hover:bg-white/30 transition-all transform hover:scale-110 backdrop-blur-sm"
            >
              <span className="text-3xl mb-1">{mood.emoji}</span>
              <span className="text-xs font-medium">{mood.label}</span>
            </button>
          ))}
        </div>
        <div className="mt-4 text-sm opacity-80">
          <p>Votre dernière humeur : <span className="font-semibold">Bien 🙂</span> (il y a 2h)</p>
        </div>
      </CardContent>
    </Card>
  );
};

// Graphique de suivi d'humeur et stress amélioré
const MoodStressChart = ({ data }: { data: any[] }) => {
  return (
    <Card className="shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-semibold">Suivi Humeur & Stress</CardTitle>
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          Tendance positive ↑
        </Badge>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={250}>
          <AreaChart data={data}>
            <defs>
              <linearGradient id="colorMood" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
              </linearGradient>
              <linearGradient id="colorStress" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="day" stroke="#888" fontSize={12} />
            <YAxis stroke="#888" fontSize={12} domain={[0, 10]} />
            <Tooltip 
              contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.95)', borderRadius: '8px', border: '1px solid #e5e7eb' }}
            />
            <Area 
              type="monotone" 
              dataKey="mood" 
              stroke="#10b981" 
              fillOpacity={1}
              fill="url(#colorMood)"
              strokeWidth={3}
            />
            <Area 
              type="monotone" 
              dataKey="stress" 
              stroke="#f59e0b" 
              fillOpacity={1}
              fill="url(#colorStress)"
              strokeWidth={3}
            />
          </AreaChart>
        </ResponsiveContainer>
        <div className="flex justify-center gap-6 mt-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Humeur</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Stress</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Carte de focus du jour améliorée
const TodaysFocus = ({ objective }: { objective: string }) => {
  const [progress, setProgress] = useState(75);

  return (
    <Card className="shadow-lg border-l-4 border-l-orange-500">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Target className="w-5 h-5 text-orange-500" />
          Focus du jour
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <p className="font-medium text-gray-800">{objective}</p>
            <p className="text-sm text-gray-500 mt-1">Durée estimée : 15 minutes</p>
          </div>
          <Progress value={progress} className="h-3" />
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-600">{Math.floor(progress/25)} sur 4 étapes</p>
            <p className="text-sm font-semibold text-orange-600">{progress}%</p>
          </div>
          <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
            Continuer l'exercice
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Statistiques détaillées
const DetailedStats = () => {
  const stats = [
    { label: 'Sessions cette semaine', value: '12', icon: <Activity className="w-4 h-4" />, color: 'text-blue-600' },
    { label: 'Jours consécutifs', value: '5', icon: <Award className="w-4 h-4" />, color: 'text-green-600' },
    { label: 'Temps total', value: '3h 45m', icon: <Clock className="w-4 h-4" />, color: 'text-purple-600' },
    { label: 'Exercices complétés', value: '28', icon: <Target className="w-4 h-4" />, color: 'text-orange-600' }
  ];

  return (
    <div className="grid grid-cols-2 gap-4">
      {stats.map((stat, index) => (
        <Card key={index} className="shadow-md hover:shadow-lg transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <span className={`${stat.color}`}>{stat.icon}</span>
              <span className="text-2xl font-bold">{stat.value}</span>
            </div>
            <p className="text-sm text-gray-600">{stat.label}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

// Suggestions intelligentes améliorées
const SmartSuggestions = () => {
  const suggestions = [
    {
      icon: <Zap className="w-5 h-5" />,
      title: "Respiration rapide",
      description: "Réduire le stress en 3 minutes",
      color: "text-blue-500",
      bgColor: "bg-blue-50",
      time: "3 min"
    },
    {
      icon: <Sun className="w-5 h-5" />,
      title: "Sons de la nature",
      description: "Musique de concentration",
      color: "text-green-500",
      bgColor: "bg-green-50",
      time: "15 min"
    },
    {
      icon: <Moon className="w-5 h-5" />,
      title: "Méditation du soir",
      description: "Préparer un sommeil réparateur",
      color: "text-purple-500",
      bgColor: "bg-purple-50",
      time: "10 min"
    }
  ];

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Suggestions intelligentes</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {suggestions.map((suggestion, index) => (
          <div key={index} className={`flex items-start gap-3 p-3 rounded-lg ${suggestion.bgColor} hover:opacity-90 cursor-pointer transition-all`}>
            <div className={`${suggestion.color} mt-1`}>{suggestion.icon}</div>
            <div className="flex-1">
              <div className="flex justify-between items-start">
                <h4 className="font-medium text-sm">{suggestion.title}</h4>
                <Badge variant="secondary" className="text-xs">{suggestion.time}</Badge>
              </div>
              <p className="text-xs text-gray-600 mt-1">{suggestion.description}</p>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

// Section AI Coach améliorée
const AICoachSection = () => {
  return (
    <Card className="bg-gradient-to-br from-emerald-50 to-teal-50 border-emerald-200 shadow-lg">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Brain className="w-5 h-5 text-emerald-600" />
          Votre Coach IA
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="bg-white/80 rounded-lg p-4">
            <p className="text-gray-700 italic">
              "J'ai remarqué que votre niveau de stress augmente en fin de journée. 
              Essayons une pause de 5 minutes avec un exercice de respiration pour retrouver votre équilibre."
            </p>
          </div>
          <div className="flex gap-2">
            <Button className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-white">
              Commencer l'exercice
            </Button>
            <Button variant="outline" className="border-emerald-600 text-emerald-600 hover:bg-emerald-50">
              Plus tard
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Activité récente
const RecentActivity = () => {
  const activities = [
    { type: 'journal', title: 'Entrée de journal', time: 'Il y a 2h', icon: <BookOpen className="w-4 h-4" /> },
    { type: 'exercise', title: 'Méditation guidée', time: 'Ce matin', icon: <Brain className="w-4 h-4" /> },
    { type: 'mood', title: 'Check-in humeur', time: 'Hier', icon: <Heart className="w-4 h-4" /> }
  ];

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Activité récente</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {activities.map((activity, index) => (
            <div key={index} className="flex items-center gap-3 p-2">
              <div className="text-gray-500">{activity.icon}</div>
              <div className="flex-1">
                <p className="text-sm font-medium">{activity.title}</p>
                <p className="text-xs text-gray-500">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default function DashboardPage() {
  const router = useRouter();
  const { moodData, wellnessStats, recentActivities, isLoading } = useDashboardData();
  const [selectedMood, setSelectedMood] = useState<number | null>(null);

  // Données simulées pour le graphique
  const chartData = [
    { day: 'Lun', mood: 6, stress: 4 },
    { day: 'Mar', mood: 7, stress: 3 },
    { day: 'Mer', mood: 7.5, stress: 3.5 },
    { day: 'Jeu', mood: 6.5, stress: 5 },
    { day: 'Ven', mood: 7, stress: 4 },
    { day: 'Sam', mood: 8, stress: 3 },
    { day: 'Dim', mood: 8.5, stress: 2.5 }
  ];

  const handleMoodSelect = (mood: number) => {
    setSelectedMood(mood);
    console.log('Humeur sélectionnée:', mood);
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 max-w-7xl mx-auto space-y-6">
        {/* Header avec sélecteur d'humeur */}
        <QuickMoodSelector onMoodSelect={handleMoodSelect} />

        {/* Statistiques principales */}
        <DetailedStats />

        {/* Grille principale */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Colonne gauche - Graphiques et AI Coach */}
          <div className="lg:col-span-2 space-y-6">
            <MoodStressChart data={chartData} />
            <AICoachSection />
          </div>

          {/* Colonne droite - Widgets */}
          <div className="space-y-6">
            <TodaysFocus objective="Session de confiance en soi - 5 minutes" />
            <SmartSuggestions />
            <RecentActivity />
          </div>
        </div>

        {/* Actions rapides en bas */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button 
            variant="outline" 
            className="flex flex-col items-center gap-2 h-auto py-4 hover:bg-pink-50 hover:border-pink-300 transition-colors"
            onClick={() => router.push('/journal/new')}
          >
            <Heart className="w-6 h-6 text-pink-500" />
            <span>Journal</span>
          </Button>
          <Button 
            variant="outline" 
            className="flex flex-col items-center gap-2 h-auto py-4 hover:bg-purple-50 hover:border-purple-300 transition-colors"
            onClick={() => router.push('/ai-coach')}
          >
            <Brain className="w-6 h-6 text-purple-500" />
            <span>Coach IA</span>
          </Button>
          <Button 
            variant="outline" 
            className="flex flex-col items-center gap-2 h-auto py-4 hover:bg-green-50 hover:border-green-300 transition-colors"
            onClick={() => router.push('/analytics')}
          >
            <TrendingUp className="w-6 h-6 text-green-500" />
            <span>Analyses</span>
          </Button>
          <Button 
            variant="outline" 
            className="flex flex-col items-center gap-2 h-auto py-4 hover:bg-blue-50 hover:border-blue-300 transition-colors"
            onClick={() => router.push('/programs')}
          >
            <Activity className="w-6 h-6 text-blue-500" />
            <span>Programmes</span>
          </Button>
        </div>
      </div>
    </DashboardLayout>
  );
} 