'use client';

import React, { useState } from 'react';
import { 
  Users, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  Clock, 
  AlertTriangle,
  Bell,
  Video,
  FileText,
  BarChart3,
  Heart,
  Stethoscope,
  Pill,
  Shield,
  MessageCircle,
  Star,
  CheckCircle,
  XCircle,
  Phone,
  Settings,
  Plus,
  Filter,
  MoreVertical,
  ChevronRight,
  Zap,
  Target
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Types pour le dashboard professionnel
interface ProfessionalMetrics {
  totalPatients: number;
  activePatients: number;
  appointmentsToday: number;
  appointmentsWeek: number;
  revenue: {
    today: number;
    week: number;
    month: number;
    yearly: number;
  };
  satisfaction: {
    rating: number;
    reviews: number;
    npsScore: number;
  };
  performance: {
    onTimeRate: number;
    cancellationRate: number;
    noShowRate: number;
    responseTime: number;
  };
  healthMetrics: {
    burnoutRisk: 'low' | 'medium' | 'high';
    workloadScore: number;
    wellnessIndex: number;
  };
}

interface Patient {
  id: string;
  name: string;
  age: number;
  condition: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  lastVisit: string;
  nextAppointment?: string;
  riskScore: number;
  compliance: number;
  medications: string[];
  alerts: string[];
}

interface Appointment {
  id: string;
  patientName: string;
  time: string;
  duration: number;
  type: 'video' | 'in-person' | 'phone' | 'chat';
  status: 'scheduled' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'routine' | 'urgent' | 'follow-up';
  notes?: string;
}

interface AIInsight {
  id: string;
  type: 'diagnostic' | 'treatment' | 'risk' | 'efficiency' | 'wellness';
  title: string;
  description: string;
  confidence: number;
  actionRequired: boolean;
  patientId?: string;
  priority: 'low' | 'medium' | 'high';
}

// Données de démonstration
const mockMetrics: ProfessionalMetrics = {
  totalPatients: 247,
  activePatients: 189,
  appointmentsToday: 8,
  appointmentsWeek: 32,
  revenue: {
    today: 960,
    week: 3840,
    month: 15360,
    yearly: 184320
  },
  satisfaction: {
    rating: 4.8,
    reviews: 156,
    npsScore: 78
  },
  performance: {
    onTimeRate: 94,
    cancellationRate: 3,
    noShowRate: 2,
    responseTime: 12
  },
  healthMetrics: {
    burnoutRisk: 'low',
    workloadScore: 75,
    wellnessIndex: 82
  }
};

const mockPatients: Patient[] = [
  {
    id: '1',
    name: 'Marie Dubois',
    age: 34,
    condition: 'Anxiété généralisée',
    priority: 'medium',
    lastVisit: '2024-01-15',
    nextAppointment: '2024-01-22 14:00',
    riskScore: 65,
    compliance: 88,
    medications: ['Sertraline 50mg', 'Lorazépam 0.5mg'],
    alerts: ['Médication à renouveler']
  },
  {
    id: '2',
    name: 'Jean Martin',
    age: 45,
    condition: 'Dépression majeure',
    priority: 'high',
    lastVisit: '2024-01-12',
    nextAppointment: '2024-01-20 10:30',
    riskScore: 78,
    compliance: 72,
    medications: ['Escitalopram 10mg', 'Trazodone 50mg'],
    alerts: ['Suivi rapproché requis', 'Risque suicidaire à surveiller']
  },
  {
    id: '3',
    name: 'Sophie Leroy',
    age: 28,
    condition: 'Trouble panique',
    priority: 'medium',
    lastVisit: '2024-01-10',
    riskScore: 55,
    compliance: 91,
    medications: ['Paroxétine 20mg'],
    alerts: []
  }
];

const mockAppointments: Appointment[] = [
  {
    id: '1',
    patientName: 'Marie Dubois',
    time: '09:00',
    duration: 50,
    type: 'video',
    status: 'confirmed',
    priority: 'routine',
    notes: 'Suivi thérapie cognitive comportementale'
  },
  {
    id: '2',
    patientName: 'Jean Martin',
    time: '10:30',
    duration: 60,
    type: 'in-person',
    status: 'scheduled',
    priority: 'urgent',
    notes: 'Évaluation état dépressif'
  },
  {
    id: '3',
    patientName: 'Sophie Leroy',
    time: '14:00',
    duration: 45,
    type: 'video',
    status: 'confirmed',
    priority: 'follow-up'
  }
];

const mockAIInsights: AIInsight[] = [
  {
    id: '1',
    type: 'risk',
    title: 'Patient à risque élevé détecté',
    description: 'Jean Martin présente des marqueurs de risque suicidaire accru basés sur ses dernières réponses.',
    confidence: 87,
    actionRequired: true,
    patientId: '2',
    priority: 'high'
  },
  {
    id: '2',
    type: 'treatment',
    title: 'Ajustement de traitement suggéré',
    description: 'Marie Dubois pourrait bénéficier d\'une réduction progressive du Lorazépam.',
    confidence: 76,
    actionRequired: false,
    patientId: '1',
    priority: 'medium'
  },
  {
    id: '3',
    type: 'efficiency',
    title: 'Optimisation d\'horaires',
    description: 'Vous pourriez augmenter votre efficacité de 15% en regroupant les consultations vidéo.',
    confidence: 82,
    actionRequired: false,
    priority: 'low'
  }
];

export default function ProfessionalDashboard() {
  const [selectedTimeframe, setSelectedTimeframe] = useState('week');
  const [showNotifications, setShowNotifications] = useState(false);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in-progress': return 'text-blue-600 bg-blue-100';
      case 'confirmed': return 'text-emerald-600 bg-emerald-100';
      case 'scheduled': return 'text-gray-600 bg-gray-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskColor = (score: number) => {
    if (score >= 80) return 'text-red-600';
    if (score >= 60) return 'text-orange-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Dashboard Professionnel
            </h1>
            <p className="text-gray-600 mt-1">
              Vue d'ensemble de votre pratique médicale
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="day">Aujourd'hui</option>
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois</option>
              <option value="year">Cette année</option>
            </select>
            
            <Button
              variant="outline"
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative"
            >
              <Bell className="h-4 w-4" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
            </Button>
            
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Nouveau patient
            </Button>
          </div>
        </div>

        {/* Métriques principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Patients actifs</p>
                <p className="text-3xl font-bold text-gray-900">{mockMetrics.activePatients}</p>
                <p className="text-sm text-green-600">+12 ce mois</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">RDV aujourd'hui</p>
                <p className="text-3xl font-bold text-gray-900">{mockMetrics.appointmentsToday}</p>
                <p className="text-sm text-blue-600">3 en cours</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Revenus ce mois</p>
                <p className="text-3xl font-bold text-gray-900">{mockMetrics.revenue.month.toLocaleString()}€</p>
                <p className="text-sm text-green-600">+8.2%</p>
              </div>
              <div className="h-12 w-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Satisfaction</p>
                <p className="text-3xl font-bold text-gray-900">{mockMetrics.satisfaction.rating}/5</p>
                <p className="text-sm text-yellow-600">{mockMetrics.satisfaction.reviews} avis</p>
              </div>
              <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </Card>
        </div>

        {/* Layout principal */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Colonne principale */}
          <div className="lg:col-span-2 space-y-8">
            {/* Rendez-vous du jour */}
            <Card className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  Rendez-vous d'aujourd'hui
                </h3>
                <Button variant="outline" size="sm">
                  <Calendar className="h-4 w-4 mr-2" />
                  Voir le planning
                </Button>
              </div>

              <div className="space-y-4">
                {mockAppointments.map((appointment) => (
                  <div
                    key={appointment.id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="text-lg font-semibold text-gray-900">
                        {appointment.time}
                      </div>
                      <div className="flex items-center space-x-2">
                        {appointment.type === 'video' && <Video className="h-4 w-4 text-blue-600" />}
                        {appointment.type === 'phone' && <Phone className="h-4 w-4 text-green-600" />}
                        {appointment.type === 'in-person' && <Users className="h-4 w-4 text-purple-600" />}
                        {appointment.type === 'chat' && <MessageCircle className="h-4 w-4 text-orange-600" />}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{appointment.patientName}</p>
                        <p className="text-sm text-gray-600">{appointment.duration} min</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Badge className={getPriorityColor(appointment.priority)}>
                        {appointment.priority}
                      </Badge>
                      <Badge className={getStatusColor(appointment.status)}>
                        {appointment.status}
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Insights IA */}
            <Card className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  Insights IA
                </h3>
                <div className="flex items-center space-x-2">
                  <Zap className="h-5 w-5 text-yellow-500" />
                  <span className="text-sm text-gray-600">Alimenté par l'IA</span>
                </div>
              </div>

              <div className="space-y-4">
                {mockAIInsights.map((insight) => (
                  <div
                    key={insight.id}
                    className={`p-4 rounded-lg border-l-4 ${
                      insight.priority === 'high' ? 'border-red-500 bg-red-50' :
                      insight.priority === 'medium' ? 'border-orange-500 bg-orange-50' :
                      'border-blue-500 bg-blue-50'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          {insight.type === 'risk' && <AlertTriangle className="h-4 w-4 text-red-600" />}
                          {insight.type === 'treatment' && <Pill className="h-4 w-4 text-blue-600" />}
                          {insight.type === 'efficiency' && <TrendingUp className="h-4 w-4 text-green-600" />}
                          <h4 className="font-semibold text-gray-900">{insight.title}</h4>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{insight.description}</p>
                        <div className="flex items-center space-x-4">
                          <span className="text-xs text-gray-500">
                            Confiance: {insight.confidence}%
                          </span>
                          {insight.actionRequired && (
                            <Badge className="text-xs bg-red-100 text-red-800">
                              Action requise
                            </Badge>
                          )}
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Performance Analytics */}
            <Card className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                Analyse des performances
              </h3>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-green-600">{mockMetrics.performance.onTimeRate}%</p>
                  <p className="text-sm text-gray-600">Ponctualité</p>
                </div>
                
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-blue-600">{mockMetrics.performance.responseTime}min</p>
                  <p className="text-sm text-gray-600">Temps de réponse</p>
                </div>
                
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <XCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-red-600">{mockMetrics.performance.cancellationRate}%</p>
                  <p className="text-sm text-gray-600">Annulations</p>
                </div>
                
                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                  <AlertTriangle className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-yellow-600">{mockMetrics.performance.noShowRate}%</p>
                  <p className="text-sm text-gray-600">Absences</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Colonne latérale */}
          <div className="space-y-6">
            {/* Patients à risque */}
            <Card className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Patients à surveiller
                </h3>
                <Button variant="ghost" size="sm">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                {mockPatients.filter(p => p.priority === 'high' || p.alerts.length > 0).map((patient) => (
                  <div key={patient.id} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{patient.name}</h4>
                      <div className="flex items-center space-x-2">
                        <span className={`text-sm font-medium ${getRiskColor(patient.riskScore)}`}>
                          {patient.riskScore}%
                        </span>
                        <Badge className={getPriorityColor(patient.priority)}>
                          {patient.priority}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{patient.condition}</p>
                    {patient.alerts.map((alert, index) => (
                      <div key={index} className="flex items-center space-x-1 text-xs text-orange-600">
                        <AlertTriangle className="h-3 w-3" />
                        <span>{alert}</span>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </Card>

            {/* Bien-être professionnel */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Votre bien-être
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Index de bien-être</span>
                  <span className="text-lg font-semibold text-green-600">
                    {mockMetrics.healthMetrics.wellnessIndex}/100
                  </span>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full" 
                    style={{ width: `${mockMetrics.healthMetrics.wellnessIndex}%` }}
                  ></div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Charge de travail</span>
                  <span className="text-lg font-semibold text-yellow-600">
                    {mockMetrics.healthMetrics.workloadScore}/100
                  </span>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-yellow-600 h-2 rounded-full" 
                    style={{ width: `${mockMetrics.healthMetrics.workloadScore}%` }}
                  ></div>
                </div>
                
                <div className={`p-3 rounded-lg ${
                  mockMetrics.healthMetrics.burnoutRisk === 'low' ? 'bg-green-50' :
                  mockMetrics.healthMetrics.burnoutRisk === 'medium' ? 'bg-yellow-50' :
                  'bg-red-50'
                }`}>
                  <div className="flex items-center space-x-2">
                    <Heart className={`h-4 w-4 ${
                      mockMetrics.healthMetrics.burnoutRisk === 'low' ? 'text-green-600' :
                      mockMetrics.healthMetrics.burnoutRisk === 'medium' ? 'text-yellow-600' :
                      'text-red-600'
                    }`} />
                    <span className="text-sm font-medium">
                      Risque de burnout: {mockMetrics.healthMetrics.burnoutRisk}
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            {/* Actions rapides */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Actions rapides
              </h3>
              
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter un patient
                </Button>
                
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="h-4 w-4 mr-2" />
                  Planifier RDV
                </Button>
                
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="h-4 w-4 mr-2" />
                  Rédiger note
                </Button>
                
                <Button variant="outline" className="w-full justify-start">
                  <Pill className="h-4 w-4 mr-2" />
                  Prescription
                </Button>
                
                <Button variant="outline" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Rapports
                </Button>
              </div>
            </Card>

            {/* Télémédecine quick access */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Télémédecine
              </h3>
              
              <div className="space-y-3">
                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  <Video className="h-4 w-4 mr-2" />
                  Démarrer consultation
                </Button>
                
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Consultations vidéo aujourd'hui</span>
                  <span className="font-medium">5/8</span>
                </div>
                
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Temps total en ligne</span>
                  <span className="font-medium">3h 45min</span>
                </div>
                
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Prochaine consultation</span>
                  <span className="font-medium text-blue-600">14:00</span>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
} 