'use client';

import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestSonnerPage() {
  const testSuccess = () => {
    toast.success('Success!', {
      description: 'This is a success notification from Son<PERSON>.',
    });
  };

  const testError = () => {
    toast.error('Error!', {
      description: 'This is an error notification from Sonner.',
    });
  };

  const testWarning = () => {
    toast.warning('Warning!', {
      description: 'This is a warning notification from Sonner.',
    });
  };

  const testInfo = () => {
    toast.info('Info!', {
      description: 'This is an info notification from Son<PERSON>.',
    });
  };

  const testCustom = () => {
    toast('Custom Toast', {
      description: 'This is a custom notification with custom styling.',
      action: {
        label: 'Action',
        onClick: () => toast.success('Action clicked!'),
      },
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Sonner Toast Notifications Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600 mb-6">
            Test the Sonner toast notification system by clicking the buttons below.
            Notifications will appear in the top-right corner.
          </p>
          
          <div className="grid grid-cols-2 gap-4">
            <Button onClick={testSuccess} variant="default" className="bg-green-600 hover:bg-green-700">
              Success Toast
            </Button>
            
            <Button onClick={testError} variant="destructive">
              Error Toast
            </Button>
            
            <Button onClick={testWarning} className="bg-yellow-600 hover:bg-yellow-700 text-white">
              Warning Toast
            </Button>
            
            <Button onClick={testInfo} className="bg-blue-600 hover:bg-blue-700 text-white">
              Info Toast
            </Button>
            
            <Button onClick={testCustom} variant="outline" className="col-span-2">
              Custom Toast with Action
            </Button>
          </div>
          
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold mb-2">Integration Status:</h3>
            <ul className="text-sm space-y-1">
              <li>✅ Sonner package installed</li>
              <li>✅ ToasterProvider component created</li>
              <li>✅ Client-side wrapper implemented</li>
              <li>✅ Custom styling applied</li>
              <li>✅ Next.js App Router compatible</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
