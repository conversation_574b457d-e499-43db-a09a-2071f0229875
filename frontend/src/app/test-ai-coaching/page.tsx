'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAICoach } from '@/hooks/useAICoach';
import { useMoodAnalytics } from '@/hooks/useMoodAnalytics';
import { useSmartNotifications } from '@/hooks/useSmartNotifications';
import { DashboardLayout } from '@/components/Layout/DashboardLayout';
import { 
  Brain, 
  BarChart3, 
  Bell, 
  Play, 
  CheckCircle, 
  AlertTriangle,
  TrendingUp,
  MessageCircle,
  Lightbulb,
  Heart,
  Zap,
  Shield,
  RefreshCw,
  TestTube,
  Activity,
  Target,
  Clock,
  Star,
  ArrowRight
} from 'lucide-react';

export default function TestAICoachingPage() {
  const [activeTest, setActiveTest] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<{ [key: string]: boolean }>({});

  // Hooks pour les fonctionnalités
  const {
    currentSession,
    sessions,
    stats: aiStats,
    isLoading: aiLoading,
    startSession,
    sendMessage,
    endSession
  } = useAICoach();

  const {
    analytics,
    isLoading: analyticsLoading,
    refreshData: refreshAnalytics,
    getWellnessScore
  } = useMoodAnalytics();

  const {
    notifications,
    rules,
    preferences,
    analytics: notifAnalytics,
    isLoading: notifLoading,
    createNotification,
    generateAINotification,
    markAsRead
  } = useSmartNotifications();

  // Tests automatisés
  const runTest = async (testName: string, testFunction: () => Promise<boolean>) => {
    setActiveTest(testName);
    try {
      const result = await testFunction();
      setTestResults(prev => ({ ...prev, [testName]: result }));
      console.log(`✅ Test ${testName}: ${result ? 'RÉUSSI' : 'ÉCHEC'}`);
    } catch (error) {
      console.error(`❌ Test ${testName}: ERREUR`, error);
      setTestResults(prev => ({ ...prev, [testName]: false }));
    } finally {
      setActiveTest(null);
    }
  };

  // Test 1: IA Coach - Création de session
  const testAICoachSession = async (): Promise<boolean> => {
    try {
      const session = await startSession('gestion_stress', 'Test de création de session IA');
      return session !== null && session.status === 'active';
    } catch (error) {
      return false;
    }
  };

  // Test 2: IA Coach - Envoi de message
  const testAICoachMessage = async (): Promise<boolean> => {
    try {
      if (!currentSession) {
        await startSession('test', 'Session de test pour message');
      }
      await sendMessage('Je me sens stressé aujourd\'hui', 4);
      return currentSession?.messages.length > 1;
    } catch (error) {
      return false;
    }
  };

  // Test 3: Analyse d'humeur - Chargement des données
  const testMoodAnalytics = async (): Promise<boolean> => {
    try {
      await refreshAnalytics();
      return analytics !== null && analytics.overview.totalEntries > 0;
    } catch (error) {
      return false;
    }
  };

  // Test 4: Analyse d'humeur - Score de bien-être
  const testWellnessScore = async (): Promise<boolean> => {
    try {
      const score = getWellnessScore();
      return score >= 0 && score <= 100;
    } catch (error) {
      return false;
    }
  };

  // Test 5: Notifications - Création manuelle
  const testCreateNotification = async (): Promise<boolean> => {
    try {
      const notification = await createNotification(
        'reminder',
        'Test de notification',
        'Ceci est un test de création de notification',
        { priority: 'medium' }
      );
      return notification !== null;
    } catch (error) {
      return false;
    }
  };

  // Test 6: Notifications - Génération IA
  const testAINotification = async (): Promise<boolean> => {
    try {
      const notification = await generateAINotification({
        currentMood: 3,
        stressLevel: 8,
        timeOfDay: 'afternoon'
      });
      return notification !== null && notification.aiGenerated === true;
    } catch (error) {
      return false;
    }
  };

  // Test 7: Intégration - Flux complet
  const testCompleteFlow = async (): Promise<boolean> => {
    try {
      // 1. Créer session IA
      const session = await startSession('test_integration', 'Test d\'intégration complète');
      if (!session) return false;

      // 2. Envoyer message avec humeur
      await sendMessage('Je teste l\'intégration complète', 7);
      
      // 3. Vérifier analytics
      await refreshAnalytics();
      
      // 4. Créer notification basée sur l'état
      await generateAINotification({
        currentMood: 7,
        stressLevel: 3,
        timeOfDay: 'afternoon'
      });

      // 5. Terminer session
      await endSession();

      return true;
    } catch (error) {
      return false;
    }
  };

  // Exécuter tous les tests
  const runAllTests = async () => {
    const tests = [
      { name: 'ai_coach_session', fn: testAICoachSession, description: 'Création session IA Coach' },
      { name: 'ai_coach_message', fn: testAICoachMessage, description: 'Envoi message IA Coach' },
      { name: 'mood_analytics', fn: testMoodAnalytics, description: 'Chargement analytics humeur' },
      { name: 'wellness_score', fn: testWellnessScore, description: 'Calcul score bien-être' },
      { name: 'create_notification', fn: testCreateNotification, description: 'Création notification' },
      { name: 'ai_notification', fn: testAINotification, description: 'Génération notification IA' },
      { name: 'complete_flow', fn: testCompleteFlow, description: 'Flux d\'intégration complet' }
    ];

    for (const test of tests) {
      await runTest(test.name, test.fn);
      // Délai entre les tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  // Calculer le score global de réussite
  const getSuccessRate = () => {
    const total = Object.keys(testResults).length;
    const passed = Object.values(testResults).filter(Boolean).length;
    return total > 0 ? Math.round((passed / total) * 100) : 0;
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* En-tête */}
        <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-0">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                  <TestTube className="w-8 h-8 text-purple-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Test Étape 3 : IA et Coaching</h1>
                  <p className="text-gray-600">Validation complète des fonctionnalités avancées</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="flex items-center space-x-2 mb-2">
                  <Badge className={`text-2xl font-bold px-4 py-2 border-0 ${
                    getSuccessRate() >= 80 ? 'bg-green-100 text-green-700' :
                    getSuccessRate() >= 60 ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  }`}>
                    {getSuccessRate()}%
                  </Badge>
                </div>
                <div className="text-sm text-gray-600">
                  {Object.values(testResults).filter(Boolean).length} / {Object.keys(testResults).length} tests réussis
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions principales */}
        <div className="flex space-x-4">
          <Button onClick={runAllTests} className="flex-1" disabled={activeTest !== null}>
            {activeTest ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Test en cours...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Exécuter tous les tests
              </>
            )}
          </Button>
          
          <Button variant="outline" onClick={() => setTestResults({})}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Réinitialiser
          </Button>
        </div>

        {/* État des fonctionnalités */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* IA Coach */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="w-5 h-5 text-purple-600" />
                <span>IA Coach</span>
                {(testResults.ai_coach_session && testResults.ai_coach_message) && (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {aiStats?.totalSessions || 0}
                  </div>
                  <div className="text-sm text-gray-600">Sessions</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {aiStats?.moodImprovementRate || 0}%
                  </div>
                  <div className="text-sm text-gray-600">Amélioration</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => runTest('ai_coach_session', testAICoachSession)}
                  disabled={activeTest === 'ai_coach_session'}
                >
                  {activeTest === 'ai_coach_session' ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : testResults.ai_coach_session ? (
                    <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                  ) : (
                    <Play className="w-4 h-4 mr-2" />
                  )}
                  Test session
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => runTest('ai_coach_message', testAICoachMessage)}
                  disabled={activeTest === 'ai_coach_message'}
                >
                  {activeTest === 'ai_coach_message' ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : testResults.ai_coach_message ? (
                    <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                  ) : (
                    <MessageCircle className="w-4 h-4 mr-2" />
                  )}
                  Test message
                </Button>
              </div>

              {currentSession && (
                <div className="p-3 bg-purple-50 rounded-lg">
                  <div className="text-sm font-medium text-purple-800">Session active</div>
                  <div className="text-xs text-purple-600">
                    {currentSession.theme} • {currentSession.messages.length} messages
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Analyse d'Humeur */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-blue-600" />
                <span>Analyse d'Humeur</span>
                {(testResults.mood_analytics && testResults.wellness_score) && (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {analytics?.overview.currentMood.toFixed(1) || 'N/A'}
                  </div>
                  <div className="text-sm text-gray-600">Humeur</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {getWellnessScore().toFixed(0) || 0}
                  </div>
                  <div className="text-sm text-gray-600">Score</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => runTest('mood_analytics', testMoodAnalytics)}
                  disabled={activeTest === 'mood_analytics'}
                >
                  {activeTest === 'mood_analytics' ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : testResults.mood_analytics ? (
                    <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                  ) : (
                    <BarChart3 className="w-4 h-4 mr-2" />
                  )}
                  Test analytics
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => runTest('wellness_score', testWellnessScore)}
                  disabled={activeTest === 'wellness_score'}
                >
                  {activeTest === 'wellness_score' ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : testResults.wellness_score ? (
                    <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                  ) : (
                    <Heart className="w-4 h-4 mr-2" />
                  )}
                  Test score
                </Button>
              </div>

              {analytics && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="text-sm font-medium text-blue-800">
                    {analytics.overview.totalEntries} entrées
                  </div>
                  <div className="text-xs text-blue-600">
                    {analytics.trends.length} tendances détectées
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notifications Intelligentes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="w-5 h-5 text-orange-600" />
                <span>Notifications IA</span>
                {(testResults.create_notification && testResults.ai_notification) && (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {notifications.length}
                  </div>
                  <div className="text-sm text-gray-600">Notifications</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {notifAnalytics?.readRate.toFixed(0) || 0}%
                  </div>
                  <div className="text-sm text-gray-600">Taux lecture</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => runTest('create_notification', testCreateNotification)}
                  disabled={activeTest === 'create_notification'}
                >
                  {activeTest === 'create_notification' ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : testResults.create_notification ? (
                    <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                  ) : (
                    <Bell className="w-4 h-4 mr-2" />
                  )}
                  Test création
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => runTest('ai_notification', testAINotification)}
                  disabled={activeTest === 'ai_notification'}
                >
                  {activeTest === 'ai_notification' ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : testResults.ai_notification ? (
                    <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                  ) : (
                    <Brain className="w-4 h-4 mr-2" />
                  )}
                  Test IA
                </Button>
              </div>

              {notifications.length > 0 && (
                <div className="p-3 bg-orange-50 rounded-lg">
                  <div className="text-sm font-medium text-orange-800">
                    {notifications.filter(n => !n.isRead).length} non lues
                  </div>
                  <div className="text-xs text-orange-600">
                    {notifications.filter(n => n.aiGenerated).length} générées par IA
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Matrice de fonctionnalités */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5" />
              <span>Matrice de Fonctionnalités - Étape 3</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                { 
                  name: 'IA Coach Interactif', 
                  status: testResults.ai_coach_session && testResults.ai_coach_message,
                  description: 'Chat intelligent avec analyse sentiment',
                  icon: Brain,
                  color: 'purple'
                },
                { 
                  name: 'Analyse d\'Humeur', 
                  status: testResults.mood_analytics && testResults.wellness_score,
                  description: 'Graphiques et tendances avancées',
                  icon: BarChart3,
                  color: 'blue'
                },
                { 
                  name: 'Notifications IA', 
                  status: testResults.create_notification && testResults.ai_notification,
                  description: 'Rappels personnalisés intelligents',
                  icon: Bell,
                  color: 'orange'
                },
                { 
                  name: 'Intégration Complète', 
                  status: testResults.complete_flow,
                  description: 'Flux de données synchronisé',
                  icon: Activity,
                  color: 'green'
                }
              ].map((feature, index) => (
                <div key={index} className={`p-4 rounded-lg border-2 ${
                  feature.status ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center space-x-3 mb-2">
                    <feature.icon className={`w-6 h-6 text-${feature.color}-600`} />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{feature.name}</div>
                    </div>
                    {feature.status ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <AlertTriangle className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                  <div className="text-sm text-gray-600">{feature.description}</div>
                  <Badge className={`mt-2 text-xs border-0 ${
                    feature.status ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {feature.status ? 'Opérationnel' : 'En attente'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Test d'intégration complète */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="w-5 h-5" />
              <span>Test d'Intégration Complète</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button
                onClick={() => runTest('complete_flow', testCompleteFlow)}
                disabled={activeTest === 'complete_flow'}
                className="w-full"
                variant={testResults.complete_flow ? 'default' : 'outline'}
              >
                {activeTest === 'complete_flow' ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Test d'intégration en cours...
                  </>
                ) : testResults.complete_flow ? (
                  <>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Test d'intégration réussi
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Lancer le test d'intégration complète
                  </>
                )}
              </Button>

              <div className="text-sm text-gray-600">
                Ce test valide le flux complet : création session IA → interaction → analyse humeur → notification → fin session
              </div>

              {testResults.complete_flow && (
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center space-x-2 text-green-800">
                    <CheckCircle className="w-5 h-5" />
                    <span className="font-medium">Intégration validée avec succès !</span>
                  </div>
                  <div className="text-sm text-green-700 mt-1">
                    Toutes les fonctionnalités de l'Étape 3 sont opérationnelles et intégrées.
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Résultats des tests */}
        {Object.keys(testResults).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-5 h-5" />
                <span>Résultats des Tests</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(testResults).map(([testName, result]) => (
                  <div key={testName} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      {result ? (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      ) : (
                        <AlertTriangle className="w-5 h-5 text-red-600" />
                      )}
                      <span className="font-medium">
                        {testName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </div>
                    <Badge className={`border-0 ${
                      result ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                    }`}>
                      {result ? 'RÉUSSI' : 'ÉCHEC'}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Prochaines étapes */}
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-0">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <ArrowRight className="w-6 h-6 text-green-600" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900">Prochaines Étapes</h3>
                <p className="text-gray-600">
                  Une fois tous les tests validés, l'Étape 3 sera complète et prête pour l'intégration Supabase.
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-600">{getSuccessRate()}%</div>
                <div className="text-sm text-gray-600">Complété</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
} 