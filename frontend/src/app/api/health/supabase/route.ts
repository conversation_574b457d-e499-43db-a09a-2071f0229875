import { NextResponse } from 'next/server';
import { createBrowserClient } from '@supabase/ssr';

export async function GET() {
  try {
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    // Test simple de connexion
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error && error.code !== '42P01') { // Ignorer l'erreur "table n'existe pas"
      return NextResponse.json({
        status: 'error',
        message: error.message,
        service: 'supabase'
      }, { status: 503 });
    }

    return NextResponse.json({
      status: 'healthy',
      service: 'supabase',
      timestamp: new Date().toISOString(),
      config: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing',
        auth: process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH === 'true',
        database: process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE === 'true'
      }
    });
  } catch (error) {
    return NextResponse.json({
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error',
      service: 'supabase'
    }, { status: 503 });
  }
}

export async function HEAD() {
  return GET();
} 