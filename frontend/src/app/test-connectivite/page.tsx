'use client';

import React, { useState, useEffect } from 'react';
import { createSupabaseClient, testSupabaseConnection, isSupabaseConfigured } from '@/lib/supabase/client';
import { getDatabaseManager } from '@/lib/database';
import { FEATURE_FLAGS } from '@/lib/config/feature-flags';

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'warning' | 'pending' | 'info';
  message: string;
  details?: any;
  timestamp?: string;
}

export default function TestConnectivite() {
  const [results, setResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [autoRun, setAutoRun] = useState(false);

  const addResult = (result: TestResult) => {
    const newResult = {
      ...result,
      timestamp: new Date().toLocaleTimeString()
    };
    setResults(prev => [...prev, newResult]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const runTests = async () => {
    setIsRunning(true);
    clearResults();

    addResult({
      name: 'Début des tests',
      status: 'info',
      message: 'Lancement des tests de connectivité Supabase...'
    });

    // Test 1: Configuration Supabase
    try {
      const isConfigured = isSupabaseConfigured();
      addResult({
        name: 'Configuration Supabase',
        status: isConfigured ? 'success' : 'warning',
        message: isConfigured ? 
          'Configuration Supabase détectée' : 
          'Configuration Supabase manquante, utilisation des valeurs par défaut',
        details: {
          hasUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
          hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        }
      });
    } catch (error) {
      addResult({
        name: 'Configuration Supabase',
        status: 'error',
        message: `Erreur configuration: ${error}`,
        details: error
      });
    }

    // Test 2: Feature Flags
    try {
      addResult({
        name: 'Feature Flags',
        status: 'info',
        message: 'Configuration des feature flags',
        details: {
          USE_SUPABASE_AUTH: FEATURE_FLAGS.USE_SUPABASE_AUTH,
          USE_SUPABASE_DATABASE: FEATURE_FLAGS.USE_SUPABASE_DATABASE,
          DUAL_DATABASE_MODE: FEATURE_FLAGS.DUAL_DATABASE_MODE,
          MIGRATE_USER_DATA: FEATURE_FLAGS.MIGRATE_USER_DATA
        }
      });
    } catch (error) {
      addResult({
        name: 'Feature Flags',
        status: 'error',
        message: `Erreur feature flags: ${error}`,
        details: error
      });
    }

    // Test 3: Client Supabase
    try {
      const client = createSupabaseClient();
      addResult({
        name: 'Client Supabase',
        status: 'success',
        message: 'Client Supabase créé avec succès',
        details: {
          type: typeof client,
          hasAuth: !!client.auth,
          hasFrom: !!client.from
        }
      });
    } catch (error) {
      addResult({
        name: 'Client Supabase',
        status: 'error',
        message: `Erreur création client: ${error}`,
        details: error
      });
    }

    // Test 4: Connectivité Supabase
    try {
      const connectionTest = await testSupabaseConnection();
      addResult({
        name: 'Connectivité Supabase',
        status: connectionTest.success ? 'success' : 'error',
        message: connectionTest.success ? 
          'Connexion Supabase réussie' : 
          `Erreur de connexion: ${connectionTest.error}`,
        details: connectionTest
      });
    } catch (error) {
      addResult({
        name: 'Connectivité Supabase',
        status: 'error',
        message: `Erreur test connexion: ${error}`,
        details: error
      });
    }

    // Test 5: Database Manager
    try {
      const dbManager = getDatabaseManager();
      addResult({
        name: 'Database Manager',
        status: 'success',
        message: 'Database Manager initialisé',
        details: {
          type: typeof dbManager,
          available: true
        }
      });

      // Test des adaptateurs
      try {
        const connectionTest = await dbManager.testConnection();
        addResult({
          name: 'Test Adaptateurs DB',
          status: (connectionTest.supabase.available || connectionTest.sqlite.available) ? 'success' : 'error',
          message: 'Test des adaptateurs de base de données',
          details: connectionTest
        });
      } catch (adapterError) {
        addResult({
          name: 'Test Adaptateurs DB',
          status: 'error',
          message: `Erreur adaptateurs: ${adapterError}`,
          details: adapterError
        });
      }
    } catch (error) {
      addResult({
        name: 'Database Manager',
        status: 'error',
        message: `Erreur database manager: ${error}`,
        details: error
      });
    }

    addResult({
      name: 'Fin des tests',
      status: 'info',
      message: 'Tests de connectivité terminés'
    });

    setIsRunning(false);
  };

  useEffect(() => {
    if (autoRun) {
      runTests();
    }
  }, [autoRun]);

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '#22c55e';
      case 'error': return '#ef4444';
      case 'warning': return '#f59e0b';
      case 'info': return '#3b82f6';
      case 'pending': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      case 'pending': return '⏳';
      default: return '⚫';
    }
  };

  return (
    <div style={{ 
      fontFamily: 'Arial, sans-serif', 
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto'
    }}>
      <h1 style={{ 
        color: '#2563eb', 
        marginBottom: '20px',
        borderBottom: '2px solid #e5e7eb',
        paddingBottom: '10px'
      }}>
        🔍 Test de Connectivité Supabase
      </h1>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={runTests}
          disabled={isRunning}
          style={{
            backgroundColor: isRunning ? '#6b7280' : '#2563eb',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '6px',
            cursor: isRunning ? 'not-allowed' : 'pointer',
            marginRight: '10px',
            fontSize: '16px'
          }}
        >
          {isRunning ? '🔄 Tests en cours...' : '🚀 Lancer les tests'}
        </button>

        <button
          onClick={clearResults}
          style={{
            backgroundColor: '#6b7280',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          🧹 Vider les résultats
        </button>

        <label style={{ marginLeft: '20px', fontSize: '14px' }}>
          <input
            type="checkbox"
            checked={autoRun}
            onChange={(e) => setAutoRun(e.target.checked)}
            style={{ marginRight: '8px' }}
          />
          Lancer automatiquement au chargement
        </label>
      </div>

      {results.length > 0 && (
        <div style={{
          backgroundColor: '#f8fafc',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '20px'
        }}>
          <h2 style={{ marginTop: 0, color: '#1e293b' }}>Résultats des tests:</h2>
          
          {results.map((result, index) => (
            <div key={index} style={{
              backgroundColor: 'white',
              border: `2px solid ${getStatusColor(result.status)}`,
              borderRadius: '6px',
              padding: '15px',
              marginBottom: '10px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '8px'
              }}>
                <span style={{ marginRight: '8px', fontSize: '18px' }}>
                  {getStatusIcon(result.status)}
                </span>
                <strong style={{ color: '#1e293b' }}>{result.name}</strong>
                {result.timestamp && (
                  <span style={{ 
                    marginLeft: 'auto', 
                    fontSize: '12px', 
                    color: '#6b7280' 
                  }}>
                    {result.timestamp}
                  </span>
                )}
              </div>
              
              <p style={{ 
                margin: '8px 0', 
                color: '#374151',
                fontSize: '14px'
              }}>
                {result.message}
              </p>
              
              {result.details && (
                <details style={{ marginTop: '10px' }}>
                  <summary style={{ 
                    cursor: 'pointer', 
                    color: '#6b7280',
                    fontSize: '12px'
                  }}>
                    Voir les détails
                  </summary>
                  <pre style={{
                    backgroundColor: '#f1f5f9',
                    padding: '10px',
                    borderRadius: '4px',
                    fontSize: '11px',
                    overflow: 'auto',
                    marginTop: '8px',
                    border: '1px solid #e2e8f0'
                  }}>
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>
      )}

      {results.length === 0 && (
        <div style={{
          backgroundColor: '#f8fafc',
          border: '2px dashed #cbd5e1',
          borderRadius: '8px',
          padding: '40px',
          textAlign: 'center',
          color: '#64748b'
        }}>
          <p style={{ margin: 0, fontSize: '16px' }}>
            Aucun test lancé. Cliquez sur "Lancer les tests" pour commencer.
          </p>
        </div>
      )}

      <div style={{
        marginTop: '30px',
        padding: '15px',
        backgroundColor: '#fef3c7',
        border: '1px solid #f59e0b',
        borderRadius: '6px',
        fontSize: '14px'
      }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#92400e' }}>
          💡 Informations importantes:
        </h3>
        <ul style={{ margin: 0, paddingLeft: '20px', color: '#92400e' }}>
          <li>Si les tests échouent, vérifiez votre fichier .env.local</li>
          <li>Les clés API Supabase peuvent expirer et nécessiter une régénération</li>
          <li>Le mode dual database permet d'utiliser SQLite en fallback</li>
          <li>Consultez la console du navigateur pour plus de détails sur les erreurs</li>
        </ul>
      </div>
    </div>
  );
} 