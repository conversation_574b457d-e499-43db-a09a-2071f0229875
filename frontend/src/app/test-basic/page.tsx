export default function TestBasic() {
  const envCheck = {
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || 'NON CONFIGURÉE',
    hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    dualMode: process.env.NEXT_PUBLIC_DUAL_DATABASE_MODE || 'false',
    nodeEnv: process.env.NODE_ENV || 'non défini',
    apiUrl: process.env.NEXT_PUBLIC_API_URL || 'NON CONFIGURÉE'
  };

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace',
      maxWidth: '900px',
      margin: '0 auto',
      backgroundColor: '#f8fafc'
    }}>
      <h1 style={{ color: '#1e40af', marginBottom: '20px' }}>
        🧪 Test Basique - MindFlow Pro
      </h1>
      
      <div style={{ 
        backgroundColor: '#dcfce7',
        border: '2px solid #16a34a',
        borderRadius: '8px',
        padding: '15px',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#15803d', margin: '0 0 10px 0' }}>
          ✅ Page fonctionnelle !
        </h2>
        <p style={{ margin: '0' }}>
          Cette page se charge correctement, ce qui indique que Next.js fonctionne.
        </p>
      </div>
      
      <div style={{ 
        backgroundColor: '#fff',
        border: '1px solid #d1d5db',
        borderRadius: '8px',
        padding: '15px',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#374151', marginTop: '0' }}>
          🔍 Variables d'environnement détectées :
        </h2>
        <ul style={{ listStyle: 'none', padding: '0' }}>
          <li style={{ marginBottom: '8px' }}>
            <strong>SUPABASE_URL:</strong>{' '}
            <span style={{ 
              color: envCheck.supabaseUrl.includes('supabase.co') ? '#16a34a' : '#dc2626',
              fontWeight: 'bold'
            }}>
              {envCheck.supabaseUrl}
            </span>
          </li>
          <li style={{ marginBottom: '8px' }}>
            <strong>SUPABASE_KEY:</strong>{' '}
            <span style={{ 
              color: envCheck.hasSupabaseKey ? '#16a34a' : '#dc2626',
              fontWeight: 'bold'
            }}>
              {envCheck.hasSupabaseKey ? '✅ Configurée' : '❌ Manquante'}
            </span>
          </li>
          <li style={{ marginBottom: '8px' }}>
            <strong>DUAL_MODE:</strong> <span style={{ color: '#374151' }}>{envCheck.dualMode}</span>
          </li>
          <li style={{ marginBottom: '8px' }}>
            <strong>NODE_ENV:</strong> <span style={{ color: '#374151' }}>{envCheck.nodeEnv}</span>
          </li>
          <li style={{ marginBottom: '8px' }}>
            <strong>API_URL:</strong> <span style={{ color: '#374151' }}>{envCheck.apiUrl}</span>
          </li>
        </ul>
      </div>
      
      <div style={{ 
        backgroundColor: '#fef3c7',
        border: '1px solid #f59e0b',
        borderRadius: '8px',
        padding: '15px',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#92400e', marginTop: '0' }}>📝 Instructions :</h2>
        <ol>
          <li>Si cette page s'affiche → Next.js fonctionne ✅</li>
          <li>Vérifiez les variables ci-dessus</li>
          <li>Si des variables sont manquantes → éditez <code>frontend/.env.local</code></li>
          <li>Redémarrez le serveur après modification</li>
        </ol>
      </div>

      <div style={{ 
        backgroundColor: '#e0e7ff',
        border: '1px solid #6366f1',
        borderRadius: '8px',
        padding: '15px',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#4338ca', marginTop: '0' }}>🔗 Navigation de test :</h2>
        <ul style={{ listStyle: 'none', padding: '0' }}>
          <li style={{ marginBottom: '8px' }}>
            <a href="/" style={{ color: '#4338ca', textDecoration: 'underline' }}>
              🏠 Accueil
            </a>
          </li>
          <li style={{ marginBottom: '8px' }}>
            <a href="/test-fix" style={{ color: '#4338ca', textDecoration: 'underline' }}>
              🔧 Test Corrections
            </a>
          </li>
          <li style={{ marginBottom: '8px' }}>
            <a href="/dashboard" style={{ color: '#4338ca', textDecoration: 'underline' }}>
              📊 Dashboard Principal
            </a>
          </li>
          <li style={{ marginBottom: '8px' }}>
            <a href="/diagnostic-env" style={{ color: '#4338ca', textDecoration: 'underline' }}>
              🔍 Diagnostic Avancé
            </a>
          </li>
        </ul>
      </div>
      
      <div style={{ 
        fontSize: '12px', 
        color: '#6b7280',
        borderTop: '1px solid #e5e7eb',
        paddingTop: '15px',
        textAlign: 'center'
      }}>
        <p><strong>URL actuelle:</strong> http://localhost:3000/test-basic</p>
        <p><strong>Timestamp:</strong> {new Date().toLocaleString('fr-FR')}</p>
        <p><strong>Statut:</strong> Fonctionnel ✅</p>
      </div>
    </div>
  );
} 