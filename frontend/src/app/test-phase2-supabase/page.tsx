'use client';

import { useState, useEffect } from 'react';

export default function TestPhase2Supabase() {
  const [status, setStatus] = useState('Loading...');

  useEffect(() => {
    const checkPhase2 = () => {
      const authEnabled = process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH === 'true';
      const dbDisabled = process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE !== 'true';
      const dualMode = process.env.NEXT_PUBLIC_DUAL_DATABASE_MODE === 'true';
      
      if (authEnabled && dbDisabled && dualMode) {
        setStatus('✅ Phase 2 ACTIVE - Authentification Supabase + Base SQLite');
      } else {
        setStatus('❌ Phase 2 NON ACTIVE - Configuration requise');
      }
    };

    checkPhase2();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-center text-gray-900 mb-6">
          🚀 Test Phase 2 - Authentification Supabase
        </h1>
        
        <div className="text-center mb-8">
          <div className="text-lg mb-4">{status}</div>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Configuration actuelle :</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Auth Supabase: {process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH || 'false'}</li>
              <li>• DB Supabase: {process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE || 'false'}</li>
              <li>• Mode Dual: {process.env.NEXT_PUBLIC_DUAL_DATABASE_MODE || 'false'}</li>
            </ul>
          </div>

          <div className="text-center space-y-4">
            <a 
              href="/auth/login" 
              className="inline-block px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mr-4"
            >
              🔑 Tester Connexion
            </a>
            <a 
              href="/auth/register" 
              className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              📝 Tester Inscription
            </a>
          </div>
        </div>
      </div>
    </div>
  );
} 