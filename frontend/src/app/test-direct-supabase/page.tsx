'use client';

import { useState, useEffect } from 'react';

export default function TestDirectSupabase() {
  const [status, setStatus] = useState<any>({
    loading: false,
    message: '',
    results: {}
  });

  // Configuration Supabase
  const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // Test 1: Vérifier la configuration
  const checkConfig = () => {
    const config = {
      url: SUPABASE_URL ? '✅ Configurée' : '❌ Manquante',
      key: SUPABASE_ANON_KEY ? '✅ Configurée' : '❌ Manquante',
      auth: process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH === 'true' ? '✅ Activée' : '❌ Désactivée',
      db: process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE === 'true' ? '✅ Activée' : '❌ Désactivée',
    };
    
    setStatus({
      loading: false,
      message: 'Configuration vérifiée',
      results: { config }
    });
  };

  // Test 2: Test API direct
  const testAPI = async () => {
    setStatus({ ...status, loading: true, message: 'Test API en cours...' });
    
    try {
      const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        }
      });
      
      const result = {
        status: response.status,
        ok: response.ok,
        statusText: response.statusText
      };
      
      setStatus({
        loading: false,
        message: response.ok ? '✅ API Supabase accessible' : '❌ Erreur API',
        results: { ...status.results, api: result }
      });
    } catch (error) {
      setStatus({
        loading: false,
        message: '❌ Erreur de connexion',
        results: { ...status.results, api: { error: error instanceof Error ? error.message : String(error) } }
      });
    }
  };

  // Test 3: Créer un utilisateur test
  const createTestUser = async () => {
    setStatus({ ...status, loading: true, message: 'Création utilisateur test...' });
    
    const email = `test${Date.now()}@mindflow.pro`;
    const password = 'TestPassword123!';
    
    try {
      const response = await fetch(`${SUPABASE_URL}/auth/v1/signup`, {
        method: 'POST',
        headers: {
          'apikey': SUPABASE_ANON_KEY || '',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });
      
      const data = await response.json();
      
      setStatus({
        loading: false,
        message: response.ok ? `✅ Utilisateur créé: ${email}` : `❌ Erreur: ${data.error || data.msg}`,
        results: { ...status.results, user: { email, created: response.ok } }
      });
    } catch (error) {
      setStatus({
        loading: false,
        message: '❌ Erreur création utilisateur',
        results: { ...status.results, user: { error: error instanceof Error ? error.message : String(error) } }
      });
    }
  };

  // Test automatique au chargement
  useEffect(() => {
    checkConfig();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-800 mb-8 text-center">
          🚀 Test Direct Supabase
        </h1>

        {/* Status Message */}
        <div className={`mb-6 p-4 rounded-lg text-center font-medium ${
          status.message.includes('✅') ? 'bg-green-100 text-green-800 border border-green-300' :
          status.message.includes('❌') ? 'bg-red-100 text-red-800 border border-red-300' :
          'bg-blue-100 text-blue-800 border border-blue-300'
        }`}>
          {status.loading ? '⏳ ' : ''}{status.message || 'Prêt pour les tests'}
        </div>

        {/* Boutons de test */}
        <div className="space-y-4">
          <button
            onClick={checkConfig}
            disabled={status.loading}
            className="w-full py-4 px-6 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 font-semibold text-lg shadow-lg transform hover:scale-105 transition"
          >
            🔧 Vérifier Configuration
          </button>

          <button
            onClick={testAPI}
            disabled={status.loading}
            className="w-full py-4 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-semibold text-lg shadow-lg transform hover:scale-105 transition"
          >
            🔗 Tester API Supabase
          </button>

          <button
            onClick={createTestUser}
            disabled={status.loading}
            className="w-full py-4 px-6 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 font-semibold text-lg shadow-lg transform hover:scale-105 transition"
          >
            👤 Créer Utilisateur Test
          </button>
        </div>

        {/* Résultats détaillés */}
        {status.results.config && (
          <div className="mt-8 bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-xl font-bold mb-4">📋 Configuration</h2>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>URL Supabase:</span>
                <span>{status.results.config.url}</span>
              </div>
              <div className="flex justify-between">
                <span>Clé API:</span>
                <span>{status.results.config.key}</span>
              </div>
              <div className="flex justify-between">
                <span>Auth Supabase:</span>
                <span>{status.results.config.auth}</span>
              </div>
              <div className="flex justify-between">
                <span>DB Supabase:</span>
                <span>{status.results.config.db}</span>
              </div>
            </div>
          </div>
        )}

        {status.results.api && (
          <div className="mt-4 bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-xl font-bold mb-4">🌐 Test API</h2>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
              {JSON.stringify(status.results.api, null, 2)}
            </pre>
          </div>
        )}

        {status.results.user && (
          <div className="mt-4 bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-xl font-bold mb-4">👤 Utilisateur Test</h2>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
              {JSON.stringify(status.results.user, null, 2)}
            </pre>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-amber-50 border border-amber-200 p-6 rounded-lg">
          <h3 className="font-bold text-amber-800 mb-2">📝 Instructions:</h3>
          <ol className="list-decimal list-inside space-y-2 text-amber-700">
            <li>Cliquez sur "Vérifier Configuration" pour voir l'état</li>
            <li>Cliquez sur "Tester API Supabase" pour vérifier la connexion</li>
            <li>Cliquez sur "Créer Utilisateur Test" pour tester l'inscription</li>
            <li>Si tout est vert (✅), Supabase est fonctionnel !</li>
          </ol>
        </div>

        {/* Liens utiles */}
        <div className="mt-6 text-center space-y-2">
          <a href="/test-phase4-supabase" className="text-blue-600 hover:underline block">
            → Test Phase 4 Original
          </a>
          <a href="/test-supabase-schema" className="text-blue-600 hover:underline block">
            → Voir le Schéma SQL
          </a>
          <a href="https://app.supabase.com" target="_blank" className="text-blue-600 hover:underline block">
            → Dashboard Supabase ↗
          </a>
        </div>
      </div>
    </div>
  );
} 