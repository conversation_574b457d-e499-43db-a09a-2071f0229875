'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthStore } from '@/stores/authStore';
import { apiService } from '@/services/api';

interface WellnessModule {
  id: string;
  title: string;
  description: string;
  moduleType: string;
  difficultyLevel: string;
  estimatedDurationMinutes: number;
  content: {
    sections: Array<{
      id: string;
      title: string;
      content: string;
      type: 'text' | 'video' | 'audio' | 'exercise' | 'quiz';
      duration?: number;
      resources?: string[];
    }>;
  };
  learningObjectives: string[];
  prerequisites?: string[];
  tags: string[];
  resources?: Array<{
    title: string;
    url: string;
    type: 'article' | 'video' | 'podcast' | 'book' | 'app';
  }>;
  authorNotes?: string;
  requiresSupervision: boolean;
  averageRating: number;
  ratingCount: number;
  completionCount: number;
}

interface UserProgress {
  id: string;
  moduleId: string;
  status: string;
  progressPercentage: number;
  completedSections: string[];
  sectionProgress: Record<string, {
    completed: boolean;
    timeSpent: number;
    lastAccessed: string;
    notes?: string;
  }>;
  totalTimeSpentMinutes: number;
  startedAt?: string;
  completedAt?: string;
  lastAccessedAt?: string;
  userRating?: number;
  userFeedback?: string;
}

const MODULE_TYPE_LABELS: Record<string, string> = {
  mindfulness: 'Mindfulness',
  anxiety_relief: 'Anxiety Relief',
  depression_support: 'Depression Support',
  stress_management: 'Stress Management',
  sleep_hygiene: 'Sleep Hygiene',
  cognitive_behavioral: 'Cognitive Behavioral',
  emotional_regulation: 'Emotional Regulation',
  relationship_skills: 'Relationship Skills',
  self_care: 'Self Care',
  trauma_recovery: 'Trauma Recovery',
};

const DIFFICULTY_LABELS: Record<string, { label: string; color: string }> = {
  beginner: { label: 'Beginner', color: 'bg-green-100 text-green-800' },
  intermediate: { label: 'Intermediate', color: 'bg-yellow-100 text-yellow-800' },
  advanced: { label: 'Advanced', color: 'bg-red-100 text-red-800' },
};

const SECTION_TYPE_ICONS: Record<string, string> = {
  text: '📖',
  video: '🎥',
  audio: '🎧',
  exercise: '🧘‍♀️',
  quiz: '❓',
};

export default function WellnessModuleDetailPage() {
  const { user, isAuthenticated } = useAuthStore();
  const router = useRouter();
  const params = useParams();
  const moduleId = params.id as string;
  
  const [module, setModule] = useState<WellnessModule | null>(null);
  const [progress, setProgress] = useState<UserProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [sectionStartTime, setSectionStartTime] = useState<Date | null>(null);
  const [showRating, setShowRating] = useState(false);
  const [rating, setRating] = useState(0);
  const [feedback, setFeedback] = useState('');
  const [isSubmittingRating, setIsSubmittingRating] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  // Load module and progress data
  useEffect(() => {
    const loadData = async () => {
      if (!isAuthenticated || !moduleId) return;

      try {
        setIsLoading(true);
        setError(null);

        // Load module details and user progress
        const [moduleResponse, progressResponse] = await Promise.allSettled([
          apiService.getWellnessModule(moduleId),
          apiService.getUserModuleProgress(),
        ]);

        if (moduleResponse.status === 'fulfilled' && moduleResponse.value.data.success) {
          setModule(moduleResponse.value.data.data.module);
        } else {
          throw new Error('Failed to load module details');
        }

        if (progressResponse.status === 'fulfilled' && progressResponse.value.data.success) {
          const userProgressData = progressResponse.value.data.data.progress;
          const moduleProgress = userProgressData.find((p: UserProgress) => p.moduleId === moduleId);
          if (moduleProgress) {
            setProgress(moduleProgress);
            // Set current section based on progress
            if (moduleProgress.completedSections.length > 0) {
              const lastCompletedIndex = moduleResponse.value.data.data.module.content.sections.findIndex(
                (section: { id: string }) => section.id === moduleProgress.completedSections[moduleProgress.completedSections.length - 1]
              );
              setCurrentSectionIndex(Math.min(lastCompletedIndex + 1, moduleResponse.value.data.data.module.content.sections.length - 1));
            }
          }
        }
      } catch (err: unknown) {
        const errorMessage = (err as { message?: string })?.message || 'Failed to load module';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [isAuthenticated, moduleId]);

  // Track section start time
  useEffect(() => {
    setSectionStartTime(new Date());
  }, [currentSectionIndex]);

  const handleStartModule = async () => {
    try {
      const response = await apiService.startWellnessModule(moduleId);
      if (response.data.success) {
        setProgress(response.data.data.progress);
        setSectionStartTime(new Date());
      }
    } catch (err: unknown) {
      const errorMessage = (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to start module';
      setError(errorMessage);
    }
  };

  const handleCompleteSection = async (sectionId: string) => {
    if (!sectionStartTime) return;

    try {
      const timeSpent = Math.round((new Date().getTime() - sectionStartTime.getTime()) / 60000); // Convert to minutes
      
      const response = await apiService.updateModuleProgress(moduleId, sectionId, {
        timeSpentMinutes: timeSpent,
      });

      if (response.data.success) {
        setProgress(response.data.data.progress);
        
        // Move to next section if available
        if (module && currentSectionIndex < module.content.sections.length - 1) {
          setCurrentSectionIndex(prev => prev + 1);
        } else if (response.data.data.progress.status === 'completed') {
          // Module completed, show rating option
          setShowRating(true);
        }
      }
    } catch (err: unknown) {
      const errorMessage = (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to complete section';
      setError(errorMessage);
    }
  };

  const handleRateModule = async () => {
    if (rating === 0) return;

    setIsSubmittingRating(true);
    try {
      await apiService.rateWellnessModule(moduleId, rating, feedback.trim() || undefined);

      setShowRating(false);
      setRating(0);
      setFeedback('');
      
      // Reload module to get updated rating
      const response = await apiService.getWellnessModule(moduleId);
      if (response.data.success) {
        setModule(response.data.data.module);
      }
    } catch (err: unknown) {
      const errorMessage = (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to rate module';
      setError(errorMessage);
    } finally {
      setIsSubmittingRating(false);
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes} min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  const renderStars = (rating: number, interactive = false, onStarClick?: (star: number) => void) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <button
          key={i}
          onClick={() => interactive && onStarClick && onStarClick(i)}
          className={`text-2xl ${interactive ? 'cursor-pointer hover:scale-110 transition-transform' : ''} ${
            i <= rating ? 'text-yellow-400' : 'text-gray-300'
          }`}
          disabled={!interactive}
        >
          ★
        </button>
      );
    }
    return stars;
  };

  if (!isAuthenticated || !user) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading module...</p>
        </div>
      </div>
    );
  }

  if (error || !module) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || 'Module not found'}</p>
          <Button onClick={() => router.push('/wellness')}>
            Back to Wellness Modules
          </Button>
        </div>
      </div>
    );
  }

  const currentSection = module.content.sections[currentSectionIndex];
  const difficultyInfo = DIFFICULTY_LABELS[module.difficultyLevel];
  const isSectionCompleted = progress?.completedSections.includes(currentSection.id) || false;
  const isModuleCompleted = progress?.status === 'completed';

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => router.push('/wellness')}
                className="text-gray-600 hover:text-gray-900"
              >
                ← Back to Modules
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">{module.title}</h1>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-xs px-2 py-1 rounded-full ${difficultyInfo.color}`}>
                {difficultyInfo.label}
              </span>
              <span className="text-sm text-gray-600">
                {formatDuration(module.estimatedDurationMinutes)}
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Module Overview */}
        {!progress && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>{module.title}</span>
                {module.requiresSupervision && (
                  <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                    👨‍⚕️ Supervised
                  </span>
                )}
              </CardTitle>
              <CardDescription>
                {MODULE_TYPE_LABELS[module.moduleType]} • {formatDuration(module.estimatedDurationMinutes)}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-6">{module.description}</p>

              {/* Learning Objectives */}
              {module.learningObjectives && module.learningObjectives.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">What you&apos;ll learn:</h3>
                  <ul className="space-y-2">
                    {module.learningObjectives.map((objective, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-green-500 mr-2 mt-1">✓</span>
                        <span className="text-gray-700">{objective}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Prerequisites */}
              {module.prerequisites && module.prerequisites.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Prerequisites:</h3>
                  <ul className="space-y-2">
                    {module.prerequisites.map((prerequisite, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">•</span>
                        <span className="text-gray-700">{prerequisite}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Module Sections Preview */}
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-3">Module Content:</h3>
                <div className="space-y-2">
                  {module.content.sections.map((section) => (
                    <div key={section.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <span className="text-2xl">{SECTION_TYPE_ICONS[section.type]}</span>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{section.title}</div>
                        <div className="text-sm text-gray-600">
                          {section.type.charAt(0).toUpperCase() + section.type.slice(1)}
                          {section.duration && ` • ${section.duration} min`}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Start Button */}
              <Button onClick={handleStartModule} size="lg" className="w-full">
                Start Module
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Module Progress */}
        {progress && (
          <div className="space-y-6">
            {/* Progress Overview */}
            <Card>
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg font-medium text-gray-900">Your Progress</h2>
                  <span className="text-sm text-gray-600">
                    {progress.progressPercentage}% Complete
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                  <div
                    className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${progress.progressPercentage}%` }}
                  />
                </div>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">
                      {progress.completedSections.length}
                    </div>
                    <div className="text-sm text-gray-600">Sections Complete</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {formatDuration(progress.totalTimeSpentMinutes)}
                    </div>
                    <div className="text-sm text-gray-600">Time Spent</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600">
                      {module.content.sections.length}
                    </div>
                    <div className="text-sm text-gray-600">Total Sections</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Current Section */}
            {!isModuleCompleted && (
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center space-x-2">
                      <span className="text-2xl">{SECTION_TYPE_ICONS[currentSection.type]}</span>
                      <span>{currentSection.title}</span>
                    </CardTitle>
                    <div className="text-sm text-gray-600">
                      Section {currentSectionIndex + 1} of {module.content.sections.length}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="prose max-w-none mb-6">
                    <div className="whitespace-pre-wrap text-gray-700">
                      {currentSection.content}
                    </div>
                  </div>

                  {/* Section Resources */}
                  {currentSection.resources && currentSection.resources.length > 0 && (
                    <div className="mb-6">
                      <h4 className="font-medium text-gray-900 mb-2">Additional Resources:</h4>
                      <ul className="space-y-1">
                        {currentSection.resources.map((resource, index) => (
                          <li key={index} className="text-blue-600 hover:text-blue-800">
                            <a href={resource} target="_blank" rel="noopener noreferrer">
                              {resource}
                            </a>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Section Navigation */}
                  <div className="flex justify-between items-center">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentSectionIndex(prev => Math.max(0, prev - 1))}
                      disabled={currentSectionIndex === 0}
                    >
                      Previous Section
                    </Button>

                    {!isSectionCompleted ? (
                      <Button
                        onClick={() => handleCompleteSection(currentSection.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Complete Section
                      </Button>
                    ) : (
                      <Button
                        onClick={() => setCurrentSectionIndex(prev => Math.min(module.content.sections.length - 1, prev + 1))}
                        disabled={currentSectionIndex === module.content.sections.length - 1}
                      >
                        Next Section
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Module Completed */}
            {isModuleCompleted && (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="text-6xl mb-4">🎉</div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    Congratulations!
                  </h2>
                  <p className="text-gray-600 mb-6">
                    You&apos;ve successfully completed the {module.title} module.
                  </p>
                  
                  {!progress.userRating && (
                    <Button
                      onClick={() => setShowRating(true)}
                      className="mb-4"
                    >
                      Rate This Module
                    </Button>
                  )}

                  <div className="flex justify-center space-x-4">
                    <Button
                      variant="outline"
                      onClick={() => router.push('/wellness')}
                    >
                      Browse More Modules
                    </Button>
                    <Button
                      onClick={() => setCurrentSectionIndex(0)}
                    >
                      Review Content
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Rating Modal */}
        {showRating && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md mx-4">
              <CardHeader>
                <CardTitle>Rate This Module</CardTitle>
                <CardDescription>
                  Help others by sharing your experience with this module
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Your Rating
                    </label>
                    <div className="flex justify-center space-x-1">
                      {renderStars(rating, true, setRating)}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Feedback (Optional)
                    </label>
                    <textarea
                      value={feedback}
                      onChange={(e) => setFeedback(e.target.value)}
                      rows={3}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Share your thoughts about this module..."
                    />
                  </div>

                  <div className="flex space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowRating(false);
                        setRating(0);
                        setFeedback('');
                      }}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleRateModule}
                      disabled={rating === 0 || isSubmittingRating}
                      className="flex-1"
                    >
                      {isSubmittingRating ? 'Submitting...' : 'Submit Rating'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Professional Disclaimer */}
        <div className="mt-8 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <span className="text-red-600 text-lg">⚠️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Important Disclaimer
              </h3>
              <p className="mt-1 text-sm text-red-700">
                This wellness module is designed to provide educational content and self-help strategies. 
                It is not a substitute for professional mental health treatment. If you&apos;re experiencing 
                severe symptoms, thoughts of self-harm, or a mental health crisis, please contact a qualified 
                mental health professional or emergency services immediately.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
