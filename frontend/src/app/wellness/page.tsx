'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthStore } from '@/stores/authStore';
import { apiService } from '@/services/api';

interface WellnessModule {
  id: string;
  title: string;
  description: string;
  moduleType: string;
  difficultyLevel: string;
  estimatedDurationMinutes: number;
  learningObjectives: string[];
  tags: string[];
  thumbnailUrl?: string;
  isFeatured: boolean;
  requiresSupervision: boolean;
  averageRating: number;
  ratingCount: number;
  completionCount: number;
  createdAt: string;
}

interface UserProgress {
  id: string;
  moduleId: string;
  status: string;
  progressPercentage: number;
  startedAt?: string;
  completedAt?: string;
  lastAccessedAt?: string;
}

const MODULE_TYPE_LABELS: Record<string, string> = {
  mindfulness: 'Mindfulness',
  anxiety_relief: 'Anxiety Relief',
  depression_support: 'Depression Support',
  stress_management: 'Stress Management',
  sleep_hygiene: 'Sleep Hygiene',
  cognitive_behavioral: 'Cognitive Behavioral',
  emotional_regulation: 'Emotional Regulation',
  relationship_skills: 'Relationship Skills',
  self_care: 'Self Care',
  trauma_recovery: 'Trauma Recovery',
};

const DIFFICULTY_LABELS: Record<string, { label: string; color: string }> = {
  beginner: { label: 'Beginner', color: 'bg-green-100 text-green-800' },
  intermediate: { label: 'Intermediate', color: 'bg-yellow-100 text-yellow-800' },
  advanced: { label: 'Advanced', color: 'bg-red-100 text-red-800' },
};

const STATUS_LABELS: Record<string, { label: string; color: string }> = {
  not_started: { label: 'Not Started', color: 'text-gray-600' },
  in_progress: { label: 'In Progress', color: 'text-blue-600' },
  completed: { label: 'Completed', color: 'text-green-600' },
  paused: { label: 'Paused', color: 'text-yellow-600' },
};

export default function WellnessModulesPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading: authLoading } = useAuthStore();
  
  const [modules, setModules] = useState<WellnessModule[]>([]);
  const [userProgress, setUserProgress] = useState<Record<string, UserProgress>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterDifficulty, setFilterDifficulty] = useState('');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [stats, setStats] = useState<{
    totalModules: number;
    modulesByType: Array<{ type: string; count: number }>;
    averageRating: number;
    totalCompletions: number;
  } | null>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [authLoading, isAuthenticated, router]);

  // Load modules and user progress
  useEffect(() => {
    const loadData = async () => {
      if (!isAuthenticated) return;

      try {
        setIsLoading(true);
        setError(null);

        // Load modules and user progress in parallel
        const [modulesResponse, progressResponse, statsResponse] = await Promise.allSettled([
          apiService.getWellnessModules({
            page: currentPage,
            limit: 12,
            moduleType: filterType || undefined,
            difficultyLevel: filterDifficulty || undefined,
            isFeatured: showFeaturedOnly || undefined,
          }),
          apiService.getUserModuleProgress(),
          apiService.getWellnessStats(),
        ]);

        if (modulesResponse.status === 'fulfilled' && modulesResponse.value.data.success) {
          setModules(modulesResponse.value.data.data.modules);
          setTotalPages(modulesResponse.value.data.data.pagination.totalPages);
        } else {
          throw new Error('Failed to load wellness modules');
        }

        if (progressResponse.status === 'fulfilled' && progressResponse.value.data.success) {
          const progressData = progressResponse.value.data.data.progress;
          const progressMap: Record<string, UserProgress> = {};
          progressData.forEach((progress: UserProgress) => {
            progressMap[progress.moduleId] = progress;
          });
          setUserProgress(progressMap);
        }

        if (statsResponse.status === 'fulfilled' && statsResponse.value.data.success) {
          setStats(statsResponse.value.data.data.stats);
        }
      } catch (err: unknown) {
        const errorMessage = (err as { message?: string })?.message || 'Failed to load wellness modules';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [isAuthenticated, currentPage, filterType, filterDifficulty, showFeaturedOnly]);

  const handleStartModule = async (moduleId: string) => {
    try {
      const response = await apiService.startWellnessModule(moduleId);
      if (response.data.success) {
        // Update user progress
        setUserProgress(prev => ({
          ...prev,
          [moduleId]: response.data.data.progress,
        }));
        // Navigate to module detail page
        router.push(`/wellness/${moduleId}`);
      }
    } catch (err: unknown) {
      const errorMessage = (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to start module';
      setError(errorMessage);
    }
  };

  const filteredModules = modules.filter(module => 
    searchTerm === '' || 
    module.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    module.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    module.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes} min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(<span key={i} className="text-yellow-400">★</span>);
    }
    if (hasHalfStar) {
      stars.push(<span key="half" className="text-yellow-400">☆</span>);
    }
    for (let i = stars.length; i < 5; i++) {
      stars.push(<span key={i} className="text-gray-300">☆</span>);
    }
    return stars;
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => router.push('/dashboard')}
                className="text-gray-600 hover:text-gray-900"
              >
                ← Back to Dashboard
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">Wellness Modules</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="text-2xl font-bold text-blue-600">{stats.totalModules}</div>
                <div className="text-sm text-gray-600">Available Modules</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="text-2xl font-bold text-green-600">{stats.totalCompletions}</div>
                <div className="text-sm text-gray-600">Total Completions</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="text-2xl font-bold text-purple-600">
                  {stats.averageRating > 0 ? (
                    <span className="flex items-center">
                      {renderStars(stats.averageRating)} 
                      <span className="ml-2">{stats.averageRating.toFixed(1)}</span>
                    </span>
                  ) : (
                    'N/A'
                  )}
                </div>
                <div className="text-sm text-gray-600">Avg Rating</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="text-2xl font-bold text-orange-600">
                  {Object.keys(userProgress).length}
                </div>
                <div className="text-sm text-gray-600">Your Progress</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="lg:col-span-2">
                <Input
                  type="text"
                  placeholder="Search modules by title, description, or tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Types</option>
                  {Object.entries(MODULE_TYPE_LABELS).map(([value, label]) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <select
                  value={filterDifficulty}
                  onChange={(e) => setFilterDifficulty(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Levels</option>
                  {Object.entries(DIFFICULTY_LABELS).map(([value, { label }]) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="featured"
                  checked={showFeaturedOnly}
                  onChange={(e) => setShowFeaturedOnly(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="featured" className="text-sm text-gray-700">
                  Featured Only
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Loading State */}
        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading wellness modules...</p>
          </div>
        ) : filteredModules.length === 0 ? (
          /* Empty State */
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-6xl mb-4">🧘‍♀️</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm || filterType || filterDifficulty ? 'No modules found' : 'No wellness modules available'}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || filterType || filterDifficulty 
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Wellness modules will appear here when they become available.'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          /* Modules Grid */
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredModules.map((module) => {
                const progress = userProgress[module.id];
                const difficultyInfo = DIFFICULTY_LABELS[module.difficultyLevel];
                const statusInfo = progress ? STATUS_LABELS[progress.status] : null;

                return (
                  <Card key={module.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center space-x-2">
                          <span className={`text-xs px-2 py-1 rounded-full ${difficultyInfo.color}`}>
                            {difficultyInfo.label}
                          </span>
                          {module.isFeatured && (
                            <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                              ⭐ Featured
                            </span>
                          )}
                          {module.requiresSupervision && (
                            <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                              👨‍⚕️ Supervised
                            </span>
                          )}
                        </div>
                      </div>
                      <CardTitle className="text-lg">{module.title}</CardTitle>
                      <CardDescription className="text-sm">
                        {MODULE_TYPE_LABELS[module.moduleType]} • {formatDuration(module.estimatedDurationMinutes)}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 mb-4 line-clamp-3">
                        {module.description}
                      </p>

                      {/* Progress Bar */}
                      {progress && (
                        <div className="mb-4">
                          <div className="flex justify-between items-center mb-1">
                            <span className={`text-sm font-medium ${statusInfo?.color}`}>
                              {statusInfo?.label}
                            </span>
                            <span className="text-sm text-gray-600">
                              {progress.progressPercentage}%
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${progress.progressPercentage}%` }}
                            />
                          </div>
                        </div>
                      )}

                      {/* Learning Objectives */}
                      {module.learningObjectives && module.learningObjectives.length > 0 && (
                        <div className="mb-4">
                          <h5 className="text-sm font-medium text-gray-900 mb-2">You&apos;ll learn:</h5>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {module.learningObjectives.slice(0, 3).map((objective, index) => (
                              <li key={index} className="flex items-start">
                                <span className="text-green-500 mr-2">•</span>
                                <span>{objective}</span>
                              </li>
                            ))}
                            {module.learningObjectives.length > 3 && (
                              <li className="text-gray-500 text-xs">
                                +{module.learningObjectives.length - 3} more objectives
                              </li>
                            )}
                          </ul>
                        </div>
                      )}

                      {/* Tags */}
                      {module.tags && module.tags.length > 0 && (
                        <div className="mb-4">
                          <div className="flex flex-wrap gap-1">
                            {module.tags.slice(0, 4).map((tag, index) => (
                              <span key={index} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                #{tag}
                              </span>
                            ))}
                            {module.tags.length > 4 && (
                              <span className="text-xs text-gray-500">+{module.tags.length - 4}</span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Rating and Stats */}
                      <div className="flex justify-between items-center mb-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          {module.averageRating > 0 ? (
                            <>
                              {renderStars(module.averageRating)}
                              <span className="ml-1">({module.ratingCount})</span>
                            </>
                          ) : (
                            <span>No ratings yet</span>
                          )}
                        </div>
                        <div>
                          {module.completionCount} completions
                        </div>
                      </div>

                      {/* Action Button */}
                      <div className="flex space-x-2">
                        {progress ? (
                          <>
                            <Button
                              onClick={() => router.push(`/wellness/${module.id}`)}
                              className="flex-1"
                              variant={progress.status === 'completed' ? 'outline' : 'default'}
                            >
                              {progress.status === 'completed' ? 'Review' : 'Continue'}
                            </Button>
                            {progress.status !== 'completed' && (
                              <Button
                                onClick={() => router.push(`/wellness/${module.id}`)}
                                variant="outline"
                                size="sm"
                              >
                                View
                              </Button>
                            )}
                          </>
                        ) : (
                          <Button
                            onClick={() => handleStartModule(module.id)}
                            className="flex-1"
                          >
                            Start Module
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center space-x-2 mt-8">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="flex items-center px-4 py-2 text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Professional Disclaimer */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <span className="text-blue-600 text-lg">ℹ️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Professional Guidance
              </h3>
              <p className="mt-1 text-sm text-blue-700">
                These wellness modules are designed to supplement professional mental health care. 
                If you&apos;re experiencing severe symptoms or a mental health crisis, please contact
                a qualified mental health professional or emergency services immediately.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
