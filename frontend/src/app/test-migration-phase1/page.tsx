'use client';

import { useState } from 'react';
import { useJournalDataSupabase } from '@/hooks/useJournalDataSupabase';
import { useAICoachSupabase } from '@/hooks/useAICoachSupabase';
import { useMoodAnalyticsSupabase } from '@/hooks/useMoodAnalyticsSupabase';
import { useSmartNotificationsSupabase } from '@/hooks/useSmartNotificationsSupabase';

export default function TestMigrationPhase1() {
  const [activeTest, setActiveTest] = useState('journal');

  // Hooks Supabase migrés
  const journal = useJournalDataSupabase();
  const aiCoach = useAICoachSupabase();
  const moodAnalytics = useMoodAnalyticsSupabase();
  const notifications = useSmartNotificationsSupabase();

  const tests = [
    {
      id: 'journal',
      name: 'Journal Entries',
      hook: journal,
      icon: '📝',
      description: 'Test du hook useJournalDataSupabase'
    },
    {
      id: 'ai-coach',
      name: 'AI Coach',
      hook: aiCoach,
      icon: '🤖',
      description: 'Test du hook useAICoachSupabase'
    },
    {
      id: 'mood-analytics',
      name: 'Mood Analytics',
      hook: moodAnalytics,
      icon: '📊',
      description: 'Test du hook useMoodAnalyticsSupabase'
    },
    {
      id: 'notifications',
      name: 'Smart Notifications',
      hook: notifications,
      icon: '🔔',
      description: 'Test du hook useSmartNotificationsSupabase'
    }
  ];

  const currentTest = tests.find(t => t.id === activeTest);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🚀 Test Migration Phase 1 - Supabase
          </h1>
          <p className="text-gray-600">
            Validation des 4 hooks migrés depuis les données simulées vers Supabase
          </p>
        </div>

        {/* Résumé Migration */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">📋 Résumé Migration</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {tests.map((test) => (
              <div key={test.id} className="text-center">
                <div className="text-2xl mb-2">{test.icon}</div>
                <div className="text-sm font-medium text-gray-900">{test.name}</div>
                <div className={`text-xs mt-1 px-2 py-1 rounded-full ${
                  test.hook.loading ? 'bg-yellow-100 text-yellow-800' :
                  test.hook.error ? 'bg-red-100 text-red-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {test.hook.loading ? 'Chargement...' :
                   test.hook.error ? 'Erreur' : 'Connecté'}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Navigation Tests */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <h3 className="font-semibold mb-4">Tests Disponibles</h3>
              <div className="space-y-2">
                {tests.map((test) => (
                  <button
                    key={test.id}
                    onClick={() => setActiveTest(test.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      activeTest === test.id
                        ? 'bg-blue-50 border-blue-200 text-blue-900'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{test.icon}</span>
                      <div>
                        <div className="font-medium text-sm">{test.name}</div>
                        <div className={`text-xs ${
                          test.hook.error ? 'text-red-600' : 'text-gray-500'
                        }`}>
                          {test.hook.loading ? 'Loading...' :
                           test.hook.error ? 'Error' : 'Ready'}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Contenu Test */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-3 mb-6">
                <span className="text-2xl">{currentTest?.icon}</span>
                <div>
                  <h3 className="text-xl font-semibold">{currentTest?.name}</h3>
                  <p className="text-gray-600 text-sm">{currentTest?.description}</p>
                </div>
              </div>

              {/* Statut du Hook */}
              <div className="mb-6">
                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${
                      currentTest?.hook.loading ? 'bg-yellow-400' :
                      currentTest?.hook.error ? 'bg-red-400' : 'bg-green-400'
                    }`}></div>
                    <span>
                      {currentTest?.hook.loading ? 'Chargement en cours...' :
                       currentTest?.hook.error ? 'Erreur de connexion' : 'Connecté à Supabase'}
                    </span>
                  </div>
                  {currentTest?.hook.isSupabase && (
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                      Supabase
                    </span>
                  )}
                </div>
              </div>

              {/* Erreur */}
              {currentTest?.hook.error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                  <h4 className="font-medium text-red-900 mb-2">Erreur détectée</h4>
                  <p className="text-red-700 text-sm">{currentTest.hook.error}</p>
                  <button
                    onClick={() => {
                      const hook = currentTest.hook as any;
                      if (hook.loadJournalData) hook.loadJournalData();
                      else if (hook.loadSessions) hook.loadSessions();
                      else if (hook.loadAnalytics) hook.loadAnalytics();
                      else if (hook.loadNotifications) hook.loadNotifications();
                      else if (hook.refreshData) hook.refreshData();
                    }}
                    className="mt-3 bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700"
                  >
                    Réessayer
                  </button>
                </div>
              )}

              {/* Données */}
              {!currentTest?.hook.loading && !currentTest?.hook.error && (
                <div>
                  {activeTest === 'journal' && (
                    <JournalTestResults data={journal} />
                  )}
                  {activeTest === 'ai-coach' && (
                    <AICoachTestResults data={aiCoach} />
                  )}
                  {activeTest === 'mood-analytics' && (
                    <MoodAnalyticsTestResults data={moodAnalytics} />
                  )}
                  {activeTest === 'notifications' && (
                    <NotificationsTestResults data={notifications} />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Composants de test individuels
function JournalTestResults({ data }: { data: any }) {
  return (
    <div>
      <h4 className="font-medium mb-3">Entrées de Journal</h4>
      <div className="space-y-3">
        <div className="text-sm text-gray-600">
          {data.entries?.length || 0} entrées trouvées dans Supabase
        </div>
        {data.entries?.slice(0, 3).map((entry: any, index: number) => (
          <div key={entry.id || index} className="border rounded p-3">
            <div className="font-medium">{entry.title}</div>
            <div className="text-sm text-gray-600 mt-1">
              {entry.content?.substring(0, 100)}...
            </div>
            <div className="text-xs text-gray-500 mt-2">
              {entry.entry_type} • Humeur: {entry.mood_level || 'N/A'}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function AICoachTestResults({ data }: { data: any }) {
  return (
    <div>
      <h4 className="font-medium mb-3">Sessions IA Coach</h4>
      <div className="space-y-3">
        <div className="text-sm text-gray-600">
          {data.sessions?.length || 0} sessions trouvées dans Supabase
        </div>
        {data.sessions?.slice(0, 3).map((session: any, index: number) => (
          <div key={session.id || index} className="border rounded p-3">
            <div className="font-medium">{session.theme}</div>
            <div className="text-sm text-gray-600 mt-1">{session.goal}</div>
            <div className="text-xs text-gray-500 mt-2">
              Status: {session.status} • Messages: {session.messages?.length || 0}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function MoodAnalyticsTestResults({ data }: { data: any }) {
  return (
    <div>
      <h4 className="font-medium mb-3">Analytics d'Humeur</h4>
      <div className="space-y-3">
        {data.analytics ? (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">Humeur Actuelle</div>
                <div className="text-xl font-bold">
                  {data.analytics.overview?.currentMood?.toFixed(1) || 'N/A'}
                </div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">Moyenne Hebdo</div>
                <div className="text-xl font-bold">
                  {data.analytics.overview?.weeklyAverage?.toFixed(1) || 'N/A'}
                </div>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              {data.analytics.chartData?.daily?.length || 0} points de données
            </div>
          </>
        ) : (
          <div className="text-sm text-gray-600">Aucune donnée disponible</div>
        )}
      </div>
    </div>
  );
}

function NotificationsTestResults({ data }: { data: any }) {
  return (
    <div>
      <h4 className="font-medium mb-3">Notifications Intelligentes</h4>
      <div className="space-y-3">
        <div className="text-sm text-gray-600">
          {data.notifications?.length || 0} notifications trouvées
        </div>
        {data.notifications?.slice(0, 3).map((notif: any, index: number) => (
          <div key={notif.id || index} className="border rounded p-3">
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 rounded text-xs ${
                notif.priority === 'high' ? 'bg-red-100 text-red-800' :
                notif.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {notif.priority}
              </span>
              <span className={`w-2 h-2 rounded-full ${
                notif.is_read ? 'bg-gray-400' : 'bg-blue-400'
              }`}></span>
            </div>
            <div className="font-medium mt-1">{notif.title}</div>
            <div className="text-sm text-gray-600 mt-1">{notif.message}</div>
            <div className="text-xs text-gray-500 mt-2">
              Type: {notif.type} • {notif.is_read ? 'Lue' : 'Non lue'}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
