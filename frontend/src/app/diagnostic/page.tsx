'use client';

import React, { useState, useEffect } from 'react';

interface DiagnosticResult {
  category: string;
  test: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
}

export default function DiagnosticPage() {
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [loading, setLoading] = useState(true);

  const addResult = (result: DiagnosticResult) => {
    setResults(prev => [...prev, result]);
  };

  useEffect(() => {
    const runDiagnostic = async () => {
      try {
        // 1. Test variables d'environnement
        addResult({
          category: 'Environment',
          test: 'Variables Supabase',
          status: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'success' : 'error',
          message: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'URL configurée' : 'URL manquante',
          details: {
            url: process.env.NEXT_PUBLIC_SUPABASE_URL,
            key: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Configurée' : 'Manquante'
          }
        });

        // 2. Test imports
        try {
          await import('@/lib/supabase/client');
          addResult({
            category: 'Imports',
            test: 'Client Supabase',
            status: 'success',
            message: 'Import réussi',
          });
        } catch (error) {
          addResult({
            category: 'Imports',
            test: 'Client Supabase',
            status: 'error',
            message: 'Erreur import',
            details: error
          });
        }

        try {
          await import('@/lib/config/feature-flags');
          addResult({
            category: 'Imports',
            test: 'Feature Flags',
            status: 'success',
            message: 'Import réussi',
          });
        } catch (error) {
          addResult({
            category: 'Imports',
            test: 'Feature Flags',
            status: 'error',
            message: 'Erreur import',
            details: error
          });
        }

        try {
          await import('@/lib/database');
          addResult({
            category: 'Imports',
            test: 'Database Manager',
            status: 'success',
            message: 'Import réussi',
          });
        } catch (error) {
          addResult({
            category: 'Imports',
            test: 'Database Manager',
            status: 'error',
            message: 'Erreur import',
            details: error
          });
        }

        // 3. Test composants UI
        try {
          await import('@/components/ui/card');
          addResult({
            category: 'UI Components',
            test: 'Card',
            status: 'success',
            message: 'Import réussi',
          });
        } catch (error) {
          addResult({
            category: 'UI Components',
            test: 'Card',
            status: 'error',
            message: 'Erreur import',
            details: error
          });
        }

        try {
          await import('@/components/ui/button');
          addResult({
            category: 'UI Components',
            test: 'Button',
            status: 'success',
            message: 'Import réussi',
          });
        } catch (error) {
          addResult({
            category: 'UI Components',
            test: 'Button',
            status: 'error',
            message: 'Erreur import',
            details: error
          });
        }

        // 4. Test Lucide React
        try {
          await import('lucide-react');
          addResult({
            category: 'Dependencies',
            test: 'Lucide React',
            status: 'success',
            message: 'Import réussi',
          });
        } catch (error) {
          addResult({
            category: 'Dependencies',
            test: 'Lucide React',
            status: 'error',
            message: 'Erreur import',
            details: error
          });
        }

        // 5. Test connexion Supabase
        try {
          const { createSupabaseClient } = await import('@/lib/supabase/client');
          const supabase = createSupabaseClient();
          
          const { data, error } = await supabase
            .from('users')
            .select('count')
            .limit(1);
          
          if (error) {
            addResult({
              category: 'Supabase',
              test: 'Connexion',
              status: 'warning',
              message: error.message,
              details: error
            });
          } else {
            addResult({
              category: 'Supabase',
              test: 'Connexion',
              status: 'success',
              message: 'Connexion réussie',
              details: data
            });
          }
        } catch (error) {
          addResult({
            category: 'Supabase',
            test: 'Connexion',
            status: 'error',
            message: 'Erreur connexion',
            details: error
          });
        }

      } catch (globalError) {
        addResult({
          category: 'Global',
          test: 'Diagnostic',
          status: 'error',
          message: 'Erreur globale',
          details: globalError
        });
      } finally {
        setLoading(false);
      }
    };

    runDiagnostic();
  }, []);

  const getStatusEmoji = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const groupedResults = results.reduce((acc, result) => {
    if (!acc[result.category]) {
      acc[result.category] = [];
    }
    acc[result.category].push(result);
    return acc;
  }, {} as Record<string, DiagnosticResult[]>);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold mb-6 text-gray-900">
            🔍 Diagnostic Complet - MindFlow Pro
          </h1>
          
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-lg">Diagnostic en cours...</span>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(groupedResults).map(([category, categoryResults]) => (
                <div key={category} className="border rounded-lg p-4">
                  <h2 className="text-xl font-semibold mb-4 text-gray-800">
                    📋 {category}
                  </h2>
                  <div className="space-y-3">
                    {categoryResults.map((result, index) => (
                      <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded">
                        <span className="text-lg">
                          {getStatusEmoji(result.status)}
                        </span>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{result.test}</span>
                            <span className={`text-sm ${getStatusColor(result.status)}`}>
                              {result.message}
                            </span>
                          </div>
                          {result.details && (
                            <details className="mt-2">
                              <summary className="text-xs text-gray-500 cursor-pointer">
                                Voir les détails
                              </summary>
                              <pre className="mt-1 text-xs bg-gray-800 text-green-400 p-2 rounded overflow-auto">
                                {JSON.stringify(result.details, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
              
              <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 className="font-bold text-blue-800 mb-2">
                  🎯 Résumé du Diagnostic
                </h3>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {results.filter(r => r.status === 'success').length}
                    </div>
                    <div>Tests réussis</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {results.filter(r => r.status === 'warning').length}
                    </div>
                    <div>Avertissements</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {results.filter(r => r.status === 'error').length}
                    </div>
                    <div>Erreurs</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 