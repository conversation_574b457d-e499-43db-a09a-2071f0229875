export const metadata = {
  title: 'Dashboard Simple - MindFlow Pro',
  description: 'Version simplifiée du tableau de bord',
};

export default function DashboardSimple() {
  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      maxWidth: '1200px',
      margin: '0 auto'
    }}>
      <header style={{ marginBottom: '30px' }}>
        <h1 style={{ 
          color: '#1e40af', 
          fontSize: '28px',
          marginBottom: '10px'
        }}>
          📊 Dashboard MindFlow Pro
        </h1>
        <p style={{ color: '#6b7280', fontSize: '16px' }}>
          Version simplifiée pour les tests de migration Supabase
        </p>
      </header>

      {/* Grille des cartes */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {/* Card 1 - Stats de bien-être */}
        <div style={{ 
          backgroundColor: '#f0f9ff',
          border: '1px solid #0ea5e9',
          borderRadius: '12px',
          padding: '20px'
        }}>
          <h2 style={{ color: '#0369a1', marginTop: '0' }}>🌟 Stats de Bien-être</h2>
          <div style={{ fontSize: '14px', color: '#374151' }}>
            <p>• Humeur moyenne: 7.2/10</p>
            <p>• Séances complétées: 24</p>
            <p>• Objectifs atteints: 12/15</p>
            <p>• Streak actuel: 5 jours</p>
          </div>
        </div>

        {/* Card 2 - Actions rapides */}
        <div style={{ 
          backgroundColor: '#f0fdf4',
          border: '1px solid #16a34a',
          borderRadius: '12px',
          padding: '20px'
        }}>
          <h2 style={{ color: '#15803d', marginTop: '0' }}>⚡ Actions Rapides</h2>
          <div>
            <button style={{ 
              display: 'block',
              width: '100%',
              padding: '8px 12px',
              margin: '8px 0',
              backgroundColor: '#dcfce7',
              border: '1px solid #16a34a',
              borderRadius: '6px',
              cursor: 'pointer'
            }}>
              📝 Nouveau journal
            </button>
            <button style={{ 
              display: 'block',
              width: '100%',
              padding: '8px 12px',
              margin: '8px 0',
              backgroundColor: '#dcfce7',
              border: '1px solid #16a34a',
              borderRadius: '6px',
              cursor: 'pointer'
            }}>
              🧘 Séance méditation
            </button>
          </div>
        </div>

        {/* Card 3 - Citation IA */}
        <div style={{ 
          backgroundColor: '#fefbf3',
          border: '1px solid #f59e0b',
          borderRadius: '12px',
          padding: '20px'
        }}>
          <h2 style={{ color: '#d97706', marginTop: '0' }}>🤖 Citation IA Coach</h2>
          <blockquote style={{ 
            fontStyle: 'italic', 
            color: '#374151',
            fontSize: '14px',
            margin: '10px 0'
          }}>
            "Chaque petit pas vers le bien-être compte. Votre progression est remarquable !"
          </blockquote>
          <p style={{ fontSize: '12px', color: '#6b7280', textAlign: 'right' }}>
            - Coach IA MindFlow
          </p>
        </div>
      </div>

      {/* Section objectifs */}
      <div style={{ 
        backgroundColor: '#fff',
        border: '1px solid #e5e7eb',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '30px'
      }}>
        <h2 style={{ color: '#374151', marginTop: '0' }}>🎯 Objectifs Actuels</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
          <div style={{ padding: '15px', backgroundColor: '#f9fafb', borderRadius: '8px' }}>
            <h3 style={{ margin: '0 0 10px 0', color: '#1f2937' }}>Méditation quotidienne</h3>
            <div style={{ 
              width: '100%', 
              height: '8px', 
              backgroundColor: '#e5e7eb', 
              borderRadius: '4px',
              marginBottom: '8px'
            }}>
              <div style={{ 
                width: '75%', 
                height: '100%', 
                backgroundColor: '#10b981', 
                borderRadius: '4px'
              }}></div>
            </div>
            <p style={{ margin: '0', fontSize: '12px', color: '#6b7280' }}>75% - 15/20 jours</p>
          </div>
          
          <div style={{ padding: '15px', backgroundColor: '#f9fafb', borderRadius: '8px' }}>
            <h3 style={{ margin: '0 0 10px 0', color: '#1f2937' }}>Journal hebdomadaire</h3>
            <div style={{ 
              width: '100%', 
              height: '8px', 
              backgroundColor: '#e5e7eb', 
              borderRadius: '4px',
              marginBottom: '8px'
            }}>
              <div style={{ 
                width: '90%', 
                height: '100%', 
                backgroundColor: '#10b981', 
                borderRadius: '4px'
              }}></div>
            </div>
            <p style={{ margin: '0', fontSize: '12px', color: '#6b7280' }}>90% - 9/10 entrées</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div style={{ 
        backgroundColor: '#f8fafc',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '20px'
      }}>
        <h2 style={{ color: '#475569', marginTop: '0' }}>🔗 Navigation</h2>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
          <a href="/" style={{ 
            padding: '8px 16px',
            backgroundColor: '#e2e8f0',
            color: '#475569',
            textDecoration: 'none',
            borderRadius: '6px',
            fontSize: '14px'
          }}>🏠 Accueil</a>
          <a href="/test-basic" style={{ 
            padding: '8px 16px',
            backgroundColor: '#e2e8f0',
            color: '#475569',
            textDecoration: 'none',
            borderRadius: '6px',
            fontSize: '14px'
          }}>🧪 Test Basic</a>
          <a href="/test-fix" style={{ 
            padding: '8px 16px',
            backgroundColor: '#e2e8f0',
            color: '#475569',
            textDecoration: 'none',
            borderRadius: '6px',
            fontSize: '14px'
          }}>🔧 Test Fix</a>
          <a href="/journal" style={{ 
            padding: '8px 16px',
            backgroundColor: '#e2e8f0',
            color: '#475569',
            textDecoration: 'none',
            borderRadius: '6px',
            fontSize: '14px'
          }}>📔 Journal</a>
        </div>
      </div>

      <footer style={{ 
        marginTop: '40px',
        textAlign: 'center',
        fontSize: '12px',
        color: '#9ca3af',
        borderTop: '1px solid #e5e7eb',
        paddingTop: '20px'
      }}>
        <p>Dashboard simplifié - MindFlow Pro Migration Supabase</p>
        <p>Timestamp: {new Date().toLocaleString('fr-FR')}</p>
      </footer>
    </div>
  );
} 