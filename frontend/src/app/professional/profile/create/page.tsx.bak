'use client';

import React, { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { 
  User,
  ArrowLeft,
  Save,
  AlertCircle,
  CheckCircle,
  DollarSign,
  Clock,
  Users,
  Award
} from 'lucide-react';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext2';
import Navigation from '@/components/Navigation';

export const dynamic = 'force-dynamic';

interface ProfessionalProfileData {
  professionalType: string;
  specializations: string[];
  languagesSpoken: string[];
  yearsOfExperience: number;
  professionalBio: string;
  hourlyRate?: number;
  currency: string;
  acceptingNewClients: boolean;
  consultationTypes: Array<{
    type: string;
    duration: number;
    price: number;
    currency: string;
  }>;
}

const CreateProfessionalProfile: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState<ProfessionalProfileData>({
    professionalType: '',
    specializations: [],
    languagesSpoken: ['English'],
    yearsOfExperience: 0,
    professionalBio: '',
    hourlyRate: undefined,
    currency: 'USD',
    acceptingNewClients: true,
    consultationTypes: [
      { type: 'video_call', duration: 60, price: 100, currency: 'USD' }
    ]
  });

  const professionalTypes = [
    'psychologist',
    'psychiatrist',
    'therapist',
    'counselor',
    'social_worker',
    'life_coach',
    'mental_health_counselor'
  ];

  const commonSpecializations = [
    'Anxiety Disorders',
    'Depression',
    'PTSD',
    'Relationship Counseling',
    'Family Therapy',
    'Cognitive Behavioral Therapy',
    'Mindfulness-Based Therapy',
    'Addiction Counseling',
    'Grief Counseling',
    'Stress Management',
    'Eating Disorders',
    'ADHD',
    'Bipolar Disorder',
    'Trauma Therapy',
    'Child Psychology',
    'Adolescent Therapy'
  ];

  const consultationTypes = [
    { value: 'video_call', label: 'Video Call', icon: '📹' },
    { value: 'phone_call', label: 'Phone Call', icon: '📞' },
    { value: 'chat', label: 'Text Chat', icon: '💬' },
    { value: 'in_person', label: 'In Person', icon: '🏢' }
  ];

  const handleInputChange = (field: keyof ProfessionalProfileData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSpecializationToggle = (specialization: string) => {
    setFormData(prev => ({
      ...prev,
      specializations: prev.specializations.includes(specialization)
        ? prev.specializations.filter(s => s !== specialization)
        : [...prev.specializations, specialization]
    }));
  };

  const handleConsultationTypeChange = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      consultationTypes: prev.consultationTypes.map((type, i) => 
        i === index ? { ...type, [field]: value } : type
      )
    }));
  };

  const addConsultationType = () => {
    setFormData(prev => ({
      ...prev,
      consultationTypes: [
        ...prev.consultationTypes,
        { type: 'video_call', duration: 60, price: 100, currency: 'USD' }
      ]
    }));
  };

  const removeConsultationType = (index: number) => {
    setFormData(prev => ({
      ...prev,
      consultationTypes: prev.consultationTypes.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.professionalType || formData.specializations.length === 0) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await apiService.createProfessionalProfile(formData);
      
      setSuccess(true);
      setTimeout(() => {
        router.push('/professional/dashboard');
      }, 2000);

    } catch (error: any) {
      console.error('Error creating professional profile:', error);
      setError(error.response?.data?.message || 'Failed to create professional profile');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Please Log In</h2>
            <Link href="/auth/login" className="text-blue-600 hover:text-blue-800">
              Go to Login
            </Link>
          </div>
        </div>
      </>
    );
  }

  if (success) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Profile Created Successfully!</h2>
            <p className="text-gray-600 mb-4">Redirecting to your professional dashboard...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Create Professional Profile - MindFlow Pro</title>
        <meta name="description" content="Create your professional profile on MindFlow Pro" />
      </Head>

      <Navigation />
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="text-gray-600 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Create Professional Profile</h1>
                <p className="text-gray-600">Set up your professional practice on MindFlow Pro</p>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                  <span className="text-red-800">{error}</span>
                </div>
              </div>
            )}

            {/* Basic Information */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Basic Information</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Professional Type *
                  </label>
                  <select
                    value={formData.professionalType}
                    onChange={(e) => handleInputChange('professionalType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="">Select your profession</option>
                    {professionalTypes.map(type => (
                      <option key={type} value={type}>
                        {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Years of Experience *
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="50"
                    value={formData.yearsOfExperience}
                    onChange={(e) => handleInputChange('yearsOfExperience', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Professional Bio *
                </label>
                <textarea
                  value={formData.professionalBio}
                  onChange={(e) => handleInputChange('professionalBio', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Tell potential clients about your background, approach, and expertise..."
                  required
                />
              </div>
            </div>

            {/* Specializations */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Specializations *</h2>
              <p className="text-gray-600 mb-4">Select your areas of expertise (choose at least one)</p>

              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {commonSpecializations.map(specialization => (
                  <label key={specialization} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.specializations.includes(specialization)}
                      onChange={() => handleSpecializationToggle(specialization)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">{specialization}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Consultation Types & Pricing */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Consultation Types & Pricing</h2>

              {formData.consultationTypes.map((consultation, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 mb-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Type
                      </label>
                      <select
                        value={consultation.type}
                        onChange={(e) => handleConsultationTypeChange(index, 'type', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        {consultationTypes.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.icon} {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Duration (minutes)
                      </label>
                      <input
                        type="number"
                        min="15"
                        max="180"
                        step="15"
                        value={consultation.duration}
                        onChange={(e) => handleConsultationTypeChange(index, 'duration', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Price ($)
                      </label>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={consultation.price}
                        onChange={(e) => handleConsultationTypeChange(index, 'price', parseFloat(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div className="flex items-end">
                      {formData.consultationTypes.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeConsultationType(index)}
                          className="w-full px-3 py-2 text-red-600 border border-red-300 rounded-lg hover:bg-red-50 transition-colors"
                        >
                          Remove
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              <button
                type="button"
                onClick={addConsultationType}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                + Add Another Consultation Type
              </button>
            </div>

            {/* Additional Settings */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Additional Settings</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hourly Rate (Optional)
                  </label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.hourlyRate || ''}
                      onChange={(e) => handleInputChange('hourlyRate', e.target.value ? parseFloat(e.target.value) : undefined)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="100.00"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Currency
                  </label>
                  <select
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="USD">USD ($)</option>
                    <option value="EUR">EUR (€)</option>
                    <option value="GBP">GBP (£)</option>
                    <option value="CAD">CAD ($)</option>
                  </select>
                </div>
              </div>

              <div className="mt-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.acceptingNewClients}
                    onChange={(e) => handleInputChange('acceptingNewClients', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    I am currently accepting new clients
                  </span>
                </label>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <Link
                href="/dashboard"
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Create Profile
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default CreateProfessionalProfile;
