'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { 
  Calendar,
  Clock,
  Users,
  Star,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  User,
  Settings,
  MessageSquare,
  Award,
  DollarSign,
  Eye,
  Edit,
  Plus,
  Phone,
  Video,
  MessageCircle
} from 'lucide-react';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext2';
import Navigation from '@/components/Navigation';

export const dynamic = 'force-dynamic';

interface ProfessionalProfile {
  id: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    profilePicture?: string;
  };
  professionalType: string;
  specializations: string[];
  languagesSpoken: string[];
  yearsOfExperience: number;
  professionalBio: string;
  hourlyRate?: number;
  currency: string;
  acceptingNewClients: boolean;
  averageRating: number;
  totalReviews: number;
  verificationStatus: string;
  consultationTypes: Array<{
    type: string;
    duration: number;
    price: number;
    currency: string;
  }>;
}

interface ProfessionalStats {
  totalAppointments: number;
  upcomingAppointments: number;
  completedAppointments: number;
  averageRating: number;
  totalReviews: number;
  monthlyRevenue: number;
  newClientsThisMonth: number;
}

interface Appointment {
  id: string;
  status: string;
  appointmentType: string;
  mode: string;
  scheduledAt: string;
  durationMinutes: number;
  client: {
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  clientNotes?: string;
  cost?: number;
  currency: string;
}

const ProfessionalDashboard: React.FC = () => {
  const { user } = useAuth();
  const [profile, setProfile] = useState<ProfessionalProfile | null>(null);
  const [stats, setStats] = useState<ProfessionalStats | null>(null);
  const [upcomingAppointments, setUpcomingAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user has a professional profile
      const profileResponse = await apiService.getMyProfessionalProfile();
      setProfile(profileResponse.data.professional);

      // Fetch professional stats and upcoming appointments
      const [statsResponse, appointmentsResponse] = await Promise.all([
        apiService.getAppointmentStats(),
        apiService.getAppointments({
          status: 'scheduled',
          limit: 5,
          dateFrom: new Date().toISOString().split('T')[0]
        })
      ]);

      setStats(statsResponse.data.stats);
      setUpcomingAppointments(appointmentsResponse.data.appointments || []);

    } catch (error: any) {
      console.error('Error fetching dashboard data:', error);
      if (error.response?.status === 404) {
        setError('Professional profile not found. Please create your professional profile first.');
      } else {
        setError('Failed to load dashboard data. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
      case 'confirmed':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getConsultationTypeIcon = (type: string) => {
    switch (type) {
      case 'video_call': return <Video className="w-4 h-4" />;
      case 'phone_call': return <Phone className="w-4 h-4" />;
      case 'chat': return <MessageCircle className="w-4 h-4" />;
      default: return <Video className="w-4 h-4" />;
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    };
  };

  if (!user) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Please Log In</h2>
            <Link href="/auth/login" className="text-blue-600 hover:text-blue-800">
              Go to Login
            </Link>
          </div>
        </div>
      </>
    );
  }

  if (loading) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Dashboard Error</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link href="/professional/profile/create" className="text-blue-600 hover:text-blue-800">
              Create Professional Profile
            </Link>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Professional Dashboard - MindFlow Pro</title>
        <meta name="description" content="Manage your professional practice on MindFlow Pro" />
      </Head>

      <Navigation />
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                  {profile?.user.profilePicture ? (
                    <img
                      src={profile.user.profilePicture}
                      alt={`${profile.user.firstName} ${profile.user.lastName}`}
                      className="w-16 h-16 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-8 h-8 text-blue-600" />
                  )}
                </div>
                
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    Welcome, Dr. {profile?.user.firstName}
                  </h1>
                  <p className="text-gray-600 capitalize">
                    {profile?.professionalType.replace('_', ' ')} • {profile?.yearsOfExperience} years experience
                  </p>
                  <div className="flex items-center mt-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="ml-1 text-sm text-gray-600">
                      {profile?.averageRating.toFixed(1)} ({profile?.totalReviews} reviews)
                    </span>
                    <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium ${
                      profile?.verificationStatus === 'verified' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {profile?.verificationStatus === 'verified' ? 'Verified' : 'Pending Verification'}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="mt-4 lg:mt-0 flex space-x-3">
                <Link
                  href="/professional/profile/edit"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Profile
                </Link>
                <Link
                  href="/appointments"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  View All Appointments
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Grid */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Calendar className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Appointments</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalAppointments}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Clock className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Upcoming Sessions</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.upcomingAppointments}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <CheckCircle className="w-6 h-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Completed Sessions</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.completedAppointments}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <DollarSign className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">${stats.monthlyRevenue}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Two Column Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content - Upcoming Appointments */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm">
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-gray-900">Upcoming Appointments</h2>
                    <Link
                      href="/appointments"
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      View All
                    </Link>
                  </div>
                </div>

                <div className="p-6">
                  {upcomingAppointments.length === 0 ? (
                    <div className="text-center py-8">
                      <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No upcoming appointments</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Your schedule is clear for now.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {upcomingAppointments.map((appointment) => {
                        const { date, time } = formatDateTime(appointment.scheduledAt);
                        return (
                          <div key={appointment.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-4">
                                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                  {appointment.client.profilePicture ? (
                                    <img
                                      src={appointment.client.profilePicture}
                                      alt={`${appointment.client.firstName} ${appointment.client.lastName}`}
                                      className="w-10 h-10 rounded-full object-cover"
                                    />
                                  ) : (
                                    <User className="w-5 h-5 text-blue-600" />
                                  )}
                                </div>

                                <div>
                                  <h4 className="text-sm font-medium text-gray-900">
                                    {appointment.client.firstName} {appointment.client.lastName}
                                  </h4>
                                  <div className="flex items-center space-x-3 text-sm text-gray-600">
                                    <span>{date} at {time}</span>
                                    <span>•</span>
                                    <div className="flex items-center space-x-1">
                                      {getConsultationTypeIcon(appointment.mode)}
                                      <span className="capitalize">{appointment.mode.replace('_', ' ')}</span>
                                    </div>
                                    <span>•</span>
                                    <span>{appointment.durationMinutes} min</span>
                                  </div>
                                  <p className="text-sm text-gray-600 capitalize">
                                    {appointment.appointmentType.replace('_', ' ')}
                                  </p>
                                </div>
                              </div>

                              <div className="flex items-center space-x-2">
                                {appointment.cost && (
                                  <span className="text-sm font-medium text-gray-900">
                                    ${appointment.cost}
                                  </span>
                                )}
                                <Link
                                  href={`/appointments/${appointment.id}`}
                                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                >
                                  View Details
                                </Link>
                              </div>
                            </div>

                            {appointment.clientNotes && (
                              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                                <p className="text-sm text-gray-700">
                                  <span className="font-medium">Client Notes:</span> {appointment.clientNotes}
                                </p>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Profile Summary */}
              {profile && (
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Profile Summary</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm font-medium text-gray-700">Type:</span>
                      <p className="text-gray-900 capitalize">{profile.professionalType.replace('_', ' ')}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">Specializations:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {profile.specializations.slice(0, 3).map((spec, index) => (
                          <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                            {spec}
                          </span>
                        ))}
                        {profile.specializations.length > 3 && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                            +{profile.specializations.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">Hourly Rate:</span>
                      <p className="text-gray-900">
                        {profile.hourlyRate ? `$${profile.hourlyRate} ${profile.currency}` : 'Not set'}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">Accepting New Clients:</span>
                      <p className={`text-sm font-medium ${profile.acceptingNewClients ? 'text-green-600' : 'text-red-600'}`}>
                        {profile.acceptingNewClients ? 'Yes' : 'No'}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <Link
                    href="/professional/profile/edit"
                    className="w-full flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Settings className="w-4 h-4 mr-3 text-gray-500" />
                    <span>Edit Profile</span>
                  </Link>

                  <Link
                    href="/appointments"
                    className="w-full flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Calendar className="w-4 h-4 mr-3 text-gray-500" />
                    <span>Manage Schedule</span>
                  </Link>

                  <Link
                    href="/professional/clients"
                    className="w-full flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Users className="w-4 h-4 mr-3 text-gray-500" />
                    <span>Client Management</span>
                  </Link>

                  <Link
                    href="/professional/analytics"
                    className="w-full flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <TrendingUp className="w-4 h-4 mr-3 text-gray-500" />
                    <span>Analytics</span>
                  </Link>
                </div>
              </div>

              {/* Professional Notice */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <Award className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      Professional Standards
                    </h3>
                    <p className="mt-1 text-sm text-blue-700">
                      Maintain the highest standards of care and professionalism.
                      All interactions are subject to professional ethics and platform guidelines.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProfessionalDashboard;
