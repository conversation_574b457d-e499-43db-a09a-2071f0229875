'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Settings,
  AlertTriangle,
  Info
} from 'lucide-react';

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'warning' | 'pending';
  message: string;
  details?: any;
}

export default function TestSupabasePage() {
  const [results, setResults] = useState<TestResult[]>([]);
  const [testing, setTesting] = useState(false);

  const addResult = (result: TestResult) => {
    setResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const testEnvironmentVariables = async () => {
    addResult({
      name: 'Variables d\'environnement',
      status: 'pending',
      message: 'Vérification des variables...'
    });

    try {
      const envVars = {
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        NEXT_PUBLIC_DUAL_DATABASE_MODE: process.env.NEXT_PUBLIC_DUAL_DATABASE_MODE,
        NEXT_PUBLIC_MIGRATE_USER_DATA: process.env.NEXT_PUBLIC_MIGRATE_USER_DATA,
        NEXT_PUBLIC_USE_SUPABASE_DATABASE: process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE,
        NEXT_PUBLIC_USE_SUPABASE_AUTH: process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH,
      };

      const missing = Object.entries(envVars).filter(([key, value]) => !value);
      
      if (missing.length === 0) {
        addResult({
          name: 'Variables d\'environnement',
          status: 'success',
          message: 'Toutes les variables sont configurées',
          details: envVars
        });
      } else {
        addResult({
          name: 'Variables d\'environnement',
          status: 'warning',
          message: `${missing.length} variable(s) manquante(s): ${missing.map(([key]) => key).join(', ')}`,
          details: envVars
        });
      }
    } catch (error) {
      addResult({
        name: 'Variables d\'environnement',
        status: 'error',
        message: 'Erreur lors de la vérification',
        details: error
      });
    }
  };

  const testSupabaseConnection = async () => {
    addResult({
      name: 'Connexion Supabase',
      status: 'pending',
      message: 'Test de connexion...'
    });

    try {
      const { createSupabaseClient } = await import('@/lib/supabase/client');
      const supabase = createSupabaseClient();
      
      const { data, error } = await supabase.from('users').select('count').limit(1);
      
      if (error) {
        addResult({
          name: 'Connexion Supabase',
          status: 'error',
          message: `Erreur Supabase: ${error.message}`,
          details: error
        });
      } else {
        addResult({
          name: 'Connexion Supabase',
          status: 'success',
          message: 'Connexion Supabase réussie !',
          details: data
        });
      }
    } catch (error) {
      addResult({
        name: 'Connexion Supabase',
        status: 'error',
        message: 'Erreur lors de la création du client Supabase',
        details: error
      });
    }
  };

  const testDatabaseAdapters = async () => {
    addResult({
      name: 'Adaptateurs de base de données',
      status: 'pending',
      message: 'Test des adaptateurs...'
    });

    try {
      const { getDatabaseManager } = await import('@/lib/database');
      const manager = await getDatabaseManager();
      
      const isDualMode = await manager.isDualModeAvailable();
      const supabaseAdapter = manager.getSupabaseAdapter();
      const sqliteAdapter = manager.getSQLiteAdapter();
      
      addResult({
        name: 'Adaptateurs de base de données',
        status: isDualMode ? 'success' : 'warning',
        message: isDualMode 
          ? 'Mode dual activé : Supabase + SQLite' 
          : 'Mode simple activé',
        details: {
          dualMode: isDualMode,
          hasSupabase: !!supabaseAdapter,
          hasSQLite: !!sqliteAdapter
        }
      });

      // Test health check
      const adapter = await manager.getAdapter();
      const health = await adapter.healthCheck();
      
      addResult({
        name: 'Health Check',
        status: 'success',
        message: `Base de données ${health.status}`,
        details: health
      });
      
    } catch (error) {
      addResult({
        name: 'Adaptateurs de base de données',
        status: 'error',
        message: 'Erreur lors du test des adaptateurs',
        details: error
      });
    }
  };

  const testFeatureFlags = async () => {
    addResult({
      name: 'Feature Flags',
      status: 'pending',
      message: 'Vérification des feature flags...'
    });

    try {
      const { FEATURE_FLAGS, isInDualMode, canUseSupabase } = await import('@/lib/config/feature-flags');
      
      addResult({
        name: 'Feature Flags',
        status: 'success',
        message: `Mode dual: ${isInDualMode()}, Supabase: ${canUseSupabase()}`,
        details: FEATURE_FLAGS
      });
    } catch (error) {
      addResult({
        name: 'Feature Flags',
        status: 'error',
        message: 'Erreur lors de la vérification des feature flags',
        details: error
      });
    }
  };

  const runAllTests = async () => {
    setTesting(true);
    clearResults();

    await testEnvironmentVariables();
    await testFeatureFlags();
    await testSupabaseConnection();
    await testDatabaseAdapters();

    setTesting(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'pending':
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />;
      default:
        return <Info className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  useEffect(() => {
    runAllTests();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Test de Migration Supabase
          </h1>
          <p className="text-gray-600">
            Vérification de la configuration Supabase et du mode dual
          </p>
        </div>

        <div className="grid gap-6">
          {/* Contrôles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5" />
                <span>Contrôles</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-4">
                <Button 
                  onClick={runAllTests} 
                  disabled={testing}
                  className="flex items-center space-x-2"
                >
                  <RefreshCw className={`w-4 h-4 ${testing ? 'animate-spin' : ''}`} />
                  <span>Relancer tous les tests</span>
                </Button>
                <Button 
                  variant="outline" 
                  onClick={clearResults}
                  disabled={testing}
                >
                  Effacer les résultats
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Résultats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="w-5 h-5" />
                <span>Résultats des Tests</span>
                <Badge variant="outline">
                  {results.length} test{results.length > 1 ? 's' : ''}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {results.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  Aucun test exécuté
                </p>
              ) : (
                <div className="space-y-4">
                  {results.map((result, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(result.status)}
                          <h3 className="font-medium">{result.name}</h3>
                        </div>
                        <Badge 
                          variant="secondary" 
                          className={getStatusColor(result.status)}
                        >
                          {result.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        {result.message}
                      </p>
                      {result.details && (
                        <details className="mt-2">
                          <summary className="text-xs text-gray-500 cursor-pointer">
                            Voir les détails
                          </summary>
                          <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 