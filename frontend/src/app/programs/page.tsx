'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DashboardLayout } from '@/components/Layout/DashboardLayout';
import { 
  Brain, 
  Heart, 
  Zap, 
  Moon, 
  Target, 
  Clock, 
  User, 
  Star,
  Play,
  Pause,
  CheckCircle,
  BookOpen,
  Calendar,
  Filter,
  Search,
  TrendingUp
} from 'lucide-react';

interface Program {
  id: string;
  title: string;
  description: string;
  category: 'stress' | 'anxiety' | 'depression' | 'sleep' | 'productivity' | 'relationships';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: string;
  sessionsCount: number;
  completedSessions: number;
  rating: number;
  reviews: number;
  instructor: string;
  isEnrolled: boolean;
  isPremium: boolean;
  features: string[];
  thumbnail: string;
  tags: string[];
}

const mockPrograms: Program[] = [
  {
    id: '1',
    title: 'Gestion du Stress au Travail',
    description: 'Programme complet pour apprendre à gérer le stress professionnel avec des techniques éprouvées.',
    category: 'stress',
    difficulty: 'beginner',
    duration: '4 semaines',
    sessionsCount: 12,
    completedSessions: 7,
    rating: 4.8,
    reviews: 1234,
    instructor: 'Dr. Marie Dubois',
    isEnrolled: true,
    isPremium: false,
    features: ['Exercices pratiques', 'Méditations guidées', 'Suivi personnalisé'],
    thumbnail: '/api/placeholder/300/200',
    tags: ['stress', 'travail', 'productivité']
  },
  {
    id: '2',
    title: 'Sommeil Réparateur',
    description: 'Améliorez la qualité de votre sommeil avec des techniques de relaxation et d\'hygiène du sommeil.',
    category: 'sleep',
    difficulty: 'beginner',
    duration: '3 semaines',
    sessionsCount: 9,
    completedSessions: 0,
    rating: 4.9,
    reviews: 892,
    instructor: 'Dr. Jean Martin',
    isEnrolled: false,
    isPremium: true,
    features: ['Audio de relaxation', 'Routine personnalisée', 'Suivi du sommeil'],
    thumbnail: '/api/placeholder/300/200',
    tags: ['sommeil', 'relaxation', 'récupération']
  },
  {
    id: '3',
    title: 'Anxiété et Confiance en Soi',
    description: 'Développez votre confiance et apprenez à gérer l\'anxiété avec des approches cognitivo-comportementales.',
    category: 'anxiety',
    difficulty: 'intermediate',
    duration: '6 semaines',
    sessionsCount: 18,
    completedSessions: 0,
    rating: 4.7,
    reviews: 756,
    instructor: 'Sarah Johnson',
    isEnrolled: false,
    isPremium: false,
    features: ['TCC guidée', 'Exercices quotidiens', 'Communauté'],
    thumbnail: '/api/placeholder/300/200',
    tags: ['anxiété', 'confiance', 'TCC']
  },
  {
    id: '4',
    title: 'Mindfulness Avancée',
    description: 'Approfondissez votre pratique de la pleine conscience avec des techniques avancées.',
    category: 'productivity',
    difficulty: 'advanced',
    duration: '8 semaines',
    sessionsCount: 24,
    completedSessions: 0,
    rating: 4.9,
    reviews: 423,
    instructor: 'Lama Tenzin',
    isEnrolled: false,
    isPremium: true,
    features: ['Méditation avancée', 'Retraites virtuelles', 'Mentoring'],
    thumbnail: '/api/placeholder/300/200',
    tags: ['mindfulness', 'méditation', 'spiritualité']
  }
];

const ProgramCard: React.FC<{ program: Program }> = ({ program }) => {
  const getCategoryConfig = () => {
    switch (program.category) {
      case 'stress':
        return { icon: Brain, color: 'text-red-600', bgColor: 'bg-red-100' };
      case 'anxiety':
        return { icon: Heart, color: 'text-purple-600', bgColor: 'bg-purple-100' };
      case 'sleep':
        return { icon: Moon, color: 'text-blue-600', bgColor: 'bg-blue-100' };
      case 'productivity':
        return { icon: Zap, color: 'text-green-600', bgColor: 'bg-green-100' };
      case 'relationships':
        return { icon: User, color: 'text-pink-600', bgColor: 'bg-pink-100' };
      default:
        return { icon: Target, color: 'text-gray-600', bgColor: 'bg-gray-100' };
    }
  };

  const getDifficultyColor = () => {
    switch (program.difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-700';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-700';
      case 'advanced':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const { icon: CategoryIcon, color, bgColor } = getCategoryConfig();
  const progress = Math.round((program.completedSessions / program.sessionsCount) * 100);

  return (
    <Card className="hover:shadow-lg transition-all duration-200 group">
      <div className="relative">
        <div className="h-48 bg-gradient-to-br from-blue-100 to-purple-100 rounded-t-lg flex items-center justify-center">
          <div className={`p-4 rounded-full ${bgColor}`}>
            <CategoryIcon className={`w-12 h-12 ${color}`} />
          </div>
        </div>
        
        {program.isPremium && (
          <Badge className="absolute top-3 right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
            <Star className="w-3 h-3 mr-1" />
            Premium
          </Badge>
        )}
      </div>

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
              {program.title}
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">par {program.instructor}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4 mt-2">
          <div className="flex items-center space-x-1">
            <Star className="w-4 h-4 text-yellow-500 fill-current" />
            <span className="text-sm font-medium">{program.rating}</span>
            <span className="text-sm text-gray-500">({program.reviews})</span>
          </div>
          
          <Badge variant="outline" className={getDifficultyColor()}>
            {program.difficulty}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-sm text-gray-700 line-clamp-2">
          {program.description}
        </p>

        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>{program.duration}</span>
          </div>
          <div className="flex items-center space-x-1">
            <BookOpen className="w-4 h-4" />
            <span>{program.sessionsCount} sessions</span>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-900">Fonctionnalités:</h4>
          <div className="flex flex-wrap gap-1">
            {program.features.slice(0, 3).map((feature, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {feature}
              </Badge>
            ))}
          </div>
        </div>

        {program.isEnrolled && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Progression</span>
              <span className="font-medium">{progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}

        <div className="flex space-x-2 pt-2">
          {program.isEnrolled ? (
            <>
              <Button className="flex-1" size="sm">
                <Play className="w-4 h-4 mr-2" />
                Continuer
              </Button>
              <Button variant="outline" size="sm">
                <Pause className="w-4 h-4" />
              </Button>
            </>
          ) : (
            <Button className="flex-1" variant="outline" size="sm">
              S'inscrire
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default function ProgramsPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = [
    { value: '', label: 'Toutes les catégories' },
    { value: 'stress', label: 'Gestion du Stress' },
    { value: 'anxiety', label: 'Anxiété' },
    { value: 'sleep', label: 'Sommeil' },
    { value: 'productivity', label: 'Productivité' },
    { value: 'relationships', label: 'Relations' }
  ];

  const difficulties = [
    { value: '', label: 'Tous niveaux' },
    { value: 'beginner', label: 'Débutant' },
    { value: 'intermediate', label: 'Intermédiaire' },
    { value: 'advanced', label: 'Avancé' }
  ];

  const filteredPrograms = mockPrograms.filter(program => {
    const matchesCategory = !selectedCategory || program.category === selectedCategory;
    const matchesDifficulty = !selectedDifficulty || program.difficulty === selectedDifficulty;
    const matchesSearch = !searchTerm || 
      program.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      program.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      program.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesCategory && matchesDifficulty && matchesSearch;
  });

  const enrolledPrograms = mockPrograms.filter(p => p.isEnrolled);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* En-tête */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Programmes Guidés</h1>
            <p className="text-gray-600 mt-1">
              Découvrez nos programmes structurés pour améliorer votre bien-être mental
            </p>
          </div>
          
          <Button variant="outline">
            <Calendar className="w-4 h-4 mr-2" />
            Mon planning
          </Button>
        </div>

        {/* Programmes en cours */}
        {enrolledPrograms.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <h2 className="text-xl font-semibold text-gray-900">
                Mes Programmes en Cours
              </h2>
              <Badge variant="outline">{enrolledPrograms.length}</Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {enrolledPrograms.map(program => (
                <ProgramCard key={`enrolled-${program.id}`} program={program} />
              ))}
            </div>
          </div>
        )}

        {/* Filtres et recherche */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Rechercher un programme..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="flex gap-4">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
                
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {difficulties.map(difficulty => (
                    <option key={difficulty.value} value={difficulty.value}>
                      {difficulty.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Programmes disponibles */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Programmes Disponibles
            </h2>
            <Badge variant="outline">
              {filteredPrograms.length} programme{filteredPrograms.length > 1 ? 's' : ''}
            </Badge>
          </div>

          {filteredPrograms.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPrograms.map(program => (
                <ProgramCard key={program.id} program={program} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Filter className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucun programme trouvé
                </h3>
                <p className="text-gray-600">
                  Essayez d'ajuster vos critères de recherche ou de filtrage.
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => {
                    setSelectedCategory('');
                    setSelectedDifficulty('');
                    setSearchTerm('');
                  }}
                >
                  Réinitialiser les filtres
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Call to action */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-100 rounded-full">
                <Target className="w-6 h-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">
                  Besoin d'aide pour choisir ?
                </h3>
                <p className="text-gray-600 text-sm mt-1">
                  Répondez à notre questionnaire personnalisé pour obtenir des recommandations adaptées à vos besoins.
                </p>
              </div>
              <Button>
                Questionnaire
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
} 