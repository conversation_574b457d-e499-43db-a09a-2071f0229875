'use client';

import { useState, useEffect } from 'react';

export default function TestActivationSupabase() {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [phase, setPhase] = useState(1);

  useEffect(() => {
    runTests();
  }, []);

  const runTests = async () => {
    const testResults: any[] = [];
    
    // Test 1: Variables d'environnement
    try {
      const envVars = {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        useAuth: process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH,
        useDatabase: process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE,
        dualMode: process.env.NEXT_PUBLIC_DUAL_DATABASE_MODE
      };

      testResults.push({
        test: 'Variables d\'environnement',
        status: envVars.supabaseUrl ? 'success' : 'error',
        message: envVars.supabaseUrl ? '✅ Variables configurées' : '❌ Variables manquantes',
        details: envVars
      });

      // Déterminer la phase actuelle
      if (envVars.useAuth === 'true' && envVars.useDatabase === 'true') {
        setPhase(3);
      } else if (envVars.useAuth === 'true') {
        setPhase(2);
      } else {
        setPhase(1);
      }

    } catch (error) {
      testResults.push({
        test: 'Variables d\'environnement',
        status: 'error',
        message: '❌ Erreur lecture variables',
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      });
    }

    // Test 2: Connectivité Supabase avec nouvelles clés
    try {
      const response = await fetch('https://kvdrukmoxetoiojazukf.supabase.co/rest/v1/', {
        headers: {
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
        }
      });

      testResults.push({
        test: 'Connectivité Supabase',
        status: response.ok ? 'success' : 'error',
        message: response.ok ? '✅ API Supabase accessible' : `❌ Erreur HTTP ${response.status}`,
        details: {
          status: response.status,
          url: 'https://kvdrukmoxetoiojazukf.supabase.co'
        }
      });
    } catch (error) {
      testResults.push({
        test: 'Connectivité Supabase',
        status: 'error',
        message: '❌ Erreur réseau',
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      });
    }

    // Test 3: Client Supabase
    try {
      const { createBrowserClient } = await import('@supabase/ssr');
      
      const client = createBrowserClient(
        'https://kvdrukmoxetoiojazukf.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
      );

      const { data, error } = await client.auth.getSession();
      
      testResults.push({
        test: 'Client Supabase',
        status: error ? 'warning' : 'success',
        message: error ? `⚠️ Client créé mais: ${error.message}` : '✅ Client Supabase opérationnel',
        details: {
          client_created: true,
          session: data.session ? 'Active' : 'Aucune session'
        }
      });
    } catch (error) {
      testResults.push({
        test: 'Client Supabase',
        status: 'error',
        message: '❌ Erreur création client',
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      });
    }

    // Test 4: Test d'écriture simple (si Phase 2+)
    if (phase >= 2) {
      try {
        const { createBrowserClient } = await import('@supabase/ssr');
        const client = createBrowserClient(
          'https://kvdrukmoxetoiojazukf.supabase.co',
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
        );

        // Test de lecture des tables
        const { data, error } = await client
          .from('users')
          .select('id')
          .limit(1);

        testResults.push({
          test: 'Test base de données',
          status: error ? 'warning' : 'success',
          message: error ? `⚠️ ${error.message}` : '✅ Accès base de données OK',
          details: { data, error }
        });
      } catch (error) {
        testResults.push({
          test: 'Test base de données',
          status: 'error',
          message: '❌ Erreur test BDD',
          error: error instanceof Error ? error.message : 'Erreur inconnue'
        });
      }
    }

    setResults(testResults);
    setLoading(false);
  };

  const getPhaseConfig = (targetPhase: number) => {
    const configs = {
      1: {
        name: 'Phase 1: Tests de connectivité',
        description: 'Validation des nouvelles clés API et connectivité de base',
        env: {
          'NEXT_PUBLIC_USE_SUPABASE_AUTH': 'false',
          'NEXT_PUBLIC_USE_SUPABASE_DATABASE': 'false',
          'NEXT_PUBLIC_DUAL_DATABASE_MODE': 'true'
        }
      },
      2: {
        name: 'Phase 2: Activation authentification',
        description: 'Activation du système d\'authentification Supabase',
        env: {
          'NEXT_PUBLIC_USE_SUPABASE_AUTH': 'true',
          'NEXT_PUBLIC_USE_SUPABASE_DATABASE': 'false',
          'NEXT_PUBLIC_DUAL_DATABASE_MODE': 'true'
        }
      },
      3: {
        name: 'Phase 3: Activation complète',
        description: 'Migration complète vers Supabase',
        env: {
          'NEXT_PUBLIC_USE_SUPABASE_AUTH': 'true',
          'NEXT_PUBLIC_USE_SUPABASE_DATABASE': 'true',
          'NEXT_PUBLIC_DUAL_DATABASE_MODE': 'false'
        }
      }
    };
    return configs[targetPhase];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return '#22c55e';
      case 'error': return '#ef4444';
      case 'warning': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '20px', fontFamily: 'system-ui' }}>
        <h1>🚀 Activation Progressive Supabase</h1>
        <p>Tests en cours...</p>
      </div>
    );
  }

  const successCount = results.filter(r => r.status === 'success').length;
  const errorCount = results.filter(r => r.status === 'error').length;
  const warningCount = results.filter(r => r.status === 'warning').length;

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'system-ui',
      maxWidth: '1200px',
      margin: '0 auto'
    }}>
      <h1 style={{ color: '#1e40af' }}>🚀 Activation Progressive Supabase</h1>
      
      {/* Phase actuelle */}
      <div style={{
        background: '#f0f9ff',
        border: '2px solid #0284c7',
        borderRadius: '8px',
        padding: '20px',
        marginBottom: '20px'
      }}>
        <h2 style={{ margin: '0 0 10px 0', color: '#0284c7' }}>
          📍 {getPhaseConfig(phase)?.name}
        </h2>
        <p style={{ margin: '0', color: '#64748b' }}>
          {getPhaseConfig(phase)?.description}
        </p>
      </div>

      {/* Résumé des tests */}
      <div style={{
        background: successCount === results.length ? '#f0fdf4' : '#fff7ed',
        border: `2px solid ${successCount === results.length ? '#22c55e' : '#f59e0b'}`,
        borderRadius: '8px',
        padding: '20px',
        marginBottom: '20px'
      }}>
        <h2 style={{ margin: '0 0 10px 0', color: '#1f2937' }}>
          🎯 Résumé des Tests
        </h2>
        <div style={{ 
          display: 'flex', 
          gap: '30px',
          fontSize: '18px',
          fontWeight: 'bold'
        }}>
          <span style={{ color: '#22c55e' }}>{successCount} Réussies</span>
          <span style={{ color: '#f59e0b' }}>{warningCount} Avertissements</span>
          <span style={{ color: '#ef4444' }}>{errorCount} Erreurs</span>
        </div>
      </div>

      {/* Configuration des phases */}
      <div style={{ marginBottom: '30px' }}>
        <h2 style={{ color: '#1f2937', marginBottom: '15px' }}>🔧 Configurations des Phases</h2>
        <div style={{ display: 'grid', gap: '15px' }}>
          {[1, 2, 3].map(phaseNum => {
            const config = getPhaseConfig(phaseNum);
            const isActive = phaseNum === phase;
            
            return (
              <div 
                key={phaseNum}
                style={{
                  border: `2px solid ${isActive ? '#0284c7' : '#e5e7eb'}`,
                  borderRadius: '8px',
                  padding: '15px',
                  background: isActive ? '#f0f9ff' : '#f9fafb'
                }}
              >
                <h3 style={{ 
                  margin: '0 0 10px 0',
                  color: isActive ? '#0284c7' : '#6b7280'
                }}>
                  {config?.name} {isActive && '(ACTUELLE)'}
                </h3>
                <p style={{ margin: '0 0 10px 0', color: '#64748b' }}>
                  {config?.description}
                </p>
                <div style={{ fontSize: '14px', fontFamily: 'monospace' }}>
                  {Object.entries(config?.env || {}).map(([key, value]) => (
                    <div key={key} style={{ color: '#4b5563' }}>
                      {key}={value}
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Résultats détaillés */}
      <h2 style={{ color: '#1f2937' }}>📊 Résultats Détaillés</h2>
      {results.map((result, index) => (
        <div 
          key={index}
          style={{
            border: `2px solid ${getStatusColor(result.status)}`,
            borderRadius: '8px',
            padding: '15px',
            marginBottom: '15px',
            background: result.status === 'success' ? '#f0fdf4' : 
                        result.status === 'warning' ? '#fffbeb' : '#fef2f2'
          }}
        >
          <h3 style={{ 
            margin: '0 0 10px 0',
            color: getStatusColor(result.status)
          }}>
            {result.test}
          </h3>
          <p style={{ margin: '0 0 10px 0', fontWeight: 'bold' }}>
            {result.message}
          </p>
          
          {result.details && (
            <details style={{ marginTop: '10px' }}>
              <summary style={{ cursor: 'pointer', color: '#6b7280' }}>
                Détails techniques
              </summary>
              <pre style={{ 
                background: '#f3f4f6',
                padding: '10px',
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto',
                marginTop: '5px'
              }}>
                {JSON.stringify(result.details, null, 2)}
              </pre>
            </details>
          )}
          
          {result.error && (
            <div style={{ 
              marginTop: '10px',
              padding: '10px',
              background: '#fee2e2',
              border: '1px solid #fecaca',
              borderRadius: '4px',
              fontSize: '14px',
              color: '#dc2626'
            }}>
              <strong>Erreur:</strong> {result.error}
            </div>
          )}
        </div>
      ))}

      {/* Instructions de migration */}
      <div style={{
        background: '#f0f9ff',
        border: '2px solid #0284c7',
        borderRadius: '8px',
        padding: '20px',
        marginTop: '30px'
      }}>
        <h2 style={{ margin: '0 0 15px 0', color: '#0284c7' }}>
          📋 Prochaines Étapes
        </h2>
        <div style={{ color: '#64748b' }}>
          {phase === 1 && (
            <div>
              <p><strong>✅ Phase 1 terminée !</strong> Passez à la Phase 2 :</p>
              <p>1. Modifiez .env.local : <code>NEXT_PUBLIC_USE_SUPABASE_AUTH=true</code></p>
              <p>2. Redémarrez le serveur</p>
              <p>3. Testez l'authentification</p>
            </div>
          )}
          {phase === 2 && (
            <div>
              <p><strong>✅ Phase 2 en cours !</strong> Testez l'authentification, puis Phase 3 :</p>
              <p>1. Modifiez .env.local : <code>NEXT_PUBLIC_USE_SUPABASE_DATABASE=true</code></p>
              <p>2. Lancez la migration des données</p>
            </div>
          )}
          {phase === 3 && (
            <div>
              <p><strong>🎉 Phase 3 - Migration complète !</strong></p>
              <p>✅ Supabase entièrement activé</p>
              <p>✅ Prêt pour la production</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 