'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext2';
import { useToast } from '@/contexts/ToastContext';
import { journalService } from '@/services/journalService';

export const dynamic = 'force-dynamic';

interface JournalStats {
  totalEntries: number;
  entriesThisWeek: number;
  entriesThisMonth: number;
  averageMoodLevel: number;
  averageStressLevel: number;
  averageEnergyLevel: number;
  averageSleepQuality: number;
  mostUsedTags: Array<{ tag: string; count: number }>;
  mostUsedEmotions: Array<{ emotion: string; count: number }>;
  entryTypeDistribution: Array<{ type: string; count: number }>;
  moodTrend: Array<{ date: string; moodLevel: number }>;
  streakDays: number;
  longestStreak: number;
}

interface RecentEntry {
  id: string;
  title: string;
  content: string;
  entryType: string;
  moodLevel: number;
  createdAt: string;
  isPrivate: boolean;
  isFavorite: boolean;
}

const MOOD_COLORS = {
  1: 'bg-red-500',
  2: 'bg-orange-500',
  3: 'bg-yellow-500',
  4: 'bg-green-500',
  5: 'bg-blue-500'
};

const MOOD_LABELS = {
  1: 'Very Poor',
  2: 'Poor',
  3: 'Okay',
  4: 'Good',
  5: 'Great'
};

export default function JournalDashboard() {
  const router = useRouter();
  const { user } = useAuth();
  const { showError } = useToast();
  
  const [stats, setStats] = useState<JournalStats | null>(null);
  const [recentEntries, setRecentEntries] = useState<RecentEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }
    
    loadDashboardData();
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load statistics
      const statsResponse = await journalService.getStats();
      if (statsResponse.success) {
        setStats(statsResponse.data.stats);
      }
      
      // Load recent entries
      const entriesResponse = await journalService.getEntries({
        page: 1,
        limit: 5
      });
      
      if (entriesResponse.success) {
        setRecentEntries(entriesResponse.data.entries);
      }
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      showError('Error loading dashboard', 'Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const truncateContent = (content: string, maxLength: number = 100) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your journal dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Journal Dashboard</h1>
              <p className="mt-2 text-gray-600">
                Track your mental health journey and writing progress
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => router.push('/journal/new')}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                New Entry
              </button>
              <button
                onClick={() => router.push('/journal')}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                View All Entries
              </button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">📝</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Entries</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.totalEntries || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">📅</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">This Week</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.entriesThisWeek || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`w-8 h-8 ${MOOD_COLORS[Math.round(stats?.averageMoodLevel || 3) as keyof typeof MOOD_COLORS]} rounded-full flex items-center justify-center`}>
                  <span className="text-white text-sm font-medium">😊</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Average Mood</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.averageMoodLevel ? stats.averageMoodLevel.toFixed(1) : 'N/A'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">🔥</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Current Streak</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.streakDays || 0} days</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Entries */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Recent Entries</h2>
              </div>
              <div className="p-6">
                {recentEntries.length > 0 ? (
                  <div className="space-y-4">
                    {recentEntries.map((entry) => (
                      <div
                        key={entry.id}
                        className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                        onClick={() => router.push(`/journal/edit/${entry.id}`)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h3 className="text-sm font-medium text-gray-900">{entry.title}</h3>
                              <div className={`w-3 h-3 ${MOOD_COLORS[entry.moodLevel as keyof typeof MOOD_COLORS]} rounded-full`}></div>
                              {entry.isFavorite && <span className="text-yellow-500">⭐</span>}
                              {entry.isPrivate && <span className="text-gray-400">🔒</span>}
                            </div>
                            <p className="mt-1 text-sm text-gray-600">
                              {truncateContent(entry.content)}
                            </p>
                            <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                              <span>{formatDate(entry.createdAt)}</span>
                              <span className="capitalize">{entry.entryType}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No journal entries yet.</p>
                    <button
                      onClick={() => router.push('/journal/new')}
                      className="mt-2 text-blue-600 hover:text-blue-700"
                    >
                      Create your first entry
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar Stats */}
          <div className="space-y-6">
            {/* Wellness Metrics */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Wellness Metrics</h3>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Stress Level</span>
                    <span className="font-medium">{stats?.averageStressLevel?.toFixed(1) || 'N/A'}/10</span>
                  </div>
                  <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-red-500 h-2 rounded-full"
                      style={{ width: `${((stats?.averageStressLevel || 0) / 10) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Energy Level</span>
                    <span className="font-medium">{stats?.averageEnergyLevel?.toFixed(1) || 'N/A'}/10</span>
                  </div>
                  <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{ width: `${((stats?.averageEnergyLevel || 0) / 10) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Sleep Quality</span>
                    <span className="font-medium">{stats?.averageSleepQuality?.toFixed(1) || 'N/A'}/10</span>
                  </div>
                  <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${((stats?.averageSleepQuality || 0) / 10) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Most Used Tags */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Most Used Tags</h3>
              <div className="space-y-2">
                {stats?.mostUsedTags?.slice(0, 5).map((tag, index) => (
                  <div key={tag.tag} className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">#{tag.tag}</span>
                    <span className="text-sm font-medium text-gray-900">{tag.count}</span>
                  </div>
                )) || (
                  <p className="text-sm text-gray-500">No tags used yet</p>
                )}
              </div>
            </div>

            {/* Most Used Emotions */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Most Used Emotions</h3>
              <div className="space-y-2">
                {stats?.mostUsedEmotions?.slice(0, 5).map((emotion, index) => (
                  <div key={emotion.emotion} className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 capitalize">{emotion.emotion}</span>
                    <span className="text-sm font-medium text-gray-900">{emotion.count}</span>
                  </div>
                )) || (
                  <p className="text-sm text-gray-500">No emotions tracked yet</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
