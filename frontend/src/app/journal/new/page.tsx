'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useJournalData, CreateJournalEntryData, ENTRY_TYPES, MOOD_LEVELS, COMMON_EMOTIONS, COMMON_TAGS } from '@/hooks/useJournalData';
import { useAuthStore } from '@/stores/authStore';

export default function NewJournalEntryPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuthStore();
  const { createEntry } = useJournalData();
  const router = useRouter();
  
  const [formData, setFormData] = useState<CreateJournalEntryData>({
    title: '',
    content: '',
    entryType: 'daily',
    moodLevel: undefined,
    stressLevel: undefined,
    energyLevel: undefined,
    sleepQuality: undefined,
    gratitudeNotes: '',
    goals: '',
    challenges: '',
    achievements: '',
    isPrivate: false,
    tags: [],
    emotions: [],
  });
  
  const [customEmotion, setCustomEmotion] = useState('');
  const [customTag, setCustomTag] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [authLoading, isAuthenticated, router]);

  const handleInputChange = (field: keyof CreateJournalEntryData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError(null);
  };

  const toggleEmotion = (emotion: string) => {
    const currentEmotions = formData.emotions || [];
    const newEmotions = currentEmotions.includes(emotion) 
      ? currentEmotions.filter(e => e !== emotion)
      : [...currentEmotions, emotion];
    
    handleInputChange('emotions', newEmotions);
  };

  const toggleTag = (tag: string) => {
    const currentTags = formData.tags || [];
    const newTags = currentTags.includes(tag) 
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag];
    
    handleInputChange('tags', newTags);
  };

  const addCustomEmotion = () => {
    if (customEmotion.trim() && !(formData.emotions || []).includes(customEmotion.trim().toLowerCase())) {
      const newEmotions = [...(formData.emotions || []), customEmotion.trim().toLowerCase()];
      handleInputChange('emotions', newEmotions);
      setCustomEmotion('');
    }
  };

  const addCustomTag = () => {
    if (customTag.trim() && !(formData.tags || []).includes(customTag.trim().toLowerCase())) {
      const newTags = [...(formData.tags || []), customTag.trim().toLowerCase()];
      handleInputChange('tags', newTags);
      setCustomTag('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim() || !formData.content.trim()) {
      setError('Le titre et le contenu sont obligatoires');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const success = await createEntry(formData);
      
      if (success) {
        setShowSuccess(true);
        setTimeout(() => {
          router.push('/journal');
        }, 1500);
      } else {
        setError('Échec de la création de l\'entrée du journal');
      }
    } catch (err) {
      console.error('Error creating journal entry:', err);
      setError('Une erreur s\'est produite lors de la création de l\'entrée');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => router.push('/journal')}
                className="text-gray-600 hover:text-gray-900"
                disabled={isSubmitting}
              >
                ← Retour au journal
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">Nouvelle entrée</h1>
            </div>
            {showSuccess && (
              <div className="flex items-center text-green-600">
                <span className="mr-2">✅</span>
                <span className="text-sm font-medium">Entrée créée avec succès !</span>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
              <div className="flex items-center">
                <span className="mr-2">⚠️</span>
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>📝</span>
                <span>Informations de base</span>
              </CardTitle>
              <CardDescription>Commencez par les éléments essentiels de votre entrée</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Entry Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Type d'entrée
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {ENTRY_TYPES.map(type => (
                    <button
                      key={type.value}
                      type="button"
                      onClick={() => handleInputChange('entryType', type.value)}
                      className={`p-3 text-sm rounded-lg border-2 transition-all ${
                        formData.entryType === type.value
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300 text-gray-700'
                      }`}
                    >
                      <div className="text-lg mb-1">{type.icon}</div>
                      <div className="font-medium">{type.label}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Titre <span className="text-red-500">*</span>
                </label>
                <Input
                  type="text"
                  placeholder="Donnez un titre significatif à votre entrée..."
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  maxLength={200}
                  required
                  className="transition-all focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  {formData.title.length}/200 caractères
                </p>
              </div>

              {/* Content */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contenu <span className="text-red-500">*</span>
                </label>
                <textarea
                  placeholder="Partagez vos pensées, sentiments et expériences..."
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  rows={8}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical transition-all"
                  required
                />
              </div>

              {/* Privacy Toggle */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPrivate"
                  checked={formData.isPrivate}
                  onChange={(e) => handleInputChange('isPrivate', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="isPrivate" className="text-sm text-gray-700 flex items-center">
                  <span className="mr-1">🔒</span>
                  Garder cette entrée privée (visible uniquement par vous)
                </label>
              </div>
            </CardContent>
          </Card>

          {/* Mood & Wellness Tracking */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>😊</span>
                <span>Suivi de l'humeur et du bien-être</span>
              </CardTitle>
              <CardDescription>Suivez votre état émotionnel et physique</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Mood Level */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Comment vous sentez-vous globalement ?
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-5 gap-2">
                  {MOOD_LEVELS.map(mood => (
                    <button
                      key={mood.value}
                      type="button"
                      onClick={() => handleInputChange('moodLevel', mood.value)}
                      className={`p-3 text-sm rounded-lg border-2 transition-all ${
                        formData.moodLevel === mood.value
                          ? 'border-blue-500 bg-blue-50 scale-105'
                          : 'border-gray-200 hover:border-gray-300 hover:scale-102'
                      }`}
                    >
                      <div className="text-2xl mb-1">{mood.emoji}</div>
                      <div className={`font-medium ${mood.color}`}>
                        {mood.label}
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Stress, Energy, Sleep Levels */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <span className="flex items-center">
                      <span className="mr-1">😰</span>
                      Niveau de stress (0-10)
                    </span>
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="10"
                    placeholder="0"
                    value={formData.stressLevel || ''}
                    onChange={(e) => handleInputChange('stressLevel', e.target.value ? parseInt(e.target.value) : undefined)}
                    className="transition-all focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">0 = Aucun stress, 10 = Très stressé</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <span className="flex items-center">
                      <span className="mr-1">⚡</span>
                      Niveau d'énergie (0-10)
                    </span>
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="10"
                    placeholder="0"
                    value={formData.energyLevel || ''}
                    onChange={(e) => handleInputChange('energyLevel', e.target.value ? parseInt(e.target.value) : undefined)}
                    className="transition-all focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">0 = Pas d'énergie, 10 = Très énergique</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <span className="flex items-center">
                      <span className="mr-1">😴</span>
                      Qualité du sommeil (0-10)
                    </span>
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="10"
                    placeholder="0"
                    value={formData.sleepQuality || ''}
                    onChange={(e) => handleInputChange('sleepQuality', e.target.value ? parseInt(e.target.value) : undefined)}
                    className="transition-all focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">0 = Mauvais sommeil, 10 = Excellent sommeil</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Emotions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>💭</span>
                <span>Émotions</span>
              </CardTitle>
              <CardDescription>Sélectionnez les émotions que vous ressentez</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {COMMON_EMOTIONS.map(emotion => (
                  <button
                    key={emotion}
                    type="button"
                    onClick={() => toggleEmotion(emotion)}
                    className={`px-3 py-1 text-sm rounded-full border transition-all ${
                      (formData.emotions || []).includes(emotion)
                        ? 'bg-blue-100 border-blue-500 text-blue-700 scale-105'
                        : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {emotion}
                  </button>
                ))}
              </div>
              
              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder="Ajouter une émotion personnalisée..."
                  value={customEmotion}
                  onChange={(e) => setCustomEmotion(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addCustomEmotion())}
                  className="transition-all focus:ring-2 focus:ring-blue-500"
                />
                <Button type="button" onClick={addCustomEmotion} variant="outline">
                  Ajouter
                </Button>
              </div>

              {/* Selected emotions display */}
              {(formData.emotions || []).length > 0 && (
                <div className="mt-3">
                  <p className="text-sm font-medium text-gray-700 mb-2">Émotions sélectionnées :</p>
                  <div className="flex flex-wrap gap-1">
                    {(formData.emotions || []).map((emotion, index) => (
                      <span key={index} className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">
                        {emotion}
                        <button
                          type="button"
                          onClick={() => toggleEmotion(emotion)}
                          className="ml-1 text-blue-500 hover:text-blue-700"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tags */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>🏷️</span>
                <span>Tags</span>
              </CardTitle>
              <CardDescription>Étiquetez votre entrée pour une organisation facile</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {COMMON_TAGS.map(tag => (
                  <button
                    key={tag}
                    type="button"
                    onClick={() => toggleTag(tag)}
                    className={`px-3 py-1 text-sm rounded-full border transition-all ${
                      (formData.tags || []).includes(tag)
                        ? 'bg-green-100 border-green-500 text-green-700 scale-105'
                        : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    #{tag}
                  </button>
                ))}
              </div>
              
              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder="Ajouter un tag personnalisé..."
                  value={customTag}
                  onChange={(e) => setCustomTag(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addCustomTag())}
                  className="transition-all focus:ring-2 focus:ring-blue-500"
                />
                <Button type="button" onClick={addCustomTag} variant="outline">
                  Ajouter
                </Button>
              </div>

              {/* Selected tags display */}
              {(formData.tags || []).length > 0 && (
                <div className="mt-3">
                  <p className="text-sm font-medium text-gray-700 mb-2">Tags sélectionnés :</p>
                  <div className="flex flex-wrap gap-1">
                    {(formData.tags || []).map((tag, index) => (
                      <span key={index} className="inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                        #{tag}
                        <button
                          type="button"
                          onClick={() => toggleTag(tag)}
                          className="ml-1 text-green-500 hover:text-green-700"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Additional Reflections */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>✨</span>
                <span>Réflexions supplémentaires</span>
              </CardTitle>
              <CardDescription>Champs optionnels pour une auto-réflexion plus profonde</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <span className="flex items-center">
                    <span className="mr-1">🙏</span>
                    Notes de gratitude
                  </span>
                </label>
                <textarea
                  value={formData.gratitudeNotes}
                  onChange={(e) => handleInputChange('gratitudeNotes', e.target.value)}
                  placeholder="Pour quoi êtes-vous reconnaissant aujourd'hui ?"
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical transition-all"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <span className="flex items-center">
                    <span className="mr-1">🎯</span>
                    Objectifs
                  </span>
                </label>
                <textarea
                  value={formData.goals}
                  onChange={(e) => handleInputChange('goals', e.target.value)}
                  placeholder="Quels objectifs poursuivez-vous ?"
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical transition-all"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <span className="flex items-center">
                    <span className="mr-1">⚠️</span>
                    Défis
                  </span>
                </label>
                <textarea
                  value={formData.challenges}
                  onChange={(e) => handleInputChange('challenges', e.target.value)}
                  placeholder="Quels défis rencontrez-vous ?"
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical transition-all"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <span className="flex items-center">
                    <span className="mr-1">🏆</span>
                    Réalisations
                  </span>
                </label>
                <textarea
                  value={formData.achievements}
                  onChange={(e) => handleInputChange('achievements', e.target.value)}
                  placeholder="Qu'avez-vous accompli récemment ?"
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical transition-all"
                />
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/journal')}
              disabled={isSubmitting}
              className="px-6"
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.title.trim() || !formData.content.trim()}
              className="px-6 bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Enregistrement...
                </span>
              ) : (
                'Enregistrer l\'entrée'
              )}
            </Button>
          </div>
        </form>

        {/* Professional Disclaimer */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <span className="text-blue-600 text-lg">ℹ️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Avertissement professionnel
              </h3>
              <p className="mt-1 text-sm text-blue-700">
                Ce journal est un outil de bien-être personnel et ne remplace pas un conseil médical professionnel. 
                Si vous ressentez une détresse sévère, veuillez consulter un professionnel de la santé mentale.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 