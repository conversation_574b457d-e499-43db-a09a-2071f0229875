'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext2';
import { useToast } from '@/contexts/ToastContext';
import { apiService } from '@/services/api';

export const dynamic = 'force-dynamic';

const ENTRY_TYPES = [
  { value: 'daily', label: 'Daily Reflection' },
  { value: 'gratitude', label: 'Gratitude' },
  { value: 'goal_setting', label: 'Goal Setting' },
  { value: 'stress_tracking', label: 'Stress Tracking' },
  { value: 'mood_tracking', label: 'Mood Tracking' },
  { value: 'reflection', label: 'General Reflection' },
];

const MOOD_LEVELS = [
  { value: 1, label: 'Very Low 😢', color: 'text-red-600' },
  { value: 2, label: 'Low 😔', color: 'text-orange-600' },
  { value: 3, label: 'Neutral 😐', color: 'text-yellow-600' },
  { value: 4, label: 'Good 🙂', color: 'text-green-600' },
  { value: 5, label: 'Excellent 😊', color: 'text-green-700' },
];

const COMMON_EMOTIONS = [
  'happy', 'sad', 'anxious', 'excited', 'frustrated', 'calm', 'overwhelmed',
  'grateful', 'hopeful', 'worried', 'content', 'lonely', 'confident', 'stressed',
  'peaceful', 'motivated', 'tired', 'energetic', 'confused', 'proud'
];

const COMMON_TAGS = [
  'work', 'family', 'friends', 'health', 'exercise', 'sleep', 'therapy',
  'medication', 'goals', 'achievements', 'challenges', 'self-care', 'relationships',
  'mindfulness', 'meditation', 'stress', 'anxiety', 'depression'
];

export default function NewJournalEntryPage() {
  const { user, isAuthenticated } = useAuth();
  const { showSuccess, showError } = useToast();
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    entryType: 'daily',
    moodLevel: undefined as number | undefined,
    stressLevel: undefined as number | undefined,
    energyLevel: undefined as number | undefined,
    sleepQuality: undefined as number | undefined,
    gratitudeNotes: '',
    goals: '',
    challenges: '',
    achievements: '',
    isPrivate: false,
  });
  
  const [selectedEmotions, setSelectedEmotions] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [customEmotion, setCustomEmotion] = useState('');
  const [customTag, setCustomTag] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  const handleInputChange = (field: string, value: string | number | boolean | undefined) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const toggleEmotion = (emotion: string) => {
    setSelectedEmotions(prev => 
      prev.includes(emotion) 
        ? prev.filter(e => e !== emotion)
        : [...prev, emotion]
    );
  };

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const addCustomEmotion = () => {
    if (customEmotion.trim() && !selectedEmotions.includes(customEmotion.trim().toLowerCase())) {
      setSelectedEmotions(prev => [...prev, customEmotion.trim().toLowerCase()]);
      setCustomEmotion('');
    }
  };

  const addCustomTag = () => {
    if (customTag.trim() && !selectedTags.includes(customTag.trim().toLowerCase())) {
      setSelectedTags(prev => [...prev, customTag.trim().toLowerCase()]);
      setCustomTag('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim() || !formData.content.trim()) {
      setError('Title and content are required');
      showError('Validation Error', 'Title and content are required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const entryData = {
        ...formData,
        emotions: selectedEmotions,
        tags: selectedTags,
      };

      await apiService.createJournalEntry(entryData);
      showSuccess('Success', 'Journal entry created successfully!');
      router.push('/journal');
    } catch (err: unknown) {
      const errorMessage = (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to create journal entry';
      setError(errorMessage);
      showError('Error', errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => router.push('/dashboard')}
                className="text-gray-600 hover:text-gray-900"
              >
                ← Back to Dashboard
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">New Journal Entry</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
              {error}
            </div>
          )}

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>Start with the basics of your journal entry</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Entry Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Entry Type
                </label>
                <select
                  value={formData.entryType}
                  onChange={(e) => handleInputChange('entryType', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {ENTRY_TYPES.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Title *
                </label>
                <Input
                  type="text"
                  placeholder="Give your entry a meaningful title..."
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  maxLength={200}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  {formData.title.length}/200 characters
                </p>
              </div>

              {/* Content */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content *
                </label>
                <textarea
                  placeholder="Write about your thoughts, feelings, experiences..."
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  rows={8}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
                  required
                />
              </div>

              {/* Privacy Toggle */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPrivate"
                  checked={formData.isPrivate}
                  onChange={(e) => handleInputChange('isPrivate', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="isPrivate" className="text-sm text-gray-700">
                  Keep this entry private (only visible to you)
                </label>
              </div>
            </CardContent>
          </Card>

          {/* Mood & Wellness Tracking */}
          <Card>
            <CardHeader>
              <CardTitle>Mood & Wellness Tracking</CardTitle>
              <CardDescription>Track your emotional and physical state</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Mood Level */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  How are you feeling overall?
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-5 gap-2">
                  {MOOD_LEVELS.map(mood => (
                    <button
                      key={mood.value}
                      type="button"
                      onClick={() => handleInputChange('moodLevel', mood.value)}
                      className={`p-3 text-sm rounded-lg border-2 transition-colors ${
                        formData.moodLevel === mood.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className={`font-medium ${mood.color}`}>
                        {mood.label}
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Stress, Energy, Sleep Levels */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stress Level (0-10)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="10"
                    placeholder="0"
                    value={formData.stressLevel || ''}
                    onChange={(e) => handleInputChange('stressLevel', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                  <p className="text-xs text-gray-500 mt-1">0 = No stress, 10 = Extremely stressed</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Energy Level (0-10)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="10"
                    placeholder="0"
                    value={formData.energyLevel || ''}
                    onChange={(e) => handleInputChange('energyLevel', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                  <p className="text-xs text-gray-500 mt-1">0 = No energy, 10 = Very energetic</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sleep Quality (0-10)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="10"
                    placeholder="0"
                    value={formData.sleepQuality || ''}
                    onChange={(e) => handleInputChange('sleepQuality', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                  <p className="text-xs text-gray-500 mt-1">0 = Poor sleep, 10 = Excellent sleep</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Emotions */}
          <Card>
            <CardHeader>
              <CardTitle>Emotions</CardTitle>
              <CardDescription>Select the emotions you&apos;re experiencing</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {COMMON_EMOTIONS.map(emotion => (
                  <button
                    key={emotion}
                    type="button"
                    onClick={() => toggleEmotion(emotion)}
                    className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                      selectedEmotions.includes(emotion)
                        ? 'bg-blue-100 border-blue-500 text-blue-700'
                        : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {emotion}
                  </button>
                ))}
              </div>
              
              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder="Add custom emotion..."
                  value={customEmotion}
                  onChange={(e) => setCustomEmotion(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addCustomEmotion())}
                />
                <Button type="button" onClick={addCustomEmotion} variant="outline">
                  Add
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Tags */}
          <Card>
            <CardHeader>
              <CardTitle>Tags</CardTitle>
              <CardDescription>Tag your entry for easy organization</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {COMMON_TAGS.map(tag => (
                  <button
                    key={tag}
                    type="button"
                    onClick={() => toggleTag(tag)}
                    className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                      selectedTags.includes(tag)
                        ? 'bg-green-100 border-green-500 text-green-700'
                        : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    #{tag}
                  </button>
                ))}
              </div>
              
              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder="Add custom tag..."
                  value={customTag}
                  onChange={(e) => setCustomTag(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addCustomTag())}
                />
                <Button type="button" onClick={addCustomTag} variant="outline">
                  Add
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Additional Reflections */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Reflections</CardTitle>
              <CardDescription>Optional fields for deeper self-reflection</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gratitude Notes
                </label>
                <textarea
                  value={formData.gratitudeNotes}
                  onChange={(e) => handleInputChange('gratitudeNotes', e.target.value)}
                  placeholder="What are you grateful for today?"
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Goals
                </label>
                <textarea
                  value={formData.goals}
                  onChange={(e) => handleInputChange('goals', e.target.value)}
                  placeholder="What goals are you working towards?"
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Challenges
                </label>
                <textarea
                  value={formData.challenges}
                  onChange={(e) => handleInputChange('challenges', e.target.value)}
                  placeholder="What challenges are you facing?"
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Achievements
                </label>
                <textarea
                  value={formData.achievements}
                  onChange={(e) => handleInputChange('achievements', e.target.value)}
                  placeholder="What have you accomplished recently?"
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
                />
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/journal')}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.title.trim() || !formData.content.trim()}
            >
              {isSubmitting ? 'Saving...' : 'Save Entry'}
            </Button>
          </div>
        </form>

        {/* Professional Disclaimer */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <span className="text-blue-600 text-lg">ℹ️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Professional Disclaimer
              </h3>
              <p className="mt-1 text-sm text-blue-700">
                Your journal entries are private and secure. However, if you&apos;re experiencing thoughts of self-harm
                or a mental health crisis, please reach out to a mental health professional immediately.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
