'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useJournalData } from '@/hooks/useJournalData';
import { useAuthStore } from '@/stores/authStore';

export default function JournalPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuthStore();
  const {
    entries,
    stats,
    loading,
    error,
    filters,
    setFilters,
    toggleFavorite,
    deleteEntry,
    getEntryTypeLabel,
    getMoodEmoji,
    formatDate,
    truncateContent,
    ENTRY_TYPES,
    MOOD_LEVELS,
  } = useJournalData();
  
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const entriesPerPage = 10;

  // Redirect if not authenticated
  if (!authLoading && !isAuthenticated) {
    router.push('/auth/login');
    return null;
  }

  // Pagination
  const totalPages = Math.ceil(entries.length / entriesPerPage);
  const startIndex = (currentPage - 1) * entriesPerPage;
  const paginatedEntries = entries.slice(startIndex, startIndex + entriesPerPage);

  const handleSearch = (searchTerm: string) => {
    setFilters({ ...filters, searchTerm });
    setCurrentPage(1);
  };

  const handleFilterChange = (field: string, value: any) => {
    setFilters({ ...filters, [field]: value });
    setCurrentPage(1);
  };

  const handleDeleteEntry = async (id: string) => {
    if (showDeleteConfirm === id) {
      const success = await deleteEntry(id);
      if (success) {
        setShowDeleteConfirm(null);
      }
    } else {
      setShowDeleteConfirm(id);
    }
  };

  const handleToggleFavorite = async (id: string) => {
    await toggleFavorite(id);
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => router.push('/dashboard')}
                className="text-gray-600 hover:text-gray-900"
              >
                ← Retour au tableau de bord
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">Mon Journal</h1>
            </div>
            <Button 
              onClick={() => router.push('/journal/new')}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <span className="mr-2">✏️</span>
              Nouvelle entrée
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.totalEntries}</div>
                    <div className="text-blue-100">Entrées totales</div>
                  </div>
                  <div className="text-4xl opacity-80">📝</div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.entriesThisWeek}</div>
                    <div className="text-green-100">Cette semaine</div>
                  </div>
                  <div className="text-4xl opacity-80">📅</div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.streakDays}</div>
                    <div className="text-purple-100">Jours consécutifs</div>
                  </div>
                  <div className="text-4xl opacity-80">🔥</div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center text-3xl font-bold">
                      {stats.averageMoodLevel > 0 ? (
                        <>
                          {getMoodEmoji(Math.round(stats.averageMoodLevel))}
                          <span className="ml-2">{stats.averageMoodLevel.toFixed(1)}</span>
                        </>
                      ) : (
                        'N/A'
                      )}
                    </div>
                    <div className="text-orange-100">Humeur moyenne</div>
                  </div>
                  <div className="text-4xl opacity-80">😊</div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filter */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Search Bar */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    type="text"
                    placeholder="Rechercher par titre, contenu, tags ou émotions..."
                    value={filters.searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="sm:w-48">
                  <select
                    value={filters.entryType}
                    onChange={(e) => handleFilterChange('entryType', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Tous les types</option>
                    {ENTRY_TYPES.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.icon} {type.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Mood Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Filtrer par humeur :
                </label>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => handleFilterChange('moodLevel', undefined)}
                    className={`px-3 py-1 text-sm rounded-full border transition-all ${
                      !filters.moodLevel
                        ? 'bg-blue-100 border-blue-500 text-blue-700'
                        : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Toutes
                  </button>
                  {MOOD_LEVELS.map(mood => (
                    <button
                      key={mood.value}
                      onClick={() => handleFilterChange('moodLevel', mood.value)}
                      className={`px-3 py-1 text-sm rounded-full border transition-all ${
                        filters.moodLevel === mood.value
                          ? 'bg-blue-100 border-blue-500 text-blue-700'
                          : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {mood.emoji} {mood.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Active Filters */}
              {(filters.searchTerm || filters.entryType || filters.moodLevel) && (
                <div className="flex items-center space-x-2 pt-2 border-t">
                  <span className="text-sm text-gray-600">Filtres actifs :</span>
                  {filters.searchTerm && (
                    <span className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">
                      Recherche: "{filters.searchTerm}"
                      <button
                        onClick={() => handleFilterChange('searchTerm', '')}
                        className="ml-1 text-blue-500 hover:text-blue-700"
                      >
                        ×
                      </button>
                    </span>
                  )}
                  {filters.entryType && (
                    <span className="inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                      Type: {getEntryTypeLabel(filters.entryType)}
                      <button
                        onClick={() => handleFilterChange('entryType', '')}
                        className="ml-1 text-green-500 hover:text-green-700"
                      >
                        ×
                      </button>
                    </span>
                  )}
                  {filters.moodLevel && (
                    <span className="inline-flex items-center px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded-full">
                      Humeur: {getMoodEmoji(filters.moodLevel)} {MOOD_LEVELS.find(m => m.value === filters.moodLevel)?.label}
                      <button
                        onClick={() => handleFilterChange('moodLevel', undefined)}
                        className="ml-1 text-orange-500 hover:text-orange-700"
                      >
                        ×
                      </button>
                    </span>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setFilters({ searchTerm: '', entryType: '', tags: [], emotions: [] })}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    Effacer tout
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            <div className="flex items-center">
              <span className="mr-2">⚠️</span>
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Chargement de vos entrées de journal...</p>
          </div>
        ) : paginatedEntries.length === 0 ? (
          /* Empty State */
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {filters.searchTerm || filters.entryType || filters.moodLevel 
                  ? 'Aucune entrée trouvée' 
                  : 'Commencez votre parcours de journaling'
                }
              </h3>
              <p className="text-gray-600 mb-6">
                {filters.searchTerm || filters.entryType || filters.moodLevel
                  ? 'Essayez d\'ajuster vos critères de recherche ou de filtre.'
                  : 'Créez votre première entrée de journal pour commencer à suivre votre parcours de santé mentale.'
                }
              </p>
              {!filters.searchTerm && !filters.entryType && !filters.moodLevel && (
                <Button 
                  onClick={() => router.push('/journal/new')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <span className="mr-2">✏️</span>
                  Écrire votre première entrée
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          /* Journal Entries */
          <div className="space-y-6">
            {paginatedEntries.map((entry) => (
              <Card key={entry.id} className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full flex items-center">
                          {ENTRY_TYPES.find(t => t.value === entry.entryType)?.icon}
                          <span className="ml-1">{getEntryTypeLabel(entry.entryType)}</span>
                        </span>
                        {entry.moodLevel && (
                          <span className="text-lg" title={`Humeur: ${MOOD_LEVELS.find(m => m.value === entry.moodLevel)?.label}`}>
                            {getMoodEmoji(entry.moodLevel)}
                          </span>
                        )}
                        {entry.isPrivate && (
                          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full flex items-center">
                            🔒 Privé
                          </span>
                        )}
                        {entry.isFavorite && (
                          <span className="text-yellow-500 text-lg" title="Favori">⭐</span>
                        )}
                      </div>
                      <CardTitle 
                        className="text-lg cursor-pointer hover:text-blue-600 transition-colors"
                        onClick={() => router.push(`/journal/edit/${entry.id}`)}
                      >
                        {entry.title}
                      </CardTitle>
                      <CardDescription className="text-sm text-gray-500">
                        {formatDate(entry.createdAt)}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleFavorite(entry.id)}
                        className={`${entry.isFavorite ? 'text-yellow-500' : 'text-gray-400'} hover:text-yellow-600`}
                      >
                        ⭐
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/journal/edit/${entry.id}`)}
                        className="text-gray-400 hover:text-blue-600"
                      >
                        ✏️
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteEntry(entry.id)}
                        className={`${showDeleteConfirm === entry.id ? 'text-red-600 bg-red-50' : 'text-gray-400'} hover:text-red-600`}
                      >
                        {showDeleteConfirm === entry.id ? '✓' : '🗑️'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-4 leading-relaxed">
                    {truncateContent(entry.content)}
                  </p>
                  
                  {/* Tags and Emotions */}
                  <div className="space-y-2">
                    {entry.tags && entry.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {entry.tags.slice(0, 5).map((tag, index) => (
                          <span key={index} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                            #{tag}
                          </span>
                        ))}
                        {entry.tags.length > 5 && (
                          <span className="text-xs text-gray-500">+{entry.tags.length - 5} autres</span>
                        )}
                      </div>
                    )}
                    
                    {entry.emotions && entry.emotions.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {entry.emotions.slice(0, 5).map((emotion, index) => (
                          <span key={index} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                            {emotion}
                          </span>
                        ))}
                        {entry.emotions.length > 5 && (
                          <span className="text-xs text-gray-500">+{entry.emotions.length - 5} autres</span>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Wellness Metrics */}
                  {(entry.stressLevel !== undefined || entry.energyLevel !== undefined || entry.sleepQuality !== undefined) && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex space-x-6 text-sm">
                        {entry.stressLevel !== undefined && (
                          <div className="flex items-center space-x-1">
                            <span className="text-gray-600">😰 Stress:</span>
                            <span className={`font-medium ${
                              entry.stressLevel <= 3 ? 'text-green-600' : 
                              entry.stressLevel <= 6 ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {entry.stressLevel}/10
                            </span>
                          </div>
                        )}
                        {entry.energyLevel !== undefined && (
                          <div className="flex items-center space-x-1">
                            <span className="text-gray-600">⚡ Énergie:</span>
                            <span className={`font-medium ${
                              entry.energyLevel >= 7 ? 'text-green-600' : 
                              entry.energyLevel >= 4 ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {entry.energyLevel}/10
                            </span>
                          </div>
                        )}
                        {entry.sleepQuality !== undefined && (
                          <div className="flex items-center space-x-1">
                            <span className="text-gray-600">😴 Sommeil:</span>
                            <span className={`font-medium ${
                              entry.sleepQuality >= 7 ? 'text-green-600' : 
                              entry.sleepQuality >= 4 ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {entry.sleepQuality}/10
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Delete Confirmation */}
                  {showDeleteConfirm === entry.id && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                      <p className="text-sm text-red-700 mb-2">
                        Êtes-vous sûr de vouloir supprimer cette entrée ? Cette action est irréversible.
                      </p>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setShowDeleteConfirm(null)}
                          className="text-gray-600"
                        >
                          Annuler
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleDeleteEntry(entry.id)}
                          className="bg-red-600 hover:bg-red-700 text-white"
                        >
                          Supprimer
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center space-x-4 mt-8">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="flex items-center"
                >
                  ← Précédent
                </Button>
                
                <div className="flex items-center space-x-2">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${
                        currentPage === page
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </div>
                
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="flex items-center"
                >
                  Suivant →
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Summary Stats */}
        {entries.length > 0 && (
          <div className="mt-8 text-center text-sm text-gray-600">
            Affichage de {startIndex + 1}-{Math.min(startIndex + entriesPerPage, entries.length)} sur {entries.length} entrées
          </div>
        )}
      </main>
    </div>
  );
}
