'use client';

import React, { useState } from 'react';
import { 
  <PERSON><PERSON>,
  Calculator,
  Brain,
  BookOpen,
  Users,
  Package,
  Settings,
  Play,
  Pause,
  Square,
  FileText,
  Activity,
  Clock,
  Star
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function ToolsPage() {
  const [activeTab, setActiveTab] = useState('voice-notes');
  const [isRecording, setIsRecording] = useState(false);

  const voiceNotes = [
    {
      id: 'vn-001',
      patientName: '<PERSON>',
      duration: 180,
      timestamp: new Date().toISOString(),
      transcription: 'Patient présente des symptômes de fatigue chronique depuis 3 semaines.',
      confidence: 94,
      status: 'completed'
    },
    {
      id: 'vn-002',
      patientName: '<PERSON>',
      duration: 240,
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      transcription: 'Suivi post-opératoire. Cicatrisation normale.',
      confidence: 96,
      status: 'completed'
    }
  ];

  const calculators = [
    {
      id: 'calc-bmi',
      name: 'Calcul IMC',
      category: 'Anthropométrie',
      usage: 95,
      accuracy: 100
    },
    {
      id: 'calc-dosage',
      name: 'Calcul de Dosage',
      category: 'Pharmacologie',
      usage: 87,
      accuracy: 98
    }
  ];

  const startRecording = () => {
    setIsRecording(true);
    setTimeout(() => setIsRecording(false), 3000);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'voice-notes':
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Mic className="h-5 w-5" />
                  Assistant de Notes Cliniques Vocal → Texte
                </span>
                <Button 
                  onClick={startRecording}
                  disabled={isRecording}
                  className={isRecording ? 'bg-red-600 hover:bg-red-700' : ''}
                >
                  {isRecording ? <Square className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                  {isRecording ? 'Arrêter' : 'Enregistrer'}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {voiceNotes.map((note) => (
                  <div key={note.id} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium">{note.patientName}</h4>
                        <p className="text-sm text-gray-500">
                          {new Date(note.timestamp).toLocaleString('fr-FR')} • {note.duration}s
                        </p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">
                        {note.confidence}% confiance
                      </Badge>
                    </div>
                    <p className="text-sm">{note.transcription}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );

      case 'calculators':
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Calculatrices Médicales
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {calculators.map((calc) => (
                  <div key={calc.id} className="p-4 border rounded-lg hover:bg-gray-50">
                    <h4 className="font-medium">{calc.name}</h4>
                    <p className="text-sm text-gray-600">{calc.category}</p>
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-sm">Usage: {calc.usage}%</span>
                      <Button size="sm">Utiliser</Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );

      default:
        return (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-gray-600">Contenu à venir pour cet onglet</p>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Outils & Productivité</h1>
          <p className="text-gray-600 mt-1">Suite complète d'outils professionnels</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Paramètres
          </Button>
        </div>
      </div>

      {/* Onglets */}
      <div className="flex gap-4 border-b">
        {[
          { id: 'voice-notes', label: 'Notes Vocales', icon: Mic },
          { id: 'calculators', label: 'Calculatrices', icon: Calculator },
          { id: 'ai-recommendations', label: 'IA Recommandations', icon: Brain },
          { id: 'research', label: 'Recherche', icon: BookOpen },
          { id: 'team', label: 'Équipe', icon: Users },
          { id: 'inventory', label: 'Inventaire', icon: Package }
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id)}
            className={`flex items-center gap-2 px-4 py-2 border-b-2 transition-colors ${
              activeTab === id 
                ? 'border-blue-500 text-blue-600' 
                : 'border-transparent text-gray-600 hover:text-gray-800'
            }`}
          >
            <Icon className="h-4 w-4" />
            {label}
          </button>
        ))}
      </div>

      {/* Contenu de l'onglet */}
      {renderTabContent()}
    </div>
  );
}