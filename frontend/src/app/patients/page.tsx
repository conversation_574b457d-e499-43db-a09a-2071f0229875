'use client';

import React, { useState } from 'react';
import { 
  Users, 
  Search, 
  Filter, 
  Plus, 
  MoreVertical,
  AlertTriangle,
  Calendar,
  FileText,
  Pill,
  Activity,
  Heart,
  ChevronRight,
  Star,
  Phone,
  Mail,
  MapPin,
  Eye,
  Edit,
  Download,
  Brain
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

interface Patient {
  id: string;
  name: string;
  age: number;
  gender: 'male' | 'female' | 'other';
  email: string;
  phone: string;
  address: string;
  avatar?: string;
  condition: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'active' | 'inactive' | 'completed';
  lastVisit: string;
  nextAppointment?: string;
  riskScore: number;
  compliance: number;
  satisfactionScore: number;
  vitalSigns: {
    bloodPressure: string;
    heartRate: number;
    temperature: number;
    weight: number;
    bmi: number;
  };
  medications: Array<{
    name: string;
    dosage: string;
    frequency: string;
    startDate: string;
    endDate?: string;
  }>;
  diagnoses: Array<{
    code: string;
    description: string;
    date: string;
    severity: 'mild' | 'moderate' | 'severe';
  }>;
  allergies: string[];
  insurance: {
    provider: string;
    policyNumber: string;
    validUntil: string;
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };
  notes: string;
  aiInsights: Array<{
    type: string;
    description: string;
    confidence: number;
    date: string;
  }>;
}

const mockPatients: Patient[] = [
  {
    id: '1',
    name: 'Marie Dubois',
    age: 34,
    gender: 'female',
    email: '<EMAIL>',
    phone: '+33 6 12 34 56 78',
    address: '15 rue de la Paix, 75001 Paris',
    condition: 'Anxiété généralisée',
    priority: 'medium',
    status: 'active',
    lastVisit: '2024-01-15',
    nextAppointment: '2024-01-22 14:00',
    riskScore: 65,
    compliance: 88,
    satisfactionScore: 4.5,
    vitalSigns: {
      bloodPressure: '120/80',
      heartRate: 72,
      temperature: 36.5,
      weight: 68,
      bmi: 22.5
    },
    medications: [
      { name: 'Sertraline', dosage: '50mg', frequency: '1x/jour', startDate: '2023-10-15' },
      { name: 'Lorazépam', dosage: '0.5mg', frequency: 'Au besoin', startDate: '2024-01-01' }
    ],
    diagnoses: [
      { code: 'F41.1', description: 'Trouble anxieux généralisé', date: '2023-10-15', severity: 'moderate' }
    ],
    allergies: ['Pénicilline'],
    insurance: {
      provider: 'CPAM',
      policyNumber: '**********',
      validUntil: '2024-12-31'
    },
    emergencyContact: {
      name: 'Pierre Dubois',
      relationship: 'Époux',
      phone: '+33 6 98 76 54 32'
    },
    notes: 'Patiente très investie dans son traitement. Progrès notables depuis début du suivi.',
    aiInsights: [
      {
        type: 'treatment',
        description: 'Réduction progressive du Lorazépam recommandée',
        confidence: 78,
        date: '2024-01-15'
      }
    ]
  },
  {
    id: '2',
    name: 'Jean Martin',
    age: 45,
    gender: 'male',
    email: '<EMAIL>',
    phone: '+33 6 87 65 43 21',
    address: '42 avenue Victor Hugo, 69002 Lyon',
    condition: 'Dépression majeure',
    priority: 'high',
    status: 'active',
    lastVisit: '2024-01-12',
    nextAppointment: '2024-01-20 10:30',
    riskScore: 78,
    compliance: 72,
    satisfactionScore: 4.2,
    vitalSigns: {
      bloodPressure: '135/85',
      heartRate: 68,
      temperature: 36.7,
      weight: 82,
      bmi: 26.8
    },
    medications: [
      { name: 'Escitalopram', dosage: '10mg', frequency: '1x/jour', startDate: '2023-11-01' },
      { name: 'Trazodone', dosage: '50mg', frequency: 'Au coucher', startDate: '2023-12-15' }
    ],
    diagnoses: [
      { code: 'F32.2', description: 'Épisode dépressif majeur sévère', date: '2023-11-01', severity: 'severe' }
    ],
    allergies: ['Aspirine'],
    insurance: {
      provider: 'Mutuelle Générale',
      policyNumber: '**********',
      validUntil: '2024-06-30'
    },
    emergencyContact: {
      name: 'Sophie Martin',
      relationship: 'Épouse',
      phone: '+33 6 11 22 33 44'
    },
    notes: 'Suivi rapproché nécessaire. Risque suicidaire à surveiller attentivement.',
    aiInsights: [
      {
        type: 'risk',
        description: 'Marqueurs de risque suicidaire accru détectés',
        confidence: 87,
        date: '2024-01-12'
      }
    ]
  }
];

export default function PatientsPage() {
  const [patients] = useState<Patient[]>(mockPatients);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedPatient, setSelectedPatient] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'cards'>('cards');

  const filteredPatients = patients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         patient.condition.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = selectedFilter === 'all' || 
                         patient.priority === selectedFilter ||
                         patient.status === selectedFilter;
    
    return matchesSearch && matchesFilter;
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskColor = (score: number) => {
    if (score >= 80) return 'text-red-600';
    if (score >= 60) return 'text-orange-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gestion des Patients</h1>
            <p className="text-gray-600 mt-1">
              Système avancé de gestion avec IA intégrée
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Exporter
            </Button>
            
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Nouveau patient
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total patients</p>
                <p className="text-3xl font-bold text-gray-900">{patients.length}</p>
                <p className="text-sm text-green-600">+5 ce mois</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Patients actifs</p>
                <p className="text-3xl font-bold text-gray-900">
                  {patients.filter(p => p.status === 'active').length}
                </p>
                <p className="text-sm text-blue-600">En suivi</p>
              </div>
              <Activity className="h-8 w-8 text-green-600" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Haute priorité</p>
                <p className="text-3xl font-bold text-gray-900">
                  {patients.filter(p => p.priority === 'high' || p.priority === 'urgent').length}
                </p>
                <p className="text-sm text-orange-600">À surveiller</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Satisfaction</p>
                <p className="text-3xl font-bold text-gray-900">4.6/5</p>
                <p className="text-sm text-yellow-600">Moyenne globale</p>
              </div>
              <Star className="h-8 w-8 text-yellow-600" />
            </div>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Rechercher par nom, condition, diagnostic..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tous les patients</option>
              <option value="active">Actifs</option>
              <option value="urgent">Urgents</option>
              <option value="high">Priorité haute</option>
              <option value="medium">Priorité moyenne</option>
              <option value="low">Priorité basse</option>
            </select>

            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filtres avancés
            </Button>

            <div className="flex border rounded-lg">
              <Button
                variant={viewMode === 'cards' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('cards')}
              >
                Cards
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                Liste
              </Button>
            </div>
          </div>
        </Card>

        {/* Patients Grid/List */}
        <div className={`grid gap-6 ${
          viewMode === 'cards' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'
        }`}>
          {filteredPatients.map((patient) => (
            <Card key={patient.id} className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full flex items-center justify-center text-white font-bold">
                    {patient.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{patient.name}</h3>
                    <p className="text-sm text-gray-600">{patient.age} ans • {patient.gender}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Badge className={getPriorityColor(patient.priority)}>
                    {patient.priority}
                  </Badge>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Condition</span>
                  <span className="text-sm font-medium">{patient.condition}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Risque</span>
                  <span className={`text-sm font-medium ${getRiskColor(patient.riskScore)}`}>
                    {patient.riskScore}%
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Compliance</span>
                  <span className="text-sm font-medium text-green-600">{patient.compliance}%</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Dernière visite</span>
                  <span className="text-sm font-medium">{patient.lastVisit}</span>
                </div>
              </div>

              {/* Vital Signs */}
              <div className="grid grid-cols-2 gap-2 mb-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <p className="text-xs text-gray-600">TA</p>
                  <p className="text-sm font-medium">{patient.vitalSigns.bloodPressure}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600">FC</p>
                  <p className="text-sm font-medium">{patient.vitalSigns.heartRate} bpm</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600">Poids</p>
                  <p className="text-sm font-medium">{patient.vitalSigns.weight} kg</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600">IMC</p>
                  <p className="text-sm font-medium">{patient.vitalSigns.bmi}</p>
                </div>
              </div>

              {/* AI Insights */}
              {patient.aiInsights.length > 0 && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Brain className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Insight IA</span>
                  </div>
                  <p className="text-sm text-blue-700">
                    {patient.aiInsights[0].description}
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Confiance: {patient.aiInsights[0].confidence}%
                  </p>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-between">
                <div className="flex space-x-2">
                  <Button variant="ghost" size="sm">
                    <Phone className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Mail className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Calendar className="h-4 w-4" />
                  </Button>
                </div>
                
                <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                  <Eye className="h-4 w-4 mr-1" />
                  Voir détails
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredPatients.length === 0 && (
          <Card className="p-12 text-center">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Aucun patient trouvé
            </h3>
            <p className="text-gray-600">
              Modifiez vos critères de recherche ou ajoutez un nouveau patient
            </p>
          </Card>
        )}
      </div>
    </div>
  );
} 