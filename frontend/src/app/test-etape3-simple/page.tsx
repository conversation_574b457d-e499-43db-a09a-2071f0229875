'use client';

import React, { useState } from 'react';
import { useAICoach } from '@/hooks/useAICoach';
import { useMoodAnalytics } from '@/hooks/useMoodAnalytics';
import { useSmartNotifications } from '@/hooks/useSmartNotifications';

export default function TestEtape3SimplePage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  // Hooks de l'Étape 3
  const aiCoach = useAICoach();
  const moodAnalytics = useMoodAnalytics();
  const notifications = useSmartNotifications();

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, result]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      // Test 1: Hook useAICoach
      addResult('🧪 Test Hook useAICoach...');
      if (aiCoach.sessions && aiCoach.stats) {
        addResult('✅ useAICoach: Hook chargé avec succès');
        addResult(`📊 Sessions: ${aiCoach.sessions.length}, Messages totaux: ${aiCoach.stats.totalMessages}`);
      } else {
        addResult('❌ useAICoach: Échec du chargement');
      }

      // Test 2: Hook useMoodAnalytics
      addResult('🧪 Test Hook useMoodAnalytics...');
      if (moodAnalytics.analytics && moodAnalytics.analytics.insights) {
        addResult('✅ useMoodAnalytics: Hook chargé avec succès');
        addResult(`📈 Points de données: ${moodAnalytics.analytics.chartData.daily.length}, Score bien-être: ${moodAnalytics.getWellnessScore()}`);
      } else {
        addResult('❌ useMoodAnalytics: Échec du chargement');
      }

      // Test 3: Hook useSmartNotifications
      addResult('🧪 Test Hook useSmartNotifications...');
      if (notifications.notifications && notifications.analytics) {
        addResult('✅ useSmartNotifications: Hook chargé avec succès');
        addResult(`🔔 Notifications: ${notifications.notifications.length}, Taux lecture: ${notifications.analytics.readRate}%`);
      } else {
        addResult('❌ useSmartNotifications: Échec du chargement');
      }

      // Test 4: Fonctionnalité IA Coach
      addResult('🧪 Test Fonctionnalité IA Coach...');
      try {
        const session = await aiCoach.startSession('gestion_stress', 'Réduire le stress quotidien');
        if (session) {
          addResult('✅ Création de session IA: Réussie');
          
          const message = await aiCoach.sendMessage('Je me sens un peu stressé aujourd\'hui', 6);
          if (message) {
            addResult('✅ Envoi de message: Réussi');
            addResult(`🤖 Réponse IA: "${message.content.substring(0, 50)}..."`);
          }
        }
      } catch (error) {
        addResult('❌ Test IA Coach: Erreur - ' + (error as Error).message);
      }

      // Test 5: Analytics d'humeur
      addResult('🧪 Test Analytics d\'humeur...');
      try {
        if (moodAnalytics.analytics) {
          const trends = moodAnalytics.getTrendsByDirection('improving');
          const predictions = moodAnalytics.analytics.predictions;
          if (trends && predictions) {
            addResult('✅ Calcul tendances et prédictions: Réussi');
            addResult(`📊 Tendances améliorantes: ${trends.length}, Prédiction demain: ${predictions.nextDay}/10`);
          }
        }
      } catch (error) {
        addResult('❌ Test Analytics: Erreur - ' + (error as Error).message);
      }

      // Test 6: Notifications intelligentes
      addResult('🧪 Test Notifications intelligentes...');
      try {
        const notification = await notifications.createNotification({
          type: 'suggestion',
          title: 'Test de notification',
          message: 'Ceci est un test',
          priority: 'medium'
        });
        if (notification) {
          addResult('✅ Création notification: Réussie');
        }
      } catch (error) {
        addResult('❌ Test Notifications: Erreur - ' + (error as Error).message);
      }

      addResult('🎉 Tests terminés !');
    } catch (error) {
      addResult('❌ Erreur générale: ' + (error as Error).message);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            🧪 Test Étape 3 - IA et Coaching
          </h1>
          <p className="text-gray-600 mb-8">
            Validation des fonctionnalités développées dans l'Étape 3
          </p>

          {/* Bouton de test */}
          <div className="mb-8">
            <button
              onClick={runTests}
              disabled={isRunning}
              className={`px-6 py-3 rounded-lg font-medium transition-all ${
                isRunning
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isRunning ? '🔄 Tests en cours...' : '▶️ Lancer les tests'}
            </button>
          </div>

          {/* Résultats des tests */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">📋 Résultats des tests</h2>
            {testResults.length === 0 ? (
              <p className="text-gray-500 italic">Aucun test exécuté</p>
            ) : (
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div key={index} className="font-mono text-sm">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Informations sur les hooks */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 mb-2">🤖 useAICoach</h3>
              <p className="text-sm text-blue-600">
                Sessions: {aiCoach.sessions?.length || 0}<br/>
                Messages: {aiCoach.stats?.totalMessages || 0}<br/>
                Chargement: {aiCoach.loading ? 'Oui' : 'Non'}
              </p>
            </div>

            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 mb-2">📊 useMoodAnalytics</h3>
              <p className="text-sm text-green-600">
                Données: {moodAnalytics.analytics?.chartData.daily.length || 0}<br/>
                Score: {moodAnalytics.getWellnessScore() || 0}/100<br/>
                Insights: {moodAnalytics.analytics?.insights.length || 0}
              </p>
            </div>

            <div className="bg-purple-50 rounded-lg p-4">
              <h3 className="font-semibold text-purple-800 mb-2">🔔 useSmartNotifications</h3>
              <p className="text-sm text-purple-600">
                Notifications: {notifications.notifications?.length || 0}<br/>
                Taux lecture: {notifications.analytics?.readRate || 0}%<br/>
                Actives: {notifications.notifications?.filter(n => !n.read).length || 0}
              </p>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex flex-wrap gap-4">
              <a href="/" className="text-blue-600 hover:text-blue-800 underline">
                🏠 Accueil
              </a>
              <a href="/test-basic" className="text-blue-600 hover:text-blue-800 underline">
                🔧 Test Basic
              </a>
              <a href="/ai-coach" className="text-blue-600 hover:text-blue-800 underline">
                🤖 IA Coach (nécessite auth)
              </a>
              <a href="/analytics" className="text-blue-600 hover:text-blue-800 underline">
                📊 Analytics (nécessite auth)
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}