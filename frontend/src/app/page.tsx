'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Brain, 
  Heart, 
  Shield, 
  Sparkles, 
  Users, 
  TrendingUp,
  ChevronRight,
  Star,
  Check,
  ArrowRight
} from 'lucide-react';

export default function Home() {
  const router = useRouter();

  const features = [
    {
      icon: <Brain className="w-8 h-8" />,
      title: "Coach IA Personnalisé",
      description: "Un assistant intelligent qui comprend vos besoins et vous guide au quotidien",
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      icon: <Heart className="w-8 h-8" />,
      title: "Suiv<PERSON> d'Hume<PERSON>",
      description: "Visualisez vos émotions et identifiez les tendances pour mieux vous comprendre",
      color: "text-pink-600",
      bgColor: "bg-pink-50"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Confidentialité Garantie",
      description: "Vos données sont protégées et restent strictement confidentielles",
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: <Sparkles className="w-8 h-8" />,
      title: "Exercices Personnalisés",
      description: "Des activités adaptées à votre état émotionnel du moment",
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    }
  ];

  const testimonials = [
    {
      name: "Marie L.",
      role: "Utilisatrice depuis 6 mois",
      content: "MindFlow Pro m'a aidée à mieux gérer mon anxiété. Le coach IA est vraiment à l'écoute.",
      rating: 5
    },
    {
      name: "Thomas D.",
      role: "Professionnel",
      content: "Un outil indispensable pour mon bien-être mental. Les analyses sont très pertinentes.",
      rating: 5
    },
    {
      name: "Sophie M.",
      role: "Étudiante",
      content: "J'adore le suivi d'humeur et les suggestions personnalisées. Ça m'aide vraiment au quotidien.",
      rating: 5
    }
  ];

  const plans = [
    {
      name: "Gratuit",
      price: "0€",
      features: [
        "Suivi d'humeur quotidien",
        "3 sessions Coach IA/mois",
        "Exercices de base",
        "Journal personnel"
      ],
      cta: "Commencer gratuitement",
      popular: false
    },
    {
      name: "Premium",
      price: "9.99€",
      period: "/mois",
      features: [
        "Tout du plan gratuit",
        "Coach IA illimité",
        "Analyses avancées",
        "Programmes personnalisés",
        "Support prioritaire"
      ],
      cta: "Essai gratuit 7 jours",
      popular: true
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-2">
              <Brain className="w-8 h-8 text-purple-600" />
              <span className="text-xl font-bold text-gray-900">MindFlow Pro</span>
            </div>
            <div className="flex items-center gap-4">
              <Button variant="ghost" onClick={() => router.push('/auth/login')}>
                Se connecter
              </Button>
              <Button 
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                onClick={() => router.push('/auth/register')}
              >
                Commencer
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 pb-32 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 bg-purple-100 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-8">
            <Sparkles className="w-4 h-4" />
            Nouveau : Coach IA amélioré avec GPT-4
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Votre bien-être mental,
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent"> simplifié</span>
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            MindFlow Pro combine l'intelligence artificielle et la psychologie positive pour vous accompagner 
            vers un meilleur équilibre émotionnel au quotidien.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg"
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8"
              onClick={() => router.push('/auth/register')}
            >
              Essayer gratuitement
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              onClick={() => router.push('/dashboard')}
            >
              Voir la démo
            </Button>
          </div>

          <div className="mt-12 flex items-center justify-center gap-8 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Check className="w-5 h-5 text-green-600" />
              Sans carte bancaire
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-5 h-5 text-green-600" />
              Annulation à tout moment
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-5 h-5 text-green-600" />
              Données sécurisées
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Tout ce dont vous avez besoin pour votre bien-être
            </h2>
            <p className="text-xl text-gray-600">
              Des outils puissants et intuitifs pour prendre soin de votre santé mentale
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className={`${feature.bgColor} ${feature.color} w-16 h-16 rounded-lg flex items-center justify-center mb-4`}>
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Ils ont transformé leur quotidien
            </h2>
            <p className="text-xl text-gray-600">
              Découvrez comment MindFlow Pro a aidé des milliers de personnes
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg">
                <CardHeader>
                  <div className="flex items-center gap-1 mb-2">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <CardTitle className="text-lg">{testimonial.name}</CardTitle>
                  <CardDescription>{testimonial.role}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 italic">"{testimonial.content}"</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Choisissez votre plan
            </h2>
            <p className="text-xl text-gray-600">
              Commencez gratuitement, évoluez à votre rythme
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {plans.map((plan, index) => (
              <Card 
                key={index} 
                className={`relative border-2 ${plan.popular ? 'border-purple-600 shadow-xl' : 'border-gray-200'}`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Plus populaire
                    </span>
                  </div>
                )}
                <CardHeader className="text-center pt-8">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold">{plan.price}</span>
                    {plan.period && <span className="text-gray-600">{plan.period}</span>}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-3">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-2">
                        <Check className="w-5 h-5 text-green-600 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className={`w-full ${plan.popular ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white' : ''}`}
                    variant={plan.popular ? 'default' : 'outline'}
                    onClick={() => router.push('/auth/register')}
                  >
                    {plan.cta}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center text-white">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Prêt à prendre soin de vous ?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Rejoignez des milliers de personnes qui ont déjà amélioré leur bien-être mental
          </p>
          <Button 
            size="lg" 
            className="bg-white text-purple-600 hover:bg-gray-100"
            onClick={() => router.push('/auth/register')}
          >
            Commencer maintenant
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-gray-900 text-gray-400">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Brain className="w-6 h-6 text-purple-400" />
            <span className="text-lg font-semibold text-white">MindFlow Pro</span>
          </div>
          <p className="text-sm">
            © 2024 MindFlow Pro. Tous droits réservés. | 
            <a href="#" className="hover:text-white ml-2">Confidentialité</a> | 
            <a href="#" className="hover:text-white ml-2">Conditions</a>
          </p>
        </div>
      </footer>
    </div>
  );
}
