'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, XCircle, AlertCircle, Activity, Database, Users, Clock, Zap } from 'lucide-react';

interface ServiceStatus {
  name: string;
  status: 'online' | 'offline' | 'warning';
  responseTime?: number;
  lastCheck: Date;
  icon: any;
}

interface Stats {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  uptime: number;
  avgResponseTime: number;
}

export default function MonitoringDashboard() {
  const [services, setServices] = useState<ServiceStatus[]>([]);
  const [stats, setStats] = useState<Stats | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  // Services à monitorer
  const servicesToCheck = [
    { name: 'Frontend Next.js', url: '/', icon: Zap },
    { name: 'API Supabase', url: '/api/health/supabase', icon: Database },
    { name: 'Auth Service', url: '/auth/login', icon: Users },
    { name: 'Dashboard', url: '/dashboard', icon: Activity }
  ];

  // Vérifier un service
  const checkService = async (service: any): Promise<ServiceStatus> => {
    const startTime = Date.now();
    
    try {
      const response = await fetch(service.url, { 
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      const responseTime = Date.now() - startTime;
      
      return {
        name: service.name,
        status: response.ok ? 'online' : 'warning',
        responseTime,
        lastCheck: new Date(),
        icon: service.icon
      };
    } catch (error) {
      return {
        name: service.name,
        status: 'offline',
        responseTime: undefined,
        lastCheck: new Date(),
        icon: service.icon
      };
    }
  };

  // Vérifier tous les services
  const checkAllServices = async () => {
    addLog('🔍 Vérification des services...');
    
    const results = await Promise.all(
      servicesToCheck.map(service => checkService(service))
    );
    
    setServices(results);
    
    // Calculer les stats
    const online = results.filter(s => s.status === 'online').length;
    const avgTime = results
      .filter(s => s.responseTime)
      .reduce((acc, s) => acc + (s.responseTime || 0), 0) / results.length;
    
    setStats({
      totalTests: (stats?.totalTests || 0) + results.length,
      passedTests: (stats?.passedTests || 0) + online,
      failedTests: (stats?.failedTests || 0) + (results.length - online),
      uptime: (online / results.length) * 100,
      avgResponseTime: avgTime
    });
    
    addLog(`✅ Vérification terminée: ${online}/${results.length} services en ligne`);
  };

  // Ajouter un log
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  // Monitoring automatique
  useEffect(() => {
    if (isMonitoring) {
      checkAllServices();
      const interval = setInterval(checkAllServices, 10000); // Toutes les 10 secondes
      return () => clearInterval(interval);
    }
  }, [isMonitoring]);

  // Check initial
  useEffect(() => {
    checkAllServices();
  }, []);

  // Déterminer la couleur du status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-600';
      case 'offline': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="w-5 h-5" />;
      case 'offline': return <XCircle className="w-5 h-5" />;
      case 'warning': return <AlertCircle className="w-5 h-5" />;
      default: return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold">🎯 Monitoring Dashboard</h1>
            <p className="text-gray-400 mt-2">MindFlow Pro - Surveillance en temps réel</p>
          </div>
          <button
            onClick={() => setIsMonitoring(!isMonitoring)}
            className={`px-6 py-3 rounded-lg font-semibold transition ${
              isMonitoring 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-green-600 hover:bg-green-700'
            }`}
          >
            {isMonitoring ? '⏸ Arrêter' : '▶️ Démarrer'} Monitoring
          </button>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400">Uptime</p>
                  <p className="text-3xl font-bold">{stats.uptime.toFixed(1)}%</p>
                </div>
                <Activity className="w-10 h-10 text-blue-500" />
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400">Tests Réussis</p>
                  <p className="text-3xl font-bold">{stats.passedTests}</p>
                </div>
                <CheckCircle className="w-10 h-10 text-green-500" />
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400">Tests Échoués</p>
                  <p className="text-3xl font-bold">{stats.failedTests}</p>
                </div>
                <XCircle className="w-10 h-10 text-red-500" />
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400">Temps de Réponse</p>
                  <p className="text-3xl font-bold">{stats.avgResponseTime.toFixed(0)}ms</p>
                </div>
                <Clock className="w-10 h-10 text-purple-500" />
              </div>
            </div>
          </div>
        )}

        {/* Services Status */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h2 className="text-2xl font-bold mb-4">📊 État des Services</h2>
            <div className="space-y-4">
              {services.map((service, index) => {
                const Icon = service.icon;
                return (
                  <div key={index} className="bg-gray-800 p-4 rounded-lg flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Icon className="w-6 h-6 text-blue-400" />
                      <div>
                        <p className="font-semibold">{service.name}</p>
                        <p className="text-sm text-gray-400">
                          Dernière vérification: {service.lastCheck.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {service.responseTime && (
                        <span className="text-sm text-gray-400">{service.responseTime}ms</span>
                      )}
                      <div className={`flex items-center space-x-1 ${getStatusColor(service.status)}`}>
                        {getStatusIcon(service.status)}
                        <span className="font-semibold capitalize">{service.status}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Logs */}
          <div>
            <h2 className="text-2xl font-bold mb-4">📝 Logs en Direct</h2>
            <div className="bg-gray-800 rounded-lg p-4 h-[400px] overflow-y-auto">
              {logs.length === 0 ? (
                <p className="text-gray-400">En attente des logs...</p>
              ) : (
                <div className="space-y-2">
                  {logs.map((log, index) => (
                    <p key={index} className="text-sm font-mono text-gray-300">
                      {log}
                    </p>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions rapides */}
        <div className="mt-8 flex flex-wrap gap-4">
          <button
            onClick={checkAllServices}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition"
          >
            🔄 Rafraîchir Maintenant
          </button>
          <a
            href="/test-phase4-supabase"
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded transition inline-block"
          >
            🧪 Test Phase 4
          </a>
          <a
            href="/test-report.json"
            download
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded transition inline-block"
          >
            📥 Télécharger Rapport
          </a>
        </div>

        {/* Info */}
        <div className="mt-8 bg-blue-900/50 border border-blue-700 rounded-lg p-4">
          <p className="text-blue-300">
            💡 <strong>Astuce:</strong> Activez le monitoring automatique pour surveiller les services toutes les 10 secondes.
            Les statistiques sont mises à jour en temps réel.
          </p>
        </div>
      </div>
    </div>
  );
} 