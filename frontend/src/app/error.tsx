'use client';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h2>🚨 Erreur détectée</h2>
      <p style={{ color: 'red', marginBottom: '20px' }}>
        {error.message || 'Une erreur inattendue s\'est produite'}
      </p>
      <button 
        onClick={() => reset()}
        style={{
          padding: '10px 20px',
          backgroundColor: '#0066cc',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Réessayer
      </button>
    </div>
  );
} 