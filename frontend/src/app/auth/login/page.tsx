'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import useAuthStore from '@/stores/authStore';
import { 
  Eye, 
  EyeOff, 
  Mail, 
  Lock, 
  Brain, 
  Sparkles,
  ArrowRight,
  Shield,
  Users,
  Zap,
  Star
} from 'lucide-react';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const router = useRouter();
  const { login } = useAuthStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      await login(email, password, rememberMe);
      router.push('/dashboard');
    } catch (error: any) {
      setError(error.message || 'Erreur de connexion. Veuillez vérifier vos identifiants.');
    } finally {
      setLoading(false);
    }
  };

  const features = [
    {
      icon: Brain,
      title: 'Coach IA Personnel',
      description: 'Un accompagnement intelligent 24h/24'
    },
    {
      icon: Shield,
      title: 'Données Sécurisées',
      description: 'Vos informations sont protégées et confidentielles'
    },
    {
      icon: Users,
      title: 'Communauté Bienveillante',
      description: 'Rejoignez une communauté de soutien'
    },
    {
      icon: Zap,
      title: 'Résultats Prouvés',
      description: 'Des outils basés sur la science'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="absolute top-0 left-0 right-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-green-600 rounded-lg flex items-center justify-center">
                <Brain className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">MindFlow Pro</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={() => router.push('/')}>
                Accueil
              </Button>
              <Button variant="outline" onClick={() => router.push('/auth/register')}>
                S'inscrire
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex min-h-screen pt-16">
        {/* Left side - Login Form */}
        <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8">
            <div className="text-center">
              <div className="inline-flex items-center space-x-2 mb-6">
                <Sparkles className="w-6 h-6 text-blue-600" />
                <h1 className="text-3xl font-bold text-gray-900">
                  Bon retour !
                </h1>
              </div>
              <p className="text-gray-600">
                Connectez-vous pour continuer votre parcours bien-être
              </p>
            </div>

            <Card className="border-0 shadow-xl">
              <CardHeader className="text-center">
                <CardTitle>Connexion</CardTitle>
                <CardDescription>
                  Accédez à votre espace personnel sécurisé
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit} className="space-y-4">
                  {error && (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  )}

                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium text-gray-700">
                      Adresse email
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="password" className="text-sm font-medium text-gray-700">
                      Mot de passe
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <Input
                        id="password"
                        type={showPassword ? 'text' : 'password'}
                        placeholder="••••••••"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="pl-10 pr-10"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={rememberMe}
                        onChange={(e) => setRememberMe(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-600">Se souvenir de moi</span>
                    </label>
                    
                    <button
                      type="button"
                      className="text-sm text-blue-600 hover:text-blue-500"
                      onClick={() => router.push('/auth/forgot-password')}
                    >
                      Mot de passe oublié ?
                    </button>
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={loading || !email || !password}
                  >
                    {loading ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        <span>Connexion...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <span>Se connecter</span>
                        <ArrowRight className="w-4 h-4" />
                      </div>
                    )}
                  </Button>
                </form>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-2 text-gray-500">
                      Nouveau sur MindFlow Pro ?
                    </span>
                  </div>
                </div>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push('/auth/register')}
                >
                  Créer un compte gratuit
                </Button>
              </CardContent>
            </Card>

            {/* Trust indicators */}
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center space-x-4">
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <Shield className="w-3 h-3" />
                  <span>Sécurisé SSL</span>
                </Badge>
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <Users className="w-3 h-3" />
                  <span>50k+ utilisateurs</span>
                </Badge>
              </div>
              <p className="text-xs text-gray-500">
                Vos données sont protégées par un chiffrement de niveau bancaire
              </p>
            </div>
          </div>
        </div>

        {/* Right side - Features */}
        <div className="hidden lg:flex lg:flex-1 lg:items-center lg:justify-center bg-gradient-to-br from-blue-600 to-purple-700 text-white p-12">
          <div className="max-w-lg space-y-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold mb-4">
                Votre bien-être, notre priorité
              </h2>
              <p className="text-blue-100 text-lg">
                Rejoignez des milliers d'utilisateurs qui transforment leur vie avec MindFlow Pro
              </p>
            </div>

            <div className="grid gap-6">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="p-2 bg-white/10 rounded-lg">
                    <feature.icon className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">{feature.title}</h3>
                    <p className="text-blue-100 text-sm">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <p className="text-sm text-blue-100 mb-2">
                "MindFlow Pro a transformé ma façon de gérer le stress au quotidien."
              </p>
              <div className="flex items-center justify-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-xs text-blue-200 mt-2">- Marie, 32 ans</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
