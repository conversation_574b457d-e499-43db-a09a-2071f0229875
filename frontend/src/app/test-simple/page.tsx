'use client';

import React, { useState, useEffect } from 'react';

export default function TestSimple() {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const runTests = async () => {
      const testResults: any[] = [];

      // Test 1: Variables d'environnement
      testResults.push({
        test: 'Variables Environnement',
        status: 'info',
        details: {
          supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || 'Non configurée',
          supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Configurée ✅' : 'Manquante ❌',
          dualMode: process.env.NEXT_PUBLIC_DUAL_DATABASE_MODE || 'false',
          useSupabaseAuth: process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH || 'false',
          useSupabaseDB: process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE || 'false'
        }
      });

      // Test 2: Import du client Supabase
      try {
        const { createSupabaseClient } = await import('@/lib/supabase/client');
        const client = createSupabaseClient();
        testResults.push({
          test: 'Client Supabase',
          status: 'success',
          details: 'Import et création réussis ✅'
        });

        // Test de connexion
        try {
          const { data, error } = await client.from('users').select('count').limit(1);
          testResults.push({
            test: 'Connexion Supabase',
            status: error ? 'error' : 'success',
            details: error ? `Erreur: ${error.message}` : 'Connexion réussie ✅'
          });
        } catch (connError) {
          testResults.push({
            test: 'Connexion Supabase',
            status: 'error',
            details: `Erreur connexion: ${connError}`
          });
        }
      } catch (importError) {
        testResults.push({
          test: 'Client Supabase',
          status: 'error',
          details: `Erreur import: ${importError}`
        });
      }

      // Test 3: Database Manager
      try {
        const { DatabaseManager } = await import('@/lib/database');
        testResults.push({
          test: 'Database Manager',
          status: 'success',
          details: 'Import réussi ✅'
        });
      } catch (dbError) {
        testResults.push({
          test: 'Database Manager',
          status: 'error',
          details: `Erreur: ${dbError}`
        });
      }

      // Test 4: Feature Flags
      try {
        const featureFlags = await import('@/lib/config/feature-flags');
        testResults.push({
          test: 'Feature Flags',
          status: 'success',
          details: 'Import réussi ✅'
        });
      } catch (flagError) {
        testResults.push({
          test: 'Feature Flags',
          status: 'error',
          details: `Erreur: ${flagError}`
        });
      }

      setResults(testResults);
      setLoading(false);
    };

    runTests();
  }, []);

  if (loading) {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1>🔄 Test de Configuration Supabase</h1>
        <p>Chargement en cours...</p>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      maxWidth: '800px',
      margin: '0 auto'
    }}>
      <h1 style={{ color: '#333', borderBottom: '2px solid #007acc', paddingBottom: '10px' }}>
        🧪 Diagnostic Supabase - MindFlow Pro
      </h1>
      
      <div style={{ marginTop: '20px' }}>
        {results.map((result, index) => (
          <div 
            key={index}
            style={{
              margin: '15px 0',
              padding: '15px',
              border: '1px solid #ddd',
              borderRadius: '8px',
              backgroundColor: result.status === 'error' ? '#ffebee' : 
                             result.status === 'success' ? '#e8f5e8' : '#f5f5f5'
            }}
          >
            <h3 style={{ 
              margin: '0 0 10px 0',
              color: result.status === 'error' ? '#d32f2f' : 
                     result.status === 'success' ? '#2e7d32' : '#666'
            }}>
              {result.test}
            </h3>
            
            {typeof result.details === 'object' ? (
              <div style={{ fontSize: '14px' }}>
                {Object.entries(result.details).map(([key, value]) => (
                  <div key={key} style={{ margin: '5px 0' }}>
                    <strong>{key}:</strong> {String(value)}
                  </div>
                ))}
              </div>
            ) : (
              <div style={{ fontSize: '14px' }}>
                {String(result.details)}
              </div>
            )}
          </div>
        ))}
      </div>

      <div style={{ 
        marginTop: '30px',
        padding: '15px',
        backgroundColor: '#e3f2fd',
        border: '1px solid #2196f3',
        borderRadius: '8px'
      }}>
        <h3 style={{ color: '#1976d2', margin: '0 0 10px 0' }}>
          📋 Instructions
        </h3>
        <ol style={{ fontSize: '14px', color: '#333' }}>
          <li>Vérifiez que toutes les variables d'environnement sont configurées</li>
          <li>Assurez-vous que le schéma SQL a été exécuté dans Supabase</li>
          <li>Testez la connexion à la base de données</li>
          <li>Activez progressivement les feature flags</li>
        </ol>
      </div>

      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        Page générée le: {new Date().toLocaleString('fr-FR')}
      </div>
    </div>
  );
} 