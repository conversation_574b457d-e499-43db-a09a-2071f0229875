'use client';

import React, { useState } from 'react';
import { 
  Calendar,
  Clock,
  Video,
  MapPin,
  Search,
  Plus,
  Star,
  ChevronRight,
  Filter,
  MessageCircle,
  X,
  CheckCircle
} from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useAppointmentsData } from '@/hooks/useAppointmentsData';

export default function AppointmentsPage() {
  const { 
    appointments, 
    stats, 
    loading,
    cancelAppointment,
    confirmAppointment,
    getUpcomingAppointments 
  } = useAppointmentsData();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'upcoming' | 'completed' | 'cancelled'>('all');
  const [showCancelModal, setShowCancelModal] = useState<string | null>(null);
  const [cancelReason, setCancelReason] = useState('');

  const filteredAppointments = appointments.filter(apt => {
    const matchesSearch = apt.professionalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         apt.professionalRole.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         apt.notes?.toLowerCase().includes(searchTerm.toLowerCase());
    
    let matchesFilter = true;
    if (filterStatus === 'upcoming') {
      const now = new Date();
      const aptDate = new Date(`${apt.date} ${apt.time}`);
      matchesFilter = aptDate > now && (apt.status === 'scheduled' || apt.status === 'confirmed');
    } else if (filterStatus === 'completed') {
      matchesFilter = apt.status === 'completed';
    } else if (filterStatus === 'cancelled') {
      matchesFilter = apt.status === 'cancelled';
    }
    
    return matchesSearch && matchesFilter;
  });

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('fr-FR', { 
      weekday: 'long', 
      day: 'numeric', 
      month: 'long' 
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'no-show':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'Planifié';
      case 'confirmed':
        return 'Confirmé';
      case 'completed':
        return 'Terminé';
      case 'cancelled':
        return 'Annulé';
      case 'no-show':
        return 'Absent';
      default:
        return status;
    }
  };

  const getConsultationTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="h-4 w-4" />;
      case 'in-person': return <MapPin className="h-4 w-4" />;
      case 'chat': return <MessageCircle className="h-4 w-4" />;
      default: return null;
    }
  };

  const getConsultationTypeText = (type: string) => {
    switch (type) {
      case 'video': return 'Vidéo consultation';
      case 'in-person': return 'En cabinet';
      case 'chat': return 'Chat';
      default: return type;
    }
  };

  const handleCancelAppointment = async (id: string) => {
    if (cancelReason.trim()) {
      await cancelAppointment(id, cancelReason);
      setShowCancelModal(null);
      setCancelReason('');
    }
  };

  const isUpcoming = (appointment: any) => {
    const now = new Date();
    const aptDate = new Date(`${appointment.date} ${appointment.time}`);
    return aptDate > now;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Mes Rendez-vous</h1>
          <p className="text-gray-600">Gérez vos consultations et suivis thérapeutiques</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card className="p-6 bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100">Total</p>
                <p className="text-3xl font-bold">{stats.total}</p>
                <p className="text-sm text-blue-100 mt-1">rendez-vous</p>
              </div>
              <Calendar className="h-10 w-10 text-blue-200" />
            </div>
          </Card>

          <Card className="p-6 bg-gradient-to-r from-green-500 to-green-600 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100">À venir</p>
                <p className="text-3xl font-bold">{stats.upcoming}</p>
                <p className="text-sm text-green-100 mt-1">consultations</p>
              </div>
              <Clock className="h-10 w-10 text-green-200" />
            </div>
          </Card>

          <Card className="p-6 bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100">Complétés</p>
                <p className="text-3xl font-bold">{stats.completed}</p>
                <p className="text-sm text-purple-100 mt-1">cette année</p>
              </div>
              <CheckCircle className="h-10 w-10 text-purple-200" />
            </div>
          </Card>

          <Card className="p-6 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-100">Note moyenne</p>
                <p className="text-3xl font-bold">{stats.averageRating}</p>
                <p className="text-sm text-yellow-100 mt-1">sur 5</p>
              </div>
              <Star className="h-10 w-10 text-yellow-200" />
            </div>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Rechercher un professionnel..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex gap-2">
              <Button
                variant={filterStatus === 'all' ? 'default' : 'outline'}
                onClick={() => setFilterStatus('all')}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Tous
              </Button>
              <Button
                variant={filterStatus === 'upcoming' ? 'default' : 'outline'}
                onClick={() => setFilterStatus('upcoming')}
              >
                À venir
              </Button>
              <Button
                variant={filterStatus === 'completed' ? 'default' : 'outline'}
                onClick={() => setFilterStatus('completed')}
              >
                Terminés
              </Button>
              <Button
                variant={filterStatus === 'cancelled' ? 'default' : 'outline'}
                onClick={() => setFilterStatus('cancelled')}
              >
                Annulés
              </Button>
            </div>

            <Link href="/professionals">
              <Button className="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Nouveau rendez-vous
              </Button>
            </Link>
          </div>
        </div>

        {/* Appointments List */}
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Chargement des rendez-vous...</p>
            </div>
          ) : filteredAppointments.length === 0 ? (
            <Card className="p-12 text-center">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Aucun rendez-vous trouvé
              </h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || filterStatus !== 'all' 
                  ? "Essayez de modifier vos critères de recherche"
                  : "Prenez votre premier rendez-vous avec un professionnel"}
              </p>
              {!searchTerm && filterStatus === 'all' && (
                <Link href="/professionals">
                  <Button className="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    Prendre un rendez-vous
                  </Button>
                </Link>
              )}
            </Card>
          ) : (
            filteredAppointments.map((appointment) => (
              <Card key={appointment.id} className="p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* Avatar */}
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-xl">
                      {appointment.professionalName.split(' ').map(n => n[0]).join('')}
                    </div>

                    {/* Info */}
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {appointment.professionalName}
                        </h3>
                        <Badge className={getStatusColor(appointment.status)}>
                          {getStatusText(appointment.status)}
                        </Badge>
                      </div>

                      <p className="text-gray-600 mb-3">{appointment.professionalRole}</p>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(appointment.date)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>{appointment.time} ({appointment.duration} min)</span>
                        </div>
                        <div className="flex items-center gap-1">
                          {getConsultationTypeIcon(appointment.type)}
                          <span>{getConsultationTypeText(appointment.type)}</span>
                        </div>
                        <div className="font-medium">
                          {appointment.price}€
                        </div>
                      </div>

                      {appointment.notes && (
                        <p className="mt-3 text-sm text-gray-600 italic">
                          Note: {appointment.notes}
                        </p>
                      )}

                      {appointment.rating && (
                        <div className="mt-3 flex items-center gap-2">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < appointment.rating! ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          {appointment.feedback && (
                            <span className="text-sm text-gray-600">"{appointment.feedback}"</span>
                          )}
                        </div>
                      )}

                      {appointment.cancelReason && (
                        <p className="mt-3 text-sm text-red-600">
                          Raison d'annulation: {appointment.cancelReason}
                        </p>
                      )}

                      {appointment.meetingLink && appointment.status === 'confirmed' && isUpcoming(appointment) && (
                        <div className="mt-3">
                          <a
                            href={appointment.meetingLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:underline flex items-center gap-1"
                          >
                            <Video className="h-4 w-4" />
                            Rejoindre la consultation
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col gap-2 ml-4">
                    <Link href={`/appointments/${appointment.id}`}>
                      <Button variant="ghost" size="sm">
                        Détails
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </Link>
                    
                    {appointment.status === 'scheduled' && isUpcoming(appointment) && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => confirmAppointment(appointment.id)}
                          className="text-green-600 hover:text-green-700"
                        >
                          Confirmer
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowCancelModal(appointment.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          Annuler
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>

        {/* Quick Actions */}
        {filteredAppointments.length > 0 && (
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    Besoin d'aide urgente ?
                  </h3>
                  <p className="text-sm text-gray-600">
                    Contactez notre service d'urgence 24/7
                  </p>
                </div>
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                  <span className="text-red-600 text-xl">🚨</span>
                </div>
              </div>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    Explorer nos programmes
                  </h3>
                  <p className="text-sm text-gray-600">
                    Découvrez nos parcours thérapeutiques
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 text-xl">🎯</span>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>

      {/* Cancel Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Annuler le rendez-vous</h3>
              <button
                onClick={() => {
                  setShowCancelModal(null);
                  setCancelReason('');
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <p className="text-gray-600 mb-4">
              Êtes-vous sûr de vouloir annuler ce rendez-vous ? Cette action est irréversible.
            </p>
            
            <textarea
              value={cancelReason}
              onChange={(e) => setCancelReason(e.target.value)}
              placeholder="Raison de l'annulation (obligatoire)"
              className="w-full p-3 border border-gray-300 rounded-lg mb-4"
              rows={3}
            />
            
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowCancelModal(null);
                  setCancelReason('');
                }}
                className="flex-1"
              >
                Retour
              </Button>
              <Button
                onClick={() => handleCancelAppointment(showCancelModal)}
                disabled={!cancelReason.trim()}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white"
              >
                Confirmer l'annulation
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
} 