'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Head from 'next/head';
import Link from 'next/link';
import { 
  ArrowLeft,
  Calendar,
  Clock,
  Video,
  Phone,
  MessageCircle,
  Users,
  User,
  Star,
  DollarSign,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RotateCcw,
  MessageSquare,
  AlertCircle
} from 'lucide-react';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext2';
import Navigation from '@/components/Layout/Navigation';

interface Appointment {
  id: string;
  status: string;
  appointmentType: string;
  mode: string;
  scheduledAt: string;
  durationMinutes: number;
  cost?: number;
  currency: string;
  clientNotes?: string;
  professionalNotes?: string;
  sessionSummary?: string;
  clientRating?: number;
  clientFeedback?: string;
  preSessionAssessment?: {
    moodLevel: number;
    stressLevel: number;
    anxietyLevel: number;
    concerns: string[];
    goals: string[];
  };
  postSessionAssessment?: {
    moodLevel: number;
    stressLevel: number;
    satisfactionLevel: number;
    helpfulnessRating: number;
    feedback: string;
  };
  actionItems?: Array<{
    description: string;
    dueDate?: string;
    completed: boolean;
    completedAt?: string;
  }>;
  followUpPlan?: {
    nextAppointmentRecommended: boolean;
    recommendedTimeframe: string;
    specificGoals: string[];
    homeworkAssignments: string[];
  };
  professional: {
    id: string;
    user: {
      firstName: string;
      lastName: string;
      profilePicture?: string;
    };
    professionalType: string;
    averageRating: number;
    totalReviews: number;
    specializations: string[];
  };
  client: {
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  createdAt: string;
  actualStartTime?: string;
  actualEndTime?: string;
  cancelledAt?: string;
  cancelledBy?: string;
  cancellationReason?: string;
}

const AppointmentDetailPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;
  const { user } = useAuth();
  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [loading, setLoading] = useState(true);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [cancellationReason, setCancellationReason] = useState('');
  const [rating, setRating] = useState(5);
  const [feedback, setFeedback] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    if (id && user) {
      fetchAppointment();
    }
  }, [id, user]);

  const fetchAppointment = async () => {
    try {
      setLoading(true);
      const response = await apiService.getAppointment(id as string);
      setAppointment(response.data.appointment);
    } catch (error) {
      console.error('Error fetching appointment:', error);
      router.push('/appointments');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelAppointment = async () => {
    if (!cancellationReason.trim()) {
      alert('Please provide a reason for cancellation');
      return;
    }

    try {
      setActionLoading(true);
      await apiService.cancelAppointment(id as string, cancellationReason.trim());
      
      setShowCancelModal(false);
      fetchAppointment(); // Refresh appointment data
    } catch (error: unknown) {
      console.error('Error cancelling appointment:', error);
      const errorMessage = error instanceof Error && 'response' in error
        ? (error as any).response?.data?.message || 'Failed to cancel appointment'
        : 'Failed to cancel appointment';
      alert(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };

  const handleRateAppointment = async () => {
    try {
      setActionLoading(true);
      await apiService.rateAppointment(id as string, rating, feedback.trim() || undefined);
      
      setShowRatingModal(false);
      fetchAppointment(); // Refresh appointment data
    } catch (error: unknown) {
      console.error('Error rating appointment:', error);
      const errorMessage = error instanceof Error && 'response' in error
        ? (error as any).response?.data?.message || 'Failed to submit rating'
        : 'Failed to submit rating';
      alert(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
      case 'confirmed':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'cancelled_by_client':
      case 'cancelled_by_professional':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'rescheduled':
        return <RotateCcw className="w-5 h-5 text-yellow-500" />;
      case 'in_progress':
        return <AlertTriangle className="w-5 h-5 text-orange-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled_by_client':
      case 'cancelled_by_professional':
        return 'bg-red-100 text-red-800';
      case 'rescheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getConsultationTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="w-5 h-5" />;
      case 'phone': return <Phone className="w-5 h-5" />;
      case 'chat': return <MessageCircle className="w-5 h-5" />;
      case 'in_person': return <Users className="w-5 h-5" />;
      default: return <Video className="w-5 h-5" />;
    }
  };

  const formatStatus = (status: string) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    };
  };

  const isUpcoming = (scheduledAt: string) => {
    return new Date(scheduledAt) > new Date();
  };

  const canCancel = (appointment: Appointment) => {
    return isUpcoming(appointment.scheduledAt) && 
           (appointment.status === 'scheduled' || appointment.status === 'confirmed');
  };

  const canRate = (appointment: Appointment) => {
    return appointment.status === 'completed' && !appointment.clientRating;
  };

  if (!user) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Please Log In</h2>
            <Link href="/auth/login" className="text-blue-600 hover:text-blue-800">
              Go to Login
            </Link>
          </div>
        </div>
      </>
    );
  }

  if (loading) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </>
    );
  }

  if (!appointment) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Appointment Not Found</h2>
            <Link href="/appointments" className="text-blue-600 hover:text-blue-800">
              ← Back to Appointments
            </Link>
          </div>
        </div>
      </>
    );
  }

  const { date, time } = formatDateTime(appointment.scheduledAt);
  const upcoming = isUpcoming(appointment.scheduledAt);

  return (
    <>
      <Head>
        <title>Appointment Details - MindFlow Pro</title>
      </Head>

      <Navigation />
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <Link 
              href="/appointments"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Appointments
            </Link>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Appointment Header */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                  {appointment.professional.user.profilePicture ? (
                    <img
                      src={appointment.professional.user.profilePicture}
                      alt={`${appointment.professional.user.firstName} ${appointment.professional.user.lastName}`}
                      className="w-16 h-16 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-8 h-8 text-blue-600" />
                  )}
                </div>

                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Session with {appointment.professional.user.firstName} {appointment.professional.user.lastName}
                  </h1>
                  <p className="text-gray-600 capitalize">
                    {appointment.professional.professionalType.replace('_', ' ')}
                  </p>
                  <div className="flex items-center mt-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="ml-1 text-sm text-gray-600">
                      {appointment.professional.averageRating.toFixed(1)} ({appointment.professional.totalReviews} reviews)
                    </span>
                  </div>
                </div>
              </div>

              <div className="text-right">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(appointment.status)} mb-2`}>
                  {getStatusIcon(appointment.status)}
                  <span className="ml-1">{formatStatus(appointment.status)}</span>
                </div>
                {upcoming && appointment.status === 'scheduled' && (
                  <div className="text-sm text-green-600 font-medium">
                    Upcoming Session
                  </div>
                )}
              </div>
            </div>

            {/* Appointment Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Date</span>
                </div>
                <p className="text-gray-900">{date}</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Time</span>
                </div>
                <p className="text-gray-900">{time}</p>
                <p className="text-sm text-gray-600">{appointment.durationMinutes} minutes</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  {getConsultationTypeIcon(appointment.mode)}
                  <span className="text-sm font-medium text-gray-700">Type</span>
                </div>
                <p className="text-gray-900 capitalize">{appointment.mode.replace('_', ' ')}</p>
                <p className="text-sm text-gray-600 capitalize">{appointment.appointmentType.replace('_', ' ')}</p>
              </div>

              {appointment.cost && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <DollarSign className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700">Cost</span>
                  </div>
                  <p className="text-gray-900">${appointment.cost} {appointment.currency}</p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="mt-6 flex flex-wrap gap-3">
              {canCancel(appointment) && (
                <button
                  onClick={() => setShowCancelModal(true)}
                  className="px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors"
                >
                  Cancel Appointment
                </button>
              )}

              {canRate(appointment) && (
                <button
                  onClick={() => setShowRatingModal(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Rate Session
                </button>
              )}

              <Link
                href={`/professionals/${appointment.professional.id}`}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                View Professional Profile
              </Link>

              {upcoming && (
                <Link
                  href={`/appointments/book?professionalId=${appointment.professional.id}`}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Book Another Session
                </Link>
              )}
            </div>
          </div>

          {/* Session Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Pre-Session Information */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Pre-Session Information</h2>

              {appointment.clientNotes && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Your Notes</h3>
                  <p className="text-gray-600 bg-gray-50 rounded-lg p-3">{appointment.clientNotes}</p>
                </div>
              )}

              {appointment.preSessionAssessment ? (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Pre-Session Assessment</h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {appointment.preSessionAssessment.moodLevel}
                        </div>
                        <div className="text-xs text-gray-600">Mood Level</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {appointment.preSessionAssessment.stressLevel}
                        </div>
                        <div className="text-xs text-gray-600">Stress Level</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">
                          {appointment.preSessionAssessment.anxietyLevel}
                        </div>
                        <div className="text-xs text-gray-600">Anxiety Level</div>
                      </div>
                    </div>

                    {appointment.preSessionAssessment.concerns.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Concerns</h4>
                        <div className="flex flex-wrap gap-2">
                          {appointment.preSessionAssessment.concerns.map((concern, index) => (
                            <span key={index} className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                              {concern}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {appointment.preSessionAssessment.goals.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Goals</h4>
                        <div className="flex flex-wrap gap-2">
                          {appointment.preSessionAssessment.goals.map((goal, index) => (
                            <span key={index} className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                              {goal}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <p className="text-gray-500 text-sm">No pre-session assessment available.</p>
              )}
            </div>

            {/* Post-Session Information */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Session Summary</h2>

              {appointment.professionalNotes && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Professional Notes</h3>
                  <p className="text-gray-600 bg-gray-50 rounded-lg p-3">{appointment.professionalNotes}</p>
                </div>
              )}

              {appointment.sessionSummary && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Session Summary</h3>
                  <p className="text-gray-600 bg-gray-50 rounded-lg p-3">{appointment.sessionSummary}</p>
                </div>
              )}

              {appointment.clientRating && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Your Rating</h3>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-5 h-5 ${
                            i < appointment.clientRating! ? 'text-yellow-400 fill-current' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">({appointment.clientRating}/5)</span>
                  </div>
                  {appointment.clientFeedback && (
                    <p className="text-gray-600 bg-gray-50 rounded-lg p-3 mt-2">{appointment.clientFeedback}</p>
                  )}
                </div>
              )}

              {appointment.actionItems && appointment.actionItems.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Action Items</h3>
                  <div className="space-y-2">
                    {appointment.actionItems.map((item, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <CheckCircle className={`w-4 h-4 mt-0.5 ${item.completed ? 'text-green-500' : 'text-gray-300'}`} />
                        <div className="flex-1">
                          <p className={`text-sm ${item.completed ? 'line-through text-gray-500' : 'text-gray-700'}`}>
                            {item.description}
                          </p>
                          {item.dueDate && (
                            <p className="text-xs text-gray-500">Due: {new Date(item.dueDate).toLocaleDateString()}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {appointment.followUpPlan && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Follow-up Plan</h3>
                  <div className="bg-blue-50 rounded-lg p-3 space-y-2">
                    {appointment.followUpPlan.nextAppointmentRecommended && (
                      <p className="text-sm text-blue-800">
                        <strong>Next appointment recommended:</strong> {appointment.followUpPlan.recommendedTimeframe}
                      </p>
                    )}
                    {appointment.followUpPlan.specificGoals.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-blue-800">Goals for next session:</p>
                        <ul className="text-sm text-blue-700 list-disc list-inside">
                          {appointment.followUpPlan.specificGoals.map((goal, index) => (
                            <li key={index}>{goal}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {appointment.followUpPlan.homeworkAssignments.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-blue-800">Homework assignments:</p>
                        <ul className="text-sm text-blue-700 list-disc list-inside">
                          {appointment.followUpPlan.homeworkAssignments.map((assignment, index) => (
                            <li key={index}>{assignment}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {!appointment.professionalNotes && !appointment.sessionSummary && appointment.status !== 'completed' && (
                <p className="text-gray-500 text-sm">Session summary will be available after completion.</p>
              )}
            </div>
          </div>

          {/* Cancellation Information */}
          {(appointment.status === 'cancelled_by_client' || appointment.status === 'cancelled_by_professional') && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-6">
              <div className="flex items-start">
                <XCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Appointment Cancelled
                  </h3>
                  <div className="mt-1 text-sm text-red-700">
                    <p>Cancelled by: {appointment.cancelledBy === 'client' ? 'You' : 'Professional'}</p>
                    {appointment.cancelledAt && (
                      <p>Cancelled on: {new Date(appointment.cancelledAt).toLocaleDateString()}</p>
                    )}
                    {appointment.cancellationReason && (
                      <p>Reason: {appointment.cancellationReason}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default AppointmentDetailPage;
