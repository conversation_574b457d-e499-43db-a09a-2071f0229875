'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { 
  Calendar,
  Clock,
  Video,
  Phone,
  MessageCircle,
  Users,
  User,
  Search,
  ChevronRight,
  CheckCircle,
  XCircle,
  AlertCircle,
  RotateCcw,
  Star,
  Plus
} from 'lucide-react';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext2';
import Navigation from '@/components/Layout/Navigation';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';

export const dynamic = 'force-dynamic';

interface Appointment {
  id: string;
  status: string;
  appointmentType: string;
  mode: string;
  scheduledAt: string;
  durationMinutes: number;
  cost?: number;
  currency: string;
  clientNotes?: string;
  professionalNotes?: string;
  clientRating?: number;
  professional: {
    id: string;
    user: {
      firstName: string;
      lastName: string;
      profilePicture?: string;
    };
    professionalType: string;
    averageRating: number;
    totalReviews: number;
  };
  client: {
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  createdAt: string;
}

interface AppointmentStats {
  totalAppointments: number;
  upcomingAppointments: number;
  appointmentsByStatus: Array<{ status: string; count: number }>;
}

const AppointmentsPage: React.FC = () => {
  const { user } = useAuth();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [stats, setStats] = useState<AppointmentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const router = useRouter();
  const { isAuthenticated, isLoading: authLoading } = useAuthStore();

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [authLoading, isAuthenticated, router]);

  useEffect(() => {
    if (user) {
      fetchAppointments();
      fetchStats();
    }
  }, [user, currentPage, statusFilter, typeFilter]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
      });

      if (statusFilter) params.append('status', statusFilter);
      if (typeFilter) params.append('appointmentType', typeFilter);

      const response = await apiService.getAppointments({
        page: currentPage,
        limit: 10,
        status: statusFilter || undefined,
        appointmentType: typeFilter || undefined,
      });

      // Defensive programming: Ensure response structure exists
      const responseData = response?.data;
      if (responseData) {
        // Safely set appointments with fallback to empty array
        setAppointments(Array.isArray(responseData.appointments) ? responseData.appointments : []);

        // Safely set pagination with fallback values
        const pagination = responseData.pagination;
        if (pagination && typeof pagination.totalPages === 'number') {
          setTotalPages(pagination.totalPages);
        } else {
          // Fallback: calculate pages based on appointments length
          const appointmentsLength = Array.isArray(responseData.appointments) ? responseData.appointments.length : 0;
          setTotalPages(appointmentsLength > 0 ? Math.ceil(appointmentsLength / 10) : 1);
        }
      } else {
        // Fallback for completely malformed response
        console.warn('Invalid API response structure for appointments');
        setAppointments([]);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('Error fetching appointments:', error);
      // Reset to safe defaults on error
      setAppointments([]);
      setTotalPages(1);
      setCurrentPage(1);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await apiService.getAppointmentStats();

      // Defensive programming: Ensure stats structure exists
      const responseData = response?.data;
      if (responseData && responseData.stats) {
        setStats(responseData.stats);
      } else {
        console.warn('Invalid API response structure for stats');
        // Set default stats structure to prevent UI errors
        setStats({
          totalAppointments: 0,
          upcomingAppointments: 0,
          appointmentsByStatus: []
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      // Set default stats on error to prevent UI crashes
      setStats({
        totalAppointments: 0,
        upcomingAppointments: 0,
        appointmentsByStatus: []
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
      case 'confirmed':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'cancelled_by_client':
      case 'cancelled_by_professional':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'rescheduled':
        return <RotateCcw className="w-4 h-4 text-yellow-500" />;
      case 'in_progress':
        return <AlertCircle className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled_by_client':
      case 'cancelled_by_professional':
        return 'bg-red-100 text-red-800';
      case 'rescheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getConsultationTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="w-4 h-4" />;
      case 'phone': return <Phone className="w-4 h-4" />;
      case 'chat': return <MessageCircle className="w-4 h-4" />;
      case 'in_person': return <Users className="w-4 h-4" />;
      default: return <Video className="w-4 h-4" />;
    }
  };

  const formatStatus = (status: string) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    };
  };

  const isUpcoming = (scheduledAt: string) => {
    return new Date(scheduledAt) > new Date();
  };

  // Defensive programming: Ensure appointments is always an array before filtering
  const filteredAppointments = (Array.isArray(appointments) ? appointments : []).filter(appointment => {
    if (!searchTerm) return true;

    try {
      // Defensive checks for appointment structure
      if (!appointment || !appointment.professional || !appointment.professional.user) {
        console.warn('Invalid appointment structure detected:', appointment);
        return false;
      }

      const searchLower = searchTerm.toLowerCase();
      const firstName = appointment.professional.user.firstName || '';
      const lastName = appointment.professional.user.lastName || '';
      const professionalName = `${firstName} ${lastName}`.toLowerCase();
      const appointmentType = (appointment.appointmentType || '').toLowerCase();

      return professionalName.includes(searchLower) || appointmentType.includes(searchLower);
    } catch (error) {
      console.error('Error filtering appointment:', error, appointment);
      return false;
    }
  });

  if (!user) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Please Log In</h2>
            <p className="text-gray-600 mb-4">You need to be logged in to view your appointments.</p>
            <Link href="/auth/login" className="text-blue-600 hover:text-blue-800">
              Go to Login
            </Link>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>My Appointments - MindFlow Pro</title>
        <meta name="description" content="Manage your mental health appointments" />
      </Head>

      <Navigation />
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">My Appointments</h1>
                <p className="mt-2 text-gray-600">
                  Manage your mental health sessions and appointments
                </p>
              </div>
              
              <div className="mt-4 lg:mt-0">
                <Link
                  href="/professionals"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Book New Appointment
                </Link>
              </div>
            </div>

            {/* Stats */}
            {stats && (
              <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 px-4 py-3 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{stats.totalAppointments}</div>
                  <div className="text-sm text-blue-600">Total Appointments</div>
                </div>
                <div className="bg-green-50 px-4 py-3 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{stats.upcomingAppointments}</div>
                  <div className="text-sm text-green-600">Upcoming Sessions</div>
                </div>
                <div className="bg-purple-50 px-4 py-3 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {Array.isArray(stats?.appointmentsByStatus)
                      ? stats.appointmentsByStatus.find(s => s?.status === 'completed')?.count || 0
                      : 0
                    }
                  </div>
                  <div className="text-sm text-purple-600">Completed Sessions</div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Filters and Search */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search appointments by professional name or type..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="flex gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Statuses</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled_by_client">Cancelled</option>
                  <option value="rescheduled">Rescheduled</option>
                </select>

                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Types</option>
                  <option value="initial_consultation">Initial Consultation</option>
                  <option value="follow_up">Follow-up</option>
                  <option value="therapy_session">Therapy Session</option>
                  <option value="assessment">Assessment</option>
                </select>
              </div>
            </div>
          </div>

          {/* Appointments List */}
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg shadow-sm p-6 animate-pulse">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                    </div>
                    <div className="h-8 bg-gray-200 rounded w-20"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredAppointments.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No appointments found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || statusFilter || typeFilter
                  ? 'Try adjusting your search criteria or filters.'
                  : 'Book your first appointment to get started.'
                }
              </p>
              {!searchTerm && !statusFilter && !typeFilter && (
                <div className="mt-6">
                  <Link
                    href="/professionals"
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Book Your First Appointment
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAppointments.map((appointment) => {
                // Defensive checks for appointment data
                if (!appointment || !appointment.id) {
                  console.warn('Invalid appointment data:', appointment);
                  return null;
                }

                try {
                  const { date, time } = formatDateTime(appointment.scheduledAt || new Date().toISOString());
                  const upcoming = isUpcoming(appointment.scheduledAt || new Date().toISOString());

                return (
                  <div key={appointment.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          {/* Professional Avatar */}
                          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            {appointment.professional?.user?.profilePicture ? (
                              <img
                                src={appointment.professional.user.profilePicture}
                                alt={`${appointment.professional.user.firstName || 'Professional'} ${appointment.professional.user.lastName || ''}`}
                                className="w-12 h-12 rounded-full object-cover"
                              />
                            ) : (
                              <User className="w-6 h-6 text-blue-600" />
                            )}
                          </div>

                          {/* Appointment Details */}
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h3 className="text-lg font-semibold text-gray-900">
                                {appointment.professional?.user?.firstName || 'Professional'} {appointment.professional?.user?.lastName || ''}
                              </h3>
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status || 'unknown')}`}>
                                {getStatusIcon(appointment.status || 'unknown')}
                                <span className="ml-1">{formatStatus(appointment.status || 'unknown')}</span>
                              </span>
                            </div>

                            <div className="flex items-center space-x-4 text-sm text-gray-600">
                              <div className="flex items-center space-x-1">
                                <Calendar className="w-4 h-4" />
                                <span>{date}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="w-4 h-4" />
                                <span>{time}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                {getConsultationTypeIcon(appointment.mode || 'video')}
                                <span className="capitalize">{(appointment.mode || 'video').replace('_', ' ')}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <span>{appointment.durationMinutes || 50} min</span>
                              </div>
                            </div>

                            <div className="mt-2 text-sm text-gray-600">
                              <span className="capitalize">{(appointment.appointmentType || 'consultation').replace('_', ' ')}</span>
                              {appointment.cost && (
                                <span className="ml-4 font-medium">${appointment.cost} {appointment.currency || 'USD'}</span>
                              )}
                            </div>

                            {/* Professional Rating */}
                            <div className="flex items-center mt-2">
                              <Star className="w-4 h-4 text-yellow-400 fill-current" />
                              <span className="ml-1 text-sm text-gray-600">
                                {(appointment.professional?.averageRating || 0).toFixed(1)} ({appointment.professional?.totalReviews || 0} reviews)
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2">
                          {upcoming && appointment.status === 'scheduled' && (
                            <div className="bg-green-50 px-3 py-1 rounded-full">
                              <span className="text-sm font-medium text-green-700">Upcoming</span>
                            </div>
                          )}

                          <Link
                            href={`/appointments/${appointment.id}`}
                            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                          >
                            View Details
                            <ChevronRight className="w-4 h-4 ml-1" />
                          </Link>
                        </div>
                      </div>

                      {/* Notes Preview */}
                      {(appointment.clientNotes || appointment.professionalNotes) && (
                        <div className="mt-4 pt-4 border-t border-gray-200">
                          {appointment.clientNotes && (
                            <div className="mb-2">
                              <span className="text-sm font-medium text-gray-700">Your notes: </span>
                              <span className="text-sm text-gray-600">{appointment.clientNotes}</span>
                            </div>
                          )}
                          {appointment.professionalNotes && (
                            <div>
                              <span className="text-sm font-medium text-gray-700">Professional notes: </span>
                              <span className="text-sm text-gray-600">{appointment.professionalNotes}</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
                } catch (error) {
                  console.error('Error rendering appointment:', error, appointment);
                  return (
                    <div key={appointment.id || Math.random()} className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <p className="text-red-800 text-sm">Error displaying appointment. Please refresh the page.</p>
                    </div>
                  );
                }
              })}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-8 flex justify-center">
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>

                {[...Array(Math.min(5, totalPages))].map((_, i) => {
                  const page = i + 1;
                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`px-4 py-2 border rounded-lg ${
                        currentPage === page
                          ? 'bg-blue-600 text-white border-blue-600'
                          : 'border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}

                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default AppointmentsPage;
