'use client';

import React, { useState } from 'react';
import { DashboardLayout } from '@/components/Layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { VideoControls, WebRTCProvider, useWebRTC } from '@/components/Telemedicine';
import { 
  Video, 
  Phone, 
  Activity,
  Brain,
  Stethoscope,
  Eye,
  Monitor,
  Wifi,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface TelemedicineSession {
  id: string;
  patientName: string;
  patientAge: number;
  chiefComplaint: string;
  vitalSigns: {
    heartRate: number;
    bloodPressure: string;
    temperature: number;
    oxygenSat: number;
  };
  aiAnalysis: {
    symptoms: string[];
    diagnosis: { condition: string; confidence: number }[];
    urgency: 'low' | 'medium' | 'high';
  };
}

function TelemedicineContent() {
  const { 
    localVideo, 
    remoteVideo, 
    isConnected, 
    connectionQuality, 
    startCall, 
    endCall, 
    toggleVideo, 
    toggleAudio,
    shareScreen,
    isVideoEnabled,
    isAudioEnabled 
  } = useWebRTC();

  const [session] = useState<TelemedicineSession>({
    id: 'tel-001',
    patientName: 'Marie Dubois',
    patientAge: 34,
    chiefComplaint: 'Douleurs abdominales persistantes',
    vitalSigns: {
      heartRate: 78,
      bloodPressure: '125/80',
      temperature: 37.2,
      oxygenSat: 98
    },
    aiAnalysis: {
      symptoms: ['douleur abdominale', 'nausées', 'fatigue'],
      diagnosis: [
        { condition: 'Gastro-entérite', confidence: 75 },
        { condition: 'SCI', confidence: 60 }
      ],
      urgency: 'medium'
    }
  });

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getConnectionStatus = () => {
    if (!isConnected) return { icon: <AlertTriangle className="w-4 h-4 text-red-500" />, text: 'Déconnecté' };
    
    switch (connectionQuality) {
      case 'excellent': return { icon: <CheckCircle className="w-4 h-4 text-green-500" />, text: 'Excellente' };
      case 'good': return { icon: <Wifi className="w-4 h-4 text-blue-500" />, text: 'Bonne' };
      case 'poor': return { icon: <AlertTriangle className="w-4 h-4 text-orange-500" />, text: 'Faible' };
      default: return { icon: <Wifi className="w-4 h-4 text-gray-500" />, text: 'Inconnue' };
    }
  };

  const connectionStatus = getConnectionStatus();

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* En-tête de la session */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Video className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Téléconsultation - {session.patientName}
                  </h1>
                  <p className="text-gray-600">{session.chiefComplaint}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  {connectionStatus.icon}
                  <span className="text-sm text-gray-600">
                    Connexion {connectionStatus.text}
                  </span>
                </div>
                <Badge className={getUrgencyColor(session.aiAnalysis.urgency)}>
                  Urgence: {session.aiAnalysis.urgency}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Zone vidéo principale */}
          <div className="lg:col-span-2 space-y-6">
            {/* Vidéos */}
            <Card>
              <CardContent className="p-0">
                <div className="relative bg-gray-900 rounded-lg overflow-hidden h-96">
                  {/* Vidéo patient (principale) */}
                  <video
                    ref={remoteVideo}
                    autoPlay
                    playsInline
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Vidéo médecin (picture-in-picture) */}
                  <div className="absolute top-4 right-4 w-32 h-24 bg-gray-800 rounded-lg overflow-hidden border-2 border-white">
                    <video
                      ref={localVideo}
                      autoPlay
                      playsInline
                      muted
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  {/* Overlay d'information */}
                  <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-3 py-2 rounded-lg">
                    <div className="text-sm font-medium">{session.patientName}</div>
                    <div className="text-xs">{session.patientAge} ans</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contrôles vidéo */}
            <VideoControls
              onVideoToggle={toggleVideo}
              onAudioToggle={toggleAudio}
              onEndCall={endCall}
              onScreenShare={shareScreen}
              isVideoEnabled={isVideoEnabled}
              isAudioEnabled={isAudioEnabled}
              isConnected={isConnected}
              connectionQuality={connectionQuality}
            />
          </div>

          {/* Panneau latéral */}
          <div className="space-y-6">
            {/* Signes vitaux */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="w-5 h-5 mr-2" />
                  Signes Vitaux
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Fréquence cardiaque</span>
                  <span className="font-medium">{session.vitalSigns.heartRate} bpm</span>
                </div>
                <div className="flex justify-between">
                  <span>Tension artérielle</span>
                  <span className="font-medium">{session.vitalSigns.bloodPressure} mmHg</span>
                </div>
                <div className="flex justify-between">
                  <span>Température</span>
                  <span className="font-medium">{session.vitalSigns.temperature}°C</span>
                </div>
                <div className="flex justify-between">
                  <span>Saturation O2</span>
                  <span className="font-medium">{session.vitalSigns.oxygenSat}%</span>
                </div>
              </CardContent>
            </Card>

            {/* Analyse IA */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Brain className="w-5 h-5 mr-2" />
                  Analyse IA
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Symptômes détectés</h4>
                  <div className="space-y-1">
                    {session.aiAnalysis.symptoms.map((symptom, index) => (
                      <Badge key={index} variant="outline" className="mr-1">
                        {symptom}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Diagnostics suggérés</h4>
                  <div className="space-y-2">
                    {session.aiAnalysis.diagnosis.map((diag, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span>{diag.condition}</span>
                        <span className="font-medium">{diag.confidence}%</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions rapides */}
            <Card>
              <CardHeader>
                <CardTitle>Actions Rapides</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full" variant="outline">
                  📋 Générer compte-rendu
                </Button>
                <Button className="w-full" variant="outline">
                  💊 Prescrire traitement
                </Button>
                <Button className="w-full" variant="outline">
                  📅 Programmer suivi
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Actions de fin de session */}
        <Card className="bg-gray-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                💡 Session en cours - Transcription automatique activée
              </div>
              <div className="flex items-center space-x-2">
                <Button onClick={startCall} disabled={isConnected}>
                  Démarrer appel
                </Button>
                <Button variant="destructive" onClick={endCall}>
                  <Phone className="w-4 h-4 mr-2" />
                  Terminer consultation
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}

export default function TelemedicineEnhanced() {
  return (
    <WebRTCProvider>
      <TelemedicineContent />
    </WebRTCProvider>
  );
}
