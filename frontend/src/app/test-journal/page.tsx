'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useJournalData } from '@/hooks/useJournalData';

export default function TestJournalPage() {
  const {
    entries,
    stats,
    loading,
    error,
    createEntry,
    toggleFavorite,
    deleteEntry,
    getEntryTypeLabel,
    getMoodEmoji,
    formatDate,
    truncateContent,
    ENTRY_TYPES,
    MOOD_LEVELS,
  } = useJournalData();
  
  const router = useRouter();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testCreateEntry = async () => {
    addTestResult('🧪 Test de création d\'entrée...');
    
    const testEntry = {
      title: `Test Entry - ${new Date().toLocaleString()}`,
      content: 'Ceci est une entrée de test créée automatiquement pour valider le système de journal.',
      entryType: 'daily',
      moodLevel: 4,
      stressLevel: 3,
      energyLevel: 7,
      sleepQuality: 8,
      emotions: ['happy', 'motivated', 'grateful'],
      tags: ['test', 'automation', 'validation'],
      gratitudeNotes: 'Je suis reconnaissant pour ce système de test fonctionnel.',
      goals: 'Terminer le développement du système de journal.',
      isPrivate: false,
    };

    try {
      const success = await createEntry(testEntry);
      if (success) {
        addTestResult('✅ Création d\'entrée réussie');
      } else {
        addTestResult('❌ Échec de la création d\'entrée');
      }
    } catch (err) {
      addTestResult(`❌ Erreur lors de la création: ${err}`);
    }
  };

  const testToggleFavorite = async () => {
    if (entries.length === 0) {
      addTestResult('⚠️ Aucune entrée disponible pour tester les favoris');
      return;
    }

    addTestResult('🧪 Test de basculement des favoris...');
    
    try {
      const firstEntry = entries[0];
      const success = await toggleFavorite(firstEntry.id);
      if (success) {
        addTestResult('✅ Basculement des favoris réussi');
      } else {
        addTestResult('❌ Échec du basculement des favoris');
      }
    } catch (err) {
      addTestResult(`❌ Erreur lors du basculement: ${err}`);
    }
  };

  const testDeleteEntry = async () => {
    if (entries.length === 0) {
      addTestResult('⚠️ Aucune entrée disponible pour tester la suppression');
      return;
    }

    addTestResult('🧪 Test de suppression d\'entrée...');
    
    try {
      const lastEntry = entries[entries.length - 1];
      const success = await deleteEntry(lastEntry.id);
      if (success) {
        addTestResult('✅ Suppression d\'entrée réussie');
      } else {
        addTestResult('❌ Échec de la suppression d\'entrée');
      }
    } catch (err) {
      addTestResult(`❌ Erreur lors de la suppression: ${err}`);
    }
  };

  const runAllTests = async () => {
    setTestResults([]);
    addTestResult('🚀 Début des tests automatisés du système de journal');
    
    await testCreateEntry();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testToggleFavorite();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testDeleteEntry();
    
    addTestResult('🏁 Tests terminés');
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => router.push('/dashboard')}
                className="text-gray-600 hover:text-gray-900"
              >
                ← Retour au tableau de bord
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">Test du Système de Journal</h1>
            </div>
            <div className="flex space-x-2">
              <Button 
                onClick={() => router.push('/journal')}
                variant="outline"
              >
                Voir le Journal
              </Button>
              <Button 
                onClick={() => router.push('/journal/new')}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Créer une Entrée
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Test Controls */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>🧪</span>
              <span>Contrôles de Test</span>
            </CardTitle>
            <CardDescription>
              Testez les fonctionnalités du système de journal
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button onClick={testCreateEntry} variant="outline" className="h-16 flex flex-col">
                <span className="text-lg mb-1">➕</span>
                <span className="text-sm">Créer Entrée</span>
              </Button>
              <Button onClick={testToggleFavorite} variant="outline" className="h-16 flex flex-col">
                <span className="text-lg mb-1">⭐</span>
                <span className="text-sm">Favori</span>
              </Button>
              <Button onClick={testDeleteEntry} variant="outline" className="h-16 flex flex-col">
                <span className="text-lg mb-1">🗑️</span>
                <span className="text-sm">Supprimer</span>
              </Button>
              <Button onClick={runAllTests} className="h-16 flex flex-col bg-green-600 hover:bg-green-700">
                <span className="text-lg mb-1">🚀</span>
                <span className="text-sm">Tous les Tests</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center space-x-2">
                <span>📊</span>
                <span>Résultats des Tests</span>
              </CardTitle>
              <Button onClick={clearTestResults} variant="ghost" size="sm">
                Effacer
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {testResults.length === 0 ? (
              <p className="text-gray-500 italic">Aucun test exécuté pour le moment.</p>
            ) : (
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono bg-gray-100 p-2 rounded">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* System Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Hook Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>⚙️</span>
                <span>État du Hook</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Chargement:</span>
                  <span className={`text-sm font-medium ${loading ? 'text-yellow-600' : 'text-green-600'}`}>
                    {loading ? '🔄 En cours' : '✅ Terminé'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Erreur:</span>
                  <span className={`text-sm font-medium ${error ? 'text-red-600' : 'text-green-600'}`}>
                    {error ? `❌ ${error}` : '✅ Aucune'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Entrées chargées:</span>
                  <span className="text-sm font-medium text-blue-600">
                    📝 {entries.length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Statistiques:</span>
                  <span className={`text-sm font-medium ${stats ? 'text-green-600' : 'text-gray-400'}`}>
                    {stats ? '✅ Disponibles' : '⏳ Indisponibles'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Statistics Overview */}
          {stats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>📈</span>
                  <span>Statistiques</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Total entrées:</span>
                    <span className="text-sm font-medium text-blue-600">
                      {stats.totalEntries}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Cette semaine:</span>
                    <span className="text-sm font-medium text-green-600">
                      {stats.entriesThisWeek}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Série actuelle:</span>
                    <span className="text-sm font-medium text-purple-600">
                      {stats.streakDays} jours
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Humeur moyenne:</span>
                    <span className="text-sm font-medium text-orange-600">
                      {stats.averageMoodLevel > 0 ? (
                        <>
                          {getMoodEmoji(Math.round(stats.averageMoodLevel))} {stats.averageMoodLevel.toFixed(1)}
                        </>
                      ) : (
                        'N/A'
                      )}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Recent Entries Preview */}
        {entries.length > 0 && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>📋</span>
                <span>Aperçu des Entrées Récentes</span>
              </CardTitle>
              <CardDescription>
                Les 3 dernières entrées du journal
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {entries.slice(0, 3).map((entry, index) => (
                  <div key={entry.id} className="border-l-4 border-l-blue-500 pl-4 py-2">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          {ENTRY_TYPES.find(t => t.value === entry.entryType)?.icon} {getEntryTypeLabel(entry.entryType)}
                        </span>
                        {entry.moodLevel && (
                          <span className="text-sm">
                            {getMoodEmoji(entry.moodLevel)}
                          </span>
                        )}
                        {entry.isFavorite && <span className="text-yellow-500">⭐</span>}
                        {entry.isPrivate && <span className="text-gray-500">🔒</span>}
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatDate(entry.createdAt)}
                      </span>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1">{entry.title}</h4>
                    <p className="text-sm text-gray-600">
                      {truncateContent(entry.content, 100)}
                    </p>
                    {(entry.tags && entry.tags.length > 0) && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {entry.tags.slice(0, 3).map((tag, tagIndex) => (
                          <span key={tagIndex} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                            #{tag}
                          </span>
                        ))}
                        {entry.tags.length > 3 && (
                          <span className="text-xs text-gray-500">+{entry.tags.length - 3}</span>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Feature Test Matrix */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>✅</span>
              <span>Matrice des Fonctionnalités</span>
            </CardTitle>
            <CardDescription>
              État des fonctionnalités du système de journal
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                { name: 'Hook useJournalData', status: 'implemented', icon: '⚙️' },
                { name: 'Création d\'entrées', status: 'implemented', icon: '➕' },
                { name: 'Liste des entrées', status: 'implemented', icon: '📋' },
                { name: 'Édition d\'entrées', status: 'implemented', icon: '✏️' },
                { name: 'Suppression d\'entrées', status: 'implemented', icon: '🗑️' },
                { name: 'Système de favoris', status: 'implemented', icon: '⭐' },
                { name: 'Filtres et recherche', status: 'implemented', icon: '🔍' },
                { name: 'Suivi de l\'humeur', status: 'implemented', icon: '😊' },
                { name: 'Tags et émotions', status: 'implemented', icon: '🏷️' },
                { name: 'Statistiques', status: 'implemented', icon: '📊' },
                { name: 'Interface responsive', status: 'implemented', icon: '📱' },
                { name: 'Gestion d\'erreurs', status: 'implemented', icon: '⚠️' },
              ].map((feature, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <span className="text-lg">{feature.icon}</span>
                  <div className="flex-1">
                    <span className="text-sm font-medium text-gray-900">{feature.name}</span>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    feature.status === 'implemented' 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-yellow-100 text-yellow-700'
                  }`}>
                    {feature.status === 'implemented' ? '✅ OK' : '⏳ En cours'}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
} 