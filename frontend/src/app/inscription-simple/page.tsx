'use client';

import { useState } from 'react';
import { createBrowserClient } from '@supabase/ssr';

export default function InscriptionSimple() {
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);

  // Client Supabase
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    
    const formData = new FormData(e.currentTarget);
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;
    const name = formData.get('name') as string;

    try {
      // Test 1: Inscription Supabase
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: { name }
        }
      });

      if (error) {
        setStatus(`❌ Erreur: ${error.message}`);
      } else {
        setStatus(`✅ Inscription réussie! Email: ${email}`);
        
        // Test 2: Connexion automatique
        const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
          email,
          password
        });
        
        if (!loginError) {
          setStatus(prev => prev + '\n✅ Connexion automatique réussie!');
        }
      }
    } catch (error) {
      setStatus(`❌ Erreur: ${error}`);
    }
    
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6">📝 Inscription Simple - Test Supabase</h1>
        
        {/* Status */}
        {status && (
          <div className={`mb-6 p-4 rounded whitespace-pre-line ${
            status.includes('✅') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {status}
          </div>
        )}

        {/* Formulaire simple */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Nom</label>
            <input 
              type="text" 
              name="name"
              defaultValue="Test User"
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Email</label>
            <input 
              type="email" 
              name="email"
              defaultValue={`test${Date.now()}@mindflow.pro`}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Mot de passe</label>
            <input 
              type="password" 
              name="password"
              defaultValue="Test123456!"
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <button 
            type="submit"
            disabled={loading}
            className={`w-full p-3 rounded font-semibold transition ${
              loading 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {loading ? '⏳ Inscription en cours...' : '🚀 S\'inscrire'}
          </button>
        </form>

        {/* Instructions */}
        <div className="mt-6 p-4 bg-yellow-50 rounded text-sm">
          <p className="font-semibold mb-2">💡 Instructions:</p>
          <ol className="list-decimal list-inside space-y-1">
            <li>Les champs sont pré-remplis avec des valeurs de test</li>
            <li>Cliquez sur "S'inscrire" pour tester</li>
            <li>Un email unique est généré automatiquement</li>
            <li>Si tout est vert, Supabase fonctionne!</li>
          </ol>
        </div>

        {/* Liens */}
        <div className="mt-6 space-y-2 text-center">
          <a href="/test-direct-supabase" className="text-blue-600 hover:underline block">
            → Test Direct API
          </a>
          <a href="/test-phase4-supabase" className="text-blue-600 hover:underline block">
            → Test Phase 4
          </a>
        </div>
      </div>
    </div>
  );
} 