'use client';

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/Layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { MoodTrendChart } from '@/components/Analytics/MoodTrendChart';
import { 
  Brain, 
  Heart, 
  TrendingUp, 
  Activity,
  Target,
  Zap,
  Moon,
  Calendar,
  Award,
  Clock,
  Lightbulb,
  ArrowRight
} from 'lucide-react';

// Données de demo optimisées
const sampleMoodData = [
  { date: '2024-01-15', mood: 7, anxiety: 3, stress: 4, energy: 8, sleep_quality: 7 },
  { date: '2024-01-16', mood: 8, anxiety: 2, stress: 3, energy: 9, sleep_quality: 8 },
  { date: '2024-01-17', mood: 6, anxiety: 4, stress: 5, energy: 6, sleep_quality: 6 },
  { date: '2024-01-18', mood: 9, anxiety: 1, stress: 2, energy: 9, sleep_quality: 9 },
  { date: '2024-01-19', mood: 8, anxiety: 2, stress: 3, energy: 8, sleep_quality: 8 },
];

// Quick Actions optimized
const QuickActions = () => {
  const actions = [
    { label: 'Journal', icon: <Calendar className="w-5 h-5" />, color: 'bg-blue-500', href: '/journal' },
    { label: 'IA Coach', icon: <Brain className="w-5 h-5" />, color: 'bg-purple-500', href: '/ai-coach' },
    { label: 'Analytics', icon: <TrendingUp className="w-5 h-5" />, color: 'bg-green-500', href: '/analytics' },
    { label: 'Télémédecine', icon: <Heart className="w-5 h-5" />, color: 'bg-red-500', href: '/telemedicine-enhanced' }
  ];

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="w-5 h-5 text-yellow-500" />
          Actions Rapides
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {actions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              className="h-20 flex flex-col items-center justify-center gap-2 hover:scale-105 transition-transform"
              onClick={() => window.location.href = action.href}
            >
              <div className={`p-2 rounded-full ${action.color} text-white`}>
                {action.icon}
              </div>
              <span className="text-sm font-medium">{action.label}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Wellness Score optimized
const WellnessScore = () => {
  const score = 85;
  
  return (
    <Card className="shadow-lg border-l-4 border-l-green-500">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-green-500" />
          Score de Bien-être
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-center">
            <div className="text-4xl font-bold text-green-600">{score}/100</div>
            <p className="text-sm text-gray-600">Excellent progrès !</p>
          </div>
          <Progress value={score} className="h-3" />
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-blue-600">Humeur</div>
              <div>8.2/10</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-purple-600">Énergie</div>
              <div>8.5/10</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// AI Insights optimized
const AIInsights = () => {
  const insights = [
    {
      title: "Tendance positive détectée",
      description: "Votre humeur s'améliore depuis 3 jours",
      type: "positive",
      icon: <TrendingUp className="w-4 h-4" />
    },
    {
      title: "Suggestion d'exercice",
      description: "15 min de méditation recommandée",
      type: "suggestion",
      icon: <Lightbulb className="w-4 h-4" />
    }
  ];

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="w-5 h-5 text-purple-500" />
          Insights IA
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {insights.map((insight, index) => (
            <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
              <div className={`p-1 rounded-full ${
                insight.type === 'positive' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'
              }`}>
                {insight.icon}
              </div>
              <div className="flex-1">
                <div className="font-medium text-sm">{insight.title}</div>
                <div className="text-xs text-gray-600 mt-1">{insight.description}</div>
              </div>
              <ArrowRight className="w-4 h-4 text-gray-400" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default function DashboardOptimizedPage() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Mettre à jour le titre de la page dynamiquement
    document.title = 'Dashboard Optimisé - MindFlow Pro';
    
    // Simulation de chargement optimisé
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard Optimisé</h1>
            <p className="text-gray-600 mt-1">
              Vue d'ensemble améliorée de votre bien-être mental
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-green-50 text-green-700">
              ✨ Nouvelles fonctionnalités
            </Badge>
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              🚀 Performance optimisée
            </Badge>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Mood Chart */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-blue-500" />
                  Tendances d'Humeur - 7 derniers jours
                </CardTitle>
              </CardHeader>
              <CardContent>
                <MoodTrendChart 
                  data={sampleMoodData}
                  height={300}
                  metric="mood"
                  variant="area"
                  animated={true}
                />
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <QuickActions />
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Wellness Score */}
            <WellnessScore />

            {/* AI Insights */}
            <AIInsights />

            {/* Stats Card */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5 text-orange-500" />
                  Statistiques
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Sessions cette semaine</span>
                    <span className="font-semibold">12</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Jours consécutifs</span>
                    <span className="font-semibold">5</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Temps total</span>
                    <span className="font-semibold">3h 45m</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Performance Metrics */}
        <Card className="shadow-lg bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-blue-500" />
              Optimisations de Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-white rounded-lg">
                <div className="text-2xl font-bold text-green-600">&lt; 2s</div>
                <div className="text-sm text-gray-600">Temps de chargement</div>
              </div>
              <div className="text-center p-4 bg-white rounded-lg">
                <div className="text-2xl font-bold text-blue-600">95%</div>
                <div className="text-sm text-gray-600">Score Performance</div>
              </div>
              <div className="text-center p-4 bg-white rounded-lg">
                <div className="text-2xl font-bold text-purple-600">&lt; 2MB</div>
                <div className="text-sm text-gray-600">Taille Bundle</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
