'use client';

import React, { useState, useEffect } from 'react';

export default function ApiTestPage() {
  const [healthStatus, setHealthStatus] = useState<string>('Chargement...');
  const [testStatus, setTestStatus] = useState<string>('Chargement...');
  const [statusData, setStatusData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHealthStatus = async () => {
      try {
        const response = await fetch('http://localhost:4000/api/v1/health');
        const data = await response.json();
        setHealthStatus(JSON.stringify(data, null, 2));
      } catch (err) {
        setHealthStatus('Erreur: Impossible de se connecter au endpoint health');
        setError(`Health endpoint error: ${err instanceof Error ? err.message : String(err)}`);
      }
    };

    const fetchTestStatus = async () => {
      try {
        const response = await fetch('http://localhost:4000/api/v1/test');
        const data = await response.json();
        setTestStatus(JSON.stringify(data, null, 2));
      } catch (err) {
        setTestStatus('Erreur: Impossible de se connecter au endpoint test');
        setError(`Test endpoint error: ${err instanceof Error ? err.message : String(err)}`);
      }
    };

    const fetchServerStatus = async () => {
      try {
        const response = await fetch('http://localhost:4000/api/v1/status');
        const data = await response.json();
        setStatusData(data);
      } catch (err) {
        setStatusData(null);
        setError(`Status endpoint error: ${err instanceof Error ? err.message : String(err)}`);
      }
    };

    fetchHealthStatus();
    fetchTestStatus();
    fetchServerStatus();
  }, []);

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Test de l'API MindFlow Pro</h1>
      
      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
          <p className="font-bold">Erreur</p>
          <p>{error}</p>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Health Endpoint</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
            {healthStatus}
          </pre>
        </div>
        
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Test Endpoint</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
            {testStatus}
          </pre>
        </div>
      </div>
      
      {statusData && (
        <div className="mt-6 bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Server Status</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-700">Server</h3>
              <p className="text-lg">{statusData.server}</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-700">Uptime</h3>
              <p className="text-lg">{Math.floor(statusData.uptime / 60)} minutes</p>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-medium text-purple-700">Environment</h3>
              <p className="text-lg">{statusData.environment}</p>
            </div>
            
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h3 className="font-medium text-yellow-700">Memory Usage</h3>
              <p className="text-lg">{Math.round(statusData.memory.heapUsed / 1024 / 1024 * 100) / 100} MB</p>
            </div>
            
            <div className="bg-indigo-50 p-4 rounded-lg">
              <h3 className="font-medium text-indigo-700">Active Sessions</h3>
              <p className="text-lg">{statusData.activeSessions}</p>
            </div>
            
            <div className="bg-pink-50 p-4 rounded-lg">
              <h3 className="font-medium text-pink-700">Registered Users</h3>
              <p className="text-lg">{statusData.registeredUsers}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 