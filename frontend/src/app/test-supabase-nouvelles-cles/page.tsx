'use client';

import { useState, useEffect } from 'react';

export default function TestSupabaseNouvellesCles() {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    runTests();
  }, []);

  const runTests = async () => {
    const testResults: any[] = [];
    
    // Test 1: Configuration des nouvelles clés
    try {
      const config = {
        url: 'https://kvdrukmoxetoiojazukf.supabase.co',
        anon_key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
      };
      
      testResults.push({
        test: 'Configuration nouvelles clés',
        status: 'success',
        message: 'Nouvelles clés API configurées',
        details: {
          url: config.url,
          key_preview: config.anon_key.substring(0, 30) + '...',
          key_length: config.anon_key.length
        }
      });
    } catch (error) {
      testResults.push({
        test: 'Configuration nouvelles clés',
        status: 'error',
        message: 'Erreur configuration',
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      });
    }

    // Test 2: Test HTTP de base avec nouvelles clés
    try {
      const response = await fetch('https://kvdrukmoxetoiojazukf.supabase.co/rest/v1/', {
        headers: {
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
        }
      });

      if (response.ok) {
        testResults.push({
          test: 'Test HTTP nouvelles clés',
          status: 'success',
          message: `✅ API accessible (${response.status})`,
          details: {
            status: response.status,
            headers: Object.fromEntries(response.headers.entries())
          }
        });
      } else {
        const errorText = await response.text();
        testResults.push({
          test: 'Test HTTP nouvelles clés',
          status: 'error',
          message: `❌ Erreur HTTP (${response.status})`,
          details: {
            status: response.status,
            error: errorText
          }
        });
      }
    } catch (error) {
      testResults.push({
        test: 'Test HTTP nouvelles clés',
        status: 'error',
        message: '❌ Erreur réseau',
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      });
    }

    // Test 3: Test client Supabase avec nouvelles clés
    try {
      const { createBrowserClient } = await import('@supabase/ssr');
      
      const client = createBrowserClient(
        'https://kvdrukmoxetoiojazukf.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
      );

      const { data, error } = await client.auth.getSession();
      
      if (error) {
        testResults.push({
          test: 'Client Supabase nouvelles clés',
          status: 'error',
          message: '❌ Erreur client Supabase',
          error: error.message,
          details: error
        });
      } else {
        testResults.push({
          test: 'Client Supabase nouvelles clés',
          status: 'success',
          message: '✅ Client Supabase fonctionnel',
          details: {
            session: data.session ? 'Session active' : 'Pas de session',
            client_created: true
          }
        });
      }
    } catch (error) {
      testResults.push({
        test: 'Client Supabase nouvelles clés',
        status: 'error',
        message: '❌ Erreur import/création client',
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      });
    }

    // Test 4: Variables d'environnement
    const envVars = {
      'NEXT_PUBLIC_SUPABASE_URL': process.env.NEXT_PUBLIC_SUPABASE_URL,
      'NEXT_PUBLIC_SUPABASE_ANON_KEY': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Définie' : 'Non définie'
    };

    testResults.push({
      test: 'Variables d\'environnement',
      status: envVars['NEXT_PUBLIC_SUPABASE_URL'] && envVars['NEXT_PUBLIC_SUPABASE_ANON_KEY'] !== 'Non définie' ? 'success' : 'warning',
      message: envVars['NEXT_PUBLIC_SUPABASE_URL'] ? '✅ Variables configurées' : '⚠️ Variables manquantes',
      details: envVars
    });

    setResults(testResults);
    setLoading(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return '#22c55e';
      case 'error': return '#ef4444';
      case 'warning': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '20px', fontFamily: 'system-ui' }}>
        <h1>🧪 Test Nouvelles Clés Supabase</h1>
        <p>Tests en cours...</p>
      </div>
    );
  }

  const successCount = results.filter(r => r.status === 'success').length;
  const errorCount = results.filter(r => r.status === 'error').length;
  const warningCount = results.filter(r => r.status === 'warning').length;

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'system-ui',
      maxWidth: '1200px',
      margin: '0 auto'
    }}>
      <h1 style={{ color: '#1e40af' }}>🧪 Test Nouvelles Clés Supabase</h1>
      
      {/* Résumé */}
      <div style={{
        background: successCount === results.length ? '#f0fdf4' : '#fff7ed',
        border: `2px solid ${successCount === results.length ? '#22c55e' : '#f59e0b'}`,
        borderRadius: '8px',
        padding: '20px',
        marginBottom: '20px'
      }}>
        <h2 style={{ margin: '0 0 10px 0', color: '#1f2937' }}>
          🎯 Résumé du Diagnostic
        </h2>
        <div style={{ 
          display: 'flex', 
          gap: '30px',
          fontSize: '18px',
          fontWeight: 'bold'
        }}>
          <span style={{ color: '#22c55e' }}>{successCount} Réussies</span>
          <span style={{ color: '#f59e0b' }}>{warningCount} Avertissements</span>
          <span style={{ color: '#ef4444' }}>{errorCount} Erreurs</span>
        </div>
      </div>

      {/* Résultats détaillés */}
      {results.map((result, index) => (
        <div 
          key={index}
          style={{
            border: `2px solid ${getStatusColor(result.status)}`,
            borderRadius: '8px',
            padding: '15px',
            marginBottom: '15px',
            background: result.status === 'success' ? '#f0fdf4' : 
                       result.status === 'error' ? '#fef2f2' : '#fffbeb'
          }}
        >
          <h3 style={{ 
            margin: '0 0 10px 0',
            color: getStatusColor(result.status),
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            {getStatusIcon(result.status)} {result.test}
          </h3>
          
          <p style={{ 
            margin: '5px 0',
            color: '#374151',
            fontWeight: 'bold'
          }}>
            {result.message}
          </p>

          {result.error && (
            <div style={{
              background: '#fee2e2',
              border: '1px solid #fecaca',
              borderRadius: '4px',
              padding: '10px',
              margin: '10px 0',
              fontFamily: 'monospace',
              fontSize: '14px',
              color: '#dc2626'
            }}>
              <strong>Erreur:</strong> {result.error}
            </div>
          )}

          {result.details && (
            <details style={{ marginTop: '10px' }}>
              <summary style={{ 
                cursor: 'pointer',
                color: '#6b7280',
                fontSize: '14px'
              }}>
                Voir les détails
              </summary>
              <pre style={{
                background: '#f9fafb',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                padding: '10px',
                margin: '10px 0',
                fontSize: '12px',
                overflow: 'auto',
                color: '#374151'
              }}>
                {JSON.stringify(result.details, null, 2)}
              </pre>
            </details>
          )}
        </div>
      ))}

      <div style={{
        background: '#f8fafc',
        border: '1px solid #e2e8f0',
        borderRadius: '8px',
        padding: '15px',
        marginTop: '20px'
      }}>
        <h3 style={{ color: '#475569', margin: '0 0 10px 0' }}>📋 Instructions</h3>
        <p style={{ margin: '5px 0', color: '#64748b' }}>
          1. Si vous voyez des erreurs "Invalid API key", les nouvelles clés sont maintenant intégrées<br/>
          2. Créez un fichier <code>.env.local</code> dans le dossier frontend avec les nouvelles clés<br/>
          3. Redémarrez le serveur Next.js après avoir créé le fichier .env.local<br/>
          4. Les nouvelles clés sont valides jusqu'en 2066 !
        </p>
      </div>
    </div>
  );
} 