'use client';

import React, { useState } from 'react';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Bell, 
  Calendar, 
  MessageCircle, 
  AlertTriangle, 
  Activity, 
  Heart,
  Users,
  BarChart3,
  Wifi,
  WifiOff
} from 'lucide-react';

export default function TestNotificationsPage() {
  const { accessToken } = useAuthStore();
  const { isConnected, connectionStatus, notifications } = useWebSocket();
  const [isLoading, setIsLoading] = useState(false);
  const [lastResponse, setLastResponse] = useState<string>('');

  const sendTestNotification = async (endpoint: string, type: string) => {
    if (!accessToken) {
      setLastResponse('Error: Not authenticated');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`http://localhost:4000/api/v1/test-notifications/${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (response.ok) {
        setLastResponse(`✅ ${type} notification sent successfully`);
      } else {
        setLastResponse(`❌ Error: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      setLastResponse(`❌ Network error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testButtons = [
    {
      endpoint: 'test-appointment-reminder',
      type: 'Appointment Reminder',
      icon: <Calendar className="w-4 h-4" />,
      color: 'bg-blue-500 hover:bg-blue-600',
      description: 'Test appointment reminder notification'
    },
    {
      endpoint: 'test-appointment-status',
      type: 'Appointment Status',
      icon: <Calendar className="w-4 h-4" />,
      color: 'bg-green-500 hover:bg-green-600',
      description: 'Test appointment status update'
    },
    {
      endpoint: 'test-new-message',
      type: 'New Message',
      icon: <MessageCircle className="w-4 h-4" />,
      color: 'bg-purple-500 hover:bg-purple-600',
      description: 'Test new message notification'
    },
    {
      endpoint: 'test-crisis-alert',
      type: 'Crisis Alert',
      icon: <AlertTriangle className="w-4 h-4" />,
      color: 'bg-red-500 hover:bg-red-600',
      description: 'Test crisis alert notification'
    },
    {
      endpoint: 'test-wellness-progress',
      type: 'Wellness Progress',
      icon: <Activity className="w-4 h-4" />,
      color: 'bg-indigo-500 hover:bg-indigo-600',
      description: 'Test wellness progress update'
    },
    {
      endpoint: 'test-mood-update',
      type: 'Mood Update',
      icon: <Heart className="w-4 h-4" />,
      color: 'bg-pink-500 hover:bg-pink-600',
      description: 'Test mood tracking update'
    },
    {
      endpoint: 'test-professional-availability',
      type: 'Professional Availability',
      icon: <Users className="w-4 h-4" />,
      color: 'bg-teal-500 hover:bg-teal-600',
      description: 'Test professional availability change'
    },
    {
      endpoint: 'test-dashboard-update',
      type: 'Dashboard Update',
      icon: <BarChart3 className="w-4 h-4" />,
      color: 'bg-orange-500 hover:bg-orange-600',
      description: 'Test dashboard data update'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Real-Time Notifications Test
          </h1>
          <p className="text-gray-600">
            Test the WebSocket real-time notification system for MindFlow Pro
          </p>
        </div>

        {/* Connection Status */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {isConnected ? (
                <Wifi className="w-5 h-5 text-green-600" />
              ) : (
                <WifiOff className="w-5 h-5 text-red-600" />
              )}
              <span>WebSocket Connection Status</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className={`text-lg font-semibold ${
                  isConnected ? 'text-green-600' : 'text-red-600'
                }`}>
                  {isConnected ? 'Connected' : 'Disconnected'}
                </div>
                <div className="text-sm text-gray-600">Connection Status</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-blue-600">
                  {connectionStatus}
                </div>
                <div className="text-sm text-gray-600">Connection State</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-purple-600">
                  {notifications.total}
                </div>
                <div className="text-sm text-gray-600">Total Notifications</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notification Counters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="w-5 h-5 text-blue-600" />
              <span>Current Notification Counts</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {notifications.appointments}
                </div>
                <div className="text-sm text-gray-600">Appointments</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {notifications.messages}
                </div>
                <div className="text-sm text-gray-600">Messages</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {notifications.crisisAlerts}
                </div>
                <div className="text-sm text-gray-600">Crisis Alerts</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {notifications.total}
                </div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Buttons */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Notification Types</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {testButtons.map((button) => (
                <Button
                  key={button.endpoint}
                  onClick={() => sendTestNotification(button.endpoint, button.type)}
                  disabled={isLoading || !isConnected}
                  className={`${button.color} text-white p-4 h-auto flex flex-col items-center space-y-2`}
                >
                  {button.icon}
                  <span className="font-medium">{button.type}</span>
                  <span className="text-xs opacity-90 text-center">
                    {button.description}
                  </span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Response Display */}
        {lastResponse && (
          <Card>
            <CardHeader>
              <CardTitle>Last Response</CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`p-4 rounded-lg font-mono text-sm ${
                lastResponse.startsWith('✅') 
                  ? 'bg-green-50 text-green-800 border border-green-200' 
                  : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                {lastResponse}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-gray-600">
              <p>1. Make sure you're logged in and the WebSocket connection shows as "Connected"</p>
              <p>2. Click any test button to send a real-time notification</p>
              <p>3. Watch for toast notifications to appear in the top-right corner</p>
              <p>4. Check the notification counters to see them update in real-time</p>
              <p>5. Go back to the dashboard to see the real-time notifications panel</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
