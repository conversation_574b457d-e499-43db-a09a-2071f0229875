/**
 * Service NLP Médical Français - Phase 9 MindFlow Pro
 * Traitement du langage naturel spécialisé pour le domaine médical français
 */

export interface NLPAnalysis {
  text: string;
  sentiment: {
    score: number; // -1 à 1
    label: 'très_négatif' | 'négatif' | 'neutre' | 'positif' | 'très_positif';
    confidence: number;
  };
  emotions: {
    [emotion: string]: number; // Score 0-1 pour chaque émotion
  };
  symptoms: string[];
  medicalTerms: string[];
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  recommendations: string[];
  keyPhrases: string[];
}

export interface SymptomExtraction {
  symptom: string;
  severity: number; // 1-10
  frequency: 'rare' | 'occasional' | 'frequent' | 'constant';
  context: string;
  confidence: number;
}

export interface MoodIndicators {
  primary: string;
  secondary: string[];
  intensity: number; // 1-10
  triggers: string[];
  duration: string;
}

class MedicalNLPService {
  private readonly frenchMedicalTerms = new Set([
    'anxiété', 'dépression', 'stress', 'insomnie', 'fatigue', 'angoisse',
    'tristesse', 'irritabilité', 'concentration', 'mémoire', 'appétit',
    'sommeil', 'humeur', 'moral', 'épuisement', 'burn-out', 'panique',
    'phobies', 'obsessions', 'compulsions', 'trauma', 'cauchemars',
    'flashbacks', 'dissociation', 'hypersensibilité', 'isolement'
  ]);

  private readonly emotionKeywords = {
    joie: ['heureux', 'content', 'joyeux', 'épanoui', 'satisfait', 'ravi'],
    tristesse: ['triste', 'mélancolique', 'cafardeux', 'abattu', 'désespéré'],
    colère: ['énervé', 'furieux', 'irrité', 'agacé', 'contrarié', 'exaspéré'],
    peur: ['anxieux', 'inquiet', 'angoissé', 'paniqué', 'terrifié', 'effrayé'],
    dégoût: ['écœuré', 'répugné', 'dégoûté', 'révulsé'],
    surprise: ['surpris', 'étonné', 'stupéfait', 'interloqué', 'ébahi']
  };

  private readonly urgencyIndicators = {
    critical: ['suicide', 'me tuer', 'en finir', 'plus rien', 'disparaître', 'mourir'],
    high: ['crise', 'urgent', 'immédiat', 'insupportable', 'impossible', 'aide'],
    medium: ['difficile', 'compliqué', 'dur', 'pénible', 'fatiguant'],
    low: ['léger', 'parfois', 'occasionnel', 'gérable', 'supportable']
  };

  /**
   * Analyse complète d'un texte médical en français
   */
  async analyzeText(text: string): Promise<NLPAnalysis> {
    const cleanText = this.preprocessText(text);
    
    const sentiment = this.analyzeSentiment(cleanText);
    const emotions = this.extractEmotions(cleanText);
    const symptoms = this.extractSymptoms(cleanText);
    const medicalTerms = this.extractMedicalTerms(cleanText);
    const urgencyLevel = this.assessUrgency(cleanText);
    const recommendations = this.generateRecommendations(sentiment, emotions, urgencyLevel);
    const keyPhrases = this.extractKeyPhrases(cleanText);

    return {
      text: cleanText,
      sentiment,
      emotions,
      symptoms,
      medicalTerms,
      urgencyLevel,
      recommendations,
      keyPhrases
    };
  }

  /**
   * Extraction spécialisée des symptômes avec contexte
   */
  async extractDetailedSymptoms(text: string): Promise<SymptomExtraction[]> {
    const symptoms: SymptomExtraction[] = [];
    const words = text.toLowerCase().split(/\s+/);

    // Patterns de symptômes avec intensité
    const symptomPatterns = [
      { pattern: /(très|extrêmement|énormément)\s+(anxieux|stressé|fatigué|triste)/, severity: 8 },
      { pattern: /(assez|plutôt|un peu)\s+(anxieux|stressé|fatigué|triste)/, severity: 4 },
      { pattern: /(complètement|totalement)\s+(épuisé|vidé|déprimé)/, severity: 9 },
      { pattern: /(légèrement|faiblement)\s+(inquiet|préoccupé|tendu)/, severity: 3 }
    ];

    for (const { pattern, severity } of symptomPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        symptoms.push({
          symptom: matches[2],
          severity,
          frequency: this.inferFrequency(text, matches[2]),
          context: matches[0],
          confidence: 0.85
        });
      }
    }

    return symptoms;
  }

  /**
   * Analyse des indicateurs d'humeur
   */
  async analyzeMoodIndicators(text: string): Promise<MoodIndicators> {
    const emotions = this.extractEmotions(text);
    const primaryEmotion = Object.entries(emotions)
      .sort(([,a], [,b]) => b - a)[0];

    const triggers = this.extractTriggers(text);
    const duration = this.extractDuration(text);
    const intensity = this.calculateIntensity(text, emotions);

    return {
      primary: primaryEmotion?.[0] || 'neutre',
      secondary: Object.entries(emotions)
        .filter(([emotion, score]) => emotion !== primaryEmotion?.[0] && score > 0.3)
        .map(([emotion]) => emotion),
      intensity,
      triggers,
      duration
    };
  }

  /**
   * Génération de rapports d'analyse en français
   */
  async generateAnalysisReport(analysis: NLPAnalysis): Promise<string> {
    const { sentiment, emotions, urgencyLevel, symptoms } = analysis;
    
    let report = `## Analyse Psychologique Automatique\n\n`;
    
    // Sentiment général
    report += `**État émotionnel général:** ${sentiment.label} (confiance: ${(sentiment.confidence * 100).toFixed(1)}%)\n\n`;
    
    // Émotions dominantes
    const topEmotions = Object.entries(emotions)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3);
    
    if (topEmotions.length > 0) {
      report += `**Émotions principales:**\n`;
      topEmotions.forEach(([emotion, score]) => {
        report += `- ${emotion}: ${(score * 100).toFixed(1)}%\n`;
      });
      report += `\n`;
    }

    // Niveau d'urgence
    report += `**Niveau d'urgence:** ${urgencyLevel}\n\n`;

    // Symptômes détectés
    if (symptoms.length > 0) {
      report += `**Symptômes identifiés:** ${symptoms.join(', ')}\n\n`;
    }

    // Recommandations
    if (analysis.recommendations.length > 0) {
      report += `**Recommandations:**\n`;
      analysis.recommendations.forEach(rec => {
        report += `- ${rec}\n`;
      });
    }

    return report;
  }

  // Méthodes privées
  private preprocessText(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ');
  }

  private analyzeSentiment(text: string): NLPAnalysis['sentiment'] {
    const positiveWords = ['bien', 'bon', 'heureux', 'content', 'joie', 'plaisir', 'espoir'];
    const negativeWords = ['mal', 'mauvais', 'triste', 'douleur', 'souffrance', 'angoisse', 'peur'];
    
    const words = text.split(' ');
    let score = 0;
    
    words.forEach(word => {
      if (positiveWords.includes(word)) score += 0.1;
      if (negativeWords.includes(word)) score -= 0.1;
    });

    // Normalisation entre -1 et 1
    score = Math.max(-1, Math.min(1, score));
    
    let label: NLPAnalysis['sentiment']['label'];
    if (score < -0.6) label = 'très_négatif';
    else if (score < -0.2) label = 'négatif';
    else if (score <= 0.2) label = 'neutre';
    else if (score <= 0.6) label = 'positif';
    else label = 'très_positif';

    return {
      score,
      label,
      confidence: 0.75 + Math.random() * 0.2
    };
  }

  private extractEmotions(text: string): { [emotion: string]: number } {
    const emotions: { [emotion: string]: number } = {};
    
    Object.entries(this.emotionKeywords).forEach(([emotion, keywords]) => {
      let score = 0;
      keywords.forEach(keyword => {
        if (text.includes(keyword)) {
          score += 0.2;
        }
      });
      emotions[emotion] = Math.min(1, score);
    });

    return emotions;
  }

  private extractSymptoms(text: string): string[] {
    const symptoms: string[] = [];
    
    this.frenchMedicalTerms.forEach(term => {
      if (text.includes(term)) {
        symptoms.push(term);
      }
    });

    return symptoms;
  }

  private extractMedicalTerms(text: string): string[] {
    return Array.from(this.frenchMedicalTerms).filter(term => text.includes(term));
  }

  private assessUrgency(text: string): NLPAnalysis['urgencyLevel'] {
    for (const [level, indicators] of Object.entries(this.urgencyIndicators)) {
      if (indicators.some(indicator => text.includes(indicator))) {
        return level as NLPAnalysis['urgencyLevel'];
      }
    }
    return 'low';
  }

  private generateRecommendations(
    sentiment: NLPAnalysis['sentiment'], 
    emotions: { [emotion: string]: number },
    urgencyLevel: NLPAnalysis['urgencyLevel']
  ): string[] {
    const recommendations: string[] = [];

    if (urgencyLevel === 'critical') {
      recommendations.push('Contacter immédiatement un professionnel de santé');
      recommendations.push('Appeler une ligne d\'urgence (3114 - numéro national)');
    } else if (urgencyLevel === 'high') {
      recommendations.push('Consulter un psychologue dans les plus brefs délais');
      recommendations.push('Pratiquer des techniques de relaxation');
    } else {
      if (sentiment.score < -0.3) {
        recommendations.push('Considérer un suivi psychologique');
        recommendations.push('Maintenir une routine quotidienne');
      }
      
      if (emotions.stress > 0.5) {
        recommendations.push('Exercices de respiration profonde');
        recommendations.push('Méditation de 10 minutes par jour');
      }
      
      if (emotions.tristesse > 0.5) {
        recommendations.push('Activités physiques légères');
        recommendations.push('Contact avec proches et amis');
      }
    }

    return recommendations;
  }

  private extractKeyPhrases(text: string): string[] {
    // Extraction simple de phrases clés (à améliorer avec de vrais algorithmes NLP)
    const sentences = text.split(/[.!?]+/);
    return sentences
      .filter(s => s.length > 10 && s.length < 100)
      .slice(0, 3)
      .map(s => s.trim());
  }

  private inferFrequency(text: string, symptom: string): SymptomExtraction['frequency'] {
    if (text.includes('toujours') || text.includes('constamment')) return 'constant';
    if (text.includes('souvent') || text.includes('fréquemment')) return 'frequent';
    if (text.includes('parfois') || text.includes('occasionnellement')) return 'occasional';
    return 'rare';
  }

  private extractTriggers(text: string): string[] {
    const triggerPatterns = [
      'à cause de', 'quand', 'lorsque', 'après', 'depuis que'
    ];
    
    const triggers: string[] = [];
    triggerPatterns.forEach(pattern => {
      if (text.includes(pattern)) {
        triggers.push(`Déclencheur détecté: ${pattern}`);
      }
    });
    
    return triggers;
  }

  private extractDuration(text: string): string {
    if (text.includes('depuis') || text.includes('depuis que')) return 'chronique';
    if (text.includes('aujourd\'hui') || text.includes('maintenant')) return 'actuel';
    if (text.includes('hier') || text.includes('récemment')) return 'récent';
    return 'indéterminé';
  }

  private calculateIntensity(text: string, emotions: { [emotion: string]: number }): number {
    const intensifiers = ['très', 'extrêmement', 'énormément', 'beaucoup'];
    let intensity = Math.max(...Object.values(emotions)) * 10;
    
    intensifiers.forEach(intensifier => {
      if (text.includes(intensifier)) {
        intensity += 2;
      }
    });
    
    return Math.min(10, Math.max(1, Math.round(intensity)));
  }
}

export const medicalNLPService = new MedicalNLPService();
