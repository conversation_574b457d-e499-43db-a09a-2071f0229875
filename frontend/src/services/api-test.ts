const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api/v1';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface HealthCheckResponse {
  success: boolean;
  status: string;
  version: string;
  timestamp: string;
  database: string;
  api: string;
}

class ApiTestService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const url = `${API_BASE_URL}${endpoint}`;
      console.log(`🌐 API Request: ${options.method || 'GET'} ${url}`);
      
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ API Response:`, data);
      
      return {
        success: true,
        data,
      };
    } catch (error) {
      console.error(`❌ API Error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async healthCheck(): Promise<ApiResponse<HealthCheckResponse>> {
    return this.request<HealthCheckResponse>('/health');
  }

  async testEndpoint(): Promise<ApiResponse> {
    return this.request('/test');
  }

  async getApiInfo(): Promise<ApiResponse> {
    // Test avec l'endpoint racine du serveur
    const baseUrl = API_BASE_URL.replace('/api/v1', '');
    const response = await fetch(baseUrl);
    const data = await response.json();
    return { success: true, data };
  }
}

export const apiTestService = new ApiTestService();
export default apiTestService; 