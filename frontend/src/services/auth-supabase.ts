'use client';

import { createSupabaseClient } from '@/lib/supabase/browser-client';
import { AuthResponse, User } from '@supabase/supabase-js';
import { FEATURE_FLAGS } from '@/lib/config/feature-flags';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
}

export interface AuthUserResponse {
  user: {
    id: string;
    email: string;
    name: string;
  };
  token: string;
}

class SupabaseAuthService {
  private supabase = createSupabaseClient();

  async login(credentials: LoginCredentials): Promise<AuthUserResponse> {
    try {
      console.log('🔐 Tentative de connexion avec:', credentials.email);
      
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        console.error('❌ Erreur Supabase login:', error);
        throw new Error(error.message || 'Erreur de connexion');
      }

      if (!data.user) {
        throw new Error('Aucun utilisateur trouvé');
      }

      console.log('✅ Connexion réussie:', data.user.email);

      return {
        user: {
          id: data.user.id,
          email: data.user.email || '',
          name: data.user.user_metadata?.name || data.user.email?.split('@')[0] || 'Utilisateur',
        },
        token: data.session?.access_token || '',
      };
    } catch (error: any) {
      console.error('🚨 Erreur de connexion:', error);
      throw error;
    }
  }

  async register(userData: RegisterData): Promise<AuthUserResponse> {
    try {
      console.log('📝 Tentative d\'inscription avec:', userData.email);
      
      const { data, error } = await this.supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            name: userData.name,
            full_name: userData.name,
          },
        },
      });

      if (error) {
        console.error('❌ Erreur Supabase register:', error);
        throw new Error(error.message || 'Erreur d\'inscription');
      }

      if (!data.user) {
        throw new Error('Erreur lors de la création du compte');
      }

      console.log('✅ Inscription réussie:', data.user.email);

      return {
        user: {
          id: data.user.id,
          email: data.user.email || '',
          name: userData.name,
        },
        token: data.session?.access_token || '',
      };
    } catch (error: any) {
      console.error('🚨 Erreur d\'inscription:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      console.log('🚪 Déconnexion...');
      const { error } = await this.supabase.auth.signOut();
      
      if (error) {
        console.error('❌ Erreur lors de la déconnexion:', error);
        throw error;
      }

      console.log('✅ Déconnexion réussie');
    } catch (error) {
      console.error('🚨 Erreur de déconnexion:', error);
      throw error;
    }
  }

  async getCurrentUser(): Promise<AuthUserResponse | null> {
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser();

      if (error) {
        console.error('❌ Erreur lors de la récupération de l\'utilisateur:', error);
        return null;
      }

      if (!user) {
        return null;
      }

      console.log('👤 Utilisateur actuel:', user.email);

      return {
        user: {
          id: user.id,
          email: user.email || '',
          name: user.user_metadata?.name || user.user_metadata?.full_name || user.email?.split('@')[0] || 'Utilisateur',
        },
        token: '', // Le token sera géré automatiquement par Supabase
      };
    } catch (error) {
      console.error('🚨 Erreur getCurrentUser:', error);
      return null;
    }
  }

  async getSession() {
    try {
      const { data: { session }, error } = await this.supabase.auth.getSession();
      
      if (error) {
        console.error('❌ Erreur lors de la récupération de la session:', error);
        return null;
      }

      return session;
    } catch (error) {
      console.error('🚨 Erreur getSession:', error);
      return null;
    }
  }

  // Observer les changements d'état d'authentification
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return this.supabase.auth.onAuthStateChange(callback);
  }

  getAuthHeader(): { Authorization: string } | {} {
    // Avec Supabase, les headers sont gérés automatiquement
    return {};
  }
}

// Instance singleton
const SupabaseAuth = new SupabaseAuthService();

export default SupabaseAuth;

// Fonction helper pour choisir le bon service d'auth
export const getAuthService = () => {
  if (FEATURE_FLAGS.USE_SUPABASE_AUTH) {
    return SupabaseAuth;
  }
  // Fallback vers l'ancienne API (pour compatibilité)
  return null;
}; 