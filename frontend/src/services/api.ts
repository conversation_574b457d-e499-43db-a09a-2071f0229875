import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import type { User, Tokens, ApiResponse, JournalEntry } from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api/v1';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
      withCredentials: true, // Include credentials for CORS
    });

    // Log API configuration in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🔗 API Service initialized with base URL:', API_BASE_URL);
    }

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token refresh and network errors
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        // Log network errors in development
        if (process.env.NODE_ENV === 'development') {
          if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
            console.error('🚨 Network Error:', {
              message: error.message,
              code: error.code,
              config: {
                url: error.config?.url,
                method: error.config?.method,
                baseURL: error.config?.baseURL,
              }
            });
          }
        }

        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = this.getRefreshToken();
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              this.setTokens(response.data.tokens.accessToken, response.data.tokens.refreshToken);
              originalRequest.headers.Authorization = `Bearer ${response.data.tokens.accessToken}`;
              return this.api(originalRequest);
            }
          } catch {
            this.clearTokens();
            if (typeof window !== 'undefined') {
              window.location.href = '/auth/login';
            }
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Token management
  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('accessToken');
    }
    return null;
  }

  private getRefreshToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('refreshToken');
    }
    return null;
  }

  private setTokens(accessToken: string, refreshToken: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', refreshToken);
    }
  }

  private clearTokens(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
    }
  }

  // Méthode utilitaire pour extraire un message d'erreur lisible
  static extractErrorMessage(error: unknown): string {
    if (axios.isAxiosError(error)) {
      return (
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        'Erreur inconnue.'
      );
    }
    if (error instanceof Error) {
      return error.message;
    }
    return 'Erreur inconnue.';
  }

  // Basic HTTP methods
  async get(url: string, params?: Record<string, unknown>): Promise<AxiosResponse> {
    return this.api.get(url, { params });
  }

  async post(url: string, data?: Record<string, unknown>): Promise<AxiosResponse> {
    return this.api.post(url, data);
  }

  async put(url: string, data?: Record<string, unknown>): Promise<AxiosResponse> {
    return this.api.put(url, data);
  }

  async delete(url: string): Promise<AxiosResponse> {
    return this.api.delete(url);
  }

  // Auth endpoints
  async login(credentials: { email: string; password: string }): Promise<ApiResponse<{ user: User; tokens: Tokens }>> {
    const response = await this.api.post<ApiResponse<{ user: User; tokens: Tokens }>>('/auth/login', credentials);
    const data = response.data;
    if (data && data.success && data.data?.tokens) {
      this.setTokens(data.data.tokens.accessToken, data.data.tokens.refreshToken);
      return data;
    }
    return {
      success: false,
      message: 'Réponse du serveur invalide.',
      data: undefined,
    };
  }

  async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
    confirmPassword?: string;
  }): Promise<ApiResponse<{ user: User }>> {
    const { confirmPassword, ...registrationData } = userData;
    const response = await this.api.post<ApiResponse<{ user: User }>>('/auth/register', registrationData);
    return response.data;
  }

  async refreshToken(refreshToken: string): Promise<ApiResponse<{ tokens: Tokens }>> {
    const response = await this.api.post<ApiResponse<{ tokens: Tokens }>>('/auth/refresh', { refreshToken });
    return response.data;
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    const response = await this.api.get<ApiResponse<User>>('/auth/me');
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } finally {
      this.clearTokens();
    }
  }

  // Journal endpoints
  async createJournalEntry(entryData: {
    title: string;
    content: string;
    entryType?: string;
    moodLevel?: number;
    tags?: string[];
    emotions?: string[];
    stressLevel?: number;
    energyLevel?: number;
    sleepQuality?: number;
    gratitudeNotes?: string;
    goals?: string;
    challenges?: string;
    achievements?: string;
    isPrivate?: boolean;
  }): Promise<ApiResponse<JournalEntry>> {
    const response = await this.api.post<ApiResponse<JournalEntry>>('/journal', entryData);
    const data = response.data;
    if (data && data.success) {
      return data;
    }
    return {
      success: false,
      message: 'Réponse du serveur invalide.',
      data: undefined,
    };
  }

  async getJournalEntries(params?: {
    page?: number;
    limit?: number;
    entryType?: string;
    moodLevel?: number;
    isPrivate?: boolean;
    isFavorite?: boolean;
    startDate?: string;
    endDate?: string;
    tags?: string[];
    emotions?: string[];
  }): Promise<ApiResponse<JournalEntry[]>> {
    const response = await this.api.get<ApiResponse<JournalEntry[]>>('/journal', { params });
    const data = response.data;
    if (data && data.success) {
      return data;
    }
    return {
      success: false,
      message: 'Réponse du serveur invalide.',
      data: undefined,
    };
  }

  async getJournalEntry(id: string): Promise<ApiResponse<JournalEntry>> {
    const response = await this.api.get<ApiResponse<JournalEntry>>(`/journal/${id}`);
    const data = response.data;
    if (data && data.success) {
      return data;
    }
    return {
      success: false,
      message: 'Réponse du serveur invalide.',
      data: undefined,
    };
  }

  async updateJournalEntry(id: string, entryData: Partial<JournalEntry>): Promise<ApiResponse<JournalEntry>> {
    const response = await this.api.put<ApiResponse<JournalEntry>>(`/journal/${id}`, entryData);
    const data = response.data;
    if (data && data.success) {
      return data;
    }
    return {
      success: false,
      message: 'Réponse du serveur invalide.',
      data: undefined,
    };
  }

  async deleteJournalEntry(id: string): Promise<ApiResponse<{ deleted: boolean }>> {
    const response = await this.api.delete<ApiResponse<{ deleted: boolean }>>(`/journal/${id}`);
    const data = response.data;
    if (data && data.success) {
      return data;
    }
    return {
      success: false,
      message: 'Réponse du serveur invalide.',
      data: undefined,
    };
  }

  async getJournalStats(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/journal/stats');
    const data = response.data;
    if (data && data.success) {
      return data;
    }
    return {
      success: false,
      message: 'Réponse du serveur invalide.',
      data: undefined,
    };
  }

  async getJournalSuggestions(): Promise<ApiResponse<string[]>> {
    const response = await this.api.get<ApiResponse<string[]>>('/journal/suggestions');
    const data = response.data;
    if (data && data.success) {
      return data;
    }
    return {
      success: false,
      message: 'Réponse du serveur invalide.',
      data: undefined,
    };
  }

  // AI Coach endpoints
  async createAIInteraction(data: {
    userMessage: string;
    interactionType?: string;
    sessionId?: string;
    context?: Record<string, unknown>;
  }): Promise<AxiosResponse> {
    return this.api.post('/ai-coach/interact', data);
  }

  async getAIInteractions(params?: {
    page?: number;
    limit?: number;
    sessionId?: string;
  }): Promise<AxiosResponse> {
    return this.api.get('/ai-coach/interactions', { params });
  }

  async getAIInteraction(id: string): Promise<AxiosResponse> {
    return this.api.get(`/ai-coach/interactions/${id}`);
  }

  async rateAIInteraction(id: string, rating: number, feedback?: string): Promise<AxiosResponse> {
    return this.api.post(`/ai-coach/interactions/${id}/rate`, { rating, feedback });
  }

  async getAISessions(): Promise<AxiosResponse> {
    return this.api.get('/ai-coach/sessions');
  }

  async getAIStats(): Promise<AxiosResponse> {
    return this.api.get('/ai-coach/stats');
  }

  async getCrisisResources(): Promise<AxiosResponse> {
    return this.api.get('/ai-coach/crisis-resources');
  }

  async getWellnessTips(): Promise<AxiosResponse> {
    return this.api.get('/ai-coach/wellness-tips');
  }

  // Wellness Modules endpoints
  async getWellnessModules(params?: {
    page?: number;
    limit?: number;
    moduleType?: string;
    difficultyLevel?: string;
    isFeatured?: boolean;
    requiresSupervision?: boolean;
    tags?: string[];
  }): Promise<AxiosResponse> {
    return this.api.get('/wellness', { params });
  }

  async getWellnessModule(moduleId: string): Promise<AxiosResponse> {
    return this.api.get(`/wellness/${moduleId}`);
  }

  async startWellnessModule(moduleId: string): Promise<AxiosResponse> {
    return this.api.post(`/wellness/${moduleId}/start`);
  }

  async updateModuleProgress(moduleId: string, sectionId: string, data: {
    timeSpentMinutes?: number;
    notes?: string;
  }): Promise<AxiosResponse> {
    return this.api.post(`/wellness/${moduleId}/sections/${sectionId}/complete`, data);
  }

  async getUserModuleProgress(params?: { page?: number; limit?: number }): Promise<AxiosResponse> {
    return this.api.get('/wellness/progress', { params });
  }

  async rateWellnessModule(moduleId: string, rating: number, feedback?: string): Promise<AxiosResponse> {
    return this.api.post(`/wellness/${moduleId}/rate`, { rating, feedback });
  }

  async getWellnessStats(): Promise<AxiosResponse> {
    return this.api.get('/wellness/stats');
  }

  async getWellnessFilterOptions(): Promise<AxiosResponse> {
    return this.api.get('/wellness/filter-options');
  }

  // Professional Network endpoints
  async getProfessionals(params?: {
    page?: number;
    limit?: number;
    professionalType?: string;
    acceptingNewClients?: boolean;
    minRating?: number;
    maxHourlyRate?: number;
    specializations?: string[];
    languagesSpoken?: string[];
    consultationType?: string;
    city?: string;
    state?: string;
    country?: string;
  }): Promise<AxiosResponse> {
    return this.api.get('/professionals', { params });
  }

  async getProfessional(id: string): Promise<AxiosResponse> {
    return this.api.get(`/professionals/${id}`);
  }

  async searchProfessionals(query: string, params?: {
    page?: number;
    limit?: number;
    professionalType?: string;
    acceptingNewClients?: boolean;
    specializations?: string[];
  }): Promise<AxiosResponse> {
    return this.api.get('/professionals/search', {
      params: { q: query, ...params }
    });
  }

  async getProfessionalStats(): Promise<AxiosResponse> {
    return this.api.get('/professionals/stats');
  }

  async getProfessionalFilterOptions(): Promise<AxiosResponse> {
    return this.api.get('/professionals/filter-options');
  }

  async createProfessionalProfile(data: Record<string, unknown>): Promise<AxiosResponse> {
    return this.api.post('/professionals', data);
  }

  async updateProfessionalProfile(data: Record<string, unknown>): Promise<AxiosResponse> {
    return this.api.put('/professionals/me', data);
  }

  async getMyProfessionalProfile(): Promise<AxiosResponse> {
    return this.api.get('/professionals/me');
  }

  async verifyProfessional(id: string, status: string, notes?: string): Promise<AxiosResponse> {
    return this.api.post(`/professionals/${id}/verify`, { status, notes });
  }

  // Appointments endpoints
  async getAppointments(params?: {
    page?: number;
    limit?: number;
    status?: string;
    appointmentType?: string;
    mode?: string;
    dateFrom?: string;
    dateTo?: string;
    professionalId?: string;
    clientId?: string;
    isEmergency?: boolean;
  }): Promise<AxiosResponse> {
    return this.api.get('/appointments', { params });
  }

  async getAppointment(id: string): Promise<AxiosResponse> {
    return this.api.get(`/appointments/${id}`);
  }

  async createAppointment(data: {
    professionalId: string;
    appointmentType: string;
    mode: string;
    scheduledAt: Date;
    durationMinutes?: number;
    clientNotes?: string;
    preSessionAssessment?: {
      moodLevel: number;
      stressLevel: number;
      anxietyLevel: number;
      concerns: string[];
      goals: string[];
    };
    isEmergency?: boolean;
  }): Promise<AxiosResponse> {
    return this.api.post('/appointments', data);
  }

  async updateAppointment(id: string, data: Record<string, unknown>): Promise<AxiosResponse> {
    return this.api.put(`/appointments/${id}`, data);
  }

  async cancelAppointment(id: string, reason: string): Promise<AxiosResponse> {
    return this.api.post(`/appointments/${id}/cancel`, { reason });
  }

  async rescheduleAppointment(id: string, newScheduledAt: Date, reason: string): Promise<AxiosResponse> {
    return this.api.post(`/appointments/${id}/reschedule`, {
      newScheduledAt: newScheduledAt.toISOString(),
      reason,
    });
  }

  async getAvailableSlots(professionalId: string, date: string, duration?: number): Promise<AxiosResponse> {
    const params: Record<string, unknown> = { date };
    if (duration) params.duration = duration.toString();
    return this.api.get(`/appointments/professionals/${professionalId}/available-slots`, { params });
  }

  async getAppointmentStats(): Promise<AxiosResponse> {
    return this.api.get('/appointments/stats');
  }

  async getAppointmentFilterOptions(): Promise<AxiosResponse> {
    return this.api.get('/appointments/filter-options');
  }

  async rateAppointment(id: string, rating: number, feedback?: string): Promise<AxiosResponse> {
    return this.api.put(`/appointments/${id}`, {
      clientRating: rating,
      clientFeedback: feedback,
    });
  }

  // Health check
  async healthCheck(): Promise<AxiosResponse> {
    return this.api.get('/health');
  }

  // Utility method to check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.getToken();
  }
}

export const apiService = new ApiService();
export default apiService;
