import { io, Socket } from 'socket.io-client';

// WebSocket Event Types (matching backend)
export interface WebSocketEvents {
  // Authentication
  authenticate: (token: string) => void;
  authenticated: (user: unknown) => void;
  authentication_error: (error: string) => void;

  // Appointment Events
  appointment_reminder: (data: AppointmentReminderData) => void;
  appointment_status_update: (data: AppointmentStatusData) => void;
  professional_availability_change: (data: ProfessionalAvailabilityData) => void;
  appointment_booking_confirmation: (data: AppointmentBookingData) => void;

  // Crisis Alerts
  crisis_alert: (data: CrisisAlertData) => void;
  emergency_contact_notification: (data: EmergencyContactData) => void;
  crisis_resource_recommendation: (data: CrisisResourceData) => void;

  // Messaging
  new_message: (data: MessageData) => void;
  message_read: (data: MessageReadData) => void;
  typing_indicator: (data: TypingIndicatorData) => void;
  unread_count_update: (data: UnreadCountData) => void;

  // Real-time Updates
  dashboard_update: (data: DashboardUpdateData) => void;
  mood_tracking_update: (data: MoodTrackingData) => void;
  wellness_progress_update: (data: WellnessProgressData) => void;

  // Connection Events
  connect: () => void;
  disconnect: (reason: string) => void;
  error: (error: Error) => void;
  ping: () => void;
  pong: () => void;
}

// Data Interfaces (matching backend)
export interface AppointmentReminderData {
  appointmentId: string;
  professionalName: string;
  scheduledAt: string;
  reminderType: '15min' | '1hour' | '24hour';
  appointmentType: string;
  mode: 'video' | 'phone' | 'chat' | 'in_person';
}

export interface AppointmentStatusData {
  appointmentId: string;
  status: 'confirmed' | 'cancelled' | 'rescheduled' | 'completed';
  newScheduledAt?: string;
  reason?: string;
  professionalName: string;
}

export interface ProfessionalAvailabilityData {
  professionalId: string;
  professionalName: string;
  isOnline: boolean;
  availableSlots?: string[];
  lastSeen?: string;
}

export interface AppointmentBookingData {
  appointmentId: string;
  professionalName: string;
  scheduledAt: string;
  appointmentType: string;
  mode: string;
  confirmationNumber: string;
}

export interface CrisisAlertData {
  alertId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  resources: Array<{
    type: 'hotline' | 'chat' | 'emergency';
    name: string;
    contact: string;
    available24h: boolean;
  }>;
  location?: {
    latitude: number;
    longitude: number;
    city: string;
    state: string;
  };
}

export interface EmergencyContactData {
  contactId: string;
  contactName: string;
  contactType: 'professional' | 'emergency' | 'family';
  message: string;
  urgency: 'immediate' | 'urgent' | 'normal';
}

export interface CrisisResourceData {
  resourceId: string;
  title: string;
  description: string;
  type: 'article' | 'video' | 'exercise' | 'contact';
  url?: string;
  priority: number;
}

export interface MessageData {
  messageId: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderType: 'user' | 'professional' | 'system';
  content: string;
  timestamp: string;
  messageType: 'text' | 'image' | 'file' | 'system';
}

export interface MessageReadData {
  messageId: string;
  conversationId: string;
  readBy: string;
  readAt: string;
}

export interface TypingIndicatorData {
  conversationId: string;
  userId: string;
  userName: string;
  isTyping: boolean;
}

export interface UnreadCountData {
  conversationId: string;
  unreadCount: number;
  totalUnreadCount: number;
}

export interface DashboardUpdateData {
  type: 'stats' | 'activity' | 'appointments' | 'mood' | 'initial';
  data: unknown;
  timestamp: string;
}

export interface MoodTrackingData {
  entryId: string;
  mood: number;
  energy: number;
  stress: number;
  anxiety: number;
  timestamp: string;
}

export interface WellnessProgressData {
  moduleId: string;
  moduleName: string;
  progress: number;
  completed: boolean;
  timestamp: string;
}

// Connection Status
export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'authenticated' | 'error';

// Event Listeners
type EventListener<T = unknown> = (data: T) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private connectionStatus: ConnectionStatus = 'disconnected';
  private eventListeners: Map<string, Set<EventListener<any>>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.setupHeartbeat();
  }

  // Connection Management
  public connect(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        if (this.socket?.connected) {
          resolve();
          return;
        }

        this.connectionStatus = 'connecting';
        this.notifyStatusChange();

        // Create socket connection
        this.socket = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000', {
          transports: ['websocket', 'polling'],
          timeout: 20000,
          forceNew: true,
        });

        // Setup event handlers
        this.setupEventHandlers(resolve, reject);

        // Authenticate after connection
        this.socket.on('connect', () => {
          console.log('🔗 WebSocket connected');
          this.connectionStatus = 'connected';
          this.notifyStatusChange();
          this.socket!.emit('authenticate', token);
        });

      } catch (error) {
        console.error('WebSocket connection error:', error);
        this.connectionStatus = 'error';
        this.notifyStatusChange();
        reject(error);
      }
    });
  }

  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.connectionStatus = 'disconnected';
    this.notifyStatusChange();
    this.clearHeartbeat();
  }

  public isConnected(): boolean {
    return this.socket?.connected || false;
  }

  public getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  // Event Handling
  private setupEventHandlers(resolve: () => void, reject: (error: unknown) => void): void {
    if (!this.socket) return;

    // Authentication events
    this.socket.on('authenticated', (user: unknown) => {
      console.log('✅ WebSocket authenticated:', user);
      this.connectionStatus = 'authenticated';
      this.notifyStatusChange();
      this.reconnectAttempts = 0;
      resolve();
    });

    this.socket.on('authentication_error', (error: string) => {
      console.error('❌ WebSocket authentication error:', error);
      this.connectionStatus = 'error';
      this.notifyStatusChange();
      reject(new Error(error));
    });

    // Connection events
    this.socket.on('disconnect', (reason: string) => {
      console.log('🔌 WebSocket disconnected:', reason);
      this.connectionStatus = 'disconnected';
      this.notifyStatusChange();
      this.handleReconnection();
    });

    this.socket.on('connect_error', (error: Error) => {
      console.error('❌ WebSocket connection error:', error);
      this.connectionStatus = 'error';
      this.notifyStatusChange();
      this.handleReconnection();
    });

    // Heartbeat
    this.socket.on('pong', () => {
      // Connection is alive
    });

    // Real-time event handlers
    this.setupRealTimeEventHandlers();
  }

  private setupRealTimeEventHandlers(): void {
    if (!this.socket) return;

    // Appointment events
    this.socket.on('appointment_reminder', (data: AppointmentReminderData) => {
      this.emit('appointment_reminder', data);
    });

    this.socket.on('appointment_status_update', (data: AppointmentStatusData) => {
      this.emit('appointment_status_update', data);
    });

    this.socket.on('professional_availability_change', (data: ProfessionalAvailabilityData) => {
      this.emit('professional_availability_change', data);
    });

    this.socket.on('appointment_booking_confirmation', (data: AppointmentBookingData) => {
      this.emit('appointment_booking_confirmation', data);
    });

    // Crisis alerts
    this.socket.on('crisis_alert', (data: CrisisAlertData) => {
      this.emit('crisis_alert', data);
    });

    this.socket.on('emergency_contact_notification', (data: EmergencyContactData) => {
      this.emit('emergency_contact_notification', data);
    });

    this.socket.on('crisis_resource_recommendation', (data: CrisisResourceData) => {
      this.emit('crisis_resource_recommendation', data);
    });

    // Messaging events
    this.socket.on('new_message', (data: MessageData) => {
      this.emit('new_message', data);
    });

    this.socket.on('message_read', (data: MessageReadData) => {
      this.emit('message_read', data);
    });

    this.socket.on('typing_indicator', (data: TypingIndicatorData) => {
      this.emit('typing_indicator', data);
    });

    this.socket.on('unread_count_update', (data: UnreadCountData) => {
      this.emit('unread_count_update', data);
    });

    // Dashboard updates
    this.socket.on('dashboard_update', (data: DashboardUpdateData) => {
      this.emit('dashboard_update', data);
    });

    this.socket.on('mood_tracking_update', (data: MoodTrackingData) => {
      this.emit('mood_tracking_update', data);
    });

    this.socket.on('wellness_progress_update', (data: WellnessProgressData) => {
      this.emit('wellness_progress_update', data);
    });
  }

  // Event Listener Management
  public on<T = unknown>(event: string, listener: EventListener<T>): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    (this.eventListeners.get(event) as Set<EventListener<T>>).add(listener);
  }

  public off<T = unknown>(event: string, listener: EventListener<T>): void {
    const listeners = this.eventListeners.get(event) as Set<EventListener<T>>;
    if (listeners) {
      listeners.delete(listener);
    }
  }

  private emit<T = unknown>(event: string, data: T): void {
    const listeners = this.eventListeners.get(event) as Set<EventListener<T>>;
    if (listeners) {
      listeners.forEach(listener => listener(data));
    }
  }

  private notifyStatusChange(): void {
    this.emit('connection_status_change', this.connectionStatus);
  }

  // Reconnection Logic
  private handleReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);

    setTimeout(() => {
      if (this.connectionStatus === 'disconnected' || this.connectionStatus === 'error') {
        // Would need to get token from auth store
        // this.connect(token);
      }
    }, delay);
  }

  // Heartbeat
  private setupHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.socket?.connected) {
        this.socket.emit('ping');
      }
    }, 30000); // Send ping every 30 seconds
  }

  private clearHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // Cleanup
  public cleanup(): void {
    this.disconnect();
    this.eventListeners.clear();
    this.clearHeartbeat();
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
export default WebSocketService;
