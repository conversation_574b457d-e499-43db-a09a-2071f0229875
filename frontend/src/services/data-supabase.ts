import { createSupabaseClient } from '@/lib/supabase/browser-client';
import { FEATURE_FLAGS } from '@/lib/config/feature-flags';

export interface MoodEntry {
  id?: string;
  user_id: string;
  mood_level: number;
  energy_level?: number;
  stress_level?: number;
  notes?: string;
  triggers?: string[];
  activities?: string[];
  date: string;
  created_at?: string;
  updated_at?: string;
}

export interface JournalEntry {
  id?: string;
  user_id: string;
  title: string;
  content: string;
  mood_before?: number;
  mood_after?: number;
  tags?: string[];
  is_private?: boolean;
  ai_insights?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export class SupabaseDataService {
  private supabase = createSupabaseClient();

  // Mood Entries Management
  async getMoodEntries(userId: string, limit = 50): Promise<MoodEntry[]> {
    const { data, error } = await this.supabase
      .from('mood_entries')
      .select('*')
      .eq('user_id', userId)
      .order('date', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Erreur récupération mood entries: ${error.message}`);
    }

    return data || [];
  }

  async createMoodEntry(moodEntry: Omit<MoodEntry, 'id' | 'created_at' | 'updated_at'>): Promise<MoodEntry> {
    const { data, error } = await this.supabase
      .from('mood_entries')
      .insert([moodEntry])
      .select()
      .single();

    if (error) {
      throw new Error(`Erreur création mood entry: ${error.message}`);
    }

    return data;
  }

  async updateMoodEntry(id: string, updates: Partial<MoodEntry>): Promise<MoodEntry> {
    const { data, error } = await this.supabase
      .from('mood_entries')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Erreur mise à jour mood entry: ${error.message}`);
    }

    return data;
  }

  async deleteMoodEntry(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('mood_entries')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Erreur suppression mood entry: ${error.message}`);
    }
  }

  // Journal Entries Management
  async getJournalEntries(userId: string, limit = 50): Promise<JournalEntry[]> {
    const { data, error } = await this.supabase
      .from('journal_entries')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Erreur récupération journal entries: ${error.message}`);
    }

    return data || [];
  }

  async getJournalEntry(id: string): Promise<JournalEntry | null> {
    const { data, error } = await this.supabase
      .from('journal_entries')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Pas trouvé
      }
      throw new Error(`Erreur récupération journal entry: ${error.message}`);
    }

    return data;
  }

  async createJournalEntry(journalEntry: Omit<JournalEntry, 'id' | 'created_at' | 'updated_at'>): Promise<JournalEntry> {
    const { data, error } = await this.supabase
      .from('journal_entries')
      .insert([journalEntry])
      .select()
      .single();

    if (error) {
      throw new Error(`Erreur création journal entry: ${error.message}`);
    }

    return data;
  }

  async updateJournalEntry(id: string, updates: Partial<JournalEntry>): Promise<JournalEntry> {
    const { data, error } = await this.supabase
      .from('journal_entries')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Erreur mise à jour journal entry: ${error.message}`);
    }

    return data;
  }

  async deleteJournalEntry(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('journal_entries')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Erreur suppression journal entry: ${error.message}`);
    }
  }

  // Analytics & Stats
  async getMoodStats(userId: string, days = 30): Promise<{
    averageMood: number;
    averageEnergy: number;
    averageStress: number;
    totalEntries: number;
  }> {
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - days);

    const { data, error } = await this.supabase
      .from('mood_entries')
      .select('mood_level, energy_level, stress_level')
      .eq('user_id', userId)
      .gte('date', fromDate.toISOString().split('T')[0]);

    if (error) {
      throw new Error(`Erreur récupération stats: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return {
        averageMood: 0,
        averageEnergy: 0,
        averageStress: 0,
        totalEntries: 0,
      };
    }

    const stats = data.reduce(
      (acc, entry) => ({
        mood: acc.mood + (entry.mood_level || 0),
        energy: acc.energy + (entry.energy_level || 0),
        stress: acc.stress + (entry.stress_level || 0),
      }),
      { mood: 0, energy: 0, stress: 0 }
    );

    return {
      averageMood: Number((stats.mood / data.length).toFixed(1)),
      averageEnergy: Number((stats.energy / data.length).toFixed(1)),
      averageStress: Number((stats.stress / data.length).toFixed(1)),
      totalEntries: data.length,
    };
  }

  // Real-time subscriptions
  subscribeToMoodEntries(userId: string, callback: (payload: any) => void) {
    return this.supabase
      .channel('mood_entries_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'mood_entries',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  }

  subscribeToJournalEntries(userId: string, callback: (payload: any) => void) {
    return this.supabase
      .channel('journal_entries_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'journal_entries',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  }
}

// Fonction helper pour choisir le bon service de données
export const getDataService = () => {
  if (FEATURE_FLAGS.USE_SUPABASE_DATABASE) {
    return new SupabaseDataService();
  }
  // Fallback vers l'ancienne API (pour compatibilité)
  return null;
}; 