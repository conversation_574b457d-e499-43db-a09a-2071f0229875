import { apiService } from './api';

export interface JournalEntry {
  id: string;
  title: string;
  content: string;
  entryType: string;
  moodLevel: number;
  emotions: string[];
  tags: string[];
  stressLevel: number;
  energyLevel: number;
  sleepQuality: number;
  gratitudeNotes?: string;
  goals?: string;
  challenges?: string;
  achievements?: string;
  isPrivate: boolean;
  isFavorite: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateJournalEntryData {
  title: string;
  content: string;
  entryType?: string;
  moodLevel?: number;
  tags?: string[];
  emotions?: string[];
  stressLevel?: number;
  energyLevel?: number;
  sleepQuality?: number;
  gratitudeNotes?: string;
  goals?: string;
  challenges?: string;
  achievements?: string;
  isPrivate?: boolean;
}

export interface UpdateJournalEntryData extends Partial<CreateJournalEntryData> {
  isFavorite?: boolean;
}

export interface JournalFilters {
  page?: number;
  limit?: number;
  entryType?: string;
  moodLevel?: number;
  isPrivate?: boolean;
  isFavorite?: boolean;
  startDate?: string;
  endDate?: string;
  tags?: string[];
  emotions?: string[];
}

export interface JournalResponse {
  success: boolean;
  message: string;
  data?: any;
}

class JournalService {
  /**
   * Create a new journal entry
   */
  async createEntry(entryData: CreateJournalEntryData): Promise<JournalResponse> {
    try {
      const response = await apiService.createJournalEntry(entryData);
      return {
        success: true,
        message: response.message || 'Journal entry created successfully',
        data: response.data
      };
    } catch (error: any) {
      console.error('Error creating journal entry:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to create journal entry'
      };
    }
  }

  /**
   * Get journal entries with optional filters
   */
  async getEntries(filters?: JournalFilters): Promise<JournalResponse> {
    try {
      const response = await apiService.getJournalEntries(filters);
      return {
        success: true,
        message: response.message || 'Journal entries retrieved successfully',
        data: response.data
      };
    } catch (error: any) {
      console.error('Error fetching journal entries:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch journal entries'
      };
    }
  }

  /**
   * Get a specific journal entry by ID
   */
  async getEntry(id: string): Promise<JournalResponse> {
    try {
      const response = await apiService.getJournalEntry(id);
      return {
        success: true,
        message: response.message || 'Journal entry retrieved successfully',
        data: response.data
      };
    } catch (error: any) {
      console.error('Error fetching journal entry:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch journal entry'
      };
    }
  }

  /**
   * Update a journal entry
   */
  async updateEntry(id: string, updateData: UpdateJournalEntryData): Promise<JournalResponse> {
    try {
      const response = await apiService.updateJournalEntry(id, updateData);
      return {
        success: true,
        message: response.message || 'Journal entry updated successfully',
        data: response.data
      };
    } catch (error: any) {
      console.error('Error updating journal entry:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to update journal entry'
      };
    }
  }

  /**
   * Delete a journal entry
   */
  async deleteEntry(id: string): Promise<JournalResponse> {
    try {
      const response = await apiService.deleteJournalEntry(id);
      return {
        success: true,
        message: response.message || 'Journal entry deleted successfully'
      };
    } catch (error: any) {
      console.error('Error deleting journal entry:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to delete journal entry'
      };
    }
  }

  /**
   * Get journal statistics
   */
  async getStats(): Promise<JournalResponse> {
    try {
      const response = await apiService.getJournalStats();
      return {
        success: true,
        message: response.message || 'Journal stats retrieved successfully',
        data: response.data
      };
    } catch (error: any) {
      console.error('Error fetching journal stats:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch journal statistics'
      };
    }
  }

  /**
   * Get suggestions for tags and emotions
   */
  async getSuggestions(): Promise<JournalResponse> {
    try {
      const response = await apiService.getJournalSuggestions();
      return {
        success: true,
        message: response.message || 'Suggestions retrieved successfully',
        data: response.data
      };
    } catch (error: any) {
      console.error('Error fetching suggestions:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch suggestions'
      };
    }
  }

  /**
   * Toggle favorite status of a journal entry
   */
  async toggleFavorite(id: string, isFavorite: boolean): Promise<JournalResponse> {
    try {
      const response = await apiService.updateJournalEntry(id, { isFavorite });
      return {
        success: true,
        message: response.message || 'Favorite status updated successfully',
        data: response.data
      };
    } catch (error: any) {
      console.error('Error toggling favorite:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to update favorite status'
      };
    }
  }

  /**
   * Export journal entries to various formats
   */
  async exportEntries(format: 'pdf' | 'csv' | 'json', filters?: JournalFilters): Promise<JournalResponse> {
    try {
      // This would be implemented when export functionality is added
      // For now, return a placeholder response
      return {
        success: false,
        message: 'Export functionality not yet implemented'
      };
    } catch (error: any) {
      console.error('Error exporting entries:', error);
      return {
        success: false,
        message: 'Failed to export journal entries'
      };
    }
  }

  /**
   * Search journal entries
   */
  async searchEntries(query: string, filters?: JournalFilters): Promise<JournalResponse> {
    try {
      // Add search query to filters
      const searchFilters = {
        ...filters,
        search: query
      };
      
      const response = await apiService.getJournalEntries(searchFilters);
      return {
        success: true,
        message: response.message || 'Search completed successfully',
        data: response.data
      };
    } catch (error: any) {
      console.error('Error searching entries:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to search journal entries'
      };
    }
  }

  /**
   * Get mood trends over time
   */
  async getMoodTrends(period: 'week' | 'month' | 'year' = 'month'): Promise<JournalResponse> {
    try {
      // This would be implemented when analytics functionality is added
      // For now, return a placeholder response
      return {
        success: false,
        message: 'Mood trends functionality not yet implemented'
      };
    } catch (error: any) {
      console.error('Error fetching mood trends:', error);
      return {
        success: false,
        message: 'Failed to fetch mood trends'
      };
    }
  }

  /**
   * Validate journal entry data before submission
   */
  validateEntry(entryData: CreateJournalEntryData | UpdateJournalEntryData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required fields for creation
    if ('title' in entryData && !entryData.title?.trim()) {
      errors.push('Title is required');
    }

    if ('content' in entryData && !entryData.content?.trim()) {
      errors.push('Content is required');
    }

    // Validate title length
    if (entryData.title && entryData.title.length > 200) {
      errors.push('Title must be 200 characters or less');
    }

    // Validate mood level
    if (entryData.moodLevel !== undefined && (entryData.moodLevel < 1 || entryData.moodLevel > 5)) {
      errors.push('Mood level must be between 1 and 5');
    }

    // Validate numeric ranges
    const numericFields = [
      { field: 'stressLevel', name: 'Stress level' },
      { field: 'energyLevel', name: 'Energy level' },
      { field: 'sleepQuality', name: 'Sleep quality' }
    ];

    numericFields.forEach(({ field, name }) => {
      const value = (entryData as any)[field];
      if (value !== undefined && (value < 0 || value > 10)) {
        errors.push(`${name} must be between 0 and 10`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const journalService = new JournalService();
export default journalService;
