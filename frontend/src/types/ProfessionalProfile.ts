export interface ProfessionalProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  professionalType: string;
  specializations: string[];
  languagesSpoken: string[];
  acceptingNewClients: boolean;
  hourlyRate?: number;
  rating?: number;
  city?: string;
  state?: string;
  country?: string;
  bio?: string;
  createdAt: string;
  updatedAt: string;
  // Ajoute d'autres champs selon le modèle backend si besoin
} 