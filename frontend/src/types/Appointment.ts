export interface Appointment {
  id: string;
  professionalId: string;
  clientId: string;
  appointmentType: string;
  mode: string;
  scheduledAt: string;
  durationMinutes?: number;
  status: string;
  clientNotes?: string;
  preSessionAssessment?: {
    moodLevel: number;
    stressLevel: number;
    anxietyLevel: number;
    concerns: string[];
    goals: string[];
  };
  isEmergency?: boolean;
  createdAt: string;
  updatedAt: string;
  // Ajoute d'autres champs selon le modèle backend si besoin
} 