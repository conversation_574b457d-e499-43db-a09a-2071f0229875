// Types principaux pour l'API MindFlow Pro

export interface Tokens {
  accessToken: string;
  refreshToken: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  roles: string[];
  // Ajoute d'autres champs selon le modèle User côté backend
}

export interface JournalEntry {
  id: string;
  title: string;
  content: string;
  entryType?: string;
  moodLevel?: number;
  tags?: string[];
  emotions?: string[];
  isFavorite?: boolean;
  stressLevel?: number;
  energyLevel?: number;
  sleepQuality?: number;
  gratitudeNotes?: string;
  goals?: string;
  challenges?: string;
  achievements?: string;
  isPrivate?: boolean;
  createdAt: string;
  updatedAt: string;
  // Ajoute d'autres champs selon le modèle JournalEntry côté backend
}

// Type générique pour les réponses API
export interface ApiResponse<T = unknown> {
  success: boolean;
  message?: string;
  data?: T;
}

export * from './User';
export * from './JournalEntry';
export * from './AICoachInteraction';
export * from './Appointment';
export * from './Notification';
export * from './ProfessionalProfile';
export * from './WellnessModule';
export * from './UserModuleProgress';
export * from './UserPathEnrollment';
export * from './PathModuleLink';
export * from './TherapeuticPath';
export * from './Role';
export * from './Permission';
export * from './RolePermission';
export * from './BiometricDataEntry';
// Types utilitaires
export * from './index'; 