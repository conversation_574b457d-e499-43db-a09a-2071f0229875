export interface JournalEntry {
  id: string;
  userId: string;
  title: string;
  content: string;
  entryType?: string;
  moodLevel?: number;
  tags?: string[];
  emotions?: string[];
  stressLevel?: number;
  energyLevel?: number;
  sleepQuality?: number;
  gratitudeNotes?: string;
  goals?: string;
  challenges?: string;
  achievements?: string;
  isPrivate?: boolean;
  isFavorite?: boolean;
  createdAt: string;
  updatedAt: string;
  // Ajoute d'autres champs selon le modèle backend si besoin
} 