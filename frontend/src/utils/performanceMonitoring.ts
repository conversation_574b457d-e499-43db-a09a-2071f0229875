/**
 * Frontend Performance Monitoring Utilities
 * Tracks Core Web Vitals, user interactions, and journal-specific metrics
 */

interface PerformanceMetric {
  name: string;
  value: number;
  id: string;
  timestamp: number;
  url: string;
  userAgent: string;
  userId?: string;
  sessionId: string;
}

interface JournalPerformanceMetric extends PerformanceMetric {
  operation: 'form_load' | 'form_submit' | 'entry_save' | 'entry_load' | 'validation';
  success?: boolean;
  errorMessage?: string;
  formFields?: number;
  entryLength?: number;
}

class FrontendPerformanceMonitor {
  private sessionId: string;
  private userId?: string;
  private metrics: PerformanceMetric[] = [];
  private isInitialized = false;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeMonitoring();
  }

  /**
   * Initialize performance monitoring
   */
  private initializeMonitoring(): void {
    if (typeof window === 'undefined' || this.isInitialized) return;

    this.isInitialized = true;
    this.setupWebVitalsTracking();
    this.setupErrorTracking();
    this.setupNavigationTracking();
  }

  /**
   * Set user ID for tracking
   */
  setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * Setup Core Web Vitals tracking
   */
  private setupWebVitalsTracking(): void {
    // Largest Contentful Paint (LCP)
    this.observePerformanceEntry('largest-contentful-paint', (entry: any) => {
      this.recordMetric({
        name: 'LCP',
        value: entry.startTime,
        id: this.generateId(),
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        userId: this.userId,
        sessionId: this.sessionId
      });
    });

    // First Input Delay (FID)
    this.observePerformanceEntry('first-input', (entry: any) => {
      this.recordMetric({
        name: 'FID',
        value: entry.processingStart - entry.startTime,
        id: this.generateId(),
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        userId: this.userId,
        sessionId: this.sessionId
      });
    });

    // Cumulative Layout Shift (CLS)
    let clsValue = 0;
    this.observePerformanceEntry('layout-shift', (entry: any) => {
      if (!entry.hadRecentInput) {
        clsValue += entry.value;
      }
    });

    // Report CLS on page unload
    window.addEventListener('beforeunload', () => {
      this.recordMetric({
        name: 'CLS',
        value: clsValue,
        id: this.generateId(),
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        userId: this.userId,
        sessionId: this.sessionId
      });
    });
  }

  /**
   * Setup error tracking
   */
  private setupErrorTracking(): void {
    // JavaScript errors
    window.addEventListener('error', (event) => {
      this.recordMetric({
        name: 'JS_ERROR',
        value: 1,
        id: this.generateId(),
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        userId: this.userId,
        sessionId: this.sessionId
      });
    });

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.recordMetric({
        name: 'PROMISE_REJECTION',
        value: 1,
        id: this.generateId(),
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        userId: this.userId,
        sessionId: this.sessionId
      });
    });
  }

  /**
   * Setup navigation tracking
   */
  private setupNavigationTracking(): void {
    // Page load time
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      this.recordMetric({
        name: 'PAGE_LOAD',
        value: navigation.loadEventEnd - navigation.fetchStart,
        id: this.generateId(),
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        userId: this.userId,
        sessionId: this.sessionId
      });
    });
  }

  /**
   * Observe performance entries
   */
  private observePerformanceEntry(type: string, callback: (entry: any) => void): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach(callback);
      });
      
      try {
        observer.observe({ type, buffered: true });
      } catch (e) {
        // Fallback for older browsers
        console.warn(`Performance observer for ${type} not supported`);
      }
    }
  }

  /**
   * Track journal-specific performance metrics
   */
  trackJournalFormLoad(startTime: number): void {
    const loadTime = performance.now() - startTime;
    
    const metric: JournalPerformanceMetric = {
      name: 'JOURNAL_FORM_LOAD',
      value: loadTime,
      id: this.generateId(),
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.userId,
      sessionId: this.sessionId,
      operation: 'form_load'
    };

    this.recordMetric(metric);
  }

  /**
   * Track journal form submission
   */
  trackJournalFormSubmit(startTime: number, success: boolean, errorMessage?: string): void {
    const submitTime = performance.now() - startTime;
    
    const metric: JournalPerformanceMetric = {
      name: 'JOURNAL_FORM_SUBMIT',
      value: submitTime,
      id: this.generateId(),
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.userId,
      sessionId: this.sessionId,
      operation: 'form_submit',
      success,
      errorMessage
    };

    this.recordMetric(metric);
  }

  /**
   * Track journal entry save operation
   */
  trackJournalEntrySave(startTime: number, success: boolean, entryLength?: number): void {
    const saveTime = performance.now() - startTime;
    
    const metric: JournalPerformanceMetric = {
      name: 'JOURNAL_ENTRY_SAVE',
      value: saveTime,
      id: this.generateId(),
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.userId,
      sessionId: this.sessionId,
      operation: 'entry_save',
      success,
      entryLength
    };

    this.recordMetric(metric);
  }

  /**
   * Track journal entry load operation
   */
  trackJournalEntryLoad(startTime: number, success: boolean): void {
    const loadTime = performance.now() - startTime;
    
    const metric: JournalPerformanceMetric = {
      name: 'JOURNAL_ENTRY_LOAD',
      value: loadTime,
      id: this.generateId(),
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.userId,
      sessionId: this.sessionId,
      operation: 'entry_load',
      success
    };

    this.recordMetric(metric);
  }

  /**
   * Track form validation performance
   */
  trackFormValidation(startTime: number, formFields: number): void {
    const validationTime = performance.now() - startTime;
    
    const metric: JournalPerformanceMetric = {
      name: 'JOURNAL_FORM_VALIDATION',
      value: validationTime,
      id: this.generateId(),
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.userId,
      sessionId: this.sessionId,
      operation: 'validation',
      formFields
    };

    this.recordMetric(metric);
  }

  /**
   * Record a performance metric
   */
  private recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Send to analytics service
    this.sendToAnalytics(metric);
    
    // Log performance issues
    this.checkPerformanceThresholds(metric);
  }

  /**
   * Send metrics to analytics service
   */
  private sendToAnalytics(metric: PerformanceMetric): void {
    // Send to Google Analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', metric.name, {
        event_category: 'Performance',
        event_label: metric.id,
        value: Math.round(metric.value),
        custom_map: {
          session_id: metric.sessionId,
          user_id: metric.userId
        }
      });
    }

    // Send to custom analytics endpoint
    if (process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT) {
      fetch(process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(metric)
      }).catch(error => {
        console.warn('Failed to send performance metric:', error);
      });
    }
  }

  /**
   * Check performance thresholds and log warnings
   */
  private checkPerformanceThresholds(metric: PerformanceMetric): void {
    const thresholds = {
      LCP: 2500,
      FID: 100,
      CLS: 0.1,
      PAGE_LOAD: 3000,
      JOURNAL_FORM_LOAD: 2000,
      JOURNAL_FORM_SUBMIT: 3000,
      JOURNAL_ENTRY_SAVE: 2000,
      JOURNAL_ENTRY_LOAD: 1000,
      JOURNAL_FORM_VALIDATION: 100
    };

    const threshold = thresholds[metric.name as keyof typeof thresholds];
    if (threshold && metric.value > threshold) {
      console.warn(`Performance threshold exceeded: ${metric.name} took ${metric.value}ms (threshold: ${threshold}ms)`);
    }
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    totalMetrics: number;
    averagePageLoad: number;
    averageFormLoad: number;
    averageFormSubmit: number;
    errorCount: number;
  } {
    const pageLoadMetrics = this.metrics.filter(m => m.name === 'PAGE_LOAD');
    const formLoadMetrics = this.metrics.filter(m => m.name === 'JOURNAL_FORM_LOAD');
    const formSubmitMetrics = this.metrics.filter(m => m.name === 'JOURNAL_FORM_SUBMIT');
    const errorMetrics = this.metrics.filter(m => m.name === 'JS_ERROR' || m.name === 'PROMISE_REJECTION');

    return {
      totalMetrics: this.metrics.length,
      averagePageLoad: this.calculateAverage(pageLoadMetrics),
      averageFormLoad: this.calculateAverage(formLoadMetrics),
      averageFormSubmit: this.calculateAverage(formSubmitMetrics),
      errorCount: errorMetrics.length
    };
  }

  /**
   * Calculate average value from metrics
   */
  private calculateAverage(metrics: PerformanceMetric[]): number {
    if (metrics.length === 0) return 0;
    const sum = metrics.reduce((total, metric) => total + metric.value, 0);
    return Math.round(sum / metrics.length);
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Generate unique metric ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

// Singleton instance
export const performanceMonitor = new FrontendPerformanceMonitor();

// Utility functions for easy use in components
export const trackJournalPerformance = {
  startFormLoad: () => performance.now(),
  endFormLoad: (startTime: number) => performanceMonitor.trackJournalFormLoad(startTime),
  
  startFormSubmit: () => performance.now(),
  endFormSubmit: (startTime: number, success: boolean, errorMessage?: string) => 
    performanceMonitor.trackJournalFormSubmit(startTime, success, errorMessage),
  
  startEntrySave: () => performance.now(),
  endEntrySave: (startTime: number, success: boolean, entryLength?: number) => 
    performanceMonitor.trackJournalEntrySave(startTime, success, entryLength),
  
  startEntryLoad: () => performance.now(),
  endEntryLoad: (startTime: number, success: boolean) => 
    performanceMonitor.trackJournalEntryLoad(startTime, success),
  
  startValidation: () => performance.now(),
  endValidation: (startTime: number, formFields: number) => 
    performanceMonitor.trackFormValidation(startTime, formFields)
};

export default performanceMonitor;
