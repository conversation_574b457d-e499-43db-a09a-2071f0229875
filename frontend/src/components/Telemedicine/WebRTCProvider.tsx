'use client';

import React, { createContext, useContext, useRef, useState, useEffect, ReactNode } from 'react';

interface WebRTCContextType {
  localVideo: React.RefObject<HTMLVideoElement>;
  remoteVideo: React.RefObject<HTMLVideoElement>;
  isConnected: boolean;
  connectionQuality: 'excellent' | 'good' | 'poor';
  startCall: () => Promise<void>;
  endCall: () => void;
  toggleVideo: () => void;
  toggleAudio: () => void;
  shareScreen: () => Promise<void>;
  isVideoEnabled: boolean;
  isAudioEnabled: boolean;
}

const WebRTCContext = createContext<WebRTCContextType | null>(null);

interface WebRTCProviderProps {
  children: ReactNode;
}

export function WebRTCProvider({ children }: WebRTCProviderProps) {
  const localVideo = useRef<HTMLVideoElement>(null);
  const remoteVideo = useRef<HTMLVideoElement>(null);
  const peerConnection = useRef<RTCPeerConnection | null>(null);
  const localStream = useRef<MediaStream | null>(null);

  const [isConnected, setIsConnected] = useState(false);
  const [connectionQuality, setConnectionQuality] = useState<'excellent' | 'good' | 'poor'>('good');
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);

  // Configuration STUN/TURN (simulation)
  const pcConfig = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' }
    ]
  };

  const startCall = async () => {
    try {
      // Obtenir le stream local
      localStream.current = await navigator.mediaDevices.getUserMedia({
        video: { width: 1280, height: 720 },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      if (localVideo.current) {
        localVideo.current.srcObject = localStream.current;
      }

      // Créer la connexion peer
      peerConnection.current = new RTCPeerConnection(pcConfig);

      // Ajouter les tracks locaux
      localStream.current.getTracks().forEach(track => {
        peerConnection.current?.addTrack(track, localStream.current!);
      });

      // Gérer les tracks distants
      peerConnection.current.ontrack = (event) => {
        if (remoteVideo.current) {
          remoteVideo.current.srcObject = event.streams[0];
        }
      };

      // Monitoring de la qualité de connexion
      peerConnection.current.oniceconnectionstatechange = () => {
        const state = peerConnection.current?.iceConnectionState;
        setIsConnected(state === 'connected');
        
        // Simulation de la qualité basée sur l'état
        if (state === 'connected') {
          setConnectionQuality('excellent');
        } else if (state === 'checking') {
          setConnectionQuality('good');
        } else {
          setConnectionQuality('poor');
        }
      };

      setIsConnected(true);
    } catch (error) {
      console.error('Erreur lors du démarrage de l\'appel:', error);
    }
  };

  const endCall = () => {
    if (localStream.current) {
      localStream.current.getTracks().forEach(track => track.stop());
    }
    
    if (peerConnection.current) {
      peerConnection.current.close();
    }
    
    setIsConnected(false);
    localStream.current = null;
    peerConnection.current = null;
  };

  const toggleVideo = () => {
    if (localStream.current) {
      const videoTrack = localStream.current.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoEnabled(videoTrack.enabled);
      }
    }
  };

  const toggleAudio = () => {
    if (localStream.current) {
      const audioTrack = localStream.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsAudioEnabled(audioTrack.enabled);
      }
    }
  };

  const shareScreen = async () => {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });
      
      // Remplacer le track vidéo par le partage d'écran
      const videoTrack = screenStream.getVideoTracks()[0];
      const sender = peerConnection.current?.getSenders().find(s => 
        s.track && s.track.kind === 'video'
      );
      
      if (sender) {
        await sender.replaceTrack(videoTrack);
      }
      
      // Gérer l'arrêt du partage d'écran
      videoTrack.onended = () => {
        // Revenir à la caméra
        if (localStream.current) {
          const cameraTrack = localStream.current.getVideoTracks()[0];
          if (sender && cameraTrack) {
            sender.replaceTrack(cameraTrack);
          }
        }
      };
    } catch (error) {
      console.error('Erreur lors du partage d\'écran:', error);
    }
  };

  useEffect(() => {
    return () => {
      endCall();
    };
  }, []);

  const value: WebRTCContextType = {
    localVideo,
    remoteVideo,
    isConnected,
    connectionQuality,
    startCall,
    endCall,
    toggleVideo,
    toggleAudio,
    shareScreen,
    isVideoEnabled,
    isAudioEnabled
  };

  return <WebRTCContext.Provider value={value}>{children}</WebRTCContext.Provider>;
}

export function useWebRTC() {
  const context = useContext(WebRTCContext);
  if (!context) {
    throw new Error('useWebRTC must be used within a WebRTCProvider');
  }
  return context;
}
