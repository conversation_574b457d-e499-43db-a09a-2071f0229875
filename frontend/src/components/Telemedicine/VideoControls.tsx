'use client';

import React, { useState, useRef, useEffect, memo } from 'react';
import { Button } from '@/components/ui/button';
import { Video, VideoOff, Mic, MicOff, PhoneOff, Settings, Monitor } from 'lucide-react';

interface VideoControlsProps {
  onVideoToggle?: (enabled: boolean) => void;
  onAudioToggle?: (enabled: boolean) => void;
  onEndCall?: () => void;
  onScreenShare?: () => void;
  isVideoEnabled?: boolean;
  isAudioEnabled?: boolean;
  isConnected?: boolean;
  connectionQuality?: 'excellent' | 'good' | 'poor';
}

export const VideoControls = memo<VideoControlsProps>(({
  onVideoToggle,
  onAudioToggle,
  onEndCall,
  onScreenShare,
  isVideoEnabled = true,
  isAudioEnabled = true,
  isConnected = false,
  connectionQuality = 'good'
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const qualityColors = {
    excellent: 'text-green-500',
    good: 'text-yellow-500',
    poor: 'text-red-500'
  };

  const handleVideoToggle = () => {
    const newState = !isVideoEnabled;
    onVideoToggle?.(newState);
  };

  const handleAudioToggle = () => {
    const newState = !isAudioEnabled;
    onAudioToggle?.(newState);
  };

  const handleRecord = () => {
    setIsRecording(!isRecording);
    // Logique d'enregistrement
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm text-gray-600 dark:text-gray-300">
            {isConnected ? 'Connecté' : 'Déconnecté'}
          </span>
          <span className={`text-sm ${qualityColors[connectionQuality]}`}>
            ({connectionQuality})
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          {isRecording && (
            <div className="flex items-center gap-1 text-red-500">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
              <span className="text-xs">REC</span>
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center justify-center gap-3">
        {/* Contrôle Vidéo */}
        <Button
          onClick={handleVideoToggle}
          variant={isVideoEnabled ? "default" : "destructive"}
          size="lg"
          className="w-12 h-12 rounded-full p-0"
        >
          {isVideoEnabled ? <Video size={20} /> : <VideoOff size={20} />}
        </Button>

        {/* Contrôle Audio */}
        <Button
          onClick={handleAudioToggle}
          variant={isAudioEnabled ? "default" : "destructive"}
          size="lg"
          className="w-12 h-12 rounded-full p-0"
        >
          {isAudioEnabled ? <Mic size={20} /> : <MicOff size={20} />}
        </Button>

        {/* Partage d'écran */}
        <Button
          onClick={onScreenShare}
          variant="outline"
          size="lg"
          className="w-12 h-12 rounded-full p-0"
        >
          <Monitor size={20} />
        </Button>

        {/* Enregistrement */}
        <Button
          onClick={handleRecord}
          variant={isRecording ? "destructive" : "outline"}
          size="lg"
          className="w-12 h-12 rounded-full p-0"
        >
          <div className={`w-4 h-4 ${isRecording ? 'bg-white' : 'bg-red-500'} rounded-sm`} />
        </Button>

        {/* Paramètres */}
        <Button
          onClick={() => setShowSettings(!showSettings)}
          variant="outline"
          size="lg"
          className="w-12 h-12 rounded-full p-0"
        >
          <Settings size={20} />
        </Button>

        {/* Fin d'appel */}
        <Button
          onClick={onEndCall}
          variant="destructive"
          size="lg"
          className="w-12 h-12 rounded-full p-0 bg-red-500 hover:bg-red-600"
        >
          <PhoneOff size={20} />
        </Button>
      </div>

      {showSettings && (
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="text-sm font-medium mb-2">Paramètres de la session</h4>
          <div className="space-y-2">
            <label className="flex items-center text-sm">
              <input type="checkbox" className="mr-2" defaultChecked />
              Enregistrement automatique
            </label>
            <label className="flex items-center text-sm">
              <input type="checkbox" className="mr-2" />
              Réduction de bruit
            </label>
            <label className="flex items-center text-sm">
              <input type="checkbox" className="mr-2" defaultChecked />
              Transcription automatique
            </label>
          </div>
        </div>
      )}
    </div>
  );
});

VideoControls.displayName = 'VideoControls';
