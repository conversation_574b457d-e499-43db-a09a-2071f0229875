'use client';

import React, { useState, useEffect } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';

export interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
}

const icons: Record<string, string> = {
  success: '✅',
  error: '⛔',
  warning: '⚠️',
  info: 'ℹ️',
};

const bgColors: Record<string, string> = {
  success: 'bg-green-50 border-green-400 text-green-900',
  error: 'bg-red-50 border-red-400 text-red-900',
  warning: 'bg-yellow-50 border-yellow-400 text-yellow-900',
  info: 'bg-blue-50 border-blue-400 text-blue-900',
};

const Toast: React.FC<ToastProps> = ({
  id,
  type,
  title,
  message,
  duration = 4000,
  onClose,
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
  }, [duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      setIsVisible(false);
      onClose(id);
    }, 300);
  };

  const getIcon = () => {
    return <span className="text-2xl mt-0.5" aria-hidden>{icons[type]}</span>;
  };

  const getBackgroundColor = () => {
    return bgColors[type];
  };

  const getTitleColor = () => {
    return 'text-base leading-tight';
  };

  const getMessageColor = () => {
    return 'text-gray-700';
  };

  if (!isVisible) return null;

  return (
    <div
      role="alert"
      aria-live="assertive"
      className={`flex items-start gap-3 px-4 py-3 rounded-lg border shadow-lg mb-2 animate-fade-in ${getBackgroundColor()}`}
      style={{ minWidth: 280, maxWidth: 400 }}
    >
            {getIcon()}
      <div className="flex-1">
        <div className={`font-bold ${getTitleColor()}`}>
          {title}
        </div>
        {message && (
          <div className={`text-sm mt-0.5 ${getMessageColor()}`}>
            {message}
          </div>
            )}
          </div>
            <button
              onClick={handleClose}
        className="ml-2 text-gray-400 hover:text-gray-700 focus:outline-none"
        aria-label="Fermer la notification"
            >
              <X className="h-4 w-4" />
            </button>
    </div>
  );
};

// Toast Container Component
interface ToastContainerProps {
  toasts: ToastProps[];
  onClose: (id: string) => void;
}

export const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onClose }) => {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast, index) => (
        <div
          key={toast.id}
          style={{
            transform: `translateY(${index * 10}px)`,
            zIndex: 50 - index,
          }}
        >
          <Toast {...toast} onClose={onClose} />
        </div>
      ))}
    </div>
  );
};

export default Toast;
