'use client';

import React, { memo } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

interface PerformanceDataPoint {
  metric: string;
  value: number;
  target: number;
  status: 'good' | 'warning' | 'critical';
}

interface PerformanceChartProps {
  data: PerformanceDataPoint[];
  type?: 'bar' | 'pie';
  height?: number;
}

const statusColors = {
  good: '#10b981',
  warning: '#f59e0b', 
  critical: '#ef4444'
};

export const PerformanceChart = memo<PerformanceChartProps>(({
  data,
  type = 'bar',
  height = 300
}) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900 rounded-lg">
        <div className="text-center">
          <div className="w-12 h-12 mx-auto mb-4 text-gray-400">⚡</div>
          <p className="text-gray-500 dark:text-gray-400">Aucune donnée de performance</p>
        </div>
      </div>
    );
  }

  if (type === 'pie') {
    return (
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ metric, value }) => `${metric}: ${value}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={statusColors[entry.status]} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis dataKey="metric" className="text-xs" />
        <YAxis className="text-xs" />
        <Tooltip />
        <Bar dataKey="value" fill="#8b5cf6" />
        <Bar dataKey="target" fill="#e5e7eb" opacity={0.5} />
      </BarChart>
    </ResponsiveContainer>
  );
});

PerformanceChart.displayName = 'PerformanceChart';
