'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Bar, <PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

const testData = [
  { name: 'Jan', value: 400, mood: 7, stress: 3 },
  { name: 'Feb', value: 300, mood: 8, stress: 2 },
  { name: 'Mar', value: 200, mood: 6, stress: 4 },
  { name: 'Apr', value: 278, mood: 9, stress: 1 },
  { name: 'May', value: 189, mood: 7, stress: 3 },
];

const pieData = [
  { name: 'Excellent', value: 35, color: '#10b981' },
  { name: 'Bien', value: 40, color: '#3b82f6' },
  { name: 'Neutre', value: 20, color: '#f59e0b' },
  { name: 'Bas', value: 5, color: '#ef4444' },
];

export const TestRechartsComponent = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [activeChart, setActiveChart] = useState<'line' | 'bar' | 'pie'>('line');

  useEffect(() => {
    // Simulation de chargement
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="w-full h-64 p-4 bg-white rounded-lg shadow flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const renderChart = () => {
    switch (activeChart) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={testData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="value" stroke="#8884d8" strokeWidth={2} />
              <Line type="monotone" dataKey="mood" stroke="#10b981" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        );
      
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={testData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#8884d8" />
              <Bar dataKey="mood" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        );
      
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="w-full h-64 p-4 bg-white rounded-lg shadow border">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Test Recharts Integration ✅</h3>
        <div className="flex gap-2">
          <button
            onClick={() => setActiveChart('line')}
            className={`px-3 py-1 text-sm rounded ${
              activeChart === 'line' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Line
          </button>
          <button
            onClick={() => setActiveChart('bar')}
            className={`px-3 py-1 text-sm rounded ${
              activeChart === 'bar' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Bar
          </button>
          <button
            onClick={() => setActiveChart('pie')}
            className={`px-3 py-1 text-sm rounded ${
              activeChart === 'pie' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Pie
          </button>
        </div>
      </div>
      
      <div className="h-48">
        {renderChart()}
      </div>
      
      <div className="mt-2 text-xs text-gray-500 text-center">
        🎯 Recharts fonctionnel - Graphiques interactifs disponibles
      </div>
    </div>
  );
};