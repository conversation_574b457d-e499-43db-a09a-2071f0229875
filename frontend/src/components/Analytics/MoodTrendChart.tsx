'use client';

import React, { memo } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';

interface MoodDataPoint {
  date: string;
  mood: number;
  anxiety: number;
  stress: number;
  energy: number;
  sleep_quality: number;
}

interface MoodTrendChartProps {
  data: MoodDataPoint[];
  height?: number;
  metric?: 'mood' | 'anxiety' | 'stress' | 'energy' | 'sleep_quality';
  variant?: 'line' | 'area';
  showGrid?: boolean;
  animated?: boolean;
}

const metricConfig = {
  mood: {
    label: 'Humeur',
    color: '#8b5cf6',
    gradientId: 'moodGradient'
  },
  anxiety: {
    label: 'Anxiété',
    color: '#ef4444',
    gradientId: 'anxietyGradient'
  },
  stress: {
    label: 'Stress',
    color: '#f59e0b',
    gradientId: 'stressGradient'
  },
  energy: {
    label: 'Énergie',
    color: '#10b981',
    gradientId: 'energyGradient'
  },
  sleep_quality: {
    label: 'Qualité du sommeil',
    color: '#3b82f6',
    gradientId: 'sleepGradient'
  }
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
        <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          {format(parseISO(label), 'dd MMMM yyyy', { locale: fr })}
        </p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2 text-sm">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-gray-600 dark:text-gray-300">
              {metricConfig[entry.dataKey as keyof typeof metricConfig]?.label || entry.dataKey}:
            </span>
            <span className="font-medium text-gray-900 dark:text-white">
              {entry.value}/10
            </span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export const MoodTrendChart = memo<MoodTrendChartProps>(({
  data,
  height = 300,
  metric = 'mood',
  variant = 'line',
  showGrid = true,
  animated = true
}) => {
  const config = metricConfig[metric];

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900 rounded-lg">
        <div className="text-center">
          <div className="w-12 h-12 mx-auto mb-4 text-gray-400">
            📊
          </div>
          <p className="text-gray-500 dark:text-gray-400">
            Aucune donnée disponible pour afficher le graphique
          </p>
        </div>
      </div>
    );
  }

  const ChartComponent = variant === 'area' ? AreaChart : LineChart;

  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={height}>
        <ChartComponent data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id={config.gradientId} x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={config.color} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={config.color} stopOpacity={0.1}/>
            </linearGradient>
          </defs>
          
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              className="opacity-30" 
              stroke="currentColor"
            />
          )}
          
          <XAxis 
            dataKey="date"
            tickFormatter={(value) => format(parseISO(value), 'dd/MM', { locale: fr })}
            className="text-xs"
            stroke="currentColor"
            opacity={0.7}
          />
          
          <YAxis 
            domain={[0, 10]}
            className="text-xs"
            stroke="currentColor"
            opacity={0.7}
          />
          
          <Tooltip content={<CustomTooltip />} />
          
          {variant === 'area' ? (
            <Area
              type="monotone"
              dataKey={metric}
              stroke={config.color}
              fillOpacity={1}
              fill={`url(#${config.gradientId})`}
              strokeWidth={2}
              animationBegin={animated ? 300 : 0}
              animationDuration={animated ? 1000 : 0}
            />
          ) : (
            <Line
              type="monotone"
              dataKey={metric}
              stroke={config.color}
              strokeWidth={2}
              dot={{ fill: config.color, strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: config.color, strokeWidth: 2 }}
              animationBegin={animated ? 300 : 0}
              animationDuration={animated ? 1000 : 0}
            />
          )}
        </ChartComponent>
      </ResponsiveContainer>
    </div>
  );
});

MoodTrendChart.displayName = 'MoodTrendChart'; 