'use client';

import dynamic from 'next/dynamic';
import { ComponentType } from 'react';

/**
 * NoSSR wrapper to disable server-side rendering for components
 * that have hydration issues or need client-only features
 */
function NoSSR<P extends object>(Component: ComponentType<P>) {
  return dynamic(() => Promise.resolve(Component), {
    ssr: false,
    loading: () => (
      <div className="animate-pulse">
        <div className="h-16 bg-gray-200 rounded mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>
    ),
  });
}

export default NoSSR;
