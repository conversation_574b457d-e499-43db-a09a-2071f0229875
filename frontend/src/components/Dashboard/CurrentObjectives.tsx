'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Target, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  Plus,
  MoreHorizontal,
  Calendar,
  Flag
} from 'lucide-react';

interface ObjectiveItemProps {
  id: string;
  title: string;
  description: string;
  progress: number;
  dueDate: string;
  priority: 'high' | 'medium' | 'low';
  category: string;
  completed: boolean;
}

const ObjectiveItem: React.FC<ObjectiveItemProps> = ({
  title,
  description,
  progress,
  dueDate,
  priority,
  category,
  completed
}) => {
  const getPriorityColor = () => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-700 border-green-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getProgressColor = () => {
    if (completed) return 'bg-green-500';
    if (progress >= 75) return 'bg-blue-500';
    if (progress >= 50) return 'bg-yellow-500';
    return 'bg-gray-300';
  };

  const formatDueDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'En retard';
    if (diffDays === 0) return 'Aujourd\'hui';
    if (diffDays === 1) return 'Demain';
    if (diffDays < 7) return `${diffDays} jours`;
    return date.toLocaleDateString('fr-FR', { month: 'short', day: 'numeric' });
  };

  return (
    <div className="p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            {completed ? (
              <CheckCircle className="w-4 h-4 text-green-600" />
            ) : (
              <Target className="w-4 h-4 text-blue-600" />
            )}
            <h4 className={`font-medium text-sm ${completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>
              {title}
            </h4>
          </div>
          <p className="text-xs text-gray-600 mb-2">{description}</p>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className={`text-xs ${getPriorityColor()}`}>
              {priority}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {category}
            </Badge>
          </div>
        </div>
        
        <Button variant="ghost" size="sm" className="p-1 h-6 w-6">
          <MoreHorizontal className="w-3 h-3" />
        </Button>
      </div>

      {/* Barre de progression */}
      <div className="mb-3">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs text-gray-600">Progression</span>
          <span className="text-xs font-medium text-gray-900">{progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getProgressColor()}`}
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Date limite */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center space-x-1">
          <Calendar className="w-3 h-3" />
          <span>Échéance: {formatDueDate(dueDate)}</span>
        </div>
        {!completed && (
          <Button variant="ghost" size="sm" className="h-6 text-xs px-2">
            Mettre à jour
          </Button>
        )}
      </div>
    </div>
  );
};

interface CurrentObjectivesProps {
  className?: string;
}

export const CurrentObjectives: React.FC<CurrentObjectivesProps> = ({ className }) => {
  // Données d'exemple si pas de données
  const mockObjectives = [
    {
      id: '1',
      title: 'Méditation quotidienne',
      description: 'Pratiquer 10 minutes de méditation chaque matin',
      progress: 75,
      dueDate: '2024-01-30',
      priority: 'high' as const,
      category: 'Bien-être',
      completed: false
    },
    {
      id: '2',
      title: 'Journal de gratitude',
      description: 'Écrire 3 choses positives par jour',
      progress: 60,
      dueDate: '2024-01-25',
      priority: 'medium' as const,
      category: 'Mental',
      completed: false
    },
    {
      id: '3',
      title: 'Exercice physique',
      description: 'Faire 30 minutes d\'activité physique',
      progress: 100,
      dueDate: '2024-01-20',
      priority: 'high' as const,
      category: 'Physique',
      completed: true
    }
  ];

  const activeObjectives = mockObjectives.filter(obj => !obj.completed);
  const completedObjectives = mockObjectives.filter(obj => obj.completed);

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Target className="w-5 h-5 text-blue-600" />
            <span>Objectifs Actuels</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {activeObjectives.length} actifs
            </Badge>
            <Button variant="ghost" size="sm" className="p-2">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Objectifs actifs */}
        {activeObjectives.length > 0 && (
          <div className="space-y-3">
            {activeObjectives.map((objective) => (
              <ObjectiveItem key={objective.id} {...objective} />
            ))}
          </div>
        )}

        {/* Objectifs complétés récents */}
        {completedObjectives.length > 0 && (
          <>
            <div className="border-t border-gray-100 pt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span>Récemment complétés</span>
              </h4>
              <div className="space-y-2">
                {completedObjectives.slice(0, 2).map((objective) => (
                  <ObjectiveItem key={objective.id} {...objective} />
                ))}
              </div>
            </div>
          </>
        )}

        {/* Message d'encouragement */}
        <div className="mt-4 p-3 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-4 h-4 text-blue-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">
                Excellent progrès !
              </p>
              <p className="text-xs text-gray-600">
                Vous êtes sur la bonne voie pour atteindre vos objectifs.
              </p>
            </div>
          </div>
        </div>

        {/* Action pour voir tous les objectifs */}
        <Button variant="outline" className="w-full text-sm" size="sm">
          <Flag className="w-4 h-4 mr-2" />
          Voir tous les objectifs
        </Button>
      </CardContent>
    </Card>
  );
}; 