'use client';

import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { format, subDays, parseISO } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  TrendingUp, 
  BarChart3, 
  Filter,
  Download,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement
);

interface MoodData {
  date: string;
  mood: number;
  energy: number;
  stress: number;
  anxiety: number;
  sleep: number;
}

interface ChartProps {
  data: MoodData[];
  isLoading?: boolean;
  className?: string;
}

interface TimeRange {
  label: string;
  days: number;
  value: '7d' | '30d' | '90d';
}

const timeRanges: TimeRange[] = [
  { label: '7 Days', days: 7, value: '7d' },
  { label: '30 Days', days: 30, value: '30d' },
  { label: '90 Days', days: 90, value: '90d' }
];

const metricConfig = {
  mood: {
    label: 'Mood',
    color: 'rgb(59, 130, 246)',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    borderColor: 'rgb(59, 130, 246)',
    icon: '😊',
    unit: '/10'
  },
  energy: {
    label: 'Energy',
    color: 'rgb(34, 197, 94)',
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    borderColor: 'rgb(34, 197, 94)',
    icon: '⚡',
    unit: '/10'
  },
  stress: {
    label: 'Stress',
    color: 'rgb(239, 68, 68)',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderColor: 'rgb(239, 68, 68)',
    icon: '😰',
    unit: '/10'
  },
  anxiety: {
    label: 'Anxiety',
    color: 'rgb(168, 85, 247)',
    backgroundColor: 'rgba(168, 85, 247, 0.1)',
    borderColor: 'rgb(168, 85, 247)',
    icon: '😟',
    unit: '/10'
  },
  sleep: {
    label: 'Sleep Quality',
    color: 'rgb(6, 182, 212)',
    backgroundColor: 'rgba(6, 182, 212, 0.1)',
    borderColor: 'rgb(6, 182, 212)',
    icon: '😴',
    unit: '/10'
  }
};

// Enhanced Mood Trends Chart Component
// Progress Tracking Charts Component
interface ProgressData {
  wellnessModules: {
    completed: number;
    total: number;
    weeklyCompletions: number[];
    monthlyCompletions: number[];
  };
  journalEntries: {
    thisWeek: number;
    thisMonth: number;
    frequency: number[];
  };
  appointments: {
    attended: number;
    total: number;
    attendanceRate: number;
    monthlyAttendance: number[];
  };
  goals: {
    achieved: number;
    total: number;
    milestones: Array<{
      name: string;
      completed: boolean;
      date?: string;
    }>;
  };
}

interface ProgressChartProps {
  data: ProgressData;
  isLoading?: boolean;
  className?: string;
}

export const ProgressTrackingCharts: React.FC<ProgressChartProps> = ({ data, isLoading, className }) => {
  const [activeTab, setActiveTab] = useState<'wellness' | 'journal' | 'appointments' | 'goals'>('wellness');

  // Wellness Module Completion Chart
  const wellnessChartData = {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: [
      {
        label: 'Modules Completed',
        data: data.wellnessModules.weeklyCompletions,
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 2,
        borderRadius: 4,
      },
    ],
  };

  // Journal Entry Frequency Chart
  const journalChartData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'Journal Entries',
        data: data.journalEntries.frequency,
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
        borderRadius: 4,
      },
    ],
  };

  // Appointment Attendance Chart
  const appointmentChartData = {
    labels: ['Attended', 'Missed'],
    datasets: [
      {
        data: [data.appointments.attended, data.appointments.total - data.appointments.attended],
        backgroundColor: ['rgba(34, 197, 94, 0.8)', 'rgba(239, 68, 68, 0.8)'],
        borderColor: ['rgb(34, 197, 94)', 'rgb(239, 68, 68)'],
        borderWidth: 2,
      },
    ],
  };

  // Goal Achievement Progress
  const goalProgress = (data.goals.achieved / data.goals.total) * 100;

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 15,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        cornerRadius: 8,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  };

  if (isLoading) {
    return (
      <Card className={`bg-white border border-gray-200 ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-green-600" />
            <span>Progress Tracking</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`bg-white border border-gray-200 ${className}`}>
      <CardHeader className="border-b border-gray-100 pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
            <BarChart3 className="w-5 h-5 text-green-600" />
            <span>Progress Tracking</span>
          </CardTitle>

          {/* Tab Navigation */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {[
              { key: 'wellness', label: 'Wellness' },
              { key: 'journal', label: 'Journal' },
              { key: 'appointments', label: 'Sessions' },
              { key: 'goals', label: 'Goals' },
            ].map((tab) => (
              <Button
                key={tab.key}
                variant={activeTab === tab.key ? "default" : "ghost"}
                size="sm"
                className={`px-3 py-1 text-xs ${
                  activeTab === tab.key
                    ? 'bg-white shadow-sm'
                    : 'hover:bg-gray-200'
                }`}
                onClick={() => setActiveTab(tab.key as any)}
              >
                {tab.label}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-6">
        <div className="h-64">
          {activeTab === 'wellness' && (
            <Bar data={wellnessChartData} options={chartOptions} />
          )}
          {activeTab === 'journal' && (
            <Bar data={journalChartData} options={chartOptions} />
          )}
          {activeTab === 'appointments' && (
            <Doughnut
              data={appointmentChartData}
              options={{
                ...chartOptions,
                scales: undefined,
                plugins: {
                  ...chartOptions.plugins,
                  legend: {
                    position: 'bottom' as const,
                  },
                },
              }}
            />
          )}
          {activeTab === 'goals' && (
            <div className="space-y-4">
              {/* Overall Progress */}
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {goalProgress.toFixed(0)}%
                </div>
                <div className="text-gray-600 mb-4">Goal Achievement Rate</div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-green-500 h-3 rounded-full transition-all duration-500"
                    style={{ width: `${goalProgress}%` }}
                  ></div>
                </div>
              </div>

              {/* Milestone Progress */}
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-900 mb-3">Recent Milestones</h4>
                {data.goals.milestones.slice(0, 4).map((milestone, index) => (
                  <div key={index} className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50">
                    <div className={`w-4 h-4 rounded-full ${
                      milestone.completed ? 'bg-green-500' : 'bg-gray-300'
                    }`}></div>
                    <span className={`flex-1 text-sm ${
                      milestone.completed ? 'text-gray-900' : 'text-gray-600'
                    }`}>
                      {milestone.name}
                    </span>
                    {milestone.date && (
                      <span className="text-xs text-gray-500">
                        {format(parseISO(milestone.date), 'MMM dd')}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Summary Stats */}
        {activeTab !== 'goals' && (
          <div className="mt-4 grid grid-cols-3 gap-4">
            {activeTab === 'wellness' && (
              <>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-lg font-semibold text-green-600">
                    {data.wellnessModules.completed}
                  </div>
                  <div className="text-xs text-gray-600">Completed</div>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-lg font-semibold text-blue-600">
                    {data.wellnessModules.total}
                  </div>
                  <div className="text-xs text-gray-600">Total Modules</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-lg font-semibold text-purple-600">
                    {((data.wellnessModules.completed / data.wellnessModules.total) * 100).toFixed(0)}%
                  </div>
                  <div className="text-xs text-gray-600">Completion Rate</div>
                </div>
              </>
            )}
            {activeTab === 'journal' && (
              <>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-lg font-semibold text-blue-600">
                    {data.journalEntries.thisWeek}
                  </div>
                  <div className="text-xs text-gray-600">This Week</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-lg font-semibold text-green-600">
                    {data.journalEntries.thisMonth}
                  </div>
                  <div className="text-xs text-gray-600">This Month</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-lg font-semibold text-purple-600">
                    {(data.journalEntries.thisMonth / 30).toFixed(1)}
                  </div>
                  <div className="text-xs text-gray-600">Daily Average</div>
                </div>
              </>
            )}
            {activeTab === 'appointments' && (
              <>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-lg font-semibold text-green-600">
                    {data.appointments.attended}
                  </div>
                  <div className="text-xs text-gray-600">Attended</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-lg font-semibold text-red-600">
                    {data.appointments.total - data.appointments.attended}
                  </div>
                  <div className="text-xs text-gray-600">Missed</div>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-lg font-semibold text-blue-600">
                    {data.appointments.attendanceRate.toFixed(0)}%
                  </div>
                  <div className="text-xs text-gray-600">Attendance Rate</div>
                </div>
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export const MoodTrendsChart: React.FC<ChartProps> = ({ data, isLoading, className }) => {
  const [selectedRange, setSelectedRange] = useState<'7d' | '30d' | '90d'>('7d');
  const [visibleMetrics, setVisibleMetrics] = useState<Set<string>>(
    new Set(['mood', 'energy', 'stress'])
  );
  const [showMovingAverage, setShowMovingAverage] = useState(true);

  // Filter data based on selected time range
  const filteredData = React.useMemo(() => {
    const days = timeRanges.find(r => r.value === selectedRange)?.days || 7;
    const cutoffDate = subDays(new Date(), days);
    return data.filter(item => parseISO(item.date) >= cutoffDate);
  }, [data, selectedRange]);

  // Calculate moving average
  const calculateMovingAverage = (values: number[], window: number = 3) => {
    return values.map((_, index, array) => {
      const start = Math.max(0, index - window + 1);
      const subset = array.slice(start, index + 1);
      return subset.reduce((sum, val) => sum + val, 0) / subset.length;
    });
  };

  const chartData = React.useMemo(() => {
    const labels = filteredData.map(item => format(parseISO(item.date), 'MMM dd'));
    
    const datasets = Object.entries(metricConfig)
      .filter(([key]) => visibleMetrics.has(key))
      .flatMap(([key, config]) => {
        const values = filteredData.map(item => item[key as keyof MoodData] as number);
        const mainDataset = {
          label: config.label,
          data: values,
          borderColor: config.borderColor,
          backgroundColor: config.backgroundColor,
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          pointRadius: 4,
          pointHoverRadius: 6,
          pointBackgroundColor: config.borderColor,
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
        };

        const datasets = [mainDataset];

        // Add moving average if enabled
        if (showMovingAverage && values.length > 2) {
          const movingAvg = calculateMovingAverage(values);
          datasets.push({
            label: `${config.label} (Avg)`,
            data: movingAvg,
            borderColor: config.borderColor,
            backgroundColor: 'transparent',
            borderWidth: 1,
            borderDash: [5, 5],
            fill: false,
            tension: 0.4,
            pointRadius: 0,
            pointHoverRadius: 0,
          });
        }

        return datasets;
      });

    return { labels, datasets };
  }, [filteredData, visibleMetrics, showMovingAverage]);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          title: (context: any) => {
            const date = filteredData[context[0].dataIndex]?.date;
            return date ? format(parseISO(date), 'EEEE, MMMM do, yyyy') : '';
          },
          label: (context: any) => {
            const metric = context.dataset.label.replace(' (Avg)', '');
            const value = context.parsed.y.toFixed(1);
            const config = Object.values(metricConfig).find(c => c.label === metric);
            return `${config?.icon || ''} ${metric}: ${value}${config?.unit || ''}`;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Date',
          font: {
            size: 12,
            weight: 'bold',
          },
        },
        grid: {
          display: false,
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Rating (1-10)',
          font: {
            size: 12,
            weight: 'bold',
          },
        },
        min: 0,
        max: 10,
        ticks: {
          stepSize: 1,
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    elements: {
      point: {
        hoverBackgroundColor: '#ffffff',
      },
    },
  };

  const toggleMetric = (metric: string) => {
    const newVisibleMetrics = new Set(visibleMetrics);
    if (newVisibleMetrics.has(metric)) {
      newVisibleMetrics.delete(metric);
    } else {
      newVisibleMetrics.add(metric);
    }
    setVisibleMetrics(newVisibleMetrics);
  };

  if (isLoading) {
    return (
      <Card className={`bg-white border border-gray-200 ${className}`}>
        <CardHeader className="border-b border-gray-100 pb-4">
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            <span>Mood & Wellness Trends</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`bg-white border border-gray-200 ${className}`}>
      <CardHeader className="border-b border-gray-100 pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            <span>Mood & Wellness Trends</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {/* Time Range Selector */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              {timeRanges.map((range) => (
                <Button
                  key={range.value}
                  variant={selectedRange === range.value ? "default" : "ghost"}
                  size="sm"
                  className={`px-3 py-1 text-xs ${
                    selectedRange === range.value 
                      ? 'bg-white shadow-sm' 
                      : 'hover:bg-gray-200'
                  }`}
                  onClick={() => setSelectedRange(range.value)}
                >
                  {range.label}
                </Button>
              ))}
            </div>

            {/* Moving Average Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowMovingAverage(!showMovingAverage)}
              className={`px-3 py-1 text-xs ${
                showMovingAverage ? 'bg-blue-100 text-blue-700' : ''
              }`}
            >
              <BarChart3 className="w-4 h-4 mr-1" />
              Avg
            </Button>

            {/* Export Button */}
            <Button variant="ghost" size="sm" className="px-3 py-1 text-xs">
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Metric Toggles */}
        <div className="flex flex-wrap gap-2 mt-4">
          {Object.entries(metricConfig).map(([key, config]) => (
            <Button
              key={key}
              variant="ghost"
              size="sm"
              onClick={() => toggleMetric(key)}
              className={`px-3 py-1 text-xs border ${
                visibleMetrics.has(key)
                  ? 'border-gray-300 bg-white shadow-sm'
                  : 'border-gray-200 bg-gray-50'
              }`}
              style={{
                borderColor: visibleMetrics.has(key) ? config.borderColor : undefined,
                color: visibleMetrics.has(key) ? config.borderColor : undefined,
              }}
            >
              {visibleMetrics.has(key) ? (
                <Eye className="w-3 h-3 mr-1" />
              ) : (
                <EyeOff className="w-3 h-3 mr-1" />
              )}
              {config.icon} {config.label}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent className="p-6">
        <div className="h-80">
          <Line data={chartData} options={options} />
        </div>
        
        {/* Chart Summary */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
          {Object.entries(metricConfig)
            .filter(([key]) => visibleMetrics.has(key))
            .map(([key, config]) => {
              const values = filteredData.map(item => item[key as keyof MoodData] as number);
              const average = values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
              const trend = values.length > 1 ? values[values.length - 1] - values[0] : 0;
              
              return (
                <div key={key} className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-lg font-semibold" style={{ color: config.borderColor }}>
                    {average.toFixed(1)}{config.unit}
                  </div>
                  <div className="text-xs text-gray-600">{config.label} Avg</div>
                  <div className={`text-xs mt-1 ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {trend >= 0 ? '↗' : '↘'} {Math.abs(trend).toFixed(1)}
                  </div>
                </div>
              );
            })}
        </div>
      </CardContent>
    </Card>
  );
};

// Wellness Journey Timeline Component
interface TimelineEvent {
  id: string;
  type: 'milestone' | 'achievement' | 'module_completion' | 'appointment' | 'journal_entry';
  title: string;
  description: string;
  date: string;
  icon: React.ReactNode;
  color: string;
  metadata?: {
    moduleId?: string;
    appointmentId?: string;
    journalEntryId?: string;
    rating?: number;
    progress?: number;
  };
}

interface TimelineProps {
  events: TimelineEvent[];
  isLoading?: boolean;
  className?: string;
  onEventClick?: (event: TimelineEvent) => void;
}

export const WellnessJourneyTimeline: React.FC<TimelineProps> = ({
  events,
  isLoading,
  className,
  onEventClick
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [selectedTypes, setSelectedTypes] = useState<Set<string>>(
    new Set(['milestone', 'achievement', 'module_completion'])
  );

  // Filter events based on selected period and types
  const filteredEvents = React.useMemo(() => {
    const now = new Date();
    let cutoffDate: Date;

    switch (selectedPeriod) {
      case 'week':
        cutoffDate = subDays(now, 7);
        break;
      case 'month':
        cutoffDate = subDays(now, 30);
        break;
      case 'quarter':
        cutoffDate = subDays(now, 90);
        break;
      case 'year':
        cutoffDate = subDays(now, 365);
        break;
      default:
        cutoffDate = subDays(now, 30);
    }

    return events
      .filter(event => parseISO(event.date) >= cutoffDate)
      .filter(event => selectedTypes.has(event.type))
      .sort((a, b) => parseISO(b.date).getTime() - parseISO(a.date).getTime());
  }, [events, selectedPeriod, selectedTypes]);

  const toggleEventType = (type: string) => {
    const newSelectedTypes = new Set(selectedTypes);
    if (newSelectedTypes.has(type)) {
      newSelectedTypes.delete(type);
    } else {
      newSelectedTypes.add(type);
    }
    setSelectedTypes(newSelectedTypes);
  };

  const eventTypeConfig = {
    milestone: { label: 'Milestones', color: 'text-purple-600', bgColor: 'bg-purple-100' },
    achievement: { label: 'Achievements', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
    module_completion: { label: 'Modules', color: 'text-green-600', bgColor: 'bg-green-100' },
    appointment: { label: 'Sessions', color: 'text-blue-600', bgColor: 'bg-blue-100' },
    journal_entry: { label: 'Journal', color: 'text-indigo-600', bgColor: 'bg-indigo-100' },
  };

  if (isLoading) {
    return (
      <Card className={`bg-white border border-gray-200 ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-purple-600" />
            <span>Wellness Journey</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`bg-white border border-gray-200 ${className}`}>
      <CardHeader className="border-b border-gray-100 pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
            <Calendar className="w-5 h-5 text-purple-600" />
            <span>Wellness Journey</span>
          </CardTitle>

          {/* Period Selector */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {[
              { key: 'week', label: '1W' },
              { key: 'month', label: '1M' },
              { key: 'quarter', label: '3M' },
              { key: 'year', label: '1Y' },
            ].map((period) => (
              <Button
                key={period.key}
                variant={selectedPeriod === period.key ? "default" : "ghost"}
                size="sm"
                className={`px-3 py-1 text-xs ${
                  selectedPeriod === period.key
                    ? 'bg-white shadow-sm'
                    : 'hover:bg-gray-200'
                }`}
                onClick={() => setSelectedPeriod(period.key as any)}
              >
                {period.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Event Type Filters */}
        <div className="flex flex-wrap gap-2 mt-4">
          {Object.entries(eventTypeConfig).map(([type, config]) => (
            <Button
              key={type}
              variant="ghost"
              size="sm"
              onClick={() => toggleEventType(type)}
              className={`px-3 py-1 text-xs border ${
                selectedTypes.has(type)
                  ? `border-gray-300 ${config.bgColor} ${config.color}`
                  : 'border-gray-200 bg-gray-50 text-gray-600'
              }`}
            >
              {selectedTypes.has(type) ? (
                <Eye className="w-3 h-3 mr-1" />
              ) : (
                <EyeOff className="w-3 h-3 mr-1" />
              )}
              {config.label}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent className="p-6">
        {filteredEvents.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-600 mb-2">No events found for this period</p>
            <p className="text-sm text-gray-500">Try adjusting your filters or time range</p>
          </div>
        ) : (
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>

            {/* Timeline Events */}
            <div className="space-y-6">
              {filteredEvents.map((event, index) => (
                <div
                  key={event.id}
                  className={`relative flex items-start space-x-4 ${
                    onEventClick ? 'cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2' : ''
                  }`}
                  onClick={() => onEventClick?.(event)}
                >
                  {/* Timeline Dot */}
                  <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full ${event.color} bg-white border-4 border-current`}>
                    {event.icon}
                  </div>

                  {/* Event Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-semibold text-gray-900">
                        {event.title}
                      </h4>
                      <span className="text-xs text-gray-500">
                        {format(parseISO(event.date), 'MMM dd, yyyy')}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {event.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
