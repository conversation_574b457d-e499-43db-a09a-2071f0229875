'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Heart, 
  Zap, 
  Brain, 
  Moon, 
  TrendingUp, 
  TrendingDown,
  Minus,
  Activity,
  Target,
  Calendar
} from 'lucide-react';
import { useDashboardStore } from '@/stores/dashboardStore';

interface WellnessStatsProps {
  className?: string;
}

interface StatCardProps {
  title: string;
  value: number;
  maxValue?: number;
  unit?: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: number;
  description?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  maxValue = 10,
  unit = '',
  icon: Icon,
  color,
  bgColor,
  trend,
  trendValue,
  description
}) => {
  const percentage = (value / maxValue) * 100;
  
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <Minus className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <Card className="border-0 shadow-sm hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className={`p-2 rounded-lg ${bgColor}`}>
            <Icon className={`w-5 h-5 ${color}`} />
          </div>
          
          {trend && trendValue && (
            <div className="flex items-center space-x-1">
              {getTrendIcon()}
              <span className={`text-xs font-medium ${getTrendColor()}`}>
                {trendValue > 0 ? '+' : ''}{trendValue}%
              </span>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-600">{title}</h3>
          
          <div className="flex items-baseline space-x-1">
            <span className="text-2xl font-bold text-gray-900">
              {value}
            </span>
            {maxValue && (
              <span className="text-sm text-gray-500">/{maxValue}</span>
            )}
            {unit && (
              <span className="text-sm text-gray-500">{unit}</span>
            )}
          </div>

          {/* Barre de progression */}
          {maxValue && (
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${color.replace('text-', 'bg-')}`}
                style={{ width: `${Math.min(percentage, 100)}%` }}
              />
            </div>
          )}

          {description && (
            <p className="text-xs text-gray-500">{description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export const WellnessStats: React.FC<WellnessStatsProps> = ({ className }) => {
  const { wellnessStats, isLoading } = useDashboardStore();

  if (isLoading) {
    return (
      <Card className={`${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-blue-600" />
            <span>Statistiques de Bien-être</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 rounded-lg h-32"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!wellnessStats) {
    return (
      <Card className={`${className}`}>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>Aucune donnée de bien-être disponible</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const stats = [
    {
      title: 'Humeur',
      value: wellnessStats.currentMood,
      icon: Heart,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100',
      trend: wellnessStats.monthlyMoodTrend,
      trendValue: 8,
      description: 'Moyenne hebdomadaire: ' + wellnessStats.weeklyMoodAverage.toFixed(1)
    },
    {
      title: 'Énergie',
      value: wellnessStats.energyLevel,
      icon: Zap,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      trend: 'up' as const,
      trendValue: 12,
      description: 'Tendance positive'
    },
    {
      title: 'Stress',
      value: 10 - wellnessStats.stressLevel, // Inverser pour que moins de stress = plus haut score
      icon: Brain,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      trend: 'stable' as const,
      trendValue: 0,
      description: 'Niveau gérable'
    },
    {
      title: 'Sommeil',
      value: wellnessStats.sleepQuality,
      icon: Moon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      trend: 'up' as const,
      trendValue: 5,
      description: 'Qualité en amélioration'
    }
  ];

  const additionalStats = [
    {
      title: 'Objectifs Complétés',
      value: wellnessStats.completedObjectives,
      maxValue: wellnessStats.totalObjectives,
      icon: Target,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: `${Math.round((wellnessStats.completedObjectives / wellnessStats.totalObjectives) * 100)}% de réussite`
    },
    {
      title: 'Séquence',
      value: wellnessStats.streakDays,
      unit: ' jours',
      icon: Calendar,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'Jours consécutifs actifs'
    }
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-blue-600" />
              <span>Statistiques de Bien-être</span>
            </div>
            <Badge variant="outline" className="text-xs">
              Aujourd'hui
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {/* Métriques principales */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {stats.map((stat, index) => (
              <StatCard key={index} {...stat} />
            ))}
          </div>

          {/* Métriques secondaires */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {additionalStats.map((stat, index) => (
              <StatCard key={index} {...stat} />
            ))}
          </div>

          {/* Résumé de performance */}
          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900 mb-1">
                  Performance Globale
                </h4>
                <p className="text-sm text-gray-600">
                  Votre bien-être est en progression constante
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-600">
                  {Math.round(((wellnessStats.currentMood + wellnessStats.energyLevel + (10 - wellnessStats.stressLevel) + wellnessStats.sleepQuality) / 4) * 10)}%
                </div>
                <div className="text-xs text-gray-500">Score global</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}; 