'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Plus,
  MessageCircle,
  BookOpen,
  Calendar,
  Heart,
  Zap,
  Brain,
  Target,
  Mic,
  Camera,
  Video,
  BarChart3
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface QuickActionsProps {
  className?: string;
}

interface ActionButtonProps {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  onClick: () => void;
  badge?: string;
  disabled?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  title,
  description,
  icon: Icon,
  color,
  bgColor,
  onClick,
  badge,
  disabled = false
}) => {
  return (
    <Button
      variant="ghost"
      className="h-auto p-4 border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all duration-200 disabled:opacity-50"
      onClick={onClick}
      disabled={disabled}
    >
      <div className="flex items-start space-x-3 w-full">
        <div className={`p-2 rounded-lg ${bgColor} flex-shrink-0`}>
          <Icon className={`w-5 h-5 ${color}`} />
        </div>
        
        <div className="flex-1 text-left">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className="font-medium text-gray-900 text-sm">{title}</h4>
            {badge && (
              <Badge variant="secondary" className="text-xs px-2 py-0">
                {badge}
              </Badge>
            )}
          </div>
          <p className="text-xs text-gray-600 leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </Button>
  );
};

export const QuickActions: React.FC<QuickActionsProps> = ({ className }) => {
  const router = useRouter();

  const handleMoodTracking = () => {
    // Ouvrir un modal ou naviguer vers le suivi d'humeur
    router.push('/dashboard?modal=mood-tracking');
  };

  const handleAICoach = () => {
    router.push('/ai-coach');
  };

  const handleJournal = () => {
    router.push('/journal');
  };

  const handlePrograms = () => {
    router.push('/programs');
  };

  const handleScheduleAppointment = () => {
    router.push('/appointments');
  };

  const handleVoiceNote = () => {
    // Implémenter l'enregistrement vocal
    console.log('Voice note recording...');
  };

  const handleQuickPhoto = () => {
    // Implémenter la capture de photo pour journal
    console.log('Photo capture...');
  };

  const handleQuickGoal = () => {
    // Ouvrir un modal pour créer un objectif rapide
    router.push('/dashboard?modal=quick-goal');
  };

  const handleAnalytics = () => {
    router.push('/dashboard/analytics');
  };

  const primaryActions = [
    {
      title: 'Suivre mon humeur',
      description: 'Enregistrez votre état émotionnel du moment',
      icon: Heart,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100',
      onClick: handleMoodTracking,
      badge: 'Rapide'
    },
    {
      title: 'Coach IA',
      description: 'Parlez avec votre coach virtuel',
      icon: MessageCircle,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      onClick: handleAICoach
    },
    {
      title: 'Nouvelle entrée',
      description: 'Écrivez dans votre journal augmenté',
      icon: BookOpen,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      onClick: handleJournal
    },
    {
      title: 'Programmes',
      description: 'Continuez vos programmes de bien-être',
      icon: Brain,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      onClick: handlePrograms,
      badge: '3 actifs'
    }
  ];

  const secondaryActions = [
    {
      title: 'Rendez-vous',
      description: 'Planifier avec un professionnel',
      icon: Calendar,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      onClick: handleScheduleAppointment
    },
    {
      title: 'Note vocale',
      description: 'Enregistrement audio rapide',
      icon: Mic,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      onClick: handleVoiceNote
    },
    {
      title: 'Photo moment',
      description: 'Capturer un instant',
      icon: Camera,
      color: 'text-teal-600',
      bgColor: 'bg-teal-100',
      onClick: handleQuickPhoto
    },
    {
      title: 'Objectif rapide',
      description: 'Définir un micro-objectif',
      icon: Target,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      onClick: handleQuickGoal
    }
  ];

  return (
    <Card className={`border-0 shadow-sm ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-yellow-600" />
            <span>Actions Rapides</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleAnalytics}
            className="text-gray-500 hover:text-gray-700"
          >
            <BarChart3 className="w-4 h-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Actions principales */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Essentiels</h4>
          <div className="space-y-2">
            {primaryActions.map((action, index) => (
              <ActionButton key={index} {...action} />
            ))}
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-100 my-4"></div>

        {/* Actions secondaires */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Plus d'options</h4>
          <div className="grid grid-cols-2 gap-2">
            {secondaryActions.map((action, index) => (
              <ActionButton key={index} {...action} />
            ))}
          </div>
        </div>

        {/* Call to action */}
        <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white rounded-lg shadow-sm">
              <Plus className="w-4 h-4 text-blue-600" />
            </div>
            <div className="flex-1">
              <h5 className="font-medium text-gray-900 text-sm">
                Nouveau sur MindFlow Pro ?
              </h5>
              <p className="text-xs text-gray-600 mt-1">
                Découvrez notre guide de démarrage rapide
              </p>
            </div>
            <Button variant="outline" size="sm" className="text-xs">
              Guide
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 