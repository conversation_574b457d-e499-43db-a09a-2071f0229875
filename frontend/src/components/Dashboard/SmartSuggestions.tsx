'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Lightbulb, 
  Brain, 
  Heart, 
  Activity, 
  Clock, 
  CheckCircle,
  X,
  RefreshCw,
  Zap,
  Star,
  TrendingUp
} from 'lucide-react';
import useDashboardStore from '@/stores/dashboardStore';

interface SuggestionProps {
  id: string;
  title: string;
  description: string;
  type: 'mood' | 'wellness' | 'productivity' | 'mindfulness';
  priority: 'high' | 'medium' | 'low';
  estimatedTime: string;
  benefits: string[];
  actionText: string;
  dismissed?: boolean;
}

const SuggestionCard: React.FC<SuggestionProps & { onDismiss: (id: string) => void; onAccept: (id: string) => void; }> = ({
  id,
  title,
  description,
  type,
  priority,
  estimatedTime,
  benefits,
  actionText,
  onDismiss,
  onAccept
}) => {
  const getTypeConfig = () => {
    switch (type) {
      case 'mood':
        return {
          icon: Heart,
          color: 'text-pink-600',
          bgColor: 'bg-pink-100',
          borderColor: 'border-pink-200'
        };
      case 'wellness':
        return {
          icon: Activity,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          borderColor: 'border-green-200'
        };
      case 'productivity':
        return {
          icon: Zap,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          borderColor: 'border-blue-200'
        };
      case 'mindfulness':
        return {
          icon: Brain,
          color: 'text-purple-600',
          bgColor: 'bg-purple-100',
          borderColor: 'border-purple-200'
        };
      default:
        return {
          icon: Lightbulb,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          borderColor: 'border-gray-200'
        };
    }
  };

  const getPriorityColor = () => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-700';
      case 'medium':
        return 'bg-yellow-100 text-yellow-700';
      case 'low':
        return 'bg-green-100 text-green-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const { icon: TypeIcon, color, bgColor, borderColor } = getTypeConfig();

  return (
    <div className={`p-4 border rounded-lg hover:shadow-sm transition-all duration-200 ${borderColor}`}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-start space-x-3">
          <div className={`p-2 rounded-lg ${bgColor}`}>
            <TypeIcon className={`w-4 h-4 ${color}`} />
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="font-medium text-sm text-gray-900">{title}</h4>
              <Badge variant="outline" className={`text-xs ${getPriorityColor()}`}>
                {priority}
              </Badge>
            </div>
            <p className="text-xs text-gray-600 mb-2">{description}</p>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDismiss(id)}
          className="p-1 h-6 w-6 text-gray-400 hover:text-gray-600"
        >
          <X className="w-3 h-3" />
        </Button>
      </div>

      {/* Temps estimé */}
      <div className="flex items-center space-x-2 mb-3">
        <Clock className="w-3 h-3 text-gray-500" />
        <span className="text-xs text-gray-600">{estimatedTime}</span>
      </div>

      {/* Bénéfices */}
      <div className="mb-4">
        <h5 className="text-xs font-medium text-gray-700 mb-2">Bénéfices attendus:</h5>
        <div className="flex flex-wrap gap-1">
          {benefits.map((benefit, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {benefit}
            </Badge>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-2">
        <Button
          size="sm"
          onClick={() => onAccept(id)}
          className="flex-1 text-xs"
        >
          <CheckCircle className="w-3 h-3 mr-1" />
          {actionText}
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="text-xs"
        >
          Plus tard
        </Button>
      </div>
    </div>
  );
};

interface SmartSuggestionsProps {
  className?: string;
}

export const SmartSuggestions: React.FC<SmartSuggestionsProps> = ({ className }) => {
  const { aiInsights, isLoading } = useDashboardStore();
  const [dismissedSuggestions, setDismissedSuggestions] = useState<Set<string>>(new Set());

  // Suggestions intelligentes basées sur les données utilisateur
  const mockSuggestions: SuggestionProps[] = [
    {
      id: '1',
      title: 'Session de respiration',
      description: 'Votre niveau de stress semble élevé. Une session de respiration guidée pourrait vous aider.',
      type: 'mindfulness',
      priority: 'high',
      estimatedTime: '5 minutes',
      benefits: ['Réduction du stress', 'Clarté mentale'],
      actionText: 'Commencer maintenant'
    },
    {
      id: '2',
      title: 'Pause mouvement',
      description: 'Vous êtes resté inactif pendant un moment. Un peu d\'exercice léger vous ferait du bien.',
      type: 'wellness',
      priority: 'medium',
      estimatedTime: '10 minutes',
      benefits: ['Énergie', 'Circulation'],
      actionText: 'Voir exercices'
    },
    {
      id: '3',
      title: 'Journal de gratitude',
      description: 'Notez 3 choses positives de votre journée pour améliorer votre humeur.',
      type: 'mood',
      priority: 'medium',
      estimatedTime: '3 minutes',
      benefits: ['Humeur positive', 'Perspective'],
      actionText: 'Ouvrir journal'
    },
    {
      id: '4',
      title: 'Planification de demain',
      description: 'Préparez votre journée de demain pour réduire l\'anxiété matinale.',
      type: 'productivity',
      priority: 'low',
      estimatedTime: '15 minutes',
      benefits: ['Organisation', 'Sérénité'],
      actionText: 'Planifier'
    }
  ];

  const activeSuggestions = mockSuggestions.filter(s => !dismissedSuggestions.has(s.id));

  const handleDismiss = (id: string) => {
    setDismissedSuggestions(prev => new Set([...prev, id]));
  };

  const handleAccept = (id: string) => {
    const suggestion = mockSuggestions.find(s => s.id === id);
    if (suggestion) {
      console.log('Accepting suggestion:', suggestion.title);
      // Implémenter la logique d'action basée sur le type
      handleDismiss(id);
    }
  };

  const handleRefresh = () => {
    setDismissedSuggestions(new Set());
  };

  if (isLoading) {
    return (
      <Card className={`${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Lightbulb className="w-5 h-5 text-yellow-600" />
            <span>Suggestions Intelligentes</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="animate-pulse p-4 border rounded-lg">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Lightbulb className="w-5 h-5 text-yellow-600" />
            <span>Suggestions Intelligentes</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {activeSuggestions.length} nouvelles
            </Badge>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleRefresh}
              className="p-2"
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {activeSuggestions.length > 0 ? (
          <>
            <div className="space-y-3">
              {activeSuggestions.slice(0, 3).map((suggestion) => (
                <SuggestionCard
                  key={suggestion.id}
                  {...suggestion}
                  onDismiss={handleDismiss}
                  onAccept={handleAccept}
                />
              ))}
            </div>

            {activeSuggestions.length > 3 && (
              <Button variant="outline" className="w-full text-sm" size="sm">
                Voir {activeSuggestions.length - 3} suggestions supplémentaires
              </Button>
            )}
          </>
        ) : (
          <div className="text-center py-6">
            <Star className="w-8 h-8 mx-auto text-yellow-500 mb-2" />
            <p className="text-sm text-gray-600 mb-2">
              Parfait ! Vous êtes à jour avec toutes nos suggestions.
            </p>
            <Button variant="ghost" size="sm" onClick={handleRefresh}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Actualiser les suggestions
            </Button>
          </div>
        )}

        {/* Informations sur l'IA */}
        <div className="mt-4 p-3 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <Brain className="w-4 h-4 text-purple-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">
                IA Personnalisée
              </p>
              <p className="text-xs text-gray-600">
                Ces suggestions sont basées sur vos habitudes et vos objectifs.
              </p>
            </div>
            <TrendingUp className="w-4 h-4 text-green-500" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 