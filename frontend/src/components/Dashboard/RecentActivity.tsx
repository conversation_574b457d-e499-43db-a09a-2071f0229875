'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Activity, 
  BookOpen, 
  Brain, 
  Heart, 
  Target, 
  Calendar,
  MessageCircle,
  TrendingUp,
  Clock,
  MoreHorizontal,
  ExternalLink
} from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'mood' | 'journal' | 'objective' | 'program' | 'chat' | 'assessment';
  title: string;
  description: string;
  timestamp: string;
  metadata?: {
    score?: number;
    duration?: string;
    progress?: number;
    [key: string]: any;
  };
}

interface RecentActivityProps {
  className?: string;
}

const ActivityItemComponent: React.FC<{ activity: ActivityItem }> = ({ activity }) => {
  const getActivityConfig = () => {
    switch (activity.type) {
      case 'mood':
        return {
          icon: Heart,
          color: 'text-pink-600',
          bgColor: 'bg-pink-100',
          label: 'Humeur'
        };
      case 'journal':
        return {
          icon: BookO<PERSON>,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          label: 'Journal'
        };
      case 'objective':
        return {
          icon: Target,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          label: 'Objectif'
        };
      case 'program':
        return {
          icon: Brain,
          color: 'text-purple-600',
          bgColor: 'bg-purple-100',
          label: 'Programme'
        };
      case 'chat':
        return {
          icon: MessageCircle,
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
          label: 'Coach IA'
        };
      case 'assessment':
        return {
          icon: TrendingUp,
          color: 'text-indigo-600',
          bgColor: 'bg-indigo-100',
          label: 'Évaluation'
        };
      default:
        return {
          icon: Activity,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          label: 'Activité'
        };
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes}min`;
    if (diffInMinutes < 1440) return `Il y a ${Math.floor(diffInMinutes / 60)}h`;
    return date.toLocaleDateString('fr-FR', { month: 'short', day: 'numeric' });
  };

  const { icon: Icon, color, bgColor, label } = getActivityConfig();

  return (
    <div className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
      <div className={`p-2 rounded-lg ${bgColor} flex-shrink-0`}>
        <Icon className={`w-4 h-4 ${color}`} />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2 mb-1">
          <Badge variant="secondary" className="text-xs">
            {label}
          </Badge>
          <span className="text-xs text-gray-500 flex items-center">
            <Clock className="w-3 h-3 mr-1" />
            {formatTimestamp(activity.timestamp)}
          </span>
        </div>
        
        <h4 className="font-medium text-sm text-gray-900 mb-1">
          {activity.title}
        </h4>
        
        <p className="text-xs text-gray-600 line-clamp-2">
          {activity.description}
        </p>
        
        {/* Métadonnées */}
        {activity.metadata && (
          <div className="flex items-center space-x-3 mt-2">
            {activity.metadata.score && (
              <span className="text-xs text-gray-500">
                Score: {activity.metadata.score}/10
              </span>
            )}
            {activity.metadata.duration && (
              <span className="text-xs text-gray-500">
                Durée: {activity.metadata.duration}
              </span>
            )}
            {activity.metadata.progress && (
              <span className="text-xs text-gray-500">
                Progrès: {activity.metadata.progress}%
              </span>
            )}
          </div>
        )}
      </div>
      
      <Button variant="ghost" size="sm" className="p-1 h-6 w-6 flex-shrink-0">
        <ExternalLink className="w-3 h-3" />
      </Button>
    </div>
  );
};

export const RecentActivity: React.FC<RecentActivityProps> = ({ className }) => {
  // Données d'exemple d'activité récente
  const recentActivities: ActivityItem[] = [
    {
      id: '1',
      type: 'mood',
      title: 'Suivi d\'humeur matinal',
      description: 'Enregistrement de votre état émotionnel du matin avec des notes sur votre énergie et stress.',
      timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
      metadata: {
        score: 7,
        duration: '2 min'
      }
    },
    {
      id: '2',
      type: 'chat',
      title: 'Session avec Coach IA',
      description: 'Discussion sur les techniques de gestion du stress et planification de votre journée.',
      timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
      metadata: {
        duration: '12 min'
      }
    },
    {
      id: '3',
      type: 'objective',
      title: 'Objectif "Méditation quotidienne" mis à jour',
      description: 'Progression vers votre objectif de méditation quotidienne de 10 minutes.',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      metadata: {
        progress: 75
      }
    }
  ];

  const getActivitySummary = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayActivities = recentActivities.filter(activity => 
      new Date(activity.timestamp) >= today
    );
    
    return {
      total: todayActivities.length,
      types: [...new Set(todayActivities.map(a => a.type))].length
    };
  };

  const summary = getActivitySummary();

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-green-600" />
            <span>Activité Récente</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {summary.total} aujourd'hui
            </Badge>
            <Button variant="ghost" size="sm" className="p-2">
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {recentActivities.length > 0 ? (
            recentActivities.map((activity) => (
              <ActivityItemComponent key={activity.id} activity={activity} />
            ))
          ) : (
            <div className="text-center py-8">
              <Activity className="w-8 h-8 mx-auto text-gray-400 mb-2" />
              <p className="text-sm text-gray-600">Aucune activité récente</p>
            </div>
          )}
        </div>

        {/* Action pour voir plus */}
        {recentActivities.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <Button variant="outline" className="w-full text-sm" size="sm">
              <Calendar className="w-4 h-4 mr-2" />
              Voir l'historique complet
            </Button>
          </div>
        )}

        {/* Message d'encouragement */}
        <div className="mt-4 p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-4 h-4 text-green-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">
                Excellente activité !
              </p>
              <p className="text-xs text-gray-600">
                Vous êtes sur la bonne voie pour atteindre vos objectifs bien-être.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 