'use client';

import React, { useState, useEffect } from 'react';
import { useWebSocket } from '@/hooks/useWebSocket';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Bell,
  Calendar,
  MessageCircle,
  AlertTriangle,
  Wifi,
  WifiOff,
  Check,
  X,
  Clock,
  User,
  Phone,
  Video,
  MessageSquare,
  MapPin,
  Stethoscope,
  Heart,
  Activity,
  Zap,
} from 'lucide-react';

interface ConnectionStatusProps {
  isConnected: boolean;
  connectionStatus: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ 
  isConnected, 
  connectionStatus 
}) => {
  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'authenticated': return 'text-green-600 bg-green-100';
      case 'connected': return 'text-blue-600 bg-blue-100';
      case 'connecting': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'authenticated': return 'Live Updates Active';
      case 'connected': return 'Connecting...';
      case 'connecting': return 'Connecting...';
      case 'error': return 'Connection Error';
      default: return 'Offline';
    }
  };

  return (
    <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs font-medium ${getStatusColor()}`}>
      {isConnected ? (
        <Wifi className="w-3 h-3" />
      ) : (
        <WifiOff className="w-3 h-3" />
      )}
      <span>{getStatusText()}</span>
      {connectionStatus === 'authenticated' && (
        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
      )}
    </div>
  );
};

interface NotificationBadgeProps {
  count: number;
  type: 'appointments' | 'messages' | 'crisisAlerts';
  onClick?: () => void;
}

export const NotificationBadge: React.FC<NotificationBadgeProps> = ({ 
  count, 
  type, 
  onClick 
}) => {
  const getIcon = () => {
    switch (type) {
      case 'appointments': return <Calendar className="w-4 h-4" />;
      case 'messages': return <MessageCircle className="w-4 h-4" />;
      case 'crisisAlerts': return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const getColor = () => {
    switch (type) {
      case 'appointments': return 'text-blue-600 bg-blue-100 hover:bg-blue-200';
      case 'messages': return 'text-green-600 bg-green-100 hover:bg-green-200';
      case 'crisisAlerts': return 'text-red-600 bg-red-100 hover:bg-red-200';
    }
  };

  if (count === 0) return null;

  return (
    <button
      onClick={onClick}
      className={`relative flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${getColor()}`}
    >
      {getIcon()}
      <span className="text-sm font-medium">{count}</span>
      {count > 0 && (
        <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
      )}
    </button>
  );
};

interface RealTimeNotificationsPanelProps {
  className?: string;
}

export const RealTimeNotificationsPanel: React.FC<RealTimeNotificationsPanelProps> = ({ 
  className 
}) => {
  const { isConnected, connectionStatus, notifications, clearNotifications } = useWebSocket();
  const [recentNotifications, setRecentNotifications] = useState<any[]>([]);

  // Listen for real-time events and add to recent notifications
  useEffect(() => {
    const handleDashboardUpdate = (event: CustomEvent) => {
      const data = event.detail;
      if (data.type !== 'initial') {
        addRecentNotification({
          id: Date.now().toString(),
          type: 'dashboard',
          title: 'Dashboard Updated',
          message: 'Your dashboard data has been refreshed',
          timestamp: new Date(),
          icon: <Activity className="w-4 h-4" />,
          color: 'text-blue-600 bg-blue-100'
        });
      }
    };

    const handleMoodUpdate = (event: CustomEvent) => {
      addRecentNotification({
        id: Date.now().toString(),
        type: 'mood',
        title: 'Mood Entry Saved',
        message: 'Your mood tracking data has been updated',
        timestamp: new Date(),
        icon: <Heart className="w-4 h-4" />,
        color: 'text-pink-600 bg-pink-100'
      });
    };

    const handleWellnessProgressUpdate = (event: CustomEvent) => {
      const data = event.detail;
      addRecentNotification({
        id: Date.now().toString(),
        type: 'wellness',
        title: data.completed ? 'Module Completed!' : 'Progress Updated',
        message: `${data.moduleName} - ${data.progress}%`,
        timestamp: new Date(),
        icon: <Zap className="w-4 h-4" />,
        color: data.completed ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'
      });
    };

    window.addEventListener('dashboard-update', handleDashboardUpdate as EventListener);
    window.addEventListener('mood-update', handleMoodUpdate as EventListener);
    window.addEventListener('wellness-progress-update', handleWellnessProgressUpdate as EventListener);

    return () => {
      window.removeEventListener('dashboard-update', handleDashboardUpdate as EventListener);
      window.removeEventListener('mood-update', handleMoodUpdate as EventListener);
      window.removeEventListener('wellness-progress-update', handleWellnessProgressUpdate as EventListener);
    };
  }, []);

  const addRecentNotification = (notification: any) => {
    setRecentNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep only 5 recent
  };

  const removeNotification = (id: string) => {
    setRecentNotifications(prev => prev.filter(n => n.id !== id));
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - timestamp.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  return (
    <Card className={`bg-white border border-gray-200 ${className}`}>
      <CardHeader className="border-b border-gray-100 pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
            <Bell className="w-5 h-5 text-blue-600" />
            <span>Live Notifications</span>
          </CardTitle>
          
          <div className="flex items-center space-x-3">
            <ConnectionStatus 
              isConnected={isConnected} 
              connectionStatus={connectionStatus} 
            />
            
            {notifications.total > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => clearNotifications()}
                className="text-xs"
              >
                Clear All
              </Button>
            )}
          </div>
        </div>

        {/* Notification Badges */}
        <div className="flex items-center space-x-2 mt-3">
          <NotificationBadge
            count={notifications.appointments}
            type="appointments"
            onClick={() => clearNotifications('appointments')}
          />
          <NotificationBadge
            count={notifications.messages}
            type="messages"
            onClick={() => clearNotifications('messages')}
          />
          <NotificationBadge
            count={notifications.crisisAlerts}
            type="crisisAlerts"
            onClick={() => clearNotifications('crisisAlerts')}
          />
        </div>
      </CardHeader>

      <CardContent className="p-4">
        {recentNotifications.length === 0 ? (
          <div className="text-center py-6">
            <Bell className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-sm text-gray-600 mb-2">No recent notifications</p>
            <p className="text-xs text-gray-500">
              {isConnected 
                ? 'You\'ll see real-time updates here' 
                : 'Connect to see live notifications'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {recentNotifications.map((notification) => (
              <div
                key={notification.id}
                className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className={`p-2 rounded-lg ${notification.color}`}>
                  {notification.icon}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">
                      {notification.title}
                    </h4>
                    <button
                      onClick={() => removeNotification(notification.id)}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {notification.message}
                  </p>
                  <div className="flex items-center space-x-2 mt-2">
                    <Clock className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-500">
                      {formatTimeAgo(notification.timestamp)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Connection Status Details */}
        {!isConnected && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <WifiOff className="w-4 h-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">
                Real-time updates unavailable
              </span>
            </div>
            <p className="text-xs text-yellow-700 mt-1">
              Some features may not work properly. Please refresh the page or check your connection.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RealTimeNotificationsPanel;
