'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  Sun, 
  Sunset, 
  Moon, 
  CloudSun,
  User,
  Setting<PERSON>,
  Bell
} from 'lucide-react';
import { useAuthStore } from '@/stores/authStore';
import { useDashboardStore } from '@/stores/dashboardStore';

interface WelcomeHeaderProps {
  className?: string;
}

export const WelcomeHeader: React.FC<WelcomeHeaderProps> = ({ className }) => {
  const { user } = useAuthStore();
  const { wellnessStats } = useDashboardStore();

  // Déterminer la période de la journée
  const getTimeOfDay = () => {
    const hour = new Date().getHours();
    if (hour < 12) return { period: 'morning', icon: Sun, greeting: 'Bonjour' };
    if (hour < 18) return { period: 'afternoon', icon: CloudSun, greeting: '<PERSON> après-midi' };
    if (hour < 22) return { period: 'evening', icon: Sunset, greeting: 'Bon<PERSON>ir' };
    return { period: 'night', icon: Moon, greeting: '<PERSON><PERSON> soirée' };
  };

  const { period, icon: TimeIcon, greeting } = getTimeOfDay();

  // Obtenir la date formatée
  const formatDate = () => {
    const today = new Date();
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return today.toLocaleDateString('fr-FR', options);
  };

  // Déterminer le message motivationnel basé sur les statistiques
  const getMotivationalMessage = () => {
    if (!wellnessStats) return "Prenez un moment pour vous aujourd'hui.";
    
    const { currentMood, streakDays, completedObjectives, totalObjectives } = wellnessStats;
    
    if (streakDays >= 7) {
      return `Impressionnant ! ${streakDays} jours consécutifs de suivi. Continuez ainsi !`;
    }
    
    if (completedObjectives > 0) {
      const completionRate = Math.round((completedObjectives / totalObjectives) * 100);
      return `Excellent progrès ! ${completionRate}% de vos objectifs sont atteints.`;
    }
    
    if (currentMood >= 7) {
      return "Vous rayonnez aujourd'hui ! Profitez de cette énergie positive.";
    }
    
    return "Chaque petit pas compte dans votre parcours bien-être.";
  };

  // Obtenir la couleur du badge basée sur l'humeur
  const getMoodBadgeColor = () => {
    if (!wellnessStats) return 'bg-gray-100 text-gray-600';
    
    const { currentMood } = wellnessStats;
    if (currentMood >= 8) return 'bg-green-100 text-green-700';
    if (currentMood >= 6) return 'bg-blue-100 text-blue-700';
    if (currentMood >= 4) return 'bg-yellow-100 text-yellow-700';
    return 'bg-red-100 text-red-700';
  };

  return (
    <Card className={`bg-gradient-to-r from-blue-50 via-white to-green-50 border-0 shadow-sm ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            {/* Salutation et nom */}
            <div className="flex items-center space-x-3 mb-2">
              <div className="flex items-center space-x-2">
                <TimeIcon className="w-6 h-6 text-amber-500" />
                <h1 className="text-2xl font-bold text-gray-900">
                  {greeting}, {user?.firstName || 'Utilisateur'}
                </h1>
              </div>
              
              {wellnessStats && (
                <Badge className={`${getMoodBadgeColor()} border-0`}>
                  Humeur: {wellnessStats.currentMood}/10
                </Badge>
              )}
            </div>

            {/* Date */}
            <div className="flex items-center space-x-2 mb-3">
              <Calendar className="w-4 h-4 text-gray-500" />
              <p className="text-sm text-gray-600 capitalize">
                {formatDate()}
              </p>
            </div>

            {/* Message motivationnel */}
            <p className="text-gray-700 max-w-2xl">
              {getMotivationalMessage()}
            </p>

            {/* Statistiques rapides */}
            {wellnessStats && (
              <div className="flex items-center space-x-6 mt-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">
                    Séquence: {wellnessStats.streakDays} jours
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">
                    Objectifs: {wellnessStats.completedObjectives}/{wellnessStats.totalObjectives}
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">
                    Programmes actifs: {wellnessStats.activePrograms}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Actions rapides */}
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-600 hover:text-gray-900"
            >
              <Bell className="w-4 h-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-600 hover:text-gray-900"
            >
              <Settings className="w-4 h-4" />
            </Button>

            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center">
              {user?.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.firstName}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <User className="w-5 h-5 text-white" />
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 