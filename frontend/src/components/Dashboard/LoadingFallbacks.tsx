'use client';

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

// Optimized loading skeleton components
export const ChartLoadingSkeleton = React.memo(() => (
  <Card className="bg-white border border-gray-200">
    <CardHeader className="border-b border-gray-100 pb-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-32 h-5 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="flex space-x-2">
          <div className="w-16 h-8 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-16 h-8 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    </CardHeader>
    <CardContent className="p-6">
      <div className="h-64 bg-gray-100 rounded animate-pulse"></div>
    </CardContent>
  </Card>
));

ChartLoadingSkeleton.displayName = 'ChartLoadingSkeleton';

export const StatsCardSkeleton = React.memo(() => (
  <Card className="bg-white border border-gray-200">
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-16 h-8 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-20 h-3 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse"></div>
      </div>
    </CardContent>
  </Card>
));

StatsCardSkeleton.displayName = 'StatsCardSkeleton';

export const ActivityListSkeleton = React.memo(() => (
  <Card className="bg-white border border-gray-200">
    <CardHeader className="border-b border-gray-100 pb-4">
      <div className="flex items-center justify-between">
        <div className="w-32 h-6 bg-gray-200 rounded animate-pulse"></div>
        <div className="w-16 h-6 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </CardHeader>
    <CardContent className="p-6">
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
            <div className="flex-1 space-y-2">
              <div className="w-3/4 h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="w-1/2 h-3 bg-gray-200 rounded animate-pulse"></div>
            </div>
            <div className="w-16 h-3 bg-gray-200 rounded animate-pulse"></div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
));

ActivityListSkeleton.displayName = 'ActivityListSkeleton';

export const AppointmentCardSkeleton = React.memo(() => (
  <Card className="bg-white border border-gray-200">
    <CardContent className="p-6">
      <div className="flex items-center space-x-4">
        <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse"></div>
        <div className="flex-1 space-y-2">
          <div className="w-3/4 h-4 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-1/2 h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="flex space-x-2">
            <div className="w-16 h-6 bg-gray-200 rounded animate-pulse"></div>
            <div className="w-20 h-6 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
        <div className="w-8 h-8 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </CardContent>
  </Card>
));

AppointmentCardSkeleton.displayName = 'AppointmentCardSkeleton';

export const NotificationPanelSkeleton = React.memo(() => (
  <Card className="bg-white border border-gray-200">
    <CardHeader className="border-b border-gray-100 pb-4">
      <div className="flex items-center justify-between">
        <div className="w-40 h-6 bg-gray-200 rounded animate-pulse"></div>
        <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse"></div>
      </div>
    </CardHeader>
    <CardContent className="p-6">
      <div className="space-y-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
            <div className="flex-1 space-y-1">
              <div className="w-full h-3 bg-gray-200 rounded animate-pulse"></div>
              <div className="w-2/3 h-3 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
));

NotificationPanelSkeleton.displayName = 'NotificationPanelSkeleton';

// Combined dashboard loading state
export const DashboardLoadingState = React.memo(() => (
  <div className="space-y-6">
    {/* Header skeleton */}
    <div className="flex items-center justify-between">
      <div className="space-y-2">
        <div className="w-48 h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>
      <div className="flex space-x-2">
        <div className="w-24 h-10 bg-gray-200 rounded animate-pulse"></div>
        <div className="w-32 h-10 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>

    {/* Stats cards skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array.from({ length: 4 }).map((_, i) => (
        <StatsCardSkeleton key={i} />
      ))}
    </div>

    {/* Main content skeleton */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2 space-y-6">
        <ChartLoadingSkeleton />
        <ChartLoadingSkeleton />
      </div>
      <div className="space-y-6">
        <ActivityListSkeleton />
        <NotificationPanelSkeleton />
      </div>
    </div>
  </div>
));

DashboardLoadingState.displayName = 'DashboardLoadingState';
