'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Quote, 
  RefreshCw, 
  Heart, 
  Sparkles,
  MessageCircle,
  Volume2,
  VolumeX
} from 'lucide-react';

interface AIQuote {
  id: string;
  text: string;
  author: string;
  category: 'motivation' | 'wellness' | 'mindfulness' | 'growth';
  mood: 'uplifting' | 'calming' | 'energizing' | 'reflective';
  timeOfDay?: 'morning' | 'afternoon' | 'evening';
}

interface AICoachQuoteProps {
  className?: string;
}

export const AICoachQuote: React.FC<AICoachQuoteProps> = ({ className }) => {
  const [currentQuote, setCurrentQuote] = useState<AIQuote | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);

  // Collection de citations personnalisées par l'IA Coach
  const quotes: AIQuote[] = [
    {
      id: '1',
      text: "Chaque petit pas vers votre bien-être est une victoire qui mérite d'être célébrée.",
      author: "Coach IA MindFlow",
      category: 'motivation',
      mood: 'uplifting',
      timeOfDay: 'morning'
    },
    {
      id: '2',
      text: "La respiration consciente est votre superpouvoir portable - utilisez-la à tout moment.",
      author: "Coach IA MindFlow",
      category: 'mindfulness',
      mood: 'calming',
      timeOfDay: 'afternoon'
    },
    {
      id: '3',
      text: "Vos émotions sont des messages, pas des maîtres. Écoutez-les avec bienveillance.",
      author: "Coach IA MindFlow",
      category: 'wellness',
      mood: 'reflective',
      timeOfDay: 'evening'
    },
    {
      id: '4',
      text: "Aujourd'hui est une nouvelle opportunité de nourrir votre esprit et votre âme.",
      author: "Coach IA MindFlow",
      category: 'motivation',
      mood: 'energizing',
      timeOfDay: 'morning'
    }
  ];

  // Sélectionner une citation basée sur l'heure de la journée
  const getQuoteForTimeOfDay = () => {
    const hour = new Date().getHours();
    let timeOfDay: 'morning' | 'afternoon' | 'evening';

    if (hour < 12) timeOfDay = 'morning';
    else if (hour < 18) timeOfDay = 'afternoon';
    else timeOfDay = 'evening';

    const timeBasedQuotes = quotes.filter(q => q.timeOfDay === timeOfDay);
    
    if (timeBasedQuotes.length > 0) {
      return timeBasedQuotes[Math.floor(Math.random() * timeBasedQuotes.length)];
    }
    
    return quotes[Math.floor(Math.random() * quotes.length)];
  };

  // Initialiser avec une citation appropriée
  useEffect(() => {
    setCurrentQuote(getQuoteForTimeOfDay());
  }, []);

  const handleRefreshQuote = async () => {
    setIsRefreshing(true);
    
    // Simulation d'un appel API pour une nouvelle citation
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    let newQuote;
    do {
      newQuote = quotes[Math.floor(Math.random() * quotes.length)];
    } while (newQuote.id === currentQuote?.id && quotes.length > 1);
    
    setCurrentQuote(newQuote);
    setIsRefreshing(false);
  };

  const handleSpeak = () => {
    if (!currentQuote) return;

    if (isSpeaking) {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
      return;
    }

    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(currentQuote.text);
      utterance.lang = 'fr-FR';
      utterance.rate = 0.8;
      utterance.pitch = 1;
      
      utterance.onstart = () => setIsSpeaking(true);
      utterance.onend = () => setIsSpeaking(false);
      utterance.onerror = () => setIsSpeaking(false);
      
      window.speechSynthesis.speak(utterance);
    }
  };

  const getMoodConfig = (mood: string) => {
    switch (mood) {
      case 'uplifting':
        return {
          bgGradient: 'from-yellow-50 to-orange-50',
          accentColor: 'text-yellow-600',
          icon: Sparkles
        };
      case 'calming':
        return {
          bgGradient: 'from-blue-50 to-cyan-50',
          accentColor: 'text-blue-600',
          icon: Heart
        };
      case 'energizing':
        return {
          bgGradient: 'from-green-50 to-emerald-50',
          accentColor: 'text-green-600',
          icon: Sparkles
        };
      case 'reflective':
        return {
          bgGradient: 'from-purple-50 to-indigo-50',
          accentColor: 'text-purple-600',
          icon: Quote
        };
      default:
        return {
          bgGradient: 'from-gray-50 to-slate-50',
          accentColor: 'text-gray-600',
          icon: Quote
        };
    }
  };

  if (!currentQuote) {
    return (
      <Card className={`${className}`}>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { bgGradient, accentColor, icon: MoodIcon } = getMoodConfig(currentQuote.mood);

  return (
    <Card className={`border-0 shadow-sm overflow-hidden ${className}`}>
      <CardContent className={`p-6 bg-gradient-to-br ${bgGradient}`}>
        <div className="space-y-4">
          {/* En-tête avec icône */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MoodIcon className={`w-5 h-5 ${accentColor}`} />
              <span className={`text-sm font-medium ${accentColor}`}>
                Coach IA
              </span>
            </div>
            
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSpeak}
                className="p-2 hover:bg-white/50"
                disabled={!('speechSynthesis' in window)}
              >
                {isSpeaking ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefreshQuote}
                disabled={isRefreshing}
                className="p-2 hover:bg-white/50"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>

          {/* Citation */}
          <div className="relative">
            <Quote className={`w-8 h-8 ${accentColor} opacity-20 absolute -top-2 -left-2`} />
            <blockquote className="text-gray-800 font-medium leading-relaxed pl-6">
              "{currentQuote.text}"
            </blockquote>
          </div>

          {/* Auteur et catégorie */}
          <div className="flex items-center justify-between pt-2">
            <div className="text-sm text-gray-600">
              — {currentQuote.author}
            </div>
            <div className={`text-xs px-2 py-1 rounded-full bg-white/50 ${accentColor}`}>
              {currentQuote.category}
            </div>
          </div>

          {/* Action vers le coach IA */}
          <div className="pt-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-700 hover:bg-white/50 w-full justify-start"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Parler avec votre Coach IA
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 