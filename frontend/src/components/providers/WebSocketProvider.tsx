'use client';

import { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './AuthProvider';
import { toast } from 'sonner';

interface WebSocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  emit: (event: string, data?: any) => void;
  on: (event: string, callback: (data: any) => void) => void;
  off: (event: string, callback?: (data: any) => void) => void;
  joinRoom: (room: string) => void;
  leaveRoom: (room: string) => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

interface WebSocketProviderProps {
  children: ReactNode;
}

export function WebSocketProvider({ children }: WebSocketProviderProps) {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { user, isAuthenticated } = useAuth();

  // Initialisation de la connexion WebSocket
  useEffect(() => {
    if (!isAuthenticated || !user) {
      // Fermer la connexion si l'utilisateur n'est pas authentifié
      if (socket) {
        socket.disconnect();
        setSocket(null);
        setIsConnected(false);
      }
      return;
    }

    // Créer une nouvelle connexion WebSocket
    const newSocket = io(process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:4000', {
      auth: {
        token: localStorage.getItem('accessToken'), // ou récupérer depuis le store
        userId: user.id,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      retries: 3,
    });

    // Gestionnaires d'événements de connexion
    newSocket.on('connect', () => {
      console.log('WebSocket connecté');
      setIsConnected(true);
      toast.success('Connexion temps réel établie');
    });

    newSocket.on('disconnect', (reason) => {
      console.log('WebSocket déconnecté:', reason);
      setIsConnected(false);
      
      if (reason === 'io server disconnect') {
        // Le serveur a fermé la connexion, on essaie de se reconnecter
        newSocket.connect();
      }
    });

    newSocket.on('connect_error', (error) => {
      console.error('Erreur de connexion WebSocket:', error);
      setIsConnected(false);
      
      // Ne pas afficher de toast d'erreur à chaque tentative de reconnexion
      if (!newSocket.recovered) {
        toast.error('Erreur de connexion temps réel');
      }
    });

    // Gestionnaires d'événements spécifiques à l'application
    newSocket.on('notification', (data) => {
      toast.info(data.message, {
        description: data.description,
        action: data.action ? {
          label: data.action.label,
          onClick: () => {
            // Gérer l'action de la notification
            if (data.action.type === 'navigate') {
              window.location.href = data.action.url;
            }
          },
        } : undefined,
      });
    });

    newSocket.on('ai_response', (data) => {
      // Émettre un événement personnalisé pour les réponses de l'IA
      window.dispatchEvent(new CustomEvent('ai-response', { detail: data }));
    });

    newSocket.on('mood_update', (data) => {
      // Mettre à jour les données d'humeur en temps réel
      window.dispatchEvent(new CustomEvent('mood-update', { detail: data }));
    });

    newSocket.on('appointment_reminder', (data) => {
      toast.info(`Rappel: ${data.title}`, {
        description: `Dans ${data.timeUntil}`,
        duration: 10000,
      });
    });

    newSocket.on('emergency_alert', (data) => {
      toast.error(data.message, {
        description: data.description,
        duration: 0, // Notification persistante pour les urgences
        action: {
          label: 'Contacter',
          onClick: () => {
            window.open(`tel:${data.emergencyNumber}`);
          },
        },
      });
    });

    // Rejoindre automatiquement la room de l'utilisateur
    newSocket.emit('join_user_room', user.id);

    setSocket(newSocket);

    // Nettoyage lors de la destruction du composant
    return () => {
      newSocket.off('connect');
      newSocket.off('disconnect');
      newSocket.off('connect_error');
      newSocket.off('notification');
      newSocket.off('ai_response');
      newSocket.off('mood_update');
      newSocket.off('appointment_reminder');
      newSocket.off('emergency_alert');
      newSocket.disconnect();
    };
  }, [isAuthenticated, user]);

  // Méthodes utilitaires
  const emit = useCallback((event: string, data?: any) => {
    if (socket && isConnected) {
      socket.emit(event, data);
    } else {
      console.warn('Tentative d\'émission sur une socket non connectée:', event);
    }
  }, [socket, isConnected]);

  const on = useCallback((event: string, callback: (data: any) => void) => {
    if (socket) {
      socket.on(event, callback);
    }
  }, [socket]);

  const off = useCallback((event: string, callback?: (data: any) => void) => {
    if (socket) {
      if (callback) {
        socket.off(event, callback);
      } else {
        socket.off(event);
      }
    }
  }, [socket]);

  const joinRoom = useCallback((room: string) => {
    emit('join_room', { room });
  }, [emit]);

  const leaveRoom = useCallback((room: string) => {
    emit('leave_room', { room });
  }, [emit]);

  const contextValue: WebSocketContextType = {
    socket,
    isConnected,
    emit,
    on,
    off,
    joinRoom,
    leaveRoom,
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
}

export function useWebSocket() {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
} 