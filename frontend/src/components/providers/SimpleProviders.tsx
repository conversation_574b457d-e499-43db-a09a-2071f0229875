'use client';

import { ThemeProvider } from 'next-themes';
import { ReactNode } from 'react';

interface SimpleProvidersProps {
  children: ReactNode;
}

export function SimpleProviders({ children }: SimpleProvidersProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem
      disableTransitionOnChange
      themes={['light', 'dark']}
    >
      {children}
    </ThemeProvider>
  );
} 