'use client';

import { createContext, useContext, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import useAuthStore, { type User } from '@/stores/authStore';
import { toast } from 'sonner';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  register: (userData: any) => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

// Pages publiques qui ne nécessitent pas d'authentification
const PUBLIC_ROUTES = [
  '/',
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/about',
  '/contact',
  '/privacy',
  '/terms',
];

// Pages qui nécessitent une authentification
const PROTECTED_ROUTES = [
  '/dashboard',
  '/coach',
  '/journal',
  '/programs',
  '/professionals',
  '/appointments',
  '/profile',
];

export function AuthProvider({ children }: AuthProviderProps) {
  const router = useRouter();
  const pathname = usePathname();
  
  const {
    user,
    isAuthenticated,
    isLoading,
    error,
    login: storeLogin,
    register: storeRegister,
    logout: storeLogout,
    refreshAuth,
    clearError,
  } = useAuthStore();

  // Vérification de l'authentification au chargement
  useEffect(() => {
    const initAuth = async () => {
      // Si on a un token de rafraîchissement, on essaie de rafraîchir la session
      const { refreshToken } = useAuthStore.getState();
      
      if (refreshToken && !isAuthenticated) {
        try {
          await refreshAuth();
        } catch (error) {
          console.error('Erreur lors du rafraîchissement de la session:', error);
          // On ne redirige pas ici, on laisse l'utilisateur sur la page actuelle
        }
      }
    };

    initAuth();
  }, [isAuthenticated, refreshAuth]);

  // Gestion des redirections basées sur l'authentification
  useEffect(() => {
    if (isLoading) return; // Attendre que le chargement soit terminé

    const isPublicRoute = PUBLIC_ROUTES.includes(pathname) || pathname.startsWith('/auth/');
    const isProtectedRoute = PROTECTED_ROUTES.some(route => pathname.startsWith(route));

    if (!isAuthenticated && isProtectedRoute) {
      // Utilisateur non authentifié essayant d'accéder à une page protégée
      toast.error('Vous devez vous connecter pour accéder à cette page');
      router.push(`/auth/login?redirect=${encodeURIComponent(pathname)}`);
    } else if (isAuthenticated && pathname.startsWith('/auth/')) {
      // Utilisateur authentifié sur une page d'authentification
      const urlParams = new URLSearchParams(window.location.search);
      const redirectTo = urlParams.get('redirect') || '/dashboard';
      router.push(redirectTo);
    }
  }, [isAuthenticated, pathname, router, isLoading]);

  // Rafraîchissement automatique du token
  useEffect(() => {
    if (!isAuthenticated) return;

    const refreshInterval = setInterval(async () => {
      try {
        await refreshAuth();
      } catch (error) {
        console.error('Erreur lors du rafraîchissement automatique:', error);
        // Si le rafraîchissement échoue, on déconnecte l'utilisateur
        storeLogout();
        toast.error('Session expirée. Veuillez vous reconnecter.');
        router.push('/auth/login');
      }
    }, 15 * 60 * 1000); // Rafraîchir toutes les 15 minutes

    return () => clearInterval(refreshInterval);
  }, [isAuthenticated, refreshAuth, storeLogout, router]);

  // Gestionnaires d'événements avec gestion d'erreur et notifications
  const handleLogin = async (email: string, password: string, rememberMe = false) => {
    try {
      await storeLogin(email, password, rememberMe);
      toast.success('Connexion réussie !');
      
      // Redirection après connexion réussie
      const urlParams = new URLSearchParams(window.location.search);
      const redirectTo = urlParams.get('redirect') || '/dashboard';
      router.push(redirectTo);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur de connexion';
      toast.error(errorMessage);
      throw error;
    }
  };

  const handleRegister = async (userData: any) => {
    try {
      await storeRegister(userData);
      toast.success('Inscription réussie ! Bienvenue sur MindFlow Pro !');
      router.push('/dashboard');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur d\'inscription';
      toast.error(errorMessage);
      throw error;
    }
  };

  const handleLogout = () => {
    storeLogout();
    toast.success('Déconnexion réussie');
    router.push('/');
  };

  // Gestionnaire d'erreur global
  useEffect(() => {
    if (error) {
      toast.error(error);
      clearError();
    }
  }, [error, clearError]);

  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    error,
    login: handleLogin,
    register: handleRegister,
    logout: handleLogout,
    clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 