'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { TrendingUp, Brain, AlertTriangle, Target } from 'lucide-react';

interface DataPoint {
  date: string;
  value: number;
  predicted?: boolean;
  confidence?: number;
}

interface PredictiveChartProps {
  title: string;
  data: DataPoint[];
  type: 'mood' | 'risk' | 'improvement' | 'activity';
  className?: string;
}

export const PredictiveChart: React.FC<PredictiveChartProps> = ({
  title,
  data,
  type,
  className = ''
}) => {
  const getIcon = () => {
    switch (type) {
      case 'mood': return <Brain className="h-6 w-6 text-blue-600" />;
      case 'risk': return <AlertTriangle className="h-6 w-6 text-red-600" />;
      case 'improvement': return <TrendingUp className="h-6 w-6 text-green-600" />;
      case 'activity': return <Target className="h-6 w-6 text-purple-600" />;
      default: return <Brain className="h-6 w-6 text-blue-600" />;
    }
  };

  const getGradientColor = () => {
    switch (type) {
      case 'mood': return 'from-blue-500 to-blue-600';
      case 'risk': return 'from-red-500 to-red-600';
      case 'improvement': return 'from-green-500 to-green-600';
      case 'activity': return 'from-purple-500 to-purple-600';
      default: return 'from-blue-500 to-blue-600';
    }
  };

  const maxValue = Math.max(...data.map(d => d.value));
  const currentValue = data[data.length - 1]?.value || 0;
  const previousValue = data[data.length - 2]?.value || 0;
  const trend = currentValue > previousValue ? 'up' : 'down';
  const trendPercentage = previousValue !== 0 ? 
    Math.abs((currentValue - previousValue) / previousValue * 100) : 0;

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          {getIcon()}
          <h3 className="text-lg font-semibold">{title}</h3>
        </div>
        <div className={`px-3 py-1 rounded-full text-xs font-semibold ${
          trend === 'up' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {trend === 'up' ? '↗' : '↘'} {trendPercentage.toFixed(1)}%
        </div>
      </div>

      {/* Chart Simulation */}
      <div className="relative h-32 mb-4">
        <div className="absolute inset-0 flex items-end justify-between gap-1">
          {data.slice(-10).map((point, index) => {
            const height = (point.value / maxValue) * 100;
            const isPredicted = point.predicted;
            
            return (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div 
                  className={`w-full rounded-t transition-all duration-500 ${
                    isPredicted 
                      ? `bg-gradient-to-t ${getGradientColor()} opacity-60 border-2 border-dashed border-gray-400`
                      : `bg-gradient-to-t ${getGradientColor()}`
                  }`}
                  style={{ height: `${height}%` }}
                />
                {isPredicted && (
                  <div className="text-xs text-gray-500 mt-1">
                    {point.confidence && `${(point.confidence * 100).toFixed(0)}%`}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Current Value */}
      <div className="flex items-center justify-between">
        <div>
          <div className="text-2xl font-bold text-gray-900">
            {currentValue.toFixed(1)}
          </div>
          <div className="text-sm text-gray-500">Valeur actuelle</div>
        </div>
        <div className="text-right">
          <div className="text-lg font-semibold text-gray-700">
            {data.filter(d => d.predicted).length}
          </div>
          <div className="text-sm text-gray-500">Prédictions</div>
        </div>
      </div>

      {/* Predicted Section */}
      {data.some(d => d.predicted) && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-blue-600 opacity-60 border-2 border-dashed border-gray-400 rounded"></div>
            <span className="text-sm font-medium text-gray-700">Prédictions IA</span>
          </div>
          <div className="text-xs text-gray-600">
            Basées sur l'analyse comportementale et les patterns historiques
          </div>
        </div>
      )}
    </Card>
  );
}; 