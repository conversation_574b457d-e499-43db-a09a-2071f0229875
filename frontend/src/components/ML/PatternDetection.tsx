'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Eye, TrendingUp, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

interface Pattern {
  id: string;
  name: string;
  type: 'behavioral' | 'temporal' | 'correlation' | 'anomaly';
  description: string;
  confidence: number;
  frequency: string;
  impact: 'positive' | 'negative' | 'neutral';
  detected_at: string;
  related_factors: string[];
}

interface PatternDetectionProps {
  className?: string;
}

export const PatternDetection: React.FC<PatternDetectionProps> = ({
  className = ''
}) => {
  const [patterns] = useState<Pattern[]>([
    {
      id: '1',
      name: 'Amélioration Matinale',
      type: 'temporal',
      description: 'Humeur significativement meilleure entre 8h-10h',
      confidence: 0.89,
      frequency: 'Quotidien (5/7 jours)',
      impact: 'positive',
      detected_at: '2024-12-29T09:30:00Z',
      related_factors: ['Sommeil', 'Routine matinale', 'Exposition lumière']
    },
    {
      id: '2', 
      name: '<PERSON>ress Professionnel',
      type: 'behavioral',
      description: 'Pic de stress lors des réunions virtuelles',
      confidence: 0.76,
      frequency: '3-4 fois/semaine',
      impact: 'negative',
      detected_at: '2024-12-28T14:15:00Z',
      related_factors: ['Travail', 'Communication', 'Fatigue']
    },
    {
      id: '3',
      name: 'Corrélation Exercice-Humeur',
      type: 'correlation',
      description: 'Activité physique correlée à +23% bien-être',
      confidence: 0.92,
      frequency: 'Variable',
      impact: 'positive',
      detected_at: '2024-12-27T16:45:00Z',
      related_factors: ['Sport', 'Endorphines', 'Energie']
    }
  ]);

  const [selectedType, setSelectedType] = useState<string>('all');

  const getPatternIcon = (type: string) => {
    switch (type) {
      case 'behavioral': return <TrendingUp className="h-5 w-5 text-blue-600" />;
      case 'temporal': return <Clock className="h-5 w-5 text-purple-600" />;
      case 'correlation': return <Search className="h-5 w-5 text-green-600" />;
      case 'anomaly': return <AlertTriangle className="h-5 w-5 text-orange-600" />;
      default: return <Search className="h-5 w-5 text-gray-600" />;
    }
  };

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'positive': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'negative': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'negative': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'behavioral': return 'bg-blue-100 text-blue-800';
      case 'temporal': return 'bg-purple-100 text-purple-800';
      case 'correlation': return 'bg-green-100 text-green-800';
      case 'anomaly': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredPatterns = selectedType === 'all' 
    ? patterns 
    : patterns.filter(p => p.type === selectedType);

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Search className="h-6 w-6 text-purple-600" />
          <h3 className="text-lg font-semibold">Détection de Patterns</h3>
        </div>
        <Badge className="bg-purple-100 text-purple-800">
          {patterns.length} pattern{patterns.length > 1 ? 's' : ''} détecté{patterns.length > 1 ? 's' : ''}
        </Badge>
      </div>

      {/* Filtres */}
      <div className="flex flex-wrap gap-2 mb-6">
        <Button
          variant={selectedType === 'all' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedType('all')}
        >
          Tous ({patterns.length})
        </Button>
        <Button
          variant={selectedType === 'behavioral' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedType('behavioral')}
        >
          Comportemental ({patterns.filter(p => p.type === 'behavioral').length})
        </Button>
        <Button
          variant={selectedType === 'temporal' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedType('temporal')}
        >
          Temporel ({patterns.filter(p => p.type === 'temporal').length})
        </Button>
        <Button
          variant={selectedType === 'correlation' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedType('correlation')}
        >
          Corrélation ({patterns.filter(p => p.type === 'correlation').length})
        </Button>
      </div>

      {/* Liste des patterns */}
      <div className="space-y-4">
        {filteredPatterns.map((pattern) => (
          <div key={pattern.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
            {/* Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-2">
                {getPatternIcon(pattern.type)}
                <h4 className="font-semibold text-gray-900">{pattern.name}</h4>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={getTypeColor(pattern.type)}>
                  {pattern.type}
                </Badge>
                <Badge className={getImpactColor(pattern.impact)}>
                  {getImpactIcon(pattern.impact)}
                  {pattern.impact}
                </Badge>
              </div>
            </div>

            {/* Description */}
            <p className="text-gray-700 mb-3">{pattern.description}</p>

            {/* Métriques */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
              <div className="text-sm">
                <span className="text-gray-500">Confiance:</span>
                <div className="flex items-center gap-2 mt-1">
                  <div className="flex-1 h-2 bg-gray-200 rounded-full">
                    <div 
                      className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"
                      style={{ width: `${pattern.confidence * 100}%` }}
                    />
                  </div>
                  <span className="font-semibold">{(pattern.confidence * 100).toFixed(0)}%</span>
                </div>
              </div>
              
              <div className="text-sm">
                <span className="text-gray-500">Fréquence:</span>
                <div className="font-semibold text-gray-900 mt-1">{pattern.frequency}</div>
              </div>
              
              <div className="text-sm">
                <span className="text-gray-500">Détecté le:</span>
                <div className="font-semibold text-gray-900 mt-1">{formatDate(pattern.detected_at)}</div>
              </div>
            </div>

            {/* Facteurs liés */}
            <div>
              <span className="text-sm text-gray-500">Facteurs liés:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {pattern.related_factors.map((factor, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {factor}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredPatterns.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>Aucun pattern trouvé pour ce filtre.</p>
        </div>
      )}
    </Card>
  );
};
