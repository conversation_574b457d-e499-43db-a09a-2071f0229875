'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Brain, Lightbulb, AlertCircle, CheckCircle } from 'lucide-react';

interface Insight {
  id: string;
  type: 'pattern' | 'recommendation' | 'alert' | 'achievement';
  title: string;
  description: string;
  confidence: number;
  timestamp: string;
}

interface MLInsightsProps {
  insights: Insight[];
  className?: string;
}

export const MLInsights: React.FC<MLInsightsProps> = ({
  insights,
  className = ''
}) => {
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'pattern': return <Brain className="h-5 w-5 text-blue-600" />;
      case 'recommendation': return <Lightbulb className="h-5 w-5 text-yellow-600" />;
      case 'alert': return <AlertCircle className="h-5 w-5 text-red-600" />;
      case 'achievement': return <CheckCircle className="h-5 w-5 text-green-600" />;
      default: return <Brain className="h-5 w-5 text-blue-600" />;
    }
  };

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <Brain className="h-6 w-6 text-purple-600" />
        <h3 className="text-lg font-semibold">Insights IA Avancés</h3>
      </div>
      
      {insights.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>Génération d'insights en cours...</p>
        </div>
      ) : (
        <div className="space-y-4">
          {insights.map((insight) => (
            <div key={insight.id} className="border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                {getInsightIcon(insight.type)}
                <h4 className="font-semibold">{insight.title}</h4>
                <Badge className="ml-auto">
                  {(insight.confidence * 100).toFixed(0)}%
                </Badge>
              </div>
              <p className="text-gray-700">{insight.description}</p>
            </div>
          ))}
        </div>
      )}
    </Card>
  );
};
