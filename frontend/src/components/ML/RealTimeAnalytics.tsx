'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Activity, Zap, TrendingUp, Users, Brain } from 'lucide-react';

interface RealTimeMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  change: number;
}

interface RealTimeAnalyticsProps {
  className?: string;
}

export const RealTimeAnalytics: React.FC<RealTimeAnalyticsProps> = ({
  className = ''
}) => {
  const [metrics, setMetrics] = useState<RealTimeMetric[]>([
    {
      id: '1',
      name: 'Prédictions/min',
      value: 0,
      unit: '/min',
      trend: 'stable',
      change: 0
    },
    {
      id: '2', 
      name: 'Précision IA',
      value: 95.2,
      unit: '%',
      trend: 'up',
      change: 2.1
    },
    {
      id: '3',
      name: 'Utilisateurs actifs',
      value: 847,
      unit: '',
      trend: 'up', 
      change: 12.5
    },
    {
      id: '4',
      name: 'Temps réponse',
      value: 45,
      unit: 'ms',
      trend: 'down',
      change: -8.3
    }
  ]);

  const [isLive, setIsLive] = useState(false);

  useEffect(() => {
    if (!isLive) return;

    const interval = setInterval(() => {
      setMetrics(prev => prev.map(metric => {
        const randomChange = (Math.random() - 0.5) * 10;
        const newValue = Math.max(0, metric.value + randomChange);
        
        return {
          ...metric,
          value: newValue,
          trend: randomChange > 2 ? 'up' : randomChange < -2 ? 'down' : 'stable',
          change: randomChange
        };
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, [isLive]);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down': return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Activity className="h-6 w-6 text-blue-600" />
          <h3 className="text-lg font-semibold">Analytics Temps Réel</h3>
        </div>
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${isLive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
          <Button
            variant={isLive ? "destructive" : "default"}
            size="sm"
            onClick={() => setIsLive(!isLive)}
            className="flex items-center gap-1"
          >
            <Zap className="h-4 w-4" />
            {isLive ? 'Arrêter' : 'Démarrer'} Live
          </Button>
        </div>
      </div>

      {/* Métriques principales */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {metrics.map((metric) => (
          <div key={metric.id} className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {typeof metric.value === 'number' ? metric.value.toFixed(1) : metric.value}
              <span className="text-sm text-gray-500 ml-1">{metric.unit}</span>
            </div>
            <div className="text-sm text-gray-600 mb-2">{metric.name}</div>
            <div className={`flex items-center justify-center gap-1 text-xs ${getTrendColor(metric.trend)}`}>
              {getTrendIcon(metric.trend)}
              {Math.abs(metric.change).toFixed(1)}%
            </div>
          </div>
        ))}
      </div>

      {/* Status IA */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Brain className="h-5 w-5" />
            <span className="font-semibold">Modèles IA</span>
          </div>
          <div className="text-2xl font-bold">3</div>
          <div className="text-sm opacity-80">Actifs</div>
        </div>

        <div className="p-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Users className="h-5 w-5" />
            <span className="font-semibold">Sessions</span>
          </div>
          <div className="text-2xl font-bold">{isLive ? Math.floor(Math.random() * 100) + 50 : 67}</div>
          <div className="text-sm opacity-80">En cours</div>
        </div>

        <div className="p-4 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Zap className="h-5 w-5" />
            <span className="font-semibold">Performance</span>
          </div>
          <div className="text-2xl font-bold">99.8%</div>
          <div className="text-sm opacity-80">Uptime</div>
        </div>
      </div>

      {/* Informations système */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <div className="text-sm text-gray-600">
          <div className="flex justify-between mb-1">
            <span>Dernière mise à jour:</span>
            <span>{new Date().toLocaleTimeString('fr-FR')}</span>
          </div>
          <div className="flex justify-between">
            <span>Mode:</span>
            <span className={isLive ? 'text-green-600 font-semibold' : 'text-gray-500'}>
              {isLive ? 'Temps réel actif' : 'Mode statique'}
            </span>
          </div>
        </div>
      </div>
    </Card>
  );
};
