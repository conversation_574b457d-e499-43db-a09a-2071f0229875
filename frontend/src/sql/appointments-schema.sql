-- Script de création des tables pour le système de rendez-vous
-- MindFlow Pro - Intégration Supabase

-- Table des professionnels (simplifiée pour les rendez-vous)
CREATE TABLE IF NOT EXISTS professionals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  role VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE,
  phone VARCHAR(50),
  avatar_url TEXT,
  specialties TEXT[],
  price_per_session DECIMAL(10,2) DEFAULT 80.00,
  currency VARCHAR(3) DEFAULT 'EUR',
  location TEXT,
  bio TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des rendez-vous
CREATE TABLE IF NOT EXISTS appointments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  professional_id UUID NOT NULL REFERENCES professionals(id) ON DELETE CASCADE,
  professional_name VA<PERSON>HAR(255) NOT NULL,
  professional_role VARCHAR(255) NOT NULL,
  professional_avatar TEXT,
  client_id UUID NOT NULL, -- Sera relié au système d'auth plus tard
  client_name VARCHAR(255) NOT NULL,
  appointment_date DATE NOT NULL,
  appointment_time TIME NOT NULL,
  duration_minutes INTEGER NOT NULL DEFAULT 60,
  type VARCHAR(20) NOT NULL CHECK (type IN ('video', 'in-person', 'chat')),
  status VARCHAR(20) NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no-show')),
  price DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'EUR',
  notes TEXT,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  feedback TEXT,
  reminder_sent BOOLEAN DEFAULT false,
  cancel_reason TEXT,
  meeting_link TEXT,
  location TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_appointments_professional_id ON appointments(professional_id);
CREATE INDEX IF NOT EXISTS idx_appointments_client_id ON appointments(client_id);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_appointments_type ON appointments(type);

-- Fonction pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
CREATE TRIGGER update_professionals_updated_at 
    BEFORE UPDATE ON professionals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at 
    BEFORE UPDATE ON appointments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insertion de professionnels de test
INSERT INTO professionals (id, name, role, email, specialties, price_per_session, location, bio) VALUES
(
  '550e8400-e29b-41d4-a716-446655440001',
  'Dr. Sophie Martin',
  'Psychologue clinicienne',
  '<EMAIL>',
  ARRAY['Thérapie cognitive comportementale', 'Gestion du stress', 'Anxiété'],
  85.00,
  '15 rue de la Paix, Paris',
  'Spécialisée dans la thérapie cognitive comportementale avec plus de 10 ans d''expérience.'
),
(
  '550e8400-e29b-41d4-a716-446655440002',
  'Dr. Jean Dupont',
  'Psychiatre',
  '<EMAIL>',
  ARRAY['Psychiatrie générale', 'Troubles bipolaires', 'Dépression'],
  120.00,
  '42 avenue Victor Hugo, Lyon',
  'Psychiatre expérimenté spécialisé dans les troubles de l''humeur.'
),
(
  '550e8400-e29b-41d4-a716-446655440003',
  'Marie Leblanc',
  'Thérapeute comportementale',
  '<EMAIL>',
  ARRAY['Thérapie comportementale', 'Phobies', 'Addictions'],
  75.00,
  '8 place Bellecour, Lyon',
  'Thérapeute spécialisée dans les troubles comportementaux et les phobies.'
),
(
  '550e8400-e29b-41d4-a716-446655440004',
  'Dr. Ahmed Benali',
  'Psychothérapeute',
  '<EMAIL>',
  ARRAY['Psychothérapie humaniste', 'Thérapie de couple', 'Traumatismes'],
  90.00,
  '25 cours Mirabeau, Marseille',
  'Psychothérapeute avec une approche humaniste centrée sur la personne.'
);

-- RLS (Row Level Security) - À configurer selon les besoins d'authentification
ALTER TABLE professionals ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;

-- Politique de base pour les professionnels (lecture publique pour l'instant)
CREATE POLICY "Professionnels visibles par tous" ON professionals
  FOR SELECT USING (is_active = true);

-- Politique de base pour les rendez-vous (les utilisateurs ne voient que leurs propres rendez-vous)
-- Note: à adapter selon le système d'auth final
CREATE POLICY "Utilisateurs voient leurs rendez-vous" ON appointments
  FOR ALL USING (true); -- Temporaire - à sécuriser plus tard

-- Vue pour les statistiques des rendez-vous
CREATE OR REPLACE VIEW appointment_stats AS
SELECT 
  COUNT(*) as total_appointments,
  COUNT(*) FILTER (WHERE status IN ('scheduled', 'confirmed') AND appointment_date >= CURRENT_DATE) as upcoming_appointments,
  COUNT(*) FILTER (WHERE status = 'completed') as completed_appointments,
  COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_appointments,
  COUNT(*) FILTER (WHERE status = 'no-show') as no_show_appointments,
  COALESCE(SUM(price) FILTER (WHERE status = 'completed'), 0) as total_revenue,
  COALESCE(AVG(rating) FILTER (WHERE rating IS NOT NULL), 0) as average_rating,
  COUNT(*) FILTER (WHERE status = 'completed' AND appointment_date >= CURRENT_DATE - INTERVAL '7 days') as weekly_appointments,
  COUNT(*) FILTER (WHERE status = 'completed' AND appointment_date >= CURRENT_DATE - INTERVAL '30 days') as monthly_appointments
FROM appointments; 