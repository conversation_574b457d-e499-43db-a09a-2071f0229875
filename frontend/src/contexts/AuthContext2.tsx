'use client';

import React, { createContext, useContext, ReactNode, useEffect } from 'react';
import { useAuthStore } from '@/stores/authStore';

// Re-export the User type from the auth store
export type { User } from '@/stores/authStore';

interface AuthContextType {
  user: any | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
    confirmPassword?: string;
  }) => Promise<void>;
  logout: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const authStore = useAuthStore();

  // Initialize auth state on client side to prevent hydration issues
  useEffect(() => {
    // Check if we have a stored token and validate it
    if (authStore.isAuthenticated && authStore.user) {
      authStore.getCurrentUser().catch(() => {
        // If validation fails, clear the auth state
        authStore.logout();
      });
    } else {
      // Set loading to false if no stored auth
      authStore.setLoading(false);
    }
  }, []);

  const value: AuthContextType = {
    user: authStore.user,
    isAuthenticated: authStore.isAuthenticated,
    isLoading: authStore.isLoading,
    login: authStore.login,
    register: authStore.register,
    logout: authStore.logout,
    getCurrentUser: authStore.getCurrentUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
