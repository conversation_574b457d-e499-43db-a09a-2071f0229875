'use client';

import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import SupabaseAuth, { AuthUserResponse } from '../services/auth-supabase';

interface AuthContextType {
  user: AuthUserResponse | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<AuthUserResponse | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        console.log('🔄 Initialisation de l\'authentification...');
        const currentUser = await SupabaseAuth.getCurrentUser();
        setUser(currentUser);
        console.log('✅ Utilisateur initial:', currentUser ? currentUser.user.email : 'Aucun');
      } catch (error) {
        console.error('❌ Erreur lors de l\'initialisation:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    initAuth();

    // Écouter les changements d'état d'authentification
    const { data: { subscription } } = SupabaseAuth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Changement d\'état auth:', event);
        
        if (event === 'SIGNED_IN' && session) {
          const currentUser = await SupabaseAuth.getCurrentUser();
          setUser(currentUser);
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
        }
        
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      console.log('🔐 Tentative de connexion depuis le contexte...');
      const response = await SupabaseAuth.login({ email, password });
      setUser(response);
      console.log('✅ Connexion réussie dans le contexte');
    } catch (error) {
      console.error('❌ Erreur de connexion dans le contexte:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (name: string, email: string, password: string) => {
    try {
      setLoading(true);
      console.log('📝 Tentative d\'inscription depuis le contexte...');
      const response = await SupabaseAuth.register({ name, email, password });
      setUser(response);
      console.log('✅ Inscription réussie dans le contexte');
    } catch (error) {
      console.error('❌ Erreur d\'inscription dans le contexte:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      console.log('🚪 Déconnexion depuis le contexte...');
      await SupabaseAuth.logout();
      setUser(null);
      console.log('✅ Déconnexion réussie dans le contexte');
    } catch (error) {
      console.error('❌ Erreur de déconnexion dans le contexte:', error);
      // Forcer la déconnexion locale même si Supabase échoue
      setUser(null);
    }
  };

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    isAuthenticated: !!user
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth doit être utilisé avec un AuthProvider');
  }
  return context;
};

export default AuthContext; 