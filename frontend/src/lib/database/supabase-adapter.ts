/**
 * Adaptateur Supabase pour l'interface DatabaseAdapter
 * Implémente toutes les opérations de base de données via Supabase
 */

import { createBrowserClient } from '@supabase/ssr'
import type { DatabaseAdapter, UserData, MoodEntryData, JournalEntryData } from './index'
import { migrationLogger } from '../config/feature-flags'

// Configuration Supabase locale
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client Supabase simple
const createSupabaseClient = () => createBrowserClient(supabaseUrl, supabaseAnonKey)

const isSupabaseConfigured = (): boolean => {
  return !!(supabaseUrl && supabaseAnonKey)
}

export class SupabaseAdapter implements DatabaseAdapter {
  private client: ReturnType<typeof createSupabaseClient>
  
  constructor() {
    if (!isSupabaseConfigured()) {
      throw new Error('Supabase is not properly configured. Check your environment variables.')
    }
    
    this.client = createSupabaseClient()
    migrationLogger.info('SupabaseAdapter initialized')
  }

  // User operations
  async createUser(userData: UserData): Promise<UserData> {
    migrationLogger.debug('Creating user in Supabase', { email: userData.email })
    
    const { data, error } = await this.client
      .from('users')
      .insert({
        email: userData.email,
        full_name: userData.full_name,
        avatar_url: userData.avatar_url,
        subscription_tier: userData.subscription_tier || 'free',
        onboarding_completed: userData.onboarding_completed || false,
        preferences: userData.preferences || {}
      })
      .select()
      .single()
    
    if (error) {
      migrationLogger.error('Failed to create user in Supabase', error)
      throw new Error(`Failed to create user: ${error.message}`)
    }
    
    return data
  }

  async getUserById(id: string): Promise<UserData | null> {
    migrationLogger.debug('Getting user by ID from Supabase', { id })
    
    const { data, error } = await this.client
      .from('users')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        // User not found
        return null
      }
      migrationLogger.error('Failed to get user by ID from Supabase', error)
      throw new Error(`Failed to get user: ${error.message}`)
    }
    
    return data
  }

  async getUserByEmail(email: string): Promise<UserData | null> {
    migrationLogger.debug('Getting user by email from Supabase', { email })
    
    const { data, error } = await this.client
      .from('users')
      .select('*')
      .eq('email', email)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        // User not found
        return null
      }
      migrationLogger.error('Failed to get user by email from Supabase', error)
      throw new Error(`Failed to get user: ${error.message}`)
    }
    
    return data
  }

  async updateUser(id: string, updates: Partial<UserData>): Promise<UserData> {
    migrationLogger.debug('Updating user in Supabase', { id, updates })
    
    const { data, error } = await this.client
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      migrationLogger.error('Failed to update user in Supabase', error)
      throw new Error(`Failed to update user: ${error.message}`)
    }
    
    return data
  }

  async deleteUser(id: string): Promise<boolean> {
    migrationLogger.debug('Deleting user from Supabase', { id })
    
    const { error } = await this.client
      .from('users')
      .delete()
      .eq('id', id)
    
    if (error) {
      migrationLogger.error('Failed to delete user from Supabase', error)
      throw new Error(`Failed to delete user: ${error.message}`)
    }
    
    return true
  }

  // Mood operations
  async createMoodEntry(data: MoodEntryData): Promise<MoodEntryData> {
    migrationLogger.debug('Creating mood entry in Supabase', { userId: data.user_id })
    
    const { data: result, error } = await this.client
      .from('mood_entries')
      .insert(data)
      .select()
      .single()
    
    if (error) {
      migrationLogger.error('Failed to create mood entry in Supabase', error)
      throw new Error(`Failed to create mood entry: ${error.message}`)
    }
    
    return result
  }

  async getUserMoodEntries(userId: string, limit: number = 50): Promise<MoodEntryData[]> {
    migrationLogger.debug('Getting user mood entries from Supabase', { userId, limit })
    
    const { data, error } = await this.client
      .from('mood_entries')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)
    
    if (error) {
      migrationLogger.error('Failed to get mood entries from Supabase', error)
      throw new Error(`Failed to get mood entries: ${error.message}`)
    }
    
    return data || []
  }

  async getMoodEntry(id: string): Promise<MoodEntryData | null> {
    const { data, error } = await this.client
      .from('mood_entries')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null
      }
      throw new Error(`Failed to get mood entry: ${error.message}`)
    }
    
    return data
  }

  async updateMoodEntry(id: string, updates: Partial<MoodEntryData>): Promise<MoodEntryData> {
    const { data, error } = await this.client
      .from('mood_entries')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to update mood entry: ${error.message}`)
    }
    
    return data
  }

  async deleteMoodEntry(id: string): Promise<boolean> {
    const { error } = await this.client
      .from('mood_entries')
      .delete()
      .eq('id', id)
    
    if (error) {
      throw new Error(`Failed to delete mood entry: ${error.message}`)
    }
    
    return true
  }

  // Journal operations
  async createJournalEntry(data: JournalEntryData): Promise<JournalEntryData> {
    migrationLogger.debug('Creating journal entry in Supabase', { userId: data.user_id })
    
    const { data: result, error } = await this.client
      .from('journal_entries')
      .insert(data)
      .select()
      .single()
    
    if (error) {
      migrationLogger.error('Failed to create journal entry in Supabase', error)
      throw new Error(`Failed to create journal entry: ${error.message}`)
    }
    
    return result
  }

  async getUserJournalEntries(userId: string, limit: number = 50): Promise<JournalEntryData[]> {
    migrationLogger.debug('Getting user journal entries from Supabase', { userId, limit })
    
    const { data, error } = await this.client
      .from('journal_entries')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)
    
    if (error) {
      migrationLogger.error('Failed to get journal entries from Supabase', error)
      throw new Error(`Failed to get journal entries: ${error.message}`)
    }
    
    return data || []
  }

  async getJournalEntry(id: string): Promise<JournalEntryData | null> {
    const { data, error } = await this.client
      .from('journal_entries')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null
      }
      throw new Error(`Failed to get journal entry: ${error.message}`)
    }
    
    return data
  }

  async updateJournalEntry(id: string, updates: Partial<JournalEntryData>): Promise<JournalEntryData> {
    const { data, error } = await this.client
      .from('journal_entries')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to update journal entry: ${error.message}`)
    }
    
    return data
  }

  async deleteJournalEntry(id: string): Promise<boolean> {
    const { error } = await this.client
      .from('journal_entries')
      .delete()
      .eq('id', id)
    
    if (error) {
      throw new Error(`Failed to delete journal entry: ${error.message}`)
    }
    
    return true
  }

  // Health check
  async healthCheck(): Promise<{ status: string, timestamp: string }> {
    try {
      // Simple query to test connection
      const { error } = await this.client
        .from('users')
        .select('count')
        .limit(1)
      
      if (error) {
        migrationLogger.error('Supabase health check failed', error)
        return {
          status: 'unhealthy',
          timestamp: new Date().toISOString()
        }
      }
      
      migrationLogger.debug('Supabase health check passed')
      return {
        status: 'healthy',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      migrationLogger.error('Supabase health check error', error)
      return {
        status: 'error',
        timestamp: new Date().toISOString()
      }
    }
  }
} 