/**
 * SQLite Database Adapter (Compatibility Layer)
 * Implémente l'interface DatabaseAdapter pour SQLite/mocks
 * Utilisé pendant la période de migration pour maintenir la compatibilité
 */

import { DatabaseAdapter, UserData, MoodEntryData, JournalEntryData } from './index'
import { migrationLogger } from '../config/feature-flags'

// Mock data pour les tests et développement
const mockUsers: UserData[] = []
const mockMoodEntries: MoodEntryData[] = []
const mockJournalEntries: JournalEntryData[] = []

export class SQLiteAdapter implements DatabaseAdapter {
  
  constructor() {
    migrationLogger.info('SQLiteAdapter initialized (mock mode)')
    this.initializeMockData()
  }
  
  private initializeMockData() {
    // Données de test pour le développement
    if (mockUsers.length === 0) {
      mockUsers.push({
        id: 'mock-user-1',
        email: '<EMAIL>',
        full_name: 'Test User',
        subscription_tier: 'free',
        onboarding_completed: true,
        preferences: { theme: 'light', notifications: true },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    }
  }
  
  // Health check
  async healthCheck(): Promise<{ status: string, timestamp: string }> {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString()
    }
  }
  
  // Helper pour générer des IDs
  private generateId(): string {
    return `mock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
  
  // User operations
  async createUser(userData: UserData): Promise<UserData> {
    const user: UserData = {
      ...userData,
      id: userData.id || this.generateId(),
      created_at: userData.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    mockUsers.push(user)
    migrationLogger.debug('Mock user created', { id: user.id })
    return user
  }
  
  async getUserById(id: string): Promise<UserData | null> {
    const user = mockUsers.find(u => u.id === id)
    return user || null
  }
  
  async getUserByEmail(email: string): Promise<UserData | null> {
    const user = mockUsers.find(u => u.email === email)
    return user || null
  }
  
  async updateUser(id: string, updates: Partial<UserData>): Promise<UserData> {
    const userIndex = mockUsers.findIndex(u => u.id === id)
    if (userIndex === -1) {
      throw new Error(`User with id ${id} not found`)
    }
    
    mockUsers[userIndex] = {
      ...mockUsers[userIndex],
      ...updates,
      updated_at: new Date().toISOString()
    }
    
    migrationLogger.debug('Mock user updated', { id })
    return mockUsers[userIndex]
  }
  
  async deleteUser(id: string): Promise<boolean> {
    const userIndex = mockUsers.findIndex(u => u.id === id)
    if (userIndex === -1) {
      return false
    }
    
    mockUsers.splice(userIndex, 1)
    migrationLogger.debug('Mock user deleted', { id })
    return true
  }
  
  // Mood operations
  async createMoodEntry(data: MoodEntryData): Promise<MoodEntryData> {
    const entry: MoodEntryData = {
      ...data,
      id: data.id || this.generateId(),
      created_at: data.created_at || new Date().toISOString()
    }
    
    mockMoodEntries.push(entry)
    migrationLogger.debug('Mock mood entry created', { id: entry.id })
    return entry
  }
  
  async getUserMoodEntries(userId: string, limit = 50): Promise<MoodEntryData[]> {
    return mockMoodEntries
      .filter(entry => entry.user_id === userId)
      .sort((a, b) => new Date(b.created_at!).getTime() - new Date(a.created_at!).getTime())
      .slice(0, limit)
  }
  
  async getMoodEntry(id: string): Promise<MoodEntryData | null> {
    const entry = mockMoodEntries.find(e => e.id === id)
    return entry || null
  }
  
  async updateMoodEntry(id: string, updates: Partial<MoodEntryData>): Promise<MoodEntryData> {
    const entryIndex = mockMoodEntries.findIndex(e => e.id === id)
    if (entryIndex === -1) {
      throw new Error(`Mood entry with id ${id} not found`)
    }
    
    mockMoodEntries[entryIndex] = {
      ...mockMoodEntries[entryIndex],
      ...updates
    }
    
    migrationLogger.debug('Mock mood entry updated', { id })
    return mockMoodEntries[entryIndex]
  }
  
  async deleteMoodEntry(id: string): Promise<boolean> {
    const entryIndex = mockMoodEntries.findIndex(e => e.id === id)
    if (entryIndex === -1) {
      return false
    }
    
    mockMoodEntries.splice(entryIndex, 1)
    migrationLogger.debug('Mock mood entry deleted', { id })
    return true
  }
  
  // Journal operations
  async createJournalEntry(data: JournalEntryData): Promise<JournalEntryData> {
    const entry: JournalEntryData = {
      ...data,
      id: data.id || this.generateId(),
      created_at: data.created_at || new Date().toISOString(),
      updated_at: data.updated_at || new Date().toISOString()
    }
    
    mockJournalEntries.push(entry)
    migrationLogger.debug('Mock journal entry created', { id: entry.id })
    return entry
  }
  
  async getUserJournalEntries(userId: string, limit = 50): Promise<JournalEntryData[]> {
    return mockJournalEntries
      .filter(entry => entry.user_id === userId)
      .sort((a, b) => new Date(b.created_at!).getTime() - new Date(a.created_at!).getTime())
      .slice(0, limit)
  }
  
  async getJournalEntry(id: string): Promise<JournalEntryData | null> {
    const entry = mockJournalEntries.find(e => e.id === id)
    return entry || null
  }
  
  async updateJournalEntry(id: string, updates: Partial<JournalEntryData>): Promise<JournalEntryData> {
    const entryIndex = mockJournalEntries.findIndex(e => e.id === id)
    if (entryIndex === -1) {
      throw new Error(`Journal entry with id ${id} not found`)
    }
    
    mockJournalEntries[entryIndex] = {
      ...mockJournalEntries[entryIndex],
      ...updates,
      updated_at: new Date().toISOString()
    }
    
    migrationLogger.debug('Mock journal entry updated', { id })
    return mockJournalEntries[entryIndex]
  }
  
  async deleteJournalEntry(id: string): Promise<boolean> {
    const entryIndex = mockJournalEntries.findIndex(e => e.id === id)
    if (entryIndex === -1) {
      return false
    }
    
    mockJournalEntries.splice(entryIndex, 1)
    migrationLogger.debug('Mock journal entry deleted', { id })
    return true
  }
  
  // Méthodes supplémentaires pour la compatibilité SQLite
  
  /**
   * Simule la récupération de toutes les données utilisateur pour migration
   */
  async getAllUsers(): Promise<UserData[]> {
    return [...mockUsers]
  }
  
  /**
   * Simule la récupération de toutes les entrées mood pour migration
   */
  async getAllMoodEntries(): Promise<MoodEntryData[]> {
    return [...mockMoodEntries]
  }
  
  /**
   * Simule la récupération de toutes les entrées journal pour migration
   */
  async getAllJournalEntries(): Promise<JournalEntryData[]> {
    return [...mockJournalEntries]
  }
} 