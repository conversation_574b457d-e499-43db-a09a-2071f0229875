/**
 * Database Abstraction Layer
 * Permet de basculer entre SQLite et Supabase de manière transparente
 */

import { FEATURE_FLAGS, migrationLogger } from '../config/feature-flags'

// Types génériques pour les opérations database
export interface UserData {
  id?: string
  email: string
  full_name?: string
  avatar_url?: string
  subscription_tier?: 'free' | 'premium' | 'enterprise'
  onboarding_completed?: boolean
  preferences?: Record<string, any>
  created_at?: string
  updated_at?: string
}

export interface MoodEntryData {
  id?: string
  user_id: string
  mood_score: number
  stress_level: number
  energy_level: number
  notes?: string
  tags?: string[]
  location?: string
  weather?: string
  created_at?: string
}

export interface JournalEntryData {
  id?: string
  user_id: string
  title?: string
  content: string
  mood_tags?: string[]
  ai_insights?: Record<string, any>
  ai_summary?: string
  is_private?: boolean
  word_count?: number
  reading_time?: number
  created_at?: string
  updated_at?: string
}

/**
 * Interface générique pour les adaptateurs de base de données
 */
export interface DatabaseAdapter {
  // User operations
  createUser(userData: UserData): Promise<UserData>
  getUserById(id: string): Promise<UserData | null>
  getUserByEmail(email: string): Promise<UserData | null>
  updateUser(id: string, updates: Partial<UserData>): Promise<UserData>
  deleteUser(id: string): Promise<boolean>
  
  // Mood operations
  createMoodEntry(data: MoodEntryData): Promise<MoodEntryData>
  getUserMoodEntries(userId: string, limit?: number): Promise<MoodEntryData[]>
  getMoodEntry(id: string): Promise<MoodEntryData | null>
  updateMoodEntry(id: string, updates: Partial<MoodEntryData>): Promise<MoodEntryData>
  deleteMoodEntry(id: string): Promise<boolean>
  
  // Journal operations
  createJournalEntry(data: JournalEntryData): Promise<JournalEntryData>
  getUserJournalEntries(userId: string, limit?: number): Promise<JournalEntryData[]>
  getJournalEntry(id: string): Promise<JournalEntryData | null>
  updateJournalEntry(id: string, updates: Partial<JournalEntryData>): Promise<JournalEntryData>
  deleteJournalEntry(id: string): Promise<boolean>
  
  // Health check
  healthCheck(): Promise<{ status: string, timestamp: string }>
}

/**
 * Database Manager - Point d'entrée principal
 * Gère la sélection de l'adaptateur selon les feature flags
 */
export class DatabaseManager {
  private supabaseAdapter?: DatabaseAdapter
  private sqliteAdapter?: DatabaseAdapter
  private initialized = false
  private initializing = false
  
  constructor() {
    // Ne pas initialiser automatiquement dans le constructeur
    // L'initialisation se fait de manière lazy
  }
  
  private async initializeAdapters() {
    if (this.initialized || this.initializing) {
      return // Éviter la double initialisation
    }
    
    this.initializing = true
    migrationLogger.info('Initializing database adapters')
    
    try {
      // Toujours initialiser SQLite en premier (fallback)
      try {
        const { SQLiteAdapter } = await import('./sqlite-adapter')
        this.sqliteAdapter = new SQLiteAdapter()
        migrationLogger.info('SQLite adapter initialized')
      } catch (sqliteError) {
        migrationLogger.warn('SQLite adapter failed to initialize', sqliteError)
        // Continuer sans SQLite si ça échoue
      }
      
      // Initialiser Supabase si configuré
      if (FEATURE_FLAGS.USE_SUPABASE_DATABASE) {
        try {
          const { SupabaseAdapter } = await import('./supabase-adapter')
          this.supabaseAdapter = new SupabaseAdapter()
          migrationLogger.info('Supabase adapter initialized')
        } catch (supabaseError) {
          migrationLogger.warn('Supabase adapter failed to initialize', supabaseError)
          // Continuer sans Supabase si ça échoue
        }
      }
      
      this.initialized = true
      this.initializing = false
      
      // Vérifier qu'au moins un adaptateur est disponible
      if (!this.supabaseAdapter && !this.sqliteAdapter) {
        throw new Error('Aucun adaptateur de base de données disponible')
      }
      
      migrationLogger.info('Database adapters initialization completed')
    } catch (error) {
      this.initializing = false
      migrationLogger.error('Failed to initialize database adapters', error)
      throw error
    }
  }
  
  /**
   * Obtient l'adaptateur principal selon la configuration
   */
  public async getAdapter(): Promise<DatabaseAdapter> {
    if (!this.initialized) {
      await this.initializeAdapters()
    }
    
    // Priorité à Supabase si configuré et disponible
    if (FEATURE_FLAGS.USE_SUPABASE_DATABASE && this.supabaseAdapter) {
      migrationLogger.debug('Using Supabase adapter')
      return this.supabaseAdapter
    }
    
    // Fallback sur SQLite
    if (this.sqliteAdapter) {
      migrationLogger.debug('Using SQLite adapter')
      return this.sqliteAdapter
    }
    
    throw new Error('No database adapter available')
  }
  
  /**
   * Obtient l'adaptateur Supabase spécifiquement
   */
  public async getSupabaseAdapter(): Promise<DatabaseAdapter | null> {
    if (!this.initialized) {
      await this.initializeAdapters()
    }
    return this.supabaseAdapter || null
  }
  
  /**
   * Obtient l'adaptateur SQLite spécifiquement
   */
  public async getSQLiteAdapter(): Promise<DatabaseAdapter | null> {
    if (!this.initialized) {
      await this.initializeAdapters()
    }
    return this.sqliteAdapter || null
  }
  
  /**
   * Vérifie si les deux adaptateurs sont disponibles (mode dual)
   */
  public async isDualModeAvailable(): Promise<boolean> {
    if (!this.initialized) {
      await this.initializeAdapters()
    }
    return !!(this.supabaseAdapter && this.sqliteAdapter)
  }
  
  /**
   * Test de connectivité pour diagnostics
   */
  public async testConnection(): Promise<{
    supabase: { available: boolean, error?: string }
    sqlite: { available: boolean, error?: string }
  }> {
    if (!this.initialized) {
      await this.initializeAdapters()
    }
    
    const result = {
      supabase: { available: false, error: undefined as string | undefined },
      sqlite: { available: false, error: undefined as string | undefined }
    }
    
    // Test Supabase
    if (this.supabaseAdapter) {
      try {
        await this.supabaseAdapter.healthCheck()
        result.supabase.available = true
      } catch (error) {
        result.supabase.error = error instanceof Error ? error.message : 'Erreur inconnue'
      }
    } else {
      result.supabase.error = 'Adaptateur non initialisé'
    }
    
    // Test SQLite
    if (this.sqliteAdapter) {
      try {
        await this.sqliteAdapter.healthCheck()
        result.sqlite.available = true
      } catch (error) {
        result.sqlite.error = error instanceof Error ? error.message : 'Erreur inconnue'
      }
    } else {
      result.sqlite.error = 'Adaptateur non initialisé'
    }
    
    return result
  }
}

// Instance singleton
let databaseManager: DatabaseManager | null = null

/**
 * Factory function pour obtenir le manager de database
 */
export const getDatabaseManager = (): DatabaseManager => {
  if (!databaseManager) {
    databaseManager = new DatabaseManager()
  }
  return databaseManager
}

/**
 * Helper function pour obtenir l'adaptateur principal rapidement
 */
export const getDatabase = async (): Promise<DatabaseAdapter> => {
  const manager = getDatabaseManager()
  return await manager.getAdapter()
} 