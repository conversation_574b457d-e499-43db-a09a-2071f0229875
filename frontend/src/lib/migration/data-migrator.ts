/**
 * Data Migrator Service
 * Gère la migration progressive des données de SQLite vers Supabase
 */

import { getDatabaseManager } from '../database'
import { migrationLogger, SUPABASE_CONFIG } from '../config/feature-flags'

export interface MigrationProgress {
  feature: string
  total: number
  completed: number
  failed: number
  errors: string[]
  startTime: Date
  endTime?: Date
  status: 'pending' | 'running' | 'completed' | 'failed'
}

export interface MigrationResult {
  success: boolean
  progress: MigrationProgress
  details?: any
}

export class DataMigrator {
  private progress: Map<string, MigrationProgress> = new Map()
  
  /**
   * Migre tous les utilisateurs de SQLite vers Supabase
   */
  async migrateUsers(): Promise<MigrationResult> {
    const feature = 'users'
    const progress = this.initializeProgress(feature)
    
    try {
      migrationLogger.info('Starting user migration')
      const manager = await getDatabaseManager()
      
      if (!manager.isDualModeAvailable()) {
        throw new Error('Both SQLite and Supabase adapters must be available for migration')
      }
      
      const sqliteAdapter = manager.getSQLiteAdapter()!
      const supabaseAdapter = manager.getSupabaseAdapter()!
      
      // Récupérer tous les utilisateurs de SQLite
      const sqliteUsers = await (sqliteAdapter as any).getAllUsers()
      progress.total = sqliteUsers.length
      
      migrationLogger.info(`Found ${sqliteUsers.length} users to migrate`)
      
      for (const user of sqliteUsers) {
        try {
          // Vérifier si l'utilisateur existe déjà dans Supabase
          const existingUser = await supabaseAdapter.getUserById(user.id!)
          
          if (existingUser) {
            migrationLogger.debug(`User ${user.email} already exists in Supabase, skipping`)
            progress.completed++
            continue
          }
          
          // Créer l'utilisateur dans Supabase
          await supabaseAdapter.createUser(user)
          progress.completed++
          
          migrationLogger.debug(`Migrated user: ${user.email}`)
          
          // Pause pour éviter de surcharger l'API
          await this.delay(100)
          
        } catch (error) {
          progress.failed++
          const errorMsg = `Failed to migrate user ${user.email}: ${error}`
          progress.errors.push(errorMsg)
          migrationLogger.error(errorMsg, error)
        }
      }
      
      progress.status = 'completed'
      progress.endTime = new Date()
      
      migrationLogger.info(`User migration completed: ${progress.completed}/${progress.total} successful`)
      
      return {
        success: progress.failed === 0,
        progress
      }
      
    } catch (error) {
      progress.status = 'failed'
      progress.endTime = new Date()
      progress.errors.push(error instanceof Error ? error.message : 'Unknown error')
      
      migrationLogger.error('User migration failed', error)
      
      return {
        success: false,
        progress
      }
    }
  }
  
  /**
   * Migre toutes les entrées mood
   */
  async migrateMoodEntries(): Promise<MigrationResult> {
    const feature = 'mood_entries'
    const progress = this.initializeProgress(feature)
    
    try {
      migrationLogger.info('Starting mood entries migration')
      const manager = await getDatabaseManager()
      
      if (!manager.isDualModeAvailable()) {
        throw new Error('Both SQLite and Supabase adapters must be available for migration')
      }
      
      const sqliteAdapter = manager.getSQLiteAdapter()!
      const supabaseAdapter = manager.getSupabaseAdapter()!
      
      const sqliteMoodEntries = await (sqliteAdapter as any).getAllMoodEntries()
      progress.total = sqliteMoodEntries.length
      
      migrationLogger.info(`Found ${sqliteMoodEntries.length} mood entries to migrate`)
      
      for (const entry of sqliteMoodEntries) {
        try {
          // Vérifier si l'entrée existe déjà
          const existingEntry = await supabaseAdapter.getMoodEntry(entry.id!)
          
          if (existingEntry) {
            migrationLogger.debug(`Mood entry ${entry.id} already exists, skipping`)
            progress.completed++
            continue
          }
          
          await supabaseAdapter.createMoodEntry(entry)
          progress.completed++
          
          migrationLogger.debug(`Migrated mood entry: ${entry.id}`)
          await this.delay(50)
          
        } catch (error) {
          progress.failed++
          const errorMsg = `Failed to migrate mood entry ${entry.id}: ${error}`
          progress.errors.push(errorMsg)
          migrationLogger.error(errorMsg, error)
        }
      }
      
      progress.status = 'completed'
      progress.endTime = new Date()
      
      migrationLogger.info(`Mood entries migration completed: ${progress.completed}/${progress.total} successful`)
      
      return {
        success: progress.failed === 0,
        progress
      }
      
    } catch (error) {
      progress.status = 'failed'
      progress.endTime = new Date()
      progress.errors.push(error instanceof Error ? error.message : 'Unknown error')
      
      return {
        success: false,
        progress
      }
    }
  }
  
  /**
   * Migre toutes les entrées journal
   */
  async migrateJournalEntries(): Promise<MigrationResult> {
    const feature = 'journal_entries'
    const progress = this.initializeProgress(feature)
    
    try {
      migrationLogger.info('Starting journal entries migration')
      const manager = await getDatabaseManager()
      
      if (!manager.isDualModeAvailable()) {
        throw new Error('Both SQLite and Supabase adapters must be available for migration')
      }
      
      const sqliteAdapter = manager.getSQLiteAdapter()!
      const supabaseAdapter = manager.getSupabaseAdapter()!
      
      const sqliteJournalEntries = await (sqliteAdapter as any).getAllJournalEntries()
      progress.total = sqliteJournalEntries.length
      
      migrationLogger.info(`Found ${sqliteJournalEntries.length} journal entries to migrate`)
      
      for (const entry of sqliteJournalEntries) {
        try {
          const existingEntry = await supabaseAdapter.getJournalEntry(entry.id!)
          
          if (existingEntry) {
            migrationLogger.debug(`Journal entry ${entry.id} already exists, skipping`)
            progress.completed++
            continue
          }
          
          await supabaseAdapter.createJournalEntry(entry)
          progress.completed++
          
          migrationLogger.debug(`Migrated journal entry: ${entry.id}`)
          await this.delay(100)
          
        } catch (error) {
          progress.failed++
          const errorMsg = `Failed to migrate journal entry ${entry.id}: ${error}`
          progress.errors.push(errorMsg)
          migrationLogger.error(errorMsg, error)
        }
      }
      
      progress.status = 'completed'
      progress.endTime = new Date()
      
      migrationLogger.info(`Journal entries migration completed: ${progress.completed}/${progress.total} successful`)
      
      return {
        success: progress.failed === 0,
        progress
      }
      
    } catch (error) {
      progress.status = 'failed'
      progress.endTime = new Date()
      progress.errors.push(error instanceof Error ? error.message : 'Unknown error')
      
      return {
        success: false,
        progress
      }
    }
  }
  
  /**
   * Migre toutes les données dans l'ordre approprié
   */
  async migrateAll(): Promise<{ [key: string]: MigrationResult }> {
    migrationLogger.info('Starting complete data migration')
    
    const results: { [key: string]: MigrationResult } = {}
    
    // Ordre de migration : users d'abord, puis les données liées
    const migrations = [
      { name: 'users', fn: () => this.migrateUsers() },
      { name: 'mood_entries', fn: () => this.migrateMoodEntries() },
      { name: 'journal_entries', fn: () => this.migrateJournalEntries() }
    ]
    
    for (const migration of migrations) {
      try {
        results[migration.name] = await migration.fn()
        
        // Si une migration critique échoue, on arrête
        if (!results[migration.name].success && migration.name === 'users') {
          migrationLogger.error('Critical migration failed, stopping')
          break
        }
        
        // Pause entre les migrations
        await this.delay(1000)
        
      } catch (error) {
        migrationLogger.error(`Migration ${migration.name} failed with error`, error)
        results[migration.name] = {
          success: false,
          progress: {
            feature: migration.name,
            total: 0,
            completed: 0,
            failed: 1,
            errors: [error instanceof Error ? error.message : 'Unknown error'],
            startTime: new Date(),
            endTime: new Date(),
            status: 'failed'
          }
        }
      }
    }
    
    return results
  }
  
  /**
   * Obtient le statut de migration pour une fonctionnalité
   */
  getMigrationProgress(feature: string): MigrationProgress | null {
    return this.progress.get(feature) || null
  }
  
  /**
   * Obtient le statut de toutes les migrations
   */
  getAllMigrationProgress(): Map<string, MigrationProgress> {
    return new Map(this.progress)
  }
  
  /**
   * Initialise le suivi de progression
   */
  private initializeProgress(feature: string): MigrationProgress {
    const progress: MigrationProgress = {
      feature,
      total: 0,
      completed: 0,
      failed: 0,
      errors: [],
      startTime: new Date(),
      status: 'running'
    }
    
    this.progress.set(feature, progress)
    return progress
  }
  
  /**
   * Utilitaire pour les pauses
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  /**
   * Valide que les données migrées sont identiques
   */
  async validateMigration(feature: string): Promise<{ valid: boolean, details: any }> {
    try {
      const manager = await getDatabaseManager()
      
      if (!manager.isDualModeAvailable()) {
        return { valid: false, details: 'Dual mode not available for validation' }
      }
      
      const sqliteAdapter = manager.getSQLiteAdapter()!
      const supabaseAdapter = manager.getSupabaseAdapter()!
      
      switch (feature) {
        case 'users':
          const sqliteUsers = await (sqliteAdapter as any).getAllUsers()
          const validations = []
          
          for (const user of sqliteUsers) {
            const supabaseUser = await supabaseAdapter.getUserById(user.id!)
            validations.push({
              id: user.id,
              exists: !!supabaseUser,
              emailMatch: supabaseUser?.email === user.email
            })
          }
          
          const validCount = validations.filter(v => v.exists && v.emailMatch).length
          
          return {
            valid: validCount === sqliteUsers.length,
            details: {
              total: sqliteUsers.length,
              validated: validCount,
              validations
            }
          }
          
        default:
          return { valid: false, details: 'Unknown feature for validation' }
      }
      
    } catch (error) {
      migrationLogger.error('Validation failed', error)
      return { valid: false, details: error }
    }
  }
} 