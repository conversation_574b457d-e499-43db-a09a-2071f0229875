import { createBrowserClient } from '@supabase/ssr'

// Configuration type-safe pour le client browser
interface SupabaseConfig {
  url: string
  anonKey: string
}

const config: SupabaseConfig = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co',
  anon<PERSON>ey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key',
}

/**
 * Client-side Supabase client
 * Utilise createBrowserClient pour les composants côté client uniquement
 */
export const createSupabaseClient = () => {
  return createBrowserClient(config.url, config.anonKey)
}

/**
 * Hooks pour vérifier la configuration
 */
export const isSupabaseConfigured = (): boolean => {
  return !!(config.url && config.anonKey && 
           config.url !== 'https://your-project.supabase.co' && 
           config.anonKey !== 'your-anon-key')
}

/**
 * Types database générés par Supabase CLI
 */
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name?: string
          avatar_url?: string
          subscription_tier: 'free' | 'premium' | 'enterprise'
          onboarding_completed: boolean
          preferences: Record<string, any>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name?: string
          avatar_url?: string
          subscription_tier?: 'free' | 'premium' | 'enterprise'
          onboarding_completed?: boolean
          preferences?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          avatar_url?: string
          subscription_tier?: 'free' | 'premium' | 'enterprise'
          onboarding_completed?: boolean
          preferences?: Record<string, any>
          updated_at?: string
        }
      }
      mood_entries: {
        Row: {
          id: string
          user_id: string
          mood_score: number
          stress_level: number
          energy_level: number
          notes?: string
          tags?: string[]
          location?: string
          weather?: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          mood_score: number
          stress_level: number
          energy_level: number
          notes?: string
          tags?: string[]
          location?: string
          weather?: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          mood_score?: number
          stress_level?: number
          energy_level?: number
          notes?: string
          tags?: string[]
          location?: string
          weather?: string
        }
      }
      journal_entries: {
        Row: {
          id: string
          user_id: string
          title?: string
          content: string
          mood_tags?: string[]
          ai_insights?: Record<string, any>
          ai_summary?: string
          is_private: boolean
          word_count?: number
          reading_time?: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title?: string
          content: string
          mood_tags?: string[]
          ai_insights?: Record<string, any>
          ai_summary?: string
          is_private?: boolean
          word_count?: number
          reading_time?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          content?: string
          mood_tags?: string[]
          ai_insights?: Record<string, any>
          ai_summary?: string
          is_private?: boolean
          word_count?: number
          reading_time?: number
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

export type SupabaseClient = ReturnType<typeof createSupabaseClient> 