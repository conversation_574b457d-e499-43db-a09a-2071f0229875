import { createBrowserClient } from '@supabase/ssr';

// Configuration Supabase avec valeurs par défaut
const SUPABASE_CONFIG = {
  url: 'https://kvdrukmoxetoiojazukf.supabase.co',
  // Nouvelle clé anon (mise à jour décembre 2024)
  anon_key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ'
};

// Client Supabase simple pour éviter les erreurs next/headers
export const createSupabaseClient = () => {
  try {
    // Priorité aux variables d'environnement, fallback sur config par défaut
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || SUPABASE_CONFIG.url;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || SUPABASE_CONFIG.anon_key;
    
    if (!supabaseUrl || !supabaseKey) {
      console.warn('Configuration Supabase manquante, utilisation des valeurs par défaut');
    }
    
    const client = createBrowserClient(supabaseUrl, supabaseKey);
    
    console.log('✅ Client Supabase créé avec succès');
    console.log(`   URL: ${supabaseUrl}`);
    console.log(`   Key: ${supabaseKey.substring(0, 20)}...`);
    
    return client;
  } catch (error) {
    console.error('❌ Erreur création client Supabase:', error);
    throw new Error(`Impossible de créer le client Supabase: ${error}`);
  }
};

export const isSupabaseConfigured = (): boolean => {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL || SUPABASE_CONFIG.url;
  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || SUPABASE_CONFIG.anon_key;
  return !!(url && key);
};

// Test de connectivité simple
export const testSupabaseConnection = async () => {
  try {
    const client = createSupabaseClient();
    
    // Test simple avec l'endpoint auth
    const { data, error } = await client.auth.getSession();
    
    if (error) {
      console.error('❌ Erreur test Supabase:', error);
      return { 
        success: false, 
        error: error.message,
        config: {
          url: process.env.NEXT_PUBLIC_SUPABASE_URL || SUPABASE_CONFIG.url,
          hasKey: !!(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || SUPABASE_CONFIG.anon_key)
        }
      };
    }
    
    console.log('✅ Test Supabase réussi');
    return { 
      success: true, 
      session: data.session,
      config: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL || SUPABASE_CONFIG.url,
        hasKey: true
      }
    };
  } catch (error) {
    console.error('❌ Erreur test Supabase:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Erreur inconnue',
      config: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL || SUPABASE_CONFIG.url,
        hasKey: !!(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || SUPABASE_CONFIG.anon_key)
      }
    };
  }
};

export type SupabaseClient = ReturnType<typeof createSupabaseClient>;

// Instance par défaut pour les hooks
export const supabase = createSupabaseClient();

// Export par défaut
export default supabase;