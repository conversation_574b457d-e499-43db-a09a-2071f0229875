import { createBrowserClient } from '@supabase/ssr';

// Configuration simple pour éviter les conflits
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Client simple pour composants côté client
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey);

// Export des fonctions utilitaires
export const createSupabaseClient = () => supabase;

export const isSupabaseConfigured = (): boolean => {
  return !!(supabaseUrl && supabaseAnonKey);
};

// Export du type pour TypeScript
export type SupabaseClient = typeof supabase; 