/**
 * Feature Flags pour migration progressive vers Supabase
 * Permet d'activer/désactiver les fonctionnalités sans redéployer
 */

export const FEATURE_FLAGS = {
  // Migration Supabase
  USE_SUPABASE_AUTH: process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH === 'true',
  USE_SUPABASE_DATABASE: process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE === 'true',
  ENABLE_REAL_TIME: process.env.NEXT_PUBLIC_ENABLE_REAL_TIME === 'true',
  
  // Migration progressive par fonctionnalité
  MIGRATE_USER_DATA: process.env.NEXT_PUBLIC_MIGRATE_USER_DATA === 'true',
  MIGRATE_MOOD_TRACKING: process.env.NEXT_PUBLIC_MIGRATE_MOOD_TRACKING === 'true',
  MIGRATE_JOURNAL_ENTRIES: process.env.NEXT_PUBLIC_MIGRATE_JOURNAL_ENTRIES === 'true',
  MIGRATE_AI_CHAT: process.env.NEXT_PUBLIC_MIGRATE_AI_CHAT === 'true',
  MIGRATE_PROGRAMS: process.env.NEXT_PUBLIC_MIGRATE_PROGRAMS === 'true',
  MIGRATE_THERAPIST_BOOKING: process.env.NEXT_PUBLIC_MIGRATE_THERAPIST_BOOKING === 'true',
  
  // Modes de test
  DUAL_DATABASE_MODE: process.env.NEXT_PUBLIC_DUAL_DATABASE_MODE === 'true',
  DEBUG_MIGRATION: process.env.NEXT_PUBLIC_DEBUG_MIGRATION === 'true',
  ENABLE_MIGRATION_LOGS: process.env.NEXT_PUBLIC_ENABLE_MIGRATION_LOGS === 'true',
} as const

/**
 * Helpers pour vérifier les feature flags
 */
export const canUseSupabase = () => {
  return FEATURE_FLAGS.USE_SUPABASE_DATABASE || FEATURE_FLAGS.USE_SUPABASE_AUTH
}

export const shouldMigrateFeature = (feature: keyof typeof FEATURE_FLAGS) => {
  return FEATURE_FLAGS[feature] && canUseSupabase()
}

export const isInDualMode = () => {
  return FEATURE_FLAGS.DUAL_DATABASE_MODE
}

/**
 * Logger pour la migration
 */
export const migrationLogger = {
  info: (message: string, data?: any) => {
    if (FEATURE_FLAGS.ENABLE_MIGRATION_LOGS) {
      console.info(`[MIGRATION] ${message}`, data)
    }
  },
  error: (message: string, error?: any) => {
    console.error(`[MIGRATION ERROR] ${message}`, error)
  },
  debug: (message: string, data?: any) => {
    if (FEATURE_FLAGS.DEBUG_MIGRATION) {
      console.debug(`[MIGRATION DEBUG] ${message}`, data)
    }
  }
}

/**
 * Configuration des timeouts et retry pour Supabase
 */
export const SUPABASE_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  TIMEOUT: 10000,
  BATCH_SIZE: 50, // Pour les migrations de données
} as const 