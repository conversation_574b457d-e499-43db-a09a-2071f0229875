'use client';

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase/client';

// Hook Supabase pour Smart Notifications (Migration de useSmartNotifications.ts)
export const useSmartNotificationsSupabase = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Charger les notifications depuis Supabase
  const loadNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Chargement notifications depuis Supabase...');

      const { data, error } = await supabase
        .from('smart_notifications')
        .select('*')
        .order('scheduled_for', { ascending: false });

      if (error) throw error;

      console.log(`✅ ${data?.length || 0} notifications chargées`);
      setNotifications(data || []);

    } catch (err) {
      console.error('❌ Erreur notifications:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // Marquer comme lue
  const markAsRead = useCallback(async (notificationId) => {
    try {
      const { error } = await supabase
        .from('smart_notifications')
        .update({ 
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('id', notificationId);

      if (error) throw error;

      console.log('✅ Notification marquée comme lue');
      
      // Mise à jour locale
      setNotifications(prev => prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, is_read: true, read_at: new Date().toISOString() }
          : notif
      ));

      return true;
    } catch (err) {
      console.error('❌ Erreur marquage lecture:', err);
      return false;
    }
  }, []);

  // Créer une notification
  const createNotification = useCallback(async (type, title, message, options = {}) => {
    try {
      console.log('📝 Création nouvelle notification...');

      const { data, error } = await supabase
        .from('smart_notifications')
        .insert({
          type,
          title,
          message,
          priority: options.priority || 'medium',
          action_text: options.actionText,
          action_url: options.actionUrl,
          ai_generated: options.aiGenerated || false,
          triggers: options.triggers || [],
          metadata: options.metadata || {}
        })
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Notification créée:', data.id);
      await loadNotifications();

      return data;
    } catch (err) {
      console.error('❌ Erreur création notification:', err);
      throw err;
    }
  }, [loadNotifications]);

  // Obtenir les notifications non lues
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(notif => !notif.is_read);
  }, [notifications]);

  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  return {
    notifications,
    loading,
    error,
    loadNotifications,
    markAsRead,
    createNotification,
    getUnreadNotifications,
    isSupabase: true
  };
};
