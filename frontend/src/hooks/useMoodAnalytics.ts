'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

// Types pour l'analyse d'humeur
export interface MoodDataPoint {
  id: string;
  date: string;
  mood: number;
  energy: number;
  stress: number;
  anxiety: number;
  sleep: number;
  factors: string[];
  notes?: string;
  weather?: string;
  activities?: string[];
}

export interface MoodTrend {
  period: 'daily' | 'weekly' | 'monthly';
  direction: 'improving' | 'declining' | 'stable';
  change: number;
  confidence: number;
  startValue: number;
  endValue: number;
  significance: 'low' | 'medium' | 'high';
}

export interface MoodPrediction {
  nextDay: number;
  nextWeek: number;
  confidence: number;
  factors: string[];
  recommendations: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

export interface MoodPattern {
  id: string;
  name: string;
  description: string;
  frequency: number;
  triggers: string[];
  timePattern: 'morning' | 'afternoon' | 'evening' | 'weekend' | 'weekday';
  averageDuration: number;
  severity: 'mild' | 'moderate' | 'severe';
  recommendations: string[];
}

export interface MoodCorrelation {
  factor: string;
  correlation: number;
  strength: 'weak' | 'moderate' | 'strong';
  direction: 'positive' | 'negative';
  examples: string[];
  recommendations: string[];
}

export interface MoodInsight {
  id: string;
  type: 'trend' | 'pattern' | 'correlation' | 'achievement' | 'warning';
  title: string;
  description: string;
  importance: 'low' | 'medium' | 'high' | 'critical';
  actionable: boolean;
  recommendations?: string[];
  data?: any;
  createdAt: Date;
}

export interface MoodAnalytics {
  overview: {
    currentMood: number;
    weeklyAverage: number;
    monthlyAverage: number;
    bestDay: { date: string; mood: number };
    worstDay: { date: string; mood: number };
    streakDays: number;
    totalEntries: number;
  };
  trends: MoodTrend[];
  predictions: MoodPrediction;
  patterns: MoodPattern[];
  correlations: MoodCorrelation[];
  insights: MoodInsight[];
  chartData: {
    daily: MoodDataPoint[];
    weekly: { week: string; average: number; count: number }[];
    monthly: { month: string; average: number; count: number }[];
    factors: { factor: string; impact: number; frequency: number }[];
  };
}

// Hook principal pour l'analyse d'humeur
export const useMoodAnalytics = (timeRange: 'week' | 'month' | 'quarter' | 'year' = 'month') => {
  const { user, isAuthenticated } = useAuth();
  const [analytics, setAnalytics] = useState<MoodAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Générer des données simulées réalistes
  const generateMockData = useCallback((): MoodAnalytics => {
    const today = new Date();
    const daysBack = timeRange === 'week' ? 7 : timeRange === 'month' ? 30 : timeRange === 'quarter' ? 90 : 365;
    
    // Générer des points de données d'humeur
    const dailyData: MoodDataPoint[] = [];
    const factors = ['travail', 'sommeil', 'exercice', 'social', 'météo', 'alimentation', 'stress'];
    const activities = ['méditation', 'sport', 'lecture', 'musique', 'promenade', 'repos', 'travail'];
    
    for (let i = daysBack; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Simulation de patterns réalistes
      const dayOfWeek = date.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      const baseMode = isWeekend ? 7.5 : 6.5; // Meilleur humeur le weekend
      
      // Ajouter des variations naturelles
      const randomVariation = (Math.random() - 0.5) * 2;
      const seasonalEffect = Math.sin((date.getMonth() / 12) * 2 * Math.PI) * 0.5; // Effet saisonnier
      
      const mood = Math.max(1, Math.min(10, baseMode + randomVariation + seasonalEffect));
      const energy = Math.max(1, Math.min(10, mood + (Math.random() - 0.5) * 1.5));
      const stress = Math.max(1, Math.min(10, 11 - mood + (Math.random() - 0.5) * 2));
      const anxiety = Math.max(1, Math.min(10, stress * 0.8 + (Math.random() - 0.5) * 1.5));
      const sleep = Math.max(4, Math.min(10, 8 + (Math.random() - 0.5) * 2));
      
      dailyData.push({
        id: `mood-${i}`,
        date: date.toISOString().split('T')[0],
        mood: Math.round(mood * 10) / 10,
        energy: Math.round(energy * 10) / 10,
        stress: Math.round(stress * 10) / 10,
        anxiety: Math.round(anxiety * 10) / 10,
        sleep: Math.round(sleep * 10) / 10,
        factors: factors.filter(() => Math.random() > 0.6).slice(0, 3),
        activities: activities.filter(() => Math.random() > 0.7).slice(0, 2),
        weather: ['ensoleillé', 'nuageux', 'pluvieux', 'orageux'][Math.floor(Math.random() * 4)],
        notes: Math.random() > 0.8 ? `Note du ${date.toLocaleDateString('fr-FR')}` : undefined
      });
    }

    // Calculer les tendances
    const calculateTrend = (data: MoodDataPoint[], period: 'daily' | 'weekly' | 'monthly'): MoodTrend => {
      const values = data.map(d => d.mood);
      const firstHalf = values.slice(0, Math.floor(values.length / 2));
      const secondHalf = values.slice(Math.floor(values.length / 2));
      
      const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
      
      const change = secondAvg - firstAvg;
      const direction = change > 0.3 ? 'improving' : change < -0.3 ? 'declining' : 'stable';
      
      return {
        period,
        direction,
        change: Math.round(change * 100) / 100,
        confidence: Math.min(100, Math.abs(change) * 50 + 50),
        startValue: Math.round(firstAvg * 10) / 10,
        endValue: Math.round(secondAvg * 10) / 10,
        significance: Math.abs(change) > 1 ? 'high' : Math.abs(change) > 0.5 ? 'medium' : 'low'
      };
    };

    // Générer des patterns
    const patterns: MoodPattern[] = [
      {
        id: 'weekend-boost',
        name: 'Amélioration weekend',
        description: 'Votre humeur s\'améliore systématiquement le weekend',
        frequency: 85,
        triggers: ['repos', 'temps libre', 'activités sociales'],
        timePattern: 'weekend',
        averageDuration: 2,
        severity: 'mild',
        recommendations: ['Planifier plus d\'activités relaxantes en semaine', 'Maintenir un équilibre travail-vie privée']
      },
      {
        id: 'monday-blues',
        name: 'Baisse du lundi',
        description: 'Tendance à une humeur plus basse le lundi',
        frequency: 65,
        triggers: ['retour au travail', 'fin du weekend'],
        timePattern: 'weekday',
        averageDuration: 1,
        severity: 'moderate',
        recommendations: ['Préparer la semaine le dimanche', 'Planifier quelque chose d\'agréable le lundi']
      }
    ];

    // Générer des corrélations
    const correlations: MoodCorrelation[] = [
      {
        factor: 'Qualité du sommeil',
        correlation: 0.72,
        strength: 'strong',
        direction: 'positive',
        examples: ['8h de sommeil = humeur +1.2 points', 'Sommeil fragmenté = humeur -0.8 points'],
        recommendations: ['Maintenir un horaire de sommeil régulier', 'Éviter les écrans avant le coucher']
      },
      {
        factor: 'Exercice physique',
        correlation: 0.58,
        strength: 'moderate',
        direction: 'positive',
        examples: ['30min d\'activité = humeur +0.9 points', 'Journée sédentaire = humeur -0.5 points'],
        recommendations: ['Intégrer 30min d\'activité quotidienne', 'Privilégier les activités en extérieur']
      },
      {
        factor: 'Stress professionnel',
        correlation: -0.64,
        strength: 'strong',
        direction: 'negative',
        examples: ['Journée stressante = humeur -1.1 points', 'Journée calme = humeur +0.7 points'],
        recommendations: ['Pratiquer des techniques de relaxation', 'Organiser mieux sa charge de travail']
      }
    ];

    // Générer des insights
    const insights: MoodInsight[] = [
      {
        id: 'trend-improvement',
        type: 'trend',
        title: 'Amélioration constante',
        description: 'Votre humeur s\'améliore progressivement depuis 2 semaines (+12%)',
        importance: 'high',
        actionable: true,
        recommendations: ['Continuez vos habitudes actuelles', 'Identifiez les facteurs de cette amélioration'],
        createdAt: new Date()
      },
      {
        id: 'sleep-correlation',
        type: 'correlation',
        title: 'Impact du sommeil',
        description: 'Une forte corrélation entre qualité de sommeil et humeur a été détectée',
        importance: 'high',
        actionable: true,
        recommendations: ['Priorisez 7-8h de sommeil par nuit', 'Créez une routine de coucher relaxante'],
        createdAt: new Date()
      },
      {
        id: 'stress-warning',
        type: 'warning',
        title: 'Niveau de stress élevé',
        description: 'Votre niveau de stress dépasse la moyenne sur les 3 derniers jours',
        importance: 'medium',
        actionable: true,
        recommendations: ['Pratiquer la méditation 10min/jour', 'Planifier des pauses régulières'],
        createdAt: new Date()
      }
    ];

    // Calculer les données de graphiques
    const weeklyData = [];
    const monthlyData = [];
    
    // Données hebdomadaires
    for (let i = 0; i < Math.ceil(dailyData.length / 7); i++) {
      const weekData = dailyData.slice(i * 7, (i + 1) * 7);
      if (weekData.length > 0) {
        const average = weekData.reduce((sum, d) => sum + d.mood, 0) / weekData.length;
        weeklyData.push({
          week: `Semaine ${i + 1}`,
          average: Math.round(average * 10) / 10,
          count: weekData.length
        });
      }
    }

    // Données mensuelles (simulées)
    for (let i = 0; i < 6; i++) {
      const monthDate = new Date();
      monthDate.setMonth(monthDate.getMonth() - i);
      monthlyData.unshift({
        month: monthDate.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' }),
        average: 6 + Math.random() * 2,
        count: 28 + Math.floor(Math.random() * 4)
      });
    }

    // Données des facteurs
    const factorData = factors.map(factor => ({
      factor,
      impact: Math.random() * 2 - 1, // -1 à 1
      frequency: Math.floor(Math.random() * 30) + 10 // 10-40%
    }));

    // Calculer l'overview
    const currentMood = dailyData[dailyData.length - 1]?.mood || 7;
    const weeklyMoods = dailyData.slice(-7).map(d => d.mood);
    const monthlyMoods = dailyData.slice(-30).map(d => d.mood);
    
    const weeklyAverage = weeklyMoods.reduce((sum, mood) => sum + mood, 0) / weeklyMoods.length;
    const monthlyAverage = monthlyMoods.reduce((sum, mood) => sum + mood, 0) / monthlyMoods.length;
    
    const sortedByMood = [...dailyData].sort((a, b) => b.mood - a.mood);
    const bestDay = sortedByMood[0];
    const worstDay = sortedByMood[sortedByMood.length - 1];

    // Calculer la série de jours consécutifs
    let streakDays = 0;
    for (let i = dailyData.length - 1; i >= 0; i--) {
      if (dailyData[i].mood >= 6) {
        streakDays++;
      } else {
        break;
      }
    }

    // Prédictions
    const predictions: MoodPrediction = {
      nextDay: Math.max(1, Math.min(10, currentMood + (Math.random() - 0.5) * 0.5)),
      nextWeek: Math.max(1, Math.min(10, weeklyAverage + (Math.random() - 0.5) * 0.3)),
      confidence: 75 + Math.floor(Math.random() * 20),
      factors: ['qualité du sommeil', 'niveau de stress', 'activité physique'],
      recommendations: [
        'Maintenir une routine de sommeil régulière',
        'Pratiquer 20 minutes de méditation quotidienne',
        'Planifier une activité agréable pour demain'
      ],
      riskLevel: currentMood < 4 ? 'high' : currentMood < 6 ? 'medium' : 'low'
    };

    return {
      overview: {
        currentMood: Math.round(currentMood * 10) / 10,
        weeklyAverage: Math.round(weeklyAverage * 10) / 10,
        monthlyAverage: Math.round(monthlyAverage * 10) / 10,
        bestDay: { date: bestDay.date, mood: bestDay.mood },
        worstDay: { date: worstDay.date, mood: worstDay.mood },
        streakDays,
        totalEntries: dailyData.length
      },
      trends: [
        calculateTrend(dailyData.slice(-7), 'weekly'),
        calculateTrend(dailyData.slice(-30), 'monthly')
      ],
      predictions,
      patterns,
      correlations,
      insights,
      chartData: {
        daily: dailyData,
        weekly: weeklyData,
        monthly: monthlyData,
        factors: factorData
      }
    };
  }, [timeRange]);

  // Charger les données
  useEffect(() => {
    if (!isAuthenticated) return;

    setIsLoading(true);
    
    // Simuler un délai de chargement
    const timer = setTimeout(() => {
      setAnalytics(generateMockData());
      setLastUpdated(new Date());
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [isAuthenticated, timeRange, generateMockData]);

  // Actualiser les données
  const refreshData = useCallback(async () => {
    setIsLoading(true);
    
    // Simuler un appel API
    await new Promise(resolve => setTimeout(resolve, 800));
    
    setAnalytics(generateMockData());
    setLastUpdated(new Date());
    setIsLoading(false);
  }, [generateMockData]);

  // Obtenir les insights par importance
  const getInsightsByImportance = useCallback((importance: 'low' | 'medium' | 'high' | 'critical') => {
    return analytics?.insights.filter(insight => insight.importance === importance) || [];
  }, [analytics]);

  // Obtenir les tendances par direction
  const getTrendsByDirection = useCallback((direction: 'improving' | 'declining' | 'stable') => {
    return analytics?.trends.filter(trend => trend.direction === direction) || [];
  }, [analytics]);

  // Obtenir les corrélations fortes
  const getStrongCorrelations = useCallback(() => {
    return analytics?.correlations.filter(corr => corr.strength === 'strong') || [];
  }, [analytics]);

  // Calculer le score de bien-être global
  const getWellnessScore = useCallback(() => {
    if (!analytics) return 0;
    
    const { overview, trends, correlations } = analytics;
    
    // Score basé sur l'humeur actuelle (40%)
    const moodScore = (overview.currentMood / 10) * 40;
    
    // Score basé sur les tendances (30%)
    const trendScore = trends.reduce((acc, trend) => {
      if (trend.direction === 'improving') return acc + 10;
      if (trend.direction === 'declining') return acc - 5;
      return acc;
    }, 0);
    
    // Score basé sur les corrélations positives (30%)
    const corrScore = correlations.reduce((acc, corr) => {
      if (corr.direction === 'positive' && corr.strength === 'strong') return acc + 5;
      return acc;
    }, 0);
    
    return Math.max(0, Math.min(100, moodScore + trendScore + corrScore));
  }, [analytics]);

  return {
    // Données
    analytics,
    isLoading,
    lastUpdated,

    // Actions
    refreshData,

    // Utilitaires
    getInsightsByImportance,
    getTrendsByDirection,
    getStrongCorrelations,
    getWellnessScore
  };
}; 