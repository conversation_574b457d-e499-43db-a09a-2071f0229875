'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

// Types pour les données du dashboard
export interface MoodEntry {
  id: string;
  date: string;
  mood: number;
  energy: number;
  stress: number;
  anxiety: number;
  sleep: number;
  notes?: string;
}

export interface WellnessStats {
  currentMood: number;
  weeklyAverage: number;
  stressLevel: number;
  energyLevel: number;
  sleepQuality: number;
  journalStreak: number;
  completedPrograms: number;
  upcomingAppointments: number;
}

export interface RecentActivity {
  id: string;
  type: 'mood' | 'journal' | 'program' | 'appointment' | 'achievement';
  title: string;
  description: string;
  timestamp: string;
  metadata?: any;
}

export interface DashboardData {
  moodData: MoodEntry[];
  wellnessStats: WellnessStats;
  recentActivities: RecentActivity[];
  objectives: any[];
  suggestions: any[];
  isLoading: boolean;
  error: string | null;
}

// Hook principal pour les données du dashboard
export const useDashboardData = () => {
  const { user, isAuthenticated } = useAuth();
  const [moodData, setMoodData] = useState<MoodEntry[]>([]);
  const [wellnessStats, setWellnessStats] = useState<WellnessStats>({
    currentMood: 0,
    weeklyAverage: 0,
    stressLevel: 0,
    energyLevel: 0,
    sleepQuality: 0,
    journalStreak: 0,
    completedPrograms: 0,
    upcomingAppointments: 0,
  });
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    const generateMockData = () => {
      const today = new Date();
      const data: MoodEntry[] = [];
      
      // Générer 30 jours de données d'humeur
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        
        data.push({
          id: `mood-${i}`,
          date: date.toISOString().split('T')[0],
          mood: Math.floor(Math.random() * 4) + 6, // 6-10
          energy: Math.floor(Math.random() * 4) + 5, // 5-9
          stress: Math.floor(Math.random() * 4) + 2, // 2-6
          anxiety: Math.floor(Math.random() * 3) + 2, // 2-5
          sleep: Math.floor(Math.random() * 3) + 6, // 6-9
          notes: i % 5 === 0 ? `Note du ${date.toLocaleDateString('fr-FR')}` : undefined,
        });
      }
      
      setMoodData(data);
      
      // Calculer les statistiques
      const currentMood = data[data.length - 1]?.mood || 7;
      const weeklyAverage = data.slice(-7).reduce((sum, entry) => sum + entry.mood, 0) / 7;
      
      setWellnessStats({
        currentMood,
        weeklyAverage: Math.round(weeklyAverage * 10) / 10,
        stressLevel: data[data.length - 1]?.stress || 3,
        energyLevel: data[data.length - 1]?.energy || 7,
        sleepQuality: data[data.length - 1]?.sleep || 7,
        journalStreak: 7,
        completedPrograms: 3,
        upcomingAppointments: 1,
      });

      // Générer des activités récentes
      const activities: RecentActivity[] = [
        {
          id: '1',
          type: 'mood',
          title: 'Suivi d\'humeur',
          description: 'Humeur enregistrée : Bien (8/10)',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: '2',
          type: 'journal',
          title: 'Entrée de journal',
          description: 'Nouvelle entrée : "Réflexions du matin"',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: '3',
          type: 'program',
          title: 'Module complété',
          description: 'Terminé : Gestion du stress - Niveau 1',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: '4',
          type: 'achievement',
          title: 'Objectif atteint',
          description: '7 jours consécutifs de suivi d\'humeur',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        },
      ];
      
      setRecentActivities(activities);
      setIsLoading(false);
    };

    // Simuler un délai de chargement
    setTimeout(generateMockData, 1000);
  }, [isAuthenticated]);

  const refreshData = () => {
    setIsLoading(true);
    // Relancer la génération des données
    setTimeout(() => {
      // Logique de rechargement ici
      setIsLoading(false);
    }, 1000);
  };

  return {
    moodData,
    wellnessStats,
    recentActivities,
    isLoading,
    refreshData,
  };
};

// Hook spécialisé pour les statistiques de bien-être
export const useWellnessStats = () => {
  const { wellnessStats, isLoading } = useDashboardData();
  
  const getStatsWithTrends = () => {
    return {
      ...wellnessStats,
      moodTrend: wellnessStats.currentMood > wellnessStats.weeklyAverage ? 'up' : 'down',
      stressTrend: wellnessStats.stressLevel < 4 ? 'good' : 'warning',
      energyTrend: wellnessStats.energyLevel > 6 ? 'up' : 'down',
    };
  };

  return {
    stats: getStatsWithTrends(),
    isLoading,
  };
};

// Hook pour les données de graphique d'humeur
export const useMoodChartData = () => {
  const { moodData, isLoading } = useDashboardData();
  
  const getChartData = () => {
    return moodData.map(entry => ({
      date: entry.date,
      mood: entry.mood,
      energy: entry.energy,
      stress: entry.stress,
      anxiety: entry.anxiety,
      sleep: entry.sleep,
    }));
  };

  return {
    data: getChartData(),
    isLoading,
  };
}; 