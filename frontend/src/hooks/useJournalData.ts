'use client';

import { useState, useEffect, useCallback } from 'react';

// Types pour le système de journal
export interface JournalEntry {
  id: string;
  title: string;
  content: string;
  entryType: string;
  moodLevel?: number;
  tags?: string[];
  emotions?: string[];
  stressLevel?: number;
  energyLevel?: number;
  sleepQuality?: number;
  gratitudeNotes?: string;
  goals?: string;
  challenges?: string;
  achievements?: string;
  isPrivate: boolean;
  isFavorite: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface JournalStats {
  totalEntries: number;
  entriesThisWeek: number;
  entriesThisMonth: number;
  averageMoodLevel: number;
  streakDays: number;
  mostUsedTags: string[];
  moodTrend: 'improving' | 'declining' | 'stable';
}

export interface CreateJournalEntryData {
  title: string;
  content: string;
  entryType?: string;
  moodLevel?: number;
  tags?: string[];
  emotions?: string[];
  stressLevel?: number;
  energyLevel?: number;
  sleepQuality?: number;
  gratitudeNotes?: string;
  goals?: string;
  challenges?: string;
  achievements?: string;
  isPrivate?: boolean;
}

export interface JournalFilters {
  searchTerm: string;
  entryType: string;
  moodLevel?: number;
  dateRange?: {
    start: Date;
    end: Date;
  };
  tags: string[];
  emotions: string[];
}

// Constantes pour le journal
export const ENTRY_TYPES = [
  { value: 'daily', label: 'Daily Reflection', icon: '📝' },
  { value: 'gratitude', label: 'Gratitude', icon: '🙏' },
  { value: 'goal_setting', label: 'Goal Setting', icon: '🎯' },
  { value: 'stress_tracking', label: 'Stress Tracking', icon: '😰' },
  { value: 'mood_tracking', label: 'Mood Tracking', icon: '😊' },
  { value: 'reflection', label: 'General Reflection', icon: '💭' },
];

export const MOOD_LEVELS = [
  { value: 1, label: 'Very Low', emoji: '😢', color: 'text-red-600' },
  { value: 2, label: 'Low', emoji: '😔', color: 'text-orange-600' },
  { value: 3, label: 'Neutral', emoji: '😐', color: 'text-yellow-600' },
  { value: 4, label: 'Good', emoji: '🙂', color: 'text-green-600' },
  { value: 5, label: 'Excellent', emoji: '😊', color: 'text-green-700' },
];

export const COMMON_EMOTIONS = [
  'happy', 'sad', 'anxious', 'excited', 'frustrated', 'calm', 'overwhelmed',
  'grateful', 'hopeful', 'worried', 'content', 'lonely', 'confident', 'stressed',
  'peaceful', 'motivated', 'tired', 'energetic', 'confused', 'proud', 'angry',
  'joyful', 'nervous', 'relaxed', 'inspired'
];

export const COMMON_TAGS = [
  'work', 'family', 'friends', 'health', 'exercise', 'sleep', 'therapy',
  'medication', 'goals', 'achievements', 'challenges', 'self-care', 'relationships',
  'mindfulness', 'meditation', 'stress', 'anxiety', 'depression', 'growth'
];

// Fonction pour générer des données simulées
const generateMockJournalData = (): JournalEntry[] => {
  const entries: JournalEntry[] = [];
  const currentDate = new Date();
  
  for (let i = 0; i < 15; i++) {
    const entryDate = new Date(currentDate);
    entryDate.setDate(currentDate.getDate() - i);
    
    const entryType = ENTRY_TYPES[Math.floor(Math.random() * ENTRY_TYPES.length)];
    const moodLevel = Math.floor(Math.random() * 5) + 1;
    
    const sampleEmotions = COMMON_EMOTIONS.slice(0, Math.floor(Math.random() * 4) + 1);
    const sampleTags = COMMON_TAGS.slice(0, Math.floor(Math.random() * 3) + 1);
    
    entries.push({
      id: `entry-${i + 1}`,
      title: `${entryType.label} - ${entryDate.toLocaleDateString()}`,
      content: `This is a sample journal entry for ${entryType.label.toLowerCase()}. Today I reflected on my thoughts and feelings, and I'm grateful for the opportunity to track my mental health journey. This entry contains meaningful insights about my current state of mind and my progress towards better well-being.`,
      entryType: entryType.value,
      moodLevel,
      tags: sampleTags,
      emotions: sampleEmotions,
      stressLevel: Math.floor(Math.random() * 11),
      energyLevel: Math.floor(Math.random() * 11),
      sleepQuality: Math.floor(Math.random() * 11),
      gratitudeNotes: i % 3 === 0 ? "I'm grateful for my health, family, and the progress I'm making." : undefined,
      goals: i % 4 === 0 ? "Continue daily journaling and maintain a positive mindset." : undefined,
      challenges: i % 5 === 0 ? "Managing stress from work and maintaining work-life balance." : undefined,
      achievements: i % 3 === 0 ? "Completed my daily meditation and exercise routine." : undefined,
      isPrivate: Math.random() > 0.7,
      isFavorite: Math.random() > 0.8,
      createdAt: entryDate.toISOString(),
      updatedAt: entryDate.toISOString(),
    });
  }
  
  return entries.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};

const generateMockJournalStats = (entries: JournalEntry[]): JournalStats => {
  const currentDate = new Date();
  const weekAgo = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);
  const monthAgo = new Date(currentDate.getTime() - 30 * 24 * 60 * 60 * 1000);
  
  const entriesThisWeek = entries.filter(entry => 
    new Date(entry.createdAt) >= weekAgo
  ).length;
  
  const entriesThisMonth = entries.filter(entry => 
    new Date(entry.createdAt) >= monthAgo
  ).length;
  
  const moodEntries = entries.filter(entry => entry.moodLevel);
  const averageMoodLevel = moodEntries.length > 0 
    ? moodEntries.reduce((sum, entry) => sum + (entry.moodLevel || 0), 0) / moodEntries.length
    : 0;
  
  const allTags = entries.flatMap(entry => entry.tags || []);
  const tagCounts = allTags.reduce((acc, tag) => {
    acc[tag] = (acc[tag] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const mostUsedTags = Object.entries(tagCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([tag]) => tag);
  
  return {
    totalEntries: entries.length,
    entriesThisWeek,
    entriesThisMonth,
    averageMoodLevel,
    streakDays: 7, // Simulated streak
    mostUsedTags,
    moodTrend: 'improving' // Simulated trend
  };
};

export const useJournalData = () => {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [stats, setStats] = useState<JournalStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<JournalFilters>({
    searchTerm: '',
    entryType: '',
    tags: [],
    emotions: []
  });

  // Simulation de chargement des données
  const loadJournalData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Simulation d'un appel API
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const mockEntries = generateMockJournalData();
      const mockStats = generateMockJournalStats(mockEntries);
      
      setEntries(mockEntries);
      setStats(mockStats);
    } catch (err) {
      setError('Failed to load journal data');
      console.error('Error loading journal data:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Charger les données au montage
  useEffect(() => {
    loadJournalData();
  }, [loadJournalData]);

  // Fonction pour créer une nouvelle entrée
  const createEntry = useCallback(async (entryData: CreateJournalEntryData): Promise<boolean> => {
    try {
      // Simulation d'un appel API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newEntry: JournalEntry = {
        id: `entry-${Date.now()}`,
        ...entryData,
        entryType: entryData.entryType || 'daily',
        isPrivate: entryData.isPrivate || false,
        isFavorite: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      setEntries(prev => [newEntry, ...prev]);
      
      // Recalculer les stats
      const updatedEntries = [newEntry, ...entries];
      const updatedStats = generateMockJournalStats(updatedEntries);
      setStats(updatedStats);
      
      return true;
    } catch (err) {
      console.error('Error creating journal entry:', err);
      return false;
    }
  }, [entries]);

  // Fonction pour mettre à jour une entrée
  const updateEntry = useCallback(async (id: string, entryData: Partial<CreateJournalEntryData>): Promise<boolean> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setEntries(prev => prev.map(entry => 
        entry.id === id 
          ? { ...entry, ...entryData, updatedAt: new Date().toISOString() }
          : entry
      ));
      
      return true;
    } catch (err) {
      console.error('Error updating journal entry:', err);
      return false;
    }
  }, []);

  // Fonction pour supprimer une entrée
  const deleteEntry = useCallback(async (id: string): Promise<boolean> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setEntries(prev => prev.filter(entry => entry.id !== id));
      return true;
    } catch (err) {
      console.error('Error deleting journal entry:', err);
      return false;
    }
  }, []);

  // Fonction pour basculer le favori
  const toggleFavorite = useCallback(async (id: string): Promise<boolean> => {
    try {
      setEntries(prev => prev.map(entry => 
        entry.id === id 
          ? { ...entry, isFavorite: !entry.isFavorite, updatedAt: new Date().toISOString() }
          : entry
      ));
      return true;
    } catch (err) {
      console.error('Error toggling favorite:', err);
      return false;
    }
  }, []);

  // Filtrer les entrées
  const filteredEntries = entries.filter(entry => {
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const matchesSearch = 
        entry.title.toLowerCase().includes(searchLower) ||
        entry.content.toLowerCase().includes(searchLower) ||
        entry.tags?.some(tag => tag.toLowerCase().includes(searchLower)) ||
        entry.emotions?.some(emotion => emotion.toLowerCase().includes(searchLower));
      
      if (!matchesSearch) return false;
    }
    
    if (filters.entryType && entry.entryType !== filters.entryType) {
      return false;
    }
    
    if (filters.moodLevel && entry.moodLevel !== filters.moodLevel) {
      return false;
    }
    
    if (filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag => 
        entry.tags?.includes(tag)
      );
      if (!hasMatchingTag) return false;
    }
    
    if (filters.emotions.length > 0) {
      const hasMatchingEmotion = filters.emotions.some(emotion => 
        entry.emotions?.includes(emotion)
      );
      if (!hasMatchingEmotion) return false;
    }
    
    return true;
  });

  // Fonctions utilitaires
  const getEntryTypeLabel = (type: string) => {
    return ENTRY_TYPES.find(t => t.value === type)?.label || type;
  };

  const getMoodEmoji = (level: number) => {
    return MOOD_LEVELS.find(m => m.value === level)?.emoji || '😐';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return {
    // Données
    entries: filteredEntries,
    allEntries: entries,
    stats,
    loading,
    error,
    filters,
    
    // Actions
    loadJournalData,
    createEntry,
    updateEntry,
    deleteEntry,
    toggleFavorite,
    setFilters,
    
    // Utilitaires
    getEntryTypeLabel,
    getMoodEmoji,
    formatDate,
    truncateContent,
    
    // Constantes
    ENTRY_TYPES,
    MOOD_LEVELS,
    COMMON_EMOTIONS,
    COMMON_TAGS,
  };
}; 