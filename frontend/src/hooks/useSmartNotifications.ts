'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

// Types pour les notifications intelligentes
export interface SmartNotification {
  id: string;
  type: 'reminder' | 'suggestion' | 'achievement' | 'warning' | 'insight' | 'emergency';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  message: string;
  actionText?: string;
  actionUrl?: string;
  aiGenerated: boolean;
  personalizedFor: string;
  triggers: string[];
  scheduledFor: Date;
  expiresAt?: Date;
  isRead: boolean;
  isActioned: boolean;
  metadata: {
    moodContext?: number;
    timeContext?: 'morning' | 'afternoon' | 'evening' | 'night';
    userActivity?: string;
    weatherContext?: string;
    stressLevel?: number;
    energyLevel?: number;
  };
  aiInsight?: {
    confidence: number;
    reasoning: string;
    expectedImpact: 'positive' | 'neutral' | 'negative';
    personalizedFactors: string[];
  };
  createdAt: Date;
  sentAt?: Date;
  readAt?: Date;
  actionedAt?: Date;
}

export interface NotificationRule {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  triggers: {
    moodThreshold?: { below?: number; above?: number };
    timeOfDay?: string[];
    dayOfWeek?: number[];
    streakBreak?: boolean;
    inactivityDays?: number;
    stressLevel?: { above?: number };
    energyLevel?: { below?: number };
    patterns?: string[];
  };
  actions: {
    notificationType: SmartNotification['type'];
    priority: SmartNotification['priority'];
    messageTemplate: string;
    actionTemplate?: string;
    delayMinutes?: number;
  };
  aiEnhanced: boolean;
  personalizationLevel: 'basic' | 'advanced' | 'deep';
  frequency: 'once' | 'daily' | 'weekly' | 'smart';
  lastTriggered?: Date;
  effectivenessScore: number;
}

export interface NotificationPreferences {
  enablePush: boolean;
  enableEmail: boolean;
  enableSMS: boolean;
  quietHours: {
    start: string;
    end: string;
    enabled: boolean;
  };
  categories: {
    reminders: boolean;
    suggestions: boolean;
    achievements: boolean;
    warnings: boolean;
    insights: boolean;
    emergency: boolean;
  };
  aiPersonalization: {
    enabled: boolean;
    level: 'basic' | 'advanced' | 'deep';
    learningMode: boolean;
  };
  frequency: {
    maxPerDay: number;
    respectMoodState: boolean;
    adaptToActivity: boolean;
  };
}

export interface NotificationAnalytics {
  totalSent: number;
  totalRead: number;
  totalActioned: number;
  readRate: number;
  actionRate: number;
  averageResponseTime: number;
  mostEffectiveTypes: string[];
  bestTimes: string[];
  userEngagement: 'low' | 'medium' | 'high';
  aiAccuracy: number;
  personalizedImpact: number;
}

// Hook principal pour les notifications intelligentes
export const useSmartNotifications = () => {
  const { user, isAuthenticated } = useAuth();
  const [notifications, setNotifications] = useState<SmartNotification[]>([]);
  const [rules, setRules] = useState<NotificationRule[]>([]);
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [analytics, setAnalytics] = useState<NotificationAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Initialiser les données simulées
  useEffect(() => {
    if (!isAuthenticated) return;

    const generateMockNotifications = (): SmartNotification[] => {
      const now = new Date();
      const notifications: SmartNotification[] = [];

      // Notifications récentes
      const notificationTemplates = [
        {
          type: 'reminder' as const,
          priority: 'medium' as const,
          title: 'Moment de méditation',
          message: 'Il est temps pour votre session de méditation quotidienne. 5 minutes peuvent faire la différence.',
          actionText: 'Commencer',
          actionUrl: '/programs/meditation',
          triggers: ['scheduled_time', 'stress_level'],
        },
        {
          type: 'suggestion' as const,
          priority: 'low' as const,
          title: 'Suggestion personnalisée',
          message: 'Votre humeur semble basse aujourd\'hui. Que diriez-vous d\'une courte promenade ?',
          actionText: 'Planifier',
          actionUrl: '/activities/walk',
          triggers: ['mood_low', 'ai_analysis'],
        },
        {
          type: 'achievement' as const,
          priority: 'low' as const,
          title: 'Félicitations ! 🎉',
          message: 'Vous avez maintenu votre série de journal pendant 7 jours consécutifs !',
          actionText: 'Voir progrès',
          actionUrl: '/analytics',
          triggers: ['streak_milestone'],
        },
        {
          type: 'insight' as const,
          priority: 'medium' as const,
          title: 'Insight IA détecté',
          message: 'L\'IA a remarqué que votre humeur s\'améliore après vos sessions de sport. Continuez !',
          actionText: 'En savoir plus',
          actionUrl: '/analytics/correlations',
          triggers: ['ai_pattern_detection'],
        },
        {
          type: 'warning' as const,
          priority: 'high' as const,
          title: 'Niveau de stress élevé',
          message: 'Votre niveau de stress est au-dessus de la normale depuis 3 jours. Prenons soin de vous.',
          actionText: 'Techniques anti-stress',
          actionUrl: '/resources/stress-management',
          triggers: ['stress_threshold', 'duration_concern'],
        }
      ];

      notificationTemplates.forEach((template, index) => {
        const scheduledTime = new Date(now);
        scheduledTime.setHours(scheduledTime.getHours() - index);

        notifications.push({
          id: `notif-${index}`,
          ...template,
          aiGenerated: index % 2 === 0,
          personalizedFor: user?.id || 'mock-user',
          scheduledFor: scheduledTime,
          expiresAt: new Date(scheduledTime.getTime() + 24 * 60 * 60 * 1000), // 24h
          isRead: index > 2,
          isActioned: index > 3,
          metadata: {
            moodContext: 4 + Math.random() * 4,
            timeContext: ['morning', 'afternoon', 'evening'][Math.floor(Math.random() * 3)] as any,
            userActivity: ['working', 'resting', 'exercising', 'socializing'][Math.floor(Math.random() * 4)],
            stressLevel: 3 + Math.random() * 4,
            energyLevel: 4 + Math.random() * 4,
          },
          aiInsight: index % 2 === 0 ? {
            confidence: 75 + Math.random() * 20,
            reasoning: 'Basé sur vos patterns comportementaux et votre historique d\'humeur',
            expectedImpact: ['positive', 'neutral'][Math.floor(Math.random() * 2)] as any,
            personalizedFactors: ['historique_humeur', 'patterns_activité', 'préférences_utilisateur']
          } : undefined,
          createdAt: new Date(scheduledTime.getTime() - 5 * 60 * 1000), // 5min avant
          sentAt: scheduledTime,
          readAt: index > 2 ? new Date(scheduledTime.getTime() + 10 * 60 * 1000) : undefined,
          actionedAt: index > 3 ? new Date(scheduledTime.getTime() + 15 * 60 * 1000) : undefined,
        });
      });

      return notifications.sort((a, b) => b.scheduledFor.getTime() - a.scheduledFor.getTime());
    };

    const generateMockRules = (): NotificationRule[] => [
      {
        id: 'daily-mood-check',
        name: 'Vérification quotidienne d\'humeur',
        description: 'Rappel pour enregistrer votre humeur si non fait dans la journée',
        isActive: true,
        triggers: {
          timeOfDay: ['20:00'],
          inactivityDays: 1,
        },
        actions: {
          notificationType: 'reminder',
          priority: 'medium',
          messageTemplate: 'N\'oubliez pas d\'enregistrer votre humeur aujourd\'hui !',
          actionTemplate: 'Enregistrer maintenant',
        },
        aiEnhanced: true,
        personalizationLevel: 'advanced',
        frequency: 'daily',
        effectivenessScore: 85,
      },
      {
        id: 'stress-intervention',
        name: 'Intervention stress élevé',
        description: 'Suggestions quand le niveau de stress dépasse le seuil',
        isActive: true,
        triggers: {
          stressLevel: { above: 7 },
        },
        actions: {
          notificationType: 'suggestion',
          priority: 'high',
          messageTemplate: 'Votre niveau de stress semble élevé. Voulez-vous essayer une technique de relaxation ?',
          actionTemplate: 'Commencer la relaxation',
          delayMinutes: 0,
        },
        aiEnhanced: true,
        personalizationLevel: 'deep',
        frequency: 'smart',
        effectivenessScore: 92,
      },
      {
        id: 'energy-boost',
        name: 'Boost d\'énergie',
        description: 'Suggestions pour augmenter l\'énergie quand elle est basse',
        isActive: true,
        triggers: {
          energyLevel: { below: 4 },
          timeOfDay: ['14:00', '15:00', '16:00'],
        },
        actions: {
          notificationType: 'suggestion',
          priority: 'medium',
          messageTemplate: 'Besoin d\'un boost d\'énergie ? Essayez une courte activité physique.',
          actionTemplate: 'Voir suggestions',
        },
        aiEnhanced: true,
        personalizationLevel: 'advanced',
        frequency: 'smart',
        effectivenessScore: 78,
      }
    ];

    const generateMockPreferences = (): NotificationPreferences => ({
      enablePush: true,
      enableEmail: false,
      enableSMS: false,
      quietHours: {
        start: '22:00',
        end: '08:00',
        enabled: true,
      },
      categories: {
        reminders: true,
        suggestions: true,
        achievements: true,
        warnings: true,
        insights: true,
        emergency: true,
      },
      aiPersonalization: {
        enabled: true,
        level: 'advanced',
        learningMode: true,
      },
      frequency: {
        maxPerDay: 5,
        respectMoodState: true,
        adaptToActivity: true,
      },
    });

    const generateMockAnalytics = (): NotificationAnalytics => ({
      totalSent: 156,
      totalRead: 134,
      totalActioned: 89,
      readRate: 85.9,
      actionRate: 57.1,
      averageResponseTime: 8.5, // minutes
      mostEffectiveTypes: ['suggestion', 'achievement', 'reminder'],
      bestTimes: ['09:00', '14:00', '19:00'],
      userEngagement: 'high',
      aiAccuracy: 87.3,
      personalizedImpact: 23.4, // % d'amélioration
    });

    setNotifications(generateMockNotifications());
    setRules(generateMockRules());
    setPreferences(generateMockPreferences());
    setAnalytics(generateMockAnalytics());
  }, [isAuthenticated, user]);

  // Marquer une notification comme lue
  const markAsRead = useCallback(async (notificationId: string) => {
    setNotifications(prev => prev.map(notif => 
      notif.id === notificationId 
        ? { ...notif, isRead: true, readAt: new Date() }
        : notif
    ));
  }, []);

  // Marquer une notification comme actionnée
  const markAsActioned = useCallback(async (notificationId: string) => {
    setNotifications(prev => prev.map(notif => 
      notif.id === notificationId 
        ? { ...notif, isActioned: true, actionedAt: new Date() }
        : notif
    ));
  }, []);

  // Supprimer une notification
  const dismissNotification = useCallback(async (notificationId: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
  }, []);

  // Créer une notification personnalisée
  const createNotification = useCallback(async (
    type: SmartNotification['type'],
    title: string,
    message: string,
    options: Partial<SmartNotification> = {}
  ) => {
    const newNotification: SmartNotification = {
      id: `custom-${Date.now()}`,
      type,
      priority: options.priority || 'medium',
      title,
      message,
      actionText: options.actionText,
      actionUrl: options.actionUrl,
      aiGenerated: false,
      personalizedFor: user?.id || 'mock-user',
      triggers: options.triggers || ['manual'],
      scheduledFor: new Date(),
      isRead: false,
      isActioned: false,
      metadata: options.metadata || {},
      createdAt: new Date(),
      ...options,
    };

    setNotifications(prev => [newNotification, ...prev]);
    return newNotification;
  }, [user]);

  // Générer une notification IA basée sur le contexte
  const generateAINotification = useCallback(async (context: {
    currentMood?: number;
    stressLevel?: number;
    energyLevel?: number;
    lastActivity?: string;
    timeOfDay?: string;
  }) => {
    // Simulation de génération IA
    const aiSuggestions = [
      {
        condition: (ctx: typeof context) => (ctx.currentMood || 5) < 4,
        notification: {
          type: 'suggestion' as const,
          priority: 'medium' as const,
          title: 'Boost d\'humeur suggéré',
          message: 'L\'IA a détecté que votre humeur pourrait bénéficier d\'une activité positive. Que diriez-vous d\'écouter votre musique préférée ?',
          actionText: 'Voir suggestions',
          actionUrl: '/activities/mood-boost',
        }
      },
      {
        condition: (ctx: typeof context) => (ctx.stressLevel || 5) > 7,
        notification: {
          type: 'warning' as const,
          priority: 'high' as const,
          title: 'Niveau de stress détecté',
          message: 'L\'IA recommande une pause de 5 minutes avec des exercices de respiration pour réduire votre stress.',
          actionText: 'Commencer maintenant',
          actionUrl: '/exercises/breathing',
        }
      },
      {
        condition: (ctx: typeof context) => (ctx.energyLevel || 5) < 3 && ctx.timeOfDay === 'afternoon',
        notification: {
          type: 'suggestion' as const,
          priority: 'medium' as const,
          title: 'Énergie faible détectée',
          message: 'L\'après-midi peut être difficile. Une courte marche ou un étirement pourrait vous revitaliser.',
          actionText: 'Voir options',
          actionUrl: '/activities/energy-boost',
        }
      }
    ];

    const applicableSuggestion = aiSuggestions.find(s => s.condition(context));
    
    if (applicableSuggestion) {
      return await createNotification(
        applicableSuggestion.notification.type,
        applicableSuggestion.notification.title,
        applicableSuggestion.notification.message,
        {
          ...applicableSuggestion.notification,
          aiGenerated: true,
          metadata: context,
          aiInsight: {
            confidence: 80 + Math.random() * 15,
            reasoning: 'Basé sur votre état actuel et vos patterns historiques',
            expectedImpact: 'positive',
            personalizedFactors: ['état_émotionnel', 'contexte_temporel', 'historique_réponses']
          }
        }
      );
    }

    return null;
  }, [createNotification]);

  // Mettre à jour les préférences
  const updatePreferences = useCallback(async (newPreferences: Partial<NotificationPreferences>) => {
    setPreferences(prev => prev ? { ...prev, ...newPreferences } : null);
  }, []);

  // Activer/désactiver une règle
  const toggleRule = useCallback(async (ruleId: string) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId 
        ? { ...rule, isActive: !rule.isActive }
        : rule
    ));
  }, []);

  // Obtenir les notifications non lues
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(notif => !notif.isRead);
  }, [notifications]);

  // Obtenir les notifications par priorité
  const getNotificationsByPriority = useCallback((priority: SmartNotification['priority']) => {
    return notifications.filter(notif => notif.priority === priority);
  }, [notifications]);

  // Obtenir les notifications par type
  const getNotificationsByType = useCallback((type: SmartNotification['type']) => {
    return notifications.filter(notif => notif.type === type);
  }, [notifications]);

  // Calculer le score d'engagement
  const getEngagementScore = useCallback(() => {
    if (!analytics) return 0;
    return (analytics.readRate * 0.4) + (analytics.actionRate * 0.6);
  }, [analytics]);

  return {
    // État
    notifications,
    rules,
    preferences,
    analytics,
    isLoading,

    // Actions
    markAsRead,
    markAsActioned,
    dismissNotification,
    createNotification,
    generateAINotification,
    updatePreferences,
    toggleRule,

    // Utilitaires
    getUnreadNotifications,
    getNotificationsByPriority,
    getNotificationsByType,
    getEngagementScore
  };
}; 