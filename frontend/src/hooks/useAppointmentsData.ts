import { useState, useCallback, useMemo } from 'react';

// Types
export interface Appointment {
  id: string;
  professionalId: string;
  professionalName: string;
  professionalRole: string;
  professionalAvatar?: string;
  clientId: string;
  clientName: string;
  date: string;
  time: string;
  duration: number; // en minutes
  type: 'video' | 'in-person' | 'chat';
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  price: number;
  currency: string;
  notes?: string;
  rating?: number;
  feedback?: string;
  reminderSent: boolean;
  createdAt: string;
  updatedAt: string;
  cancelReason?: string;
  meetingLink?: string;
  location?: string;
}

export interface AppointmentStats {
  total: number;
  upcoming: number;
  completed: number;
  cancelled: number;
  noShow: number;
  totalRevenue: number;
  averageRating: number;
  weeklyAppointments: number;
  monthlyAppointments: number;
}

export interface CreateAppointmentData {
  professionalId: string;
  date: string;
  time: string;
  duration: number;
  type: 'video' | 'in-person' | 'chat';
  notes?: string;
}

export interface UpdateAppointmentData {
  date?: string;
  time?: string;
  duration?: number;
  type?: 'video' | 'in-person' | 'chat';
  status?: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  notes?: string;
  rating?: number;
  feedback?: string;
  cancelReason?: string;
}

// Fonction pour générer des données simulées
const generateMockAppointments = (): Appointment[] => {
  const professionals = [
    { id: '1', name: 'Dr. Sophie Martin', role: 'Psychologue clinicienne' },
    { id: '2', name: 'Dr. Jean Dupont', role: 'Psychiatre' },
    { id: '3', name: 'Marie Leblanc', role: 'Thérapeute comportementale' },
    { id: '4', name: 'Dr. Ahmed Benali', role: 'Psychothérapeute' }
  ];

  const statuses: Appointment['status'][] = ['scheduled', 'confirmed', 'completed', 'cancelled', 'no-show'];
  const types: Appointment['type'][] = ['video', 'in-person', 'chat'];

  const appointments: Appointment[] = [];
  const now = new Date();

  // Générer 20 rendez-vous sur les 3 derniers mois et les 2 prochains mois
  for (let i = 0; i < 20; i++) {
    const daysOffset = Math.floor(Math.random() * 150) - 90; // -90 à +60 jours
    const appointmentDate = new Date(now);
    appointmentDate.setDate(now.getDate() + daysOffset);
    
    const professional = professionals[Math.floor(Math.random() * professionals.length)];
    const status = daysOffset < -7 
      ? ['completed', 'cancelled', 'no-show'][Math.floor(Math.random() * 3)]
      : daysOffset < 0
      ? 'completed'
      : ['scheduled', 'confirmed'][Math.floor(Math.random() * 2)];

    const appointment: Appointment = {
      id: `apt-${i + 1}`,
      professionalId: professional.id,
      professionalName: professional.name,
      professionalRole: professional.role,
      clientId: 'user-1',
      clientName: 'Utilisateur Test',
      date: appointmentDate.toISOString().split('T')[0],
      time: `${9 + Math.floor(Math.random() * 9)}:${Math.random() > 0.5 ? '00' : '30'}`,
      duration: [30, 45, 60, 90][Math.floor(Math.random() * 4)],
      type: types[Math.floor(Math.random() * types.length)],
      status: status as Appointment['status'],
      price: [70, 80, 90, 100, 120][Math.floor(Math.random() * 5)],
      currency: 'EUR',
      reminderSent: status === 'completed' || status === 'cancelled',
      createdAt: new Date(appointmentDate.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: appointmentDate.toISOString(),
    };

    // Ajouter des détails pour les rendez-vous passés
    if (status === 'completed') {
      appointment.rating = 3 + Math.floor(Math.random() * 3); // 3-5
      appointment.feedback = [
        'Excellente séance, très à l\'écoute',
        'Bonne progression, je me sens mieux',
        'Approche très professionnelle',
        'Les exercices proposés sont très utiles'
      ][Math.floor(Math.random() * 4)];
    }

    if (status === 'cancelled') {
      appointment.cancelReason = [
        'Empêchement personnel',
        'Maladie',
        'Changement d\'emploi du temps',
        'Report à une date ultérieure'
      ][Math.floor(Math.random() * 4)];
    }

    if (appointment.type === 'video') {
      appointment.meetingLink = `https://meet.mindflow.com/session-${appointment.id}`;
    } else if (appointment.type === 'in-person') {
      appointment.location = `${Math.floor(Math.random() * 50) + 1} rue de la Paix, ${['Paris', 'Lyon', 'Marseille'][Math.floor(Math.random() * 3)]}`;
    }

    if (Math.random() > 0.7) {
      appointment.notes = [
        'Discussion sur la gestion du stress au travail',
        'Suivi du traitement médicamenteux',
        'Exercices de relaxation à pratiquer',
        'Préparation pour un événement important',
        'Travail sur l\'estime de soi'
      ][Math.floor(Math.random() * 5)];
    }

    appointments.push(appointment);
  }

  return appointments.sort((a, b) => {
    const dateA = new Date(`${a.date} ${a.time}`);
    const dateB = new Date(`${b.date} ${b.time}`);
    return dateB.getTime() - dateA.getTime();
  });
};

// Hook principal
export const useAppointmentsData = () => {
  const [appointments, setAppointments] = useState<Appointment[]>(() => generateMockAppointments());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Créer un nouveau rendez-vous
  const createAppointment = useCallback(async (data: CreateAppointmentData) => {
    setLoading(true);
    setError(null);

    try {
      // Simuler un délai API
      await new Promise(resolve => setTimeout(resolve, 500));

      const professional = appointments.find(a => a.professionalId === data.professionalId);
      const newAppointment: Appointment = {
        id: `apt-${Date.now()}`,
        professionalId: data.professionalId,
        professionalName: professional?.professionalName || 'Professionnel',
        professionalRole: professional?.professionalRole || 'Thérapeute',
        clientId: 'user-1',
        clientName: 'Utilisateur Test',
        date: data.date,
        time: data.time,
        duration: data.duration,
        type: data.type,
        status: 'scheduled',
        price: 80,
        currency: 'EUR',
        notes: data.notes,
        reminderSent: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      if (data.type === 'video') {
        newAppointment.meetingLink = `https://meet.mindflow.com/session-${newAppointment.id}`;
      }

      setAppointments(prev => [newAppointment, ...prev]);
      return newAppointment;
    } catch (err) {
      setError('Erreur lors de la création du rendez-vous');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [appointments]);

  // Mettre à jour un rendez-vous
  const updateAppointment = useCallback(async (id: string, data: UpdateAppointmentData) => {
    setLoading(true);
    setError(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      setAppointments(prev => prev.map(appointment => {
        if (appointment.id === id) {
          return {
            ...appointment,
            ...data,
            updatedAt: new Date().toISOString()
          };
        }
        return appointment;
      }));
    } catch (err) {
      setError('Erreur lors de la mise à jour du rendez-vous');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Annuler un rendez-vous
  const cancelAppointment = useCallback(async (id: string, reason: string) => {
    await updateAppointment(id, {
      status: 'cancelled',
      cancelReason: reason
    });
  }, [updateAppointment]);

  // Confirmer un rendez-vous
  const confirmAppointment = useCallback(async (id: string) => {
    await updateAppointment(id, {
      status: 'confirmed'
    });
  }, [updateAppointment]);

  // Marquer comme complété avec évaluation
  const completeAppointment = useCallback(async (id: string, rating: number, feedback?: string) => {
    await updateAppointment(id, {
      status: 'completed',
      rating,
      feedback
    });
  }, [updateAppointment]);

  // Calculer les statistiques
  const stats = useMemo<AppointmentStats>(() => {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const upcomingAppointments = appointments.filter(a => {
      const appointmentDate = new Date(`${a.date} ${a.time}`);
      return appointmentDate > now && (a.status === 'scheduled' || a.status === 'confirmed');
    });

    const completedAppointments = appointments.filter(a => a.status === 'completed');
    const cancelledAppointments = appointments.filter(a => a.status === 'cancelled');
    const noShowAppointments = appointments.filter(a => a.status === 'no-show');

    const weeklyAppointments = appointments.filter(a => {
      const appointmentDate = new Date(a.date);
      return appointmentDate >= oneWeekAgo && a.status === 'completed';
    });

    const monthlyAppointments = appointments.filter(a => {
      const appointmentDate = new Date(a.date);
      return appointmentDate >= oneMonthAgo && a.status === 'completed';
    });

    const totalRevenue = completedAppointments.reduce((sum, a) => sum + a.price, 0);
    
    const ratingsSum = completedAppointments
      .filter(a => a.rating)
      .reduce((sum, a) => sum + (a.rating || 0), 0);
    
    const ratingsCount = completedAppointments.filter(a => a.rating).length;
    const averageRating = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

    return {
      total: appointments.length,
      upcoming: upcomingAppointments.length,
      completed: completedAppointments.length,
      cancelled: cancelledAppointments.length,
      noShow: noShowAppointments.length,
      totalRevenue,
      averageRating: Math.round(averageRating * 10) / 10,
      weeklyAppointments: weeklyAppointments.length,
      monthlyAppointments: monthlyAppointments.length
    };
  }, [appointments]);

  // Obtenir les prochains rendez-vous
  const getUpcomingAppointments = useCallback((limit?: number) => {
    const now = new Date();
    const upcoming = appointments
      .filter(a => {
        const appointmentDate = new Date(`${a.date} ${a.time}`);
        return appointmentDate > now && (a.status === 'scheduled' || a.status === 'confirmed');
      })
      .sort((a, b) => {
        const dateA = new Date(`${a.date} ${a.time}`);
        const dateB = new Date(`${b.date} ${b.time}`);
        return dateA.getTime() - dateB.getTime();
      });

    return limit ? upcoming.slice(0, limit) : upcoming;
  }, [appointments]);

  // Obtenir les rendez-vous par professionnel
  const getAppointmentsByProfessional = useCallback((professionalId: string) => {
    return appointments.filter(a => a.professionalId === professionalId);
  }, [appointments]);

  // Obtenir les rendez-vous par statut
  const getAppointmentsByStatus = useCallback((status: Appointment['status']) => {
    return appointments.filter(a => a.status === status);
  }, [appointments]);

  return {
    appointments,
    stats,
    loading,
    error,
    createAppointment,
    updateAppointment,
    cancelAppointment,
    confirmAppointment,
    completeAppointment,
    getUpcomingAppointments,
    getAppointmentsByProfessional,
    getAppointmentsByStatus
  };
}; 