'use client';

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase/client';

// Hook Supabase pour le Journal (Migration de useJournalData.ts)
export const useJournalDataSupabase = () => {
  const [entries, setEntries] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Charger les données depuis Supabase
  const loadJournalData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Chargement journal depuis Supabase...');

      const { data, error } = await supabase
        .from('journal_entries')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      console.log(`✅ ${data?.length || 0} entrées chargées`);
      setEntries(data || []);

    } catch (err) {
      console.error('❌ Erreur:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadJournalData();
  }, [loadJournalData]);

  return {
    entries,
    loading,
    error,
    loadJournalData,
    isSupabase: true
  };
};
