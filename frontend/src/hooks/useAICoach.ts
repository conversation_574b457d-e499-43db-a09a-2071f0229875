'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

// Types pour l'IA Coach
export interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  mood?: number;
  sentiment?: 'positive' | 'negative' | 'neutral';
  suggestedActions?: string[];
  emotionalIndicators?: string[];
}

export interface AISession {
  id: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'completed' | 'paused';
  theme: string;
  goal: string;
  messages: AIMessage[];
  moodAnalysis?: {
    initialMood: number;
    currentMood: number;
    moodTrend: 'improving' | 'declining' | 'stable';
    insights: string[];
  };
  stats?: {
    duration: number;
    messageCount: number;
    moodChanges: number;
    actionsTaken: number;
  };
}

export interface AICoachStats {
  totalSessions: number;
  totalMessages: number;
  averageSessionDuration: number;
  moodImprovementRate: number;
  favoriteTopics: string[];
  lastActivity: Date;
  weeklyUsage: number[];
  monthlyProgress: {
    sessionsCompleted: number;
    goalsAchieved: number;
    moodImprovement: number;
  };
}

export interface AIAnalysis {
  sentiment: 'positive' | 'negative' | 'neutral';
  emotionalState: string[];
  recommendedActions: string[];
  moodScore: number;
  urgencyLevel: 'low' | 'medium' | 'high' | 'crisis';
  supportResources: string[];
}

// Hook principal pour l'IA Coach
export const useAICoach = () => {
  const { user, isAuthenticated } = useAuth();
  const [currentSession, setCurrentSession] = useState<AISession | null>(null);
  const [sessions, setSessions] = useState<AISession[]>([]);
  const [stats, setStats] = useState<AICoachStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  // Initialiser les données simulées
  useEffect(() => {
    if (!isAuthenticated) return;

    const generateMockSessions = (): AISession[] => {
      const mockSessions: AISession[] = [];
      const today = new Date();

      for (let i = 0; i < 10; i++) {
        const sessionDate = new Date(today);
        sessionDate.setDate(sessionDate.getDate() - i * 2);

        const session: AISession = {
          id: `session-${i}`,
          userId: user?.id || 'mock-user',
          startTime: sessionDate,
          endTime: new Date(sessionDate.getTime() + Math.random() * 30 * 60 * 1000), // 0-30 min
          status: i === 0 ? 'active' : 'completed',
          theme: ['gestion_stress', 'amélioration_humeur', 'confiance_soi', 'anxiété', 'motivation'][Math.floor(Math.random() * 5)],
          goal: 'Améliorer mon bien-être émotionnel',
          messages: generateMockMessages(5 + Math.floor(Math.random() * 10)),
          moodAnalysis: {
            initialMood: 4 + Math.floor(Math.random() * 3),
            currentMood: 6 + Math.floor(Math.random() * 4),
            moodTrend: ['improving', 'stable', 'declining'][Math.floor(Math.random() * 3)] as 'improving' | 'stable' | 'declining',
            insights: [
              'Amélioration notable après les exercices de respiration',
              'Stress lié au travail identifié comme facteur principal',
              'Progression positive dans la gestion émotionnelle'
            ]
          },
          stats: {
            duration: Math.floor(Math.random() * 25) + 5, // 5-30 minutes
            messageCount: 8 + Math.floor(Math.random() * 15),
            moodChanges: Math.floor(Math.random() * 3) + 1,
            actionsTaken: Math.floor(Math.random() * 5) + 1
          }
        };

        mockSessions.push(session);
      }

      return mockSessions;
    };

    const generateMockMessages = (count: number): AIMessage[] => {
      const messages: AIMessage[] = [];
      const userMessages = [
        "Je me sens stressé aujourd'hui",
        "Comment puis-je améliorer mon humeur ?",
        "J'ai du mal à me concentrer au travail",
        "Je me sens anxieux avant les réunions",
        "Merci pour vos conseils, ça m'aide beaucoup"
      ];

      const aiResponses = [
        "Je comprends que vous vous sentiez stressé. C'est une réaction normale face aux défis de la vie. Parlons-en ensemble.",
        "Il existe plusieurs techniques efficaces pour améliorer l'humeur. Commençons par identifier ce qui vous préoccupe.",
        "Les difficultés de concentration peuvent avoir plusieurs causes. Explorons ensemble des stratégies pour vous aider.",
        "L'anxiété avant les réunions est très courante. Je vais vous partager quelques techniques de gestion du stress.",
        "Je suis ravi que mes conseils vous aident ! Continuons à travailler ensemble sur votre bien-être."
      ];

      for (let i = 0; i < count; i++) {
        const isUserMessage = i % 2 === 0;
        const timestamp = new Date();
        timestamp.setMinutes(timestamp.getMinutes() - (count - i) * 2);

        messages.push({
          id: `msg-${i}`,
          type: isUserMessage ? 'user' : 'ai',
          content: isUserMessage ? 
            userMessages[Math.floor(Math.random() * userMessages.length)] :
            aiResponses[Math.floor(Math.random() * aiResponses.length)],
          timestamp,
          mood: isUserMessage ? Math.floor(Math.random() * 5) + 3 : undefined,
          sentiment: isUserMessage ? 
            ['positive', 'negative', 'neutral'][Math.floor(Math.random() * 3)] as 'positive' | 'negative' | 'neutral' :
            undefined,
          suggestedActions: !isUserMessage ? [
            'Pratiquer la respiration profonde',
            'Faire une pause de 5 minutes',
            'Écrire dans votre journal'
          ] : undefined,
          emotionalIndicators: isUserMessage ? ['stress', 'fatigue', 'préoccupation'] : undefined
        });
      }

      return messages;
    };

    const generateMockStats = (): AICoachStats => ({
      totalSessions: 24,
      totalMessages: 186,
      averageSessionDuration: 18.5,
      moodImprovementRate: 78,
      favoriteTopics: ['Gestion du stress', 'Amélioration de l\'humeur', 'Confiance en soi'],
      lastActivity: new Date(),
      weeklyUsage: [3, 5, 2, 4, 6, 3, 4], // 7 derniers jours
      monthlyProgress: {
        sessionsCompleted: 12,
        goalsAchieved: 8,
        moodImprovement: 23
      }
    });

    setSessions(generateMockSessions());
    setStats(generateMockStats());
    setCurrentSession(generateMockSessions()[0]); // Première session comme active
  }, [isAuthenticated, user]);

  // Créer une nouvelle session
  const startSession = useCallback(async (theme: string, goal: string) => {
    setIsLoading(true);
    
    try {
      // Simulation d'appel API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newSession: AISession = {
        id: `session-${Date.now()}`,
        userId: user?.id || 'mock-user',
        startTime: new Date(),
        status: 'active',
        theme,
        goal,
        messages: [{
          id: 'welcome-msg',
          type: 'ai',
          content: `Bonjour ! Je suis votre coach IA personnel. Je vois que vous souhaitez travailler sur "${theme}". Comment vous sentez-vous en ce moment ?`,
          timestamp: new Date(),
          suggestedActions: [
            'Partagez votre humeur actuelle',
            'Décrivez votre situation',
            'Fixez un objectif pour cette session'
          ]
        }]
      };

      setCurrentSession(newSession);
      setSessions(prev => [newSession, ...prev]);
      
      return newSession;
    } catch (error) {
      console.error('Erreur création session:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Envoyer un message
  const sendMessage = useCallback(async (content: string, mood?: number) => {
    if (!currentSession) return;

    setIsTyping(true);

    try {
      // Ajouter le message utilisateur
      const userMessage: AIMessage = {
        id: `msg-${Date.now()}`,
        type: 'user',
        content,
        timestamp: new Date(),
        mood,
        sentiment: analyzeSentiment(content),
        emotionalIndicators: extractEmotionalIndicators(content)
      };

      setCurrentSession(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          messages: [...prev.messages, userMessage]
        };
      });

      // Simuler le délai de réponse de l'IA
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));

      // Générer la réponse de l'IA
      const aiResponse = await generateAIResponse(content, mood);

      setCurrentSession(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          messages: [...prev.messages, aiResponse]
        };
      });

    } catch (error) {
      console.error('Erreur envoi message:', error);
    } finally {
      setIsTyping(false);
    }
  }, [currentSession]);

  // Terminer une session
  const endSession = useCallback(async (feedback?: string, rating?: number) => {
    if (!currentSession) return;

    setIsLoading(true);

    try {
      const updatedSession: AISession = {
        ...currentSession,
        endTime: new Date(),
        status: 'completed',
        stats: {
          duration: Math.floor((Date.now() - currentSession.startTime.getTime()) / 60000),
          messageCount: currentSession.messages.length,
          moodChanges: calculateMoodChanges(currentSession.messages),
          actionsTaken: currentSession.messages.reduce((acc, msg) => 
            acc + (msg.suggestedActions?.length || 0), 0)
        }
      };

      setSessions(prev => prev.map(s => s.id === currentSession.id ? updatedSession : s));
      setCurrentSession(null);

      return updatedSession;
    } catch (error) {
      console.error('Erreur fin session:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [currentSession]);

  // Analyser le sentiment d'un message
  const analyzeSentiment = (text: string): 'positive' | 'negative' | 'neutral' => {
    const positiveWords = ['bien', 'heureux', 'content', 'motivé', 'confiant', 'optimiste', 'joyeux'];
    const negativeWords = ['mal', 'triste', 'stressé', 'anxieux', 'déprimé', 'fatigué', 'inquiet'];
    
    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  };

  // Extraire les indicateurs émotionnels
  const extractEmotionalIndicators = (text: string): string[] => {
    const indicators: { [key: string]: string[] } = {
      'stress': ['stressé', 'stress', 'pression', 'tendu'],
      'anxiété': ['anxieux', 'inquiet', 'nerveux', 'angoissé'],
      'fatigue': ['fatigué', 'épuisé', 'crevé', 'las'],
      'tristesse': ['triste', 'déprimé', 'mélancolique', 'abattu'],
      'colère': ['énervé', 'en colère', 'frustré', 'irrité'],
      'joie': ['heureux', 'joyeux', 'content', 'ravi'],
      'confiance': ['confiant', 'sûr', 'déterminé', 'motivé']
    };

    const lowerText = text.toLowerCase();
    const found: string[] = [];

    Object.entries(indicators).forEach(([emotion, words]) => {
      if (words.some(word => lowerText.includes(word))) {
        found.push(emotion);
      }
    });

    return found;
  };

  // Générer une réponse de l'IA
  const generateAIResponse = async (userMessage: string, mood?: number): Promise<AIMessage> => {
    const responses = {
      stress: [
        "Je comprends que vous ressentez du stress. C'est une réaction naturelle, et nous allons travailler ensemble pour le gérer.",
        "Le stress peut être accablant, mais il existe des techniques efficaces pour le réduire. Parlons de ce qui vous préoccupe.",
        "Prenons un moment pour respirer ensemble. Le stress est temporaire, et nous pouvons le surmonter."
      ],
      anxiété: [
        "L'anxiété est difficile à vivre, mais vous n'êtes pas seul(e). Je suis là pour vous accompagner.",
        "Votre anxiété est valide, et il est courageux de la partager. Explorons ensemble des moyens de la gérer.",
        "L'anxiété peut sembler écrasante, mais avec les bonnes techniques, elle devient gérable."
      ],
      tristesse: [
        "Je ressens votre tristesse, et c'est normal de se sentir ainsi parfois. Permettez-vous de ressentir ces émotions.",
        "La tristesse fait partie de l'expérience humaine. Vous êtes fort(e), et cette période passera.",
        "Merci de partager votre tristesse avec moi. Ensemble, nous pouvons trouver des moyens de vous sentir mieux."
      ],
      default: [
        "Merci de partager cela avec moi. Comment puis-je vous aider aujourd'hui ?",
        "Je vous écoute attentivement. Pouvez-vous m'en dire plus sur ce que vous ressentez ?",
        "Votre bien-être est important. Explorons ensemble comment améliorer votre situation."
      ]
    };

    const sentiment = analyzeSentiment(userMessage);
    const indicators = extractEmotionalIndicators(userMessage);
    
    let responseCategory = 'default';
    if (indicators.includes('stress')) responseCategory = 'stress';
    else if (indicators.includes('anxiété')) responseCategory = 'anxiété';
    else if (indicators.includes('tristesse')) responseCategory = 'tristesse';

    const responseOptions = responses[responseCategory as keyof typeof responses];
    const selectedResponse = responseOptions[Math.floor(Math.random() * responseOptions.length)];

    const suggestedActions = generateSuggestedActions(indicators, mood);

    return {
      id: `ai-msg-${Date.now()}`,
      type: 'ai',
      content: selectedResponse,
      timestamp: new Date(),
      suggestedActions,
      emotionalIndicators: indicators
    };
  };

  // Générer des actions suggérées
  const generateSuggestedActions = (indicators: string[], mood?: number): string[] => {
    const actions: { [key: string]: string[] } = {
      stress: [
        'Pratiquer 5 minutes de respiration profonde',
        'Faire une courte marche',
        'Écouter de la musique relaxante',
        'Pratiquer la méditation guidée'
      ],
      anxiété: [
        'Utiliser la technique 5-4-3-2-1 (grounding)',
        'Pratiquer la respiration carrée',
        'Écrire vos inquiétudes dans un journal',
        'Faire des étirements doux'
      ],
      fatigue: [
        'Prendre une pause de 10 minutes',
        'Boire un verre d\'eau',
        'Faire quelques exercices d\'étirement',
        'Planifier une pause plus longue'
      ],
      général: [
        'Tenir un journal de gratitude',
        'Pratiquer la pleine conscience',
        'Faire de l\'exercice physique',
        'Contacter un proche bienveillant'
      ]
    };

    let selectedActions: string[] = [];

    indicators.forEach(indicator => {
      if (actions[indicator]) {
        selectedActions = [...selectedActions, ...actions[indicator]];
      }
    });

    if (selectedActions.length === 0) {
      selectedActions = actions.général;
    }

    // Retourner 2-3 actions aléatoires
    const shuffled = selectedActions.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 2 + Math.floor(Math.random() * 2));
  };

  // Calculer les changements d'humeur
  const calculateMoodChanges = (messages: AIMessage[]): number => {
    const moodMessages = messages.filter(msg => msg.mood !== undefined);
    if (moodMessages.length < 2) return 0;

    let changes = 0;
    for (let i = 1; i < moodMessages.length; i++) {
      if (Math.abs(moodMessages[i].mood! - moodMessages[i-1].mood!) >= 1) {
        changes++;
      }
    }

    return changes;
  };

  // Obtenir l'analyse complète
  const getAnalysis = useCallback((sessionId: string): AIAnalysis | null => {
    const session = sessions.find(s => s.id === sessionId);
    if (!session) return null;

    const userMessages = session.messages.filter(msg => msg.type === 'user');
    const moodScores = userMessages.map(msg => msg.mood).filter(Boolean) as number[];
    const allIndicators = userMessages.flatMap(msg => msg.emotionalIndicators || []);

    const averageMood = moodScores.length > 0 ? 
      moodScores.reduce((sum, mood) => sum + mood, 0) / moodScores.length : 5;

    const sentiment: 'positive' | 'negative' | 'neutral' = 
      averageMood >= 7 ? 'positive' : averageMood <= 4 ? 'negative' : 'neutral';

    const urgencyLevel: 'low' | 'medium' | 'high' | 'crisis' = 
      averageMood <= 2 ? 'crisis' : 
      averageMood <= 4 ? 'high' : 
      averageMood <= 6 ? 'medium' : 'low';

    return {
      sentiment,
      emotionalState: [...new Set(allIndicators)],
      recommendedActions: generateSuggestedActions(allIndicators, averageMood),
      moodScore: averageMood,
      urgencyLevel,
      supportResources: urgencyLevel === 'crisis' ? [
        'Ligne d\'écoute 24h/24: 3114',
        'SOS Amitié: 09 72 39 40 50',
        'Urgences: 15 ou 112'
      ] : [
        'Exercices de relaxation',
        'Méditation guidée',
        'Ressources d\'auto-assistance'
      ]
    };
  }, [sessions]);

  return {
    // État
    currentSession,
    sessions,
    stats,
    isLoading,
    isTyping,

    // Actions
    startSession,
    sendMessage,
    endSession,
    getAnalysis,

    // Utilitaires
    analyzeSentiment,
    extractEmotionalIndicators
  };
}; 