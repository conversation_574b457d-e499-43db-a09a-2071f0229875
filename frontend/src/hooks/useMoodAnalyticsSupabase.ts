'use client';

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase/client';

// Hook Supabase pour Mood Analytics (Migration de useMoodAnalytics.ts)
export const useMoodAnalyticsSupabase = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Charger les analytics depuis Supabase
  const loadAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Chargement analytics humeur depuis Supabase...');

      const { data, error } = await supabase
        .from('mood_analytics')
        .select('*')
        .order('date', { ascending: false });

      if (error) throw error;

      console.log(`✅ ${data?.length || 0} points de données chargés`);

      // Calculer les analytics à partir des données
      const processedAnalytics = {
        overview: calculateOverview(data || []),
        chartData: {
          daily: data || [],
          weekly: calculateWeeklyData(data || []),
          monthly: calculateMonthlyData(data || [])
        },
        trends: calculateTrends(data || []),
        insights: generateInsights(data || [])
      };

      setAnalytics(processedAnalytics);

    } catch (err) {
      console.error('❌ Erreur analytics:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // Calculer les données de vue d'ensemble
  const calculateOverview = (data) => {
    if (!data.length) return { currentMood: 0, weeklyAverage: 0, monthlyAverage: 0 };

    const latest = data[0];
    const weekData = data.slice(0, 7);
    const monthData = data.slice(0, 30);

    return {
      currentMood: latest?.mood || 0,
      weeklyAverage: weekData.reduce((sum, d) => sum + (d.mood || 0), 0) / weekData.length,
      monthlyAverage: monthData.reduce((sum, d) => sum + (d.mood || 0), 0) / monthData.length,
      totalEntries: data.length,
      wellnessScore: latest?.wellness_score || 0
    };
  };

  // Calculer les données hebdomadaires
  const calculateWeeklyData = (data) => {
    const weeks = {};
    data.forEach(item => {
      const date = new Date(item.date);
      const weekKey = `Semaine ${Math.ceil(date.getDate() / 7)}`;
      if (!weeks[weekKey]) weeks[weekKey] = [];
      weeks[weekKey].push(item.mood);
    });

    return Object.entries(weeks).map(([week, moods]) => ({
      week,
      average: moods.reduce((sum, mood) => sum + mood, 0) / moods.length,
      count: moods.length
    }));
  };

  // Calculer les données mensuelles
  const calculateMonthlyData = (data) => {
    return []; // Implémentation simplifiée
  };

  // Calculer les tendances
  const calculateTrends = (data) => {
    if (data.length < 7) return [];

    const recentWeek = data.slice(0, 7);
    const previousWeek = data.slice(7, 14);

    if (previousWeek.length === 0) return [];

    const recentAvg = recentWeek.reduce((sum, d) => sum + d.mood, 0) / recentWeek.length;
    const previousAvg = previousWeek.reduce((sum, d) => sum + d.mood, 0) / previousWeek.length;
    const change = recentAvg - previousAvg;

    return [{
      period: 'weekly',
      direction: change > 0.3 ? 'improving' : change < -0.3 ? 'declining' : 'stable',
      change: Math.round(change * 10) / 10,
      confidence: 80
    }];
  };

  // Générer des insights
  const generateInsights = (data) => {
    const insights = [];

    if (data.length >= 7) {
      const avgMood = data.slice(0, 7).reduce((sum, d) => sum + d.mood, 0) / 7;
      if (avgMood > 7) {
        insights.push({
          type: 'positive',
          title: 'Excellente semaine !',
          description: 'Votre humeur moyenne cette semaine est très bonne.'
        });
      }
    }

    return insights;
  };

  useEffect(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  return {
    analytics,
    loading,
    error,
    loadAnalytics,
    isSupabase: true
  };
};
