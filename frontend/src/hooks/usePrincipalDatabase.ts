import { useState, useEffect, useCallback } from 'react';
import { createBrowserClient } from '@supabase/ssr';

/**
 * 🗄️ HOOK UNIFIÉ MINDFLOW PRO
 * 📅 Hook principal pour toutes les données
 */

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export function usePrincipalDatabase<T = any>(tableName: string) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // FETCH DATA
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      const { data: result, error: fetchError } = await supabase
        .from(tableName)
        .select('*')
        .order('created_at', { ascending: false });

      if (fetchError) throw fetchError;
      setData(result || []);
    } catch (err) {
      console.error('Erreur fetch:', err);
      setError(err instanceof Error ? err.message : 'Erreur');
    } finally {
      setLoading(false);
    }
  }, [tableName]);

  // CREATE
  const create = useCallback(async (newData: Partial<T>) => {
    try {
      const { data: result, error } = await supabase
        .from(tableName)
        .insert(newData)
        .select()
        .single();

      if (error) throw error;
      setData(prev => [result, ...prev]);
      return result;
    } catch (err) {
      console.error('Erreur création:', err);
      throw err;
    }
  }, [tableName]);

  // UPDATE
  const update = useCallback(async (id: string, updates: Partial<T>) => {
    try {
      const { data: result, error } = await supabase
        .from(tableName)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      setData(prev => prev.map(item => 
        (item as any).id === id ? result : item
      ));
      return result;
    } catch (err) {
      console.error('Erreur update:', err);
      throw err;
    }
  }, [tableName]);

  // DELETE
  const remove = useCallback(async (id: string) => {
    try {
      const { error } = await supabase
        .from(tableName)
        .delete()
        .eq('id', id);

      if (error) throw error;
      setData(prev => prev.filter(item => (item as any).id !== id));
    } catch (err) {
      console.error('Erreur suppression:', err);
      throw err;
    }
  }, [tableName]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, create, update, remove, refresh: fetchData };
}

// HOOKS SPÉCIALISÉS
export const useUsers = () => usePrincipalDatabase('users');
export const useAppointments = () => usePrincipalDatabase('appointments');
export const useProfessionals = () => usePrincipalDatabase('professionals');
export const useJournalEntries = () => usePrincipalDatabase('journal_entries');
export const useMoodAnalytics = () => usePrincipalDatabase('mood_analytics');
export const useNotifications = () => usePrincipalDatabase('smart_notifications');
