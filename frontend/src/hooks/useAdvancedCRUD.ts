
// Hook CRUD avancé généré automatiquement
import { useState, useCallback } from 'react';
import { supabase } from '../lib/supabase/client';

export const useAdvancedCRUD = (table: string) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const create = useCallback(async (data: any) => {
    setLoading(true);
    try {
      const { data: result, error } = await supabase
        .from(table)
        .insert(data)
        .select();
      
      if (error) throw error;
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [table]);
  
  // Plus de méthodes CRUD...
  
  return { create, loading, error };
};
