import { useState, useEffect, useCallback } from 'react';
import { getDataService, MoodEntry, JournalEntry } from '@/services/data-supabase';
import { useAuth } from '@/contexts/AuthContext';
import { FEATURE_FLAGS } from '@/lib/config/feature-flags';

export const useSupabaseData = () => {
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dataService = getDataService();

  const handleError = useCallback((error: any, action: string) => {
    console.error(`Erreur ${action}:`, error);
    setError(error instanceof Error ? error.message : `Erreur ${action}`);
  }, []);

  const clearError = useCallback(() => setError(null), []);

  return {
    loading,
    error,
    clearError,
    isSupabaseEnabled: FEATURE_FLAGS.USE_SUPABASE_DATABASE,
    dataService,
    handleError,
    setLoading,
    isAuthenticated,
    user,
  };
};

export const useMoodEntries = () => {
  const [moodEntries, setMoodEntries] = useState<MoodEntry[]>([]);
  const [stats, setStats] = useState<{
    averageMood: number;
    averageEnergy: number;
    averageStress: number;
    totalEntries: number;
  } | null>(null);
  
  const { loading, error, clearError, dataService, handleError, setLoading, user } = useSupabaseData();

  const loadMoodEntries = useCallback(async (limit = 50) => {
    if (!user || !dataService) return;
    
    setLoading(true);
    clearError();
    
    try {
      const entries = await dataService.getMoodEntries(user.user.id, limit);
      setMoodEntries(entries);
    } catch (error) {
      handleError(error, 'chargement mood entries');
    } finally {
      setLoading(false);
    }
  }, [user, dataService, setLoading, clearError, handleError]);

  const createMoodEntry = useCallback(async (moodData: Omit<MoodEntry, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user || !dataService) return null;
    
    setLoading(true);
    clearError();
    
    try {
      const newEntry = await dataService.createMoodEntry({
        ...moodData,
        user_id: user.user.id,
      });
      setMoodEntries(prev => [newEntry, ...prev]);
      return newEntry;
    } catch (error) {
      handleError(error, 'création mood entry');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user, dataService, setLoading, clearError, handleError]);

  const updateMoodEntry = useCallback(async (id: string, updates: Partial<MoodEntry>) => {
    if (!dataService) return null;
    
    setLoading(true);
    clearError();
    
    try {
      const updatedEntry = await dataService.updateMoodEntry(id, updates);
      setMoodEntries(prev => prev.map(entry => entry.id === id ? updatedEntry : entry));
      return updatedEntry;
    } catch (error) {
      handleError(error, 'mise à jour mood entry');
      return null;
    } finally {
      setLoading(false);
    }
  }, [dataService, setLoading, clearError, handleError]);

  const deleteMoodEntry = useCallback(async (id: string) => {
    if (!dataService) return false;
    
    setLoading(true);
    clearError();
    
    try {
      await dataService.deleteMoodEntry(id);
      setMoodEntries(prev => prev.filter(entry => entry.id !== id));
      return true;
    } catch (error) {
      handleError(error, 'suppression mood entry');
      return false;
    } finally {
      setLoading(false);
    }
  }, [dataService, setLoading, clearError, handleError]);

  const loadMoodStats = useCallback(async (days = 30) => {
    if (!user || !dataService) return;
    
    try {
      const moodStats = await dataService.getMoodStats(user.user.id, days);
      setStats(moodStats);
    } catch (error) {
      handleError(error, 'chargement statistiques');
    }
  }, [user, dataService, handleError]);

  useEffect(() => {
    if (user && dataService) {
      loadMoodEntries();
      loadMoodStats();
    }
  }, [user, dataService, loadMoodEntries, loadMoodStats]);

  return {
    moodEntries,
    stats,
    loading,
    error,
    clearError,
    loadMoodEntries,
    createMoodEntry,
    updateMoodEntry,
    deleteMoodEntry,
    loadMoodStats,
  };
};

export const useJournalEntries = () => {
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
  const { loading, error, clearError, dataService, handleError, setLoading, user } = useSupabaseData();

  const loadJournalEntries = useCallback(async (limit = 50) => {
    if (!user || !dataService) return;
    
    setLoading(true);
    clearError();
    
    try {
      const entries = await dataService.getJournalEntries(user.user.id, limit);
      setJournalEntries(entries);
    } catch (error) {
      handleError(error, 'chargement journal entries');
    } finally {
      setLoading(false);
    }
  }, [user, dataService, setLoading, clearError, handleError]);

  const getJournalEntry = useCallback(async (id: string) => {
    if (!dataService) return null;
    
    setLoading(true);
    clearError();
    
    try {
      const entry = await dataService.getJournalEntry(id);
      return entry;
    } catch (error) {
      handleError(error, 'récupération journal entry');
      return null;
    } finally {
      setLoading(false);
    }
  }, [dataService, setLoading, clearError, handleError]);

  const createJournalEntry = useCallback(async (journalData: Omit<JournalEntry, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user || !dataService) return null;
    
    setLoading(true);
    clearError();
    
    try {
      const newEntry = await dataService.createJournalEntry({
        ...journalData,
        user_id: user.user.id,
      });
      setJournalEntries(prev => [newEntry, ...prev]);
      return newEntry;
    } catch (error) {
      handleError(error, 'création journal entry');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user, dataService, setLoading, clearError, handleError]);

  const updateJournalEntry = useCallback(async (id: string, updates: Partial<JournalEntry>) => {
    if (!dataService) return null;
    
    setLoading(true);
    clearError();
    
    try {
      const updatedEntry = await dataService.updateJournalEntry(id, updates);
      setJournalEntries(prev => prev.map(entry => entry.id === id ? updatedEntry : entry));
      return updatedEntry;
    } catch (error) {
      handleError(error, 'mise à jour journal entry');
      return null;
    } finally {
      setLoading(false);
    }
  }, [dataService, setLoading, clearError, handleError]);

  const deleteJournalEntry = useCallback(async (id: string) => {
    if (!dataService) return false;
    
    setLoading(true);
    clearError();
    
    try {
      await dataService.deleteJournalEntry(id);
      setJournalEntries(prev => prev.filter(entry => entry.id !== id));
      return true;
    } catch (error) {
      handleError(error, 'suppression journal entry');
      return false;
    } finally {
      setLoading(false);
    }
  }, [dataService, setLoading, clearError, handleError]);

  useEffect(() => {
    if (user && dataService) {
      loadJournalEntries();
    }
  }, [user, dataService, loadJournalEntries]);

  return {
    journalEntries,
    loading,
    error,
    clearError,
    loadJournalEntries,
    getJournalEntry,
    createJournalEntry,
    updateJournalEntry,
    deleteJournalEntry,
  };
};

export const useRealtimeSubscriptions = () => {
  const { dataService, user } = useSupabaseData();
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!dataService || !user || !FEATURE_FLAGS.ENABLE_REAL_TIME) return;

    const moodSubscription = dataService.subscribeToMoodEntries(user.user.id, (payload) => {
      console.log('Mood entries realtime update:', payload);
      setIsConnected(true);
    });

    const journalSubscription = dataService.subscribeToJournalEntries(user.user.id, (payload) => {
      console.log('Journal entries realtime update:', payload);
      setIsConnected(true);
    });

    return () => {
      moodSubscription.unsubscribe();
      journalSubscription.unsubscribe();
      setIsConnected(false);
    };
  }, [dataService, user]);

  return {
    isConnected,
    realtimeEnabled: FEATURE_FLAGS.ENABLE_REAL_TIME,
  };
}; 