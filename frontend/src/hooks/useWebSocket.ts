'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { websocketService, ConnectionStatus } from '@/services/websocketService';
import { useAuthStore } from '@/stores/authStore';
import { toast } from 'sonner';
import AuthService from '../services/auth-service';

// Notification types for UI feedback
export interface NotificationState {
  appointments: number;
  messages: number;
  crisisAlerts: number;
  total: number;
}

export interface WebSocketHookReturn {
  isConnected: boolean;
  connectionStatus: ConnectionStatus;
  notifications: NotificationState;
  connect: () => Promise<void>;
  disconnect: () => void;
  clearNotifications: (type?: keyof NotificationState) => void;
}

interface WebSocketNotifications {
  messages?: number;
  appointments?: number;
  alerts?: number;
}

export const useWebSocket = (): WebSocketHookReturn => {
  const { user, accessToken } = useAuthStore();
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [notifications, setNotifications] = useState<NotificationState>({
    appointments: 0,
    messages: 0,
    crisisAlerts: 0,
    total: 0,
  });
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [connected, setConnected] = useState(false);

  const isConnected = connectionStatus === 'authenticated';
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  // Update notification counts
  const updateNotificationCount = useCallback((type: keyof NotificationState, increment: number = 1) => {
    setNotifications(prev => {
      const newCount = Math.max(0, prev[type] + increment);
      const newTotal = Object.keys(prev)
        .filter(key => key !== 'total')
        .reduce((sum, key) => {
          const value = key === type ? newCount : prev[key as keyof NotificationState];
          return sum + (typeof value === 'number' ? value : 0);
        }, 0);

      return {
        ...prev,
        [type]: newCount,
        total: newTotal,
      };
    });
  }, []);

  // Clear notifications
  const clearNotifications = useCallback((type?: keyof NotificationState) => {
    if (type && type !== 'total') {
      updateNotificationCount(type, -notifications[type]);
    } else {
      setNotifications({
        appointments: 0,
        messages: 0,
        crisisAlerts: 0,
        total: 0,
      });
    }
  }, [notifications, updateNotificationCount]);

  // Connect to WebSocket
  const connect = useCallback(async () => {
    if (!accessToken || !user) {
      console.warn('Cannot connect WebSocket: No token or user');
      return;
    }

    try {
      // Vérifier si nous sommes dans un environnement navigateur
      if (typeof window === 'undefined') return;

      // Créer la connexion WebSocket
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:4000';
      const ws = new WebSocket(`${wsUrl}/notifications`);

      // Ajouter le token d'authentification à la connexion
      ws.onopen = () => {
        console.log('WebSocket connecté');
        setConnected(true);
        ws.send(JSON.stringify({
          type: 'auth',
          token: accessToken
        }));
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'notifications') {
            setNotifications(data.payload);
          }
        } catch (error) {
          console.error('Erreur de parsing WebSocket:', error);
        }
      };

      ws.onclose = () => {
        console.log('WebSocket déconnecté');
        setConnected(false);
      };

      ws.onerror = (error) => {
        console.error('Erreur WebSocket:', error);
      };

      setSocket(ws);

      // Setup event listeners
      useEffect(() => {
        if (!user) return;

        // Connection status listener
        const handleConnectionStatusChange = (status: ConnectionStatus) => {
          setConnectionStatus(status);
          
          if (status === 'authenticated') {
            toast.success('Connected to real-time updates');
          } else if (status === 'error') {
            toast.error('Connection error - retrying...');
          } else if (status === 'disconnected') {
            toast.warning('Disconnected from real-time updates');
          }
        };

        // Appointment event listeners
        const handleAppointmentReminder = (data: any) => {
          updateNotificationCount('appointments');
          
          const timeMap = {
            '15min': '15 minutes',
            '1hour': '1 hour',
            '24hour': '24 hours'
          };
          
          toast.info(
            `Appointment reminder: ${data.professionalName} in ${timeMap[data.reminderType]}`,
            {
              duration: 10000,
              action: {
                label: 'View',
                onClick: () => {
                  window.location.href = '/appointments';
                },
              },
            }
          );
        };

        const handleAppointmentStatusUpdate = (data: any) => {
          updateNotificationCount('appointments');
          
          const statusMessages = {
            confirmed: 'Your appointment has been confirmed',
            cancelled: 'Your appointment has been cancelled',
            rescheduled: 'Your appointment has been rescheduled',
            completed: 'Your appointment has been completed'
          };
          
          toast.info(
            `${statusMessages[data.status]} with ${data.professionalName}`,
            {
              duration: 8000,
              action: {
                label: 'View Details',
                onClick: () => {
                  window.location.href = '/appointments';
                },
              },
            }
          );
        };

        const handleAppointmentBookingConfirmation = (data: any) => {
          updateNotificationCount('appointments');
          
          toast.success(
            `Appointment booked with ${data.professionalName}`,
            {
              description: `Confirmation: ${data.confirmationNumber}`,
              duration: 8000,
              action: {
                label: 'View',
                onClick: () => {
                  window.location.href = '/appointments';
                },
              },
            }
          );
        };

        const handleProfessionalAvailabilityChange = (data: any) => {
          if (data.isOnline) {
            toast.info(`${data.professionalName} is now available`, {
              duration: 5000,
            });
          }
        };

        // Crisis alert listeners
        const handleCrisisAlert = (data: any) => {
          updateNotificationCount('crisisAlerts');
          
          const severityColors = {
            low: 'info',
            medium: 'warning',
            high: 'error',
            critical: 'error'
          };
          
          toast.error(
            'Crisis Support Available',
            {
              description: data.message,
              duration: 15000,
              action: {
                label: 'Get Help',
                onClick: () => {
                  // Show crisis resources
                  window.location.href = '#crisis-resources';
                },
              },
            }
          );
        };

        const handleEmergencyContactNotification = (data: any) => {
          updateNotificationCount('crisisAlerts');
          
          toast.error(
            `Emergency Contact: ${data.contactName}`,
            {
              description: data.message,
              duration: 20000,
            }
          );
        };

        // Messaging listeners
        const handleNewMessage = (data: any) => {
          updateNotificationCount('messages');
          
          toast.info(
            `New message from ${data.senderName}`,
            {
              description: data.content.substring(0, 100) + (data.content.length > 100 ? '...' : ''),
              duration: 6000,
              action: {
                label: 'Reply',
                onClick: () => {
                  window.location.href = `/messages/${data.conversationId}`;
                },
              },
            }
          );
        };

        const handleUnreadCountUpdate = (data: any) => {
          setNotifications(prev => ({
            ...prev,
            messages: data.totalUnreadCount,
            total: prev.appointments + prev.crisisAlerts + data.totalUnreadCount,
          }));
        };

        // Dashboard update listeners
        const handleDashboardUpdate = (data: any) => {
          if (data.type === 'initial') {
            console.log('📊 Dashboard connected to real-time updates');
          }
          // Trigger dashboard refresh if needed
          window.dispatchEvent(new CustomEvent('dashboard-update', { detail: data }));
        };

        const handleMoodTrackingUpdate = (data: any) => {
          toast.success('Mood entry saved successfully', {
            duration: 3000,
          });
          // Trigger mood chart refresh
          window.dispatchEvent(new CustomEvent('mood-update', { detail: data }));
        };

        const handleWellnessProgressUpdate = (data: any) => {
          if (data.completed) {
            toast.success(`Congratulations! You completed ${data.moduleName}`, {
              duration: 8000,
            });
          } else {
            toast.info(`Progress updated: ${data.moduleName} (${data.progress}%)`, {
              duration: 4000,
            });
          }
          // Trigger progress chart refresh
          window.dispatchEvent(new CustomEvent('wellness-progress-update', { detail: data }));
        };

        // Register event listeners
        websocketService.on('connection_status_change', handleConnectionStatusChange);
        websocketService.on('appointment_reminder', handleAppointmentReminder);
        websocketService.on('appointment_status_update', handleAppointmentStatusUpdate);
        websocketService.on('appointment_booking_confirmation', handleAppointmentBookingConfirmation);
        websocketService.on('professional_availability_change', handleProfessionalAvailabilityChange);
        websocketService.on('crisis_alert', handleCrisisAlert);
        websocketService.on('emergency_contact_notification', handleEmergencyContactNotification);
        websocketService.on('new_message', handleNewMessage);
        websocketService.on('unread_count_update', handleUnreadCountUpdate);
        websocketService.on('dashboard_update', handleDashboardUpdate);
        websocketService.on('mood_tracking_update', handleMoodTrackingUpdate);
        websocketService.on('wellness_progress_update', handleWellnessProgressUpdate);
      }, [user, token, updateNotificationCount]);

      // Auto-connect if user is authenticated
      if (token && user) {
        connect();
      }

      // Cleanup function
      return () => {
        websocketService.off('connection_status_change', handleConnectionStatusChange);
        websocketService.off('appointment_reminder', handleAppointmentReminder);
        websocketService.off('appointment_status_update', handleAppointmentStatusUpdate);
        websocketService.off('appointment_booking_confirmation', handleAppointmentBookingConfirmation);
        websocketService.off('professional_availability_change', handleProfessionalAvailabilityChange);
        websocketService.off('crisis_alert', handleCrisisAlert);
        websocketService.off('emergency_contact_notification', handleEmergencyContactNotification);
        websocketService.off('new_message', handleNewMessage);
        websocketService.off('unread_count_update', handleUnreadCountUpdate);
        websocketService.off('dashboard_update', handleDashboardUpdate);
        websocketService.off('mood_tracking_update', handleMoodTrackingUpdate);
        websocketService.off('wellness_progress_update', handleWellnessProgressUpdate);
      };
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      toast.error('Failed to connect to real-time updates');
    }
  }, [token, user]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    websocketService.disconnect();
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  const sendMessage = (message: any) => {
    if (socket && connected) {
      socket.send(JSON.stringify(message));
    }
  };

  return {
    isConnected,
    connectionStatus,
    notifications,
    connect,
    disconnect,
    clearNotifications,
    sendMessage,
  };
};
