import { useState, useCallback, useMemo, useEffect } from 'react';
import { createSupabaseClient } from '@/lib/supabase/browser-client';

// Types (adaptés pour Supabase)
export interface Appointment {
  id: string;
  professional_id: string;
  professional_name: string;
  professional_role: string;
  professional_avatar?: string;
  client_id: string;
  client_name: string;
  appointment_date: string; // Format YYYY-MM-DD
  appointment_time: string; // Format HH:MM
  duration_minutes: number;
  type: 'video' | 'in-person' | 'chat';
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  price: number;
  currency: string;
  notes?: string;
  rating?: number;
  feedback?: string;
  reminder_sent: boolean;
  cancel_reason?: string;
  meeting_link?: string;
  location?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface AppointmentStats {
  total: number;
  upcoming: number;
  completed: number;
  cancelled: number;
  noShow: number;
  totalRevenue: number;
  averageRating: number;
  weeklyAppointments: number;
  monthlyAppointments: number;
}

export interface CreateAppointmentData {
  professional_id: string;
  appointment_date: string;
  appointment_time: string;
  duration_minutes: number;
  type: 'video' | 'in-person' | 'chat';
  notes?: string;
}

export interface UpdateAppointmentData {
  appointment_date?: string;
  appointment_time?: string;
  duration_minutes?: number;
  type?: 'video' | 'in-person' | 'chat';
  status?: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  notes?: string;
  rating?: number;
  feedback?: string;
  cancel_reason?: string;
}

// Hook principal avec Supabase
export const useAppointmentsSupabase = () => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createSupabaseClient();

  // Charger les rendez-vous depuis Supabase
  const loadAppointments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: supabaseError } = await supabase
        .from('appointments')
        .select('*')
        .order('appointment_date', { ascending: false })
        .order('appointment_time', { ascending: false });

      if (supabaseError) {
        throw supabaseError;
      }

      setAppointments(data || []);
    } catch (err) {
      console.error('Erreur lors du chargement des rendez-vous:', err);
      setError('Erreur lors du chargement des rendez-vous');
      
      // En cas d'erreur, charger des données de démonstration
      setAppointments(await generateDemoData());
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  // Charger les données au montage du composant
  useEffect(() => {
    loadAppointments();
  }, [loadAppointments]);

  // Créer un nouveau rendez-vous
  const createAppointment = useCallback(async (data: CreateAppointmentData) => {
    setLoading(true);
    setError(null);

    try {
      // Récupérer les informations du professionnel
      const { data: professional, error: profError } = await supabase
        .from('professionals')
        .select('name, role, avatar_url, price_per_session')
        .eq('id', data.professional_id)
        .single();

      if (profError) throw profError;

      const appointmentData = {
        professional_id: data.professional_id,
        professional_name: professional.name,
        professional_role: professional.role,
        professional_avatar: professional.avatar_url,
        client_id: 'demo-user-1', // À remplacer par l'auth réel
        client_name: 'Utilisateur Test',
        appointment_date: data.appointment_date,
        appointment_time: data.appointment_time,
        duration_minutes: data.duration_minutes,
        type: data.type,
        status: 'scheduled' as const,
        price: professional.price_per_session || 80,
        currency: 'EUR',
        notes: data.notes,
        reminder_sent: false,
        meeting_link: data.type === 'video' ? `https://meet.mindflow.com/session-${Date.now()}` : null,
      };

      const { data: newAppointment, error: insertError } = await supabase
        .from('appointments')
        .insert([appointmentData])
        .select()
        .single();

      if (insertError) throw insertError;

      // Mettre à jour l'état local
      setAppointments(prev => [newAppointment, ...prev]);
      return newAppointment;

    } catch (err) {
      console.error('Erreur lors de la création du rendez-vous:', err);
      setError('Erreur lors de la création du rendez-vous');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  // Mettre à jour un rendez-vous
  const updateAppointment = useCallback(async (id: string, updateData: UpdateAppointmentData) => {
    setLoading(true);
    setError(null);

    try {
      const { data: updatedAppointment, error: updateError } = await supabase
        .from('appointments')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (updateError) throw updateError;

      // Mettre à jour l'état local
      setAppointments(prev => prev.map(appointment => 
        appointment.id === id ? updatedAppointment : appointment
      ));

      return updatedAppointment;

    } catch (err) {
      console.error('Erreur lors de la mise à jour du rendez-vous:', err);
      setError('Erreur lors de la mise à jour du rendez-vous');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  // Annuler un rendez-vous
  const cancelAppointment = useCallback(async (id: string, reason: string) => {
    await updateAppointment(id, {
      status: 'cancelled',
      cancel_reason: reason
    });
  }, [updateAppointment]);

  // Confirmer un rendez-vous
  const confirmAppointment = useCallback(async (id: string) => {
    await updateAppointment(id, {
      status: 'confirmed'
    });
  }, [updateAppointment]);

  // Marquer comme complété avec évaluation
  const completeAppointment = useCallback(async (id: string, rating: number, feedback?: string) => {
    await updateAppointment(id, {
      status: 'completed',
      rating,
      feedback
    });
  }, [updateAppointment]);

  // Calculer les statistiques
  const stats = useMemo<AppointmentStats>(() => {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const upcomingAppointments = appointments.filter(a => {
      const appointmentDate = new Date(`${a.appointment_date} ${a.appointment_time}`);
      return appointmentDate > now && (a.status === 'scheduled' || a.status === 'confirmed');
    });

    const completedAppointments = appointments.filter(a => a.status === 'completed');
    const cancelledAppointments = appointments.filter(a => a.status === 'cancelled');
    const noShowAppointments = appointments.filter(a => a.status === 'no-show');

    const weeklyAppointments = appointments.filter(a => {
      const appointmentDate = new Date(a.appointment_date);
      return appointmentDate >= oneWeekAgo && a.status === 'completed';
    });

    const monthlyAppointments = appointments.filter(a => {
      const appointmentDate = new Date(a.appointment_date);
      return appointmentDate >= oneMonthAgo && a.status === 'completed';
    });

    const totalRevenue = completedAppointments.reduce((sum, a) => sum + a.price, 0);
    
    const ratingsSum = completedAppointments
      .filter(a => a.rating)
      .reduce((sum, a) => sum + (a.rating || 0), 0);
    
    const ratingsCount = completedAppointments.filter(a => a.rating).length;
    const averageRating = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

    return {
      total: appointments.length,
      upcoming: upcomingAppointments.length,
      completed: completedAppointments.length,
      cancelled: cancelledAppointments.length,
      noShow: noShowAppointments.length,
      totalRevenue,
      averageRating: Math.round(averageRating * 10) / 10,
      weeklyAppointments: weeklyAppointments.length,
      monthlyAppointments: monthlyAppointments.length
    };
  }, [appointments]);

  // Obtenir les prochains rendez-vous
  const getUpcomingAppointments = useCallback((limit?: number) => {
    const now = new Date();
    const upcoming = appointments
      .filter(a => {
        const appointmentDate = new Date(`${a.appointment_date} ${a.appointment_time}`);
        return appointmentDate > now && (a.status === 'scheduled' || a.status === 'confirmed');
      })
      .sort((a, b) => {
        const dateA = new Date(`${a.appointment_date} ${a.appointment_time}`);
        const dateB = new Date(`${b.appointment_date} ${b.appointment_time}`);
        return dateA.getTime() - dateB.getTime();
      });

    return limit ? upcoming.slice(0, limit) : upcoming;
  }, [appointments]);

  // Obtenir les rendez-vous par professionnel
  const getAppointmentsByProfessional = useCallback((professionalId: string) => {
    return appointments.filter(a => a.professional_id === professionalId);
  }, [appointments]);

  // Obtenir les rendez-vous par statut
  const getAppointmentsByStatus = useCallback((status: Appointment['status']) => {
    return appointments.filter(a => a.status === status);
  }, [appointments]);

  return {
    appointments: appointments.map(transformForDisplay),
    stats,
    loading,
    error,
    createAppointment,
    updateAppointment,
    cancelAppointment,
    confirmAppointment,
    completeAppointment,
    getUpcomingAppointments,
    getAppointmentsByProfessional,
    getAppointmentsByStatus,
    refreshAppointments: loadAppointments
  };
};

// Fonction pour transformer les données Supabase vers le format d'affichage
function transformForDisplay(appointment: Appointment) {
  return {
    id: appointment.id,
    professionalId: appointment.professional_id,
    professionalName: appointment.professional_name,
    professionalRole: appointment.professional_role,
    professionalAvatar: appointment.professional_avatar,
    clientId: appointment.client_id,
    clientName: appointment.client_name,
    date: appointment.appointment_date,
    time: appointment.appointment_time,
    duration: appointment.duration_minutes,
    type: appointment.type,
    status: appointment.status,
    price: appointment.price,
    currency: appointment.currency,
    notes: appointment.notes,
    rating: appointment.rating,
    feedback: appointment.feedback,
    reminderSent: appointment.reminder_sent,
    cancelReason: appointment.cancel_reason,
    meetingLink: appointment.meeting_link,
    location: appointment.location,
    createdAt: appointment.created_at,
    updatedAt: appointment.updated_at
  };
}

// Générer des données de démonstration
async function generateDemoData(): Promise<Appointment[]> {
  const appointments: Appointment[] = [];
  const now = new Date();

  for (let i = 0; i < 5; i++) {
    const daysOffset = Math.floor(Math.random() * 30) - 15;
    const appointmentDate = new Date(now);
    appointmentDate.setDate(now.getDate() + daysOffset);

    appointments.push({
      id: `demo-apt-${i + 1}`,
      professional_id: `prof-${i + 1}`,
      professional_name: `Dr. Professionnel ${i + 1}`,
      professional_role: 'Psychologue',
      client_id: 'demo-user-1',
      client_name: 'Utilisateur Démo',
      appointment_date: appointmentDate.toISOString().split('T')[0],
      appointment_time: '14:00',
      duration_minutes: 60,
      type: 'video',
      status: 'scheduled',
      price: 80,
      currency: 'EUR',
      reminder_sent: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      metadata: {}
    });
  }

  return appointments;
} 