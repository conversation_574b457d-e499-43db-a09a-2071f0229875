'use client';

import { useState, useEffect, useCallback } from 'react';

// Types pour l'analyse d'humeur (réutilisés)
export interface MoodDataPoint {
  id: string;
  date: string;
  mood: number;
  energy: number;
  stress: number;
  anxiety: number;
  sleep: number;
  factors: string[];
  notes?: string;
  weather?: string;
  activities?: string[];
}

export interface MoodTrend {
  period: 'daily' | 'weekly' | 'monthly';
  direction: 'improving' | 'declining' | 'stable';
  change: number;
  confidence: number;
  startValue: number;
  endValue: number;
  significance: 'low' | 'medium' | 'high';
}

export interface MoodPrediction {
  nextDay: number;
  nextWeek: number;
  confidence: number;
  factors: string[];
  recommendations: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

export interface MoodAnalytics {
  overview: {
    currentMood: number;
    weeklyAverage: number;
    monthlyAverage: number;
    bestDay: { date: string; mood: number };
    worstDay: { date: string; mood: number };
    streakDays: number;
    totalEntries: number;
  };
  trends: MoodTrend[];
  predictions: MoodPrediction;
  chartData: {
    daily: MoodDataPoint[];
    weekly: { week: string; average: number; count: number }[];
    monthly: { month: string; average: number; count: number }[];
    factors: { factor: string; impact: number; frequency: number }[];
  };
}

// Hook de test simplifié pour l'analyse d'humeur (sans authentification)
export const useMoodAnalyticsTest = () => {
  const [analytics, setAnalytics] = useState<MoodAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Générer des données simulées simplifiées
  const generateTestData = useCallback((): MoodAnalytics => {
    const today = new Date();
    const daysBack = 14; // 2 semaines de données
    
    // Générer des points de données d'humeur
    const dailyData: MoodDataPoint[] = [];
    
    for (let i = daysBack; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      const mood = 6 + Math.random() * 3; // 6-9
      const energy = mood + (Math.random() - 0.5);
      const stress = 10 - mood + Math.random();
      
      dailyData.push({
        id: `test-mood-${i}`,
        date: date.toISOString().split('T')[0],
        mood: Math.round(mood * 10) / 10,
        energy: Math.round(energy * 10) / 10,
        stress: Math.round(stress * 10) / 10,
        anxiety: Math.round(stress * 0.8 * 10) / 10,
        sleep: 7 + Math.random() * 2,
        factors: ['travail', 'sommeil'],
        activities: ['repos'],
        weather: 'ensoleillé'
      });
    }

    // Calculer les tendances
    const weeklyTrend: MoodTrend = {
      period: 'weekly',
      direction: 'improving',
      change: 0.5,
      confidence: 80,
      startValue: 7.0,
      endValue: 7.5,
      significance: 'medium'
    };

    // Prédictions simples
    const predictions: MoodPrediction = {
      nextDay: 7.8,
      nextWeek: 7.5,
      confidence: 85,
      factors: ['sommeil', 'stress'],
      recommendations: ['Maintenir routine', 'Continuer méditation'],
      riskLevel: 'low'
    };

    // Données de graphiques
    const weeklyData = [
      { week: 'Semaine 1', average: 7.2, count: 7 },
      { week: 'Semaine 2', average: 7.6, count: 7 }
    ];

    const monthlyData = [
      { month: 'Déc 2024', average: 7.1, count: 30 },
      { month: 'Jan 2025', average: 7.4, count: 31 }
    ];

    const factorData = [
      { factor: 'sommeil', impact: 0.8, frequency: 90 },
      { factor: 'exercice', impact: 0.6, frequency: 70 },
      { factor: 'stress', impact: -0.7, frequency: 60 }
    ];

    // Overview
    const currentMood = dailyData[dailyData.length - 1]?.mood || 7.5;
    const weeklyMoods = dailyData.slice(-7).map(d => d.mood);
    const weeklyAverage = weeklyMoods.reduce((sum, mood) => sum + mood, 0) / weeklyMoods.length;

    return {
      overview: {
        currentMood: Math.round(currentMood * 10) / 10,
        weeklyAverage: Math.round(weeklyAverage * 10) / 10,
        monthlyAverage: 7.3,
        bestDay: { date: dailyData[10]?.date || '2025-01-15', mood: 8.5 },
        worstDay: { date: dailyData[3]?.date || '2025-01-08', mood: 6.2 },
        streakDays: 5,
        totalEntries: dailyData.length
      },
      trends: [weeklyTrend],
      predictions,
      chartData: {
        daily: dailyData,
        weekly: weeklyData,
        monthly: monthlyData,
        factors: factorData
      }
    };
  }, []);

  // Charger les données automatiquement
  useEffect(() => {
    setIsLoading(true);
    
    const timer = setTimeout(() => {
      setAnalytics(generateTestData());
      setIsLoading(false);
    }, 500); // Délai plus court pour les tests

    return () => clearTimeout(timer);
  }, [generateTestData]);

  // Calculer le score de bien-être
  const getWellnessScore = useCallback(() => {
    if (!analytics) return 0;
    return Math.round((analytics.overview.currentMood / 10) * 100);
  }, [analytics]);

  // Obtenir les tendances par direction
  const getTrendsByDirection = useCallback((direction: 'improving' | 'declining' | 'stable') => {
    return analytics?.trends.filter(trend => trend.direction === direction) || [];
  }, [analytics]);

  return {
    analytics,
    isLoading,
    getWellnessScore,
    getTrendsByDirection
  };
}; 