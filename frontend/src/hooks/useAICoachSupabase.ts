'use client';

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase/client';

// Hook Supabase pour AI Coach (Migration de useAICoach.ts)
export const useAICoachSupabase = () => {
  const [sessions, setSessions] = useState([]);
  const [currentSession, setCurrentSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Charger les sessions depuis Supabase
  const loadSessions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Chargement sessions IA depuis Supabase...');

      const { data, error } = await supabase
        .from('ai_coaching_sessions')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      console.log(`✅ ${data?.length || 0} sessions IA chargées`);
      setSessions(data || []);

      // Trouver la session active
      const activeSession = data?.find(s => s.status === 'active');
      setCurrentSession(activeSession || null);

    } catch (err) {
      console.error('❌ Erreur sessions IA:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // Créer une nouvelle session
  const startSession = useCallback(async (theme, goal) => {
    try {
      console.log('🚀 Création nouvelle session IA...');

      const { data, error } = await supabase
        .from('ai_coaching_sessions')
        .insert({
          theme,
          goal,
          status: 'active',
          messages: [],
          mood_analysis: {},
          stats: {}
        })
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Session IA créée:', data.id);
      setCurrentSession(data);
      await loadSessions();

      return data;
    } catch (err) {
      console.error('❌ Erreur création session:', err);
      throw err;
    }
  }, [loadSessions]);

  useEffect(() => {
    loadSessions();
  }, [loadSessions]);

  return {
    sessions,
    currentSession,
    loading,
    error,
    loadSessions,
    startSession,
    isSupabase: true
  };
};
