import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export interface MoodEntry {
  id: string;
  date: string;
  mood: 1 | 2 | 3 | 4 | 5;
  energy: 1 | 2 | 3 | 4 | 5;
  stress: 1 | 2 | 3 | 4 | 5;
  sleep: number; // heures
  notes?: string;
  tags: string[];
}

export interface Objective {
  id: string;
  title: string;
  description: string;
  category: 'wellness' | 'productivity' | 'social' | 'health';
  progress: number; // 0-100
  deadline?: string;
  status: 'active' | 'completed' | 'paused';
  createdAt: string;
}

export interface AIInsight {
  id: string;
  type: 'suggestion' | 'warning' | 'achievement' | 'trend';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  category: string;
  actionable: boolean;
  createdAt: string;
  readAt?: string;
}

export interface BiometricData {
  id: string;
  type: 'heart_rate' | 'steps' | 'calories' | 'sleep_duration' | 'activity_minutes';
  value: number;
  unit: string;
  timestamp: string;
  source?: string;
}

export interface WellnessStats {
  currentMood: number;
  weeklyMoodAverage: number;
  monthlyMoodTrend: 'up' | 'down' | 'stable';
  stressLevel: number;
  energyLevel: number;
  sleepQuality: number;
  totalObjectives: number;
  completedObjectives: number;
  activePrograms: number;
  streakDays: number;
}

export interface DashboardState {
  // Données principales
  moodEntries: MoodEntry[];
  objectives: Objective[];
  aiInsights: AIInsight[];
  biometricData: BiometricData[];
  wellnessStats: WellnessStats | null;
  
  // État de l'interface
  isLoading: boolean;
  error: string | null;
  lastSync: string | null;
  
  // Filtres et vues
  dateRange: {
    start: string;
    end: string;
  };
  selectedCategories: string[];
  viewMode: 'daily' | 'weekly' | 'monthly';
  
  // Actions de données
  fetchDashboardData: () => Promise<void>;
  addMoodEntry: (entry: Omit<MoodEntry, 'id'>) => Promise<void>;
  updateObjective: (id: string, updates: Partial<Objective>) => Promise<void>;
  markInsightAsRead: (id: string) => void;
  syncBiometricData: () => Promise<void>;
  
  // Actions d'interface
  setDateRange: (start: string, end: string) => void;
  setViewMode: (mode: 'daily' | 'weekly' | 'monthly') => void;
  setSelectedCategories: (categories: string[]) => void;
  clearError: () => void;
  
  // Calculateurs
  getMoodTrend: (days: number) => 'up' | 'down' | 'stable';
  getProgressForPeriod: (start: string, end: string) => number;
  getBurnoutRisk: () => 'low' | 'medium' | 'high';
}

const useDashboardStore = create<DashboardState>()(
  devtools(
    immer((set, get) => ({
      // État initial
      moodEntries: [],
      objectives: [],
      aiInsights: [],
      biometricData: [],
      wellnessStats: null,
      isLoading: false,
      error: null,
      lastSync: null,
      
      // Filtres par défaut
      dateRange: {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        end: new Date().toISOString(),
      },
      selectedCategories: [],
      viewMode: 'weekly',

      // Actions de données
      fetchDashboardData: async () => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const authStore = (await import('./authStore')).default;
          const { accessToken } = authStore.getState();

          const [moodRes, objectivesRes, insightsRes, biometricRes, statsRes] = await Promise.all([
            fetch(`${process.env.NEXT_PUBLIC_API_URL}/dashboard/mood-entries`, {
              headers: { Authorization: `Bearer ${accessToken}` },
            }),
            fetch(`${process.env.NEXT_PUBLIC_API_URL}/dashboard/objectives`, {
              headers: { Authorization: `Bearer ${accessToken}` },
            }),
            fetch(`${process.env.NEXT_PUBLIC_API_URL}/dashboard/ai-insights`, {
              headers: { Authorization: `Bearer ${accessToken}` },
            }),
            fetch(`${process.env.NEXT_PUBLIC_API_URL}/dashboard/biometric-data`, {
              headers: { Authorization: `Bearer ${accessToken}` },
            }),
            fetch(`${process.env.NEXT_PUBLIC_API_URL}/dashboard/wellness-stats`, {
              headers: { Authorization: `Bearer ${accessToken}` },
            }),
          ]);

          const [moodEntries, objectives, aiInsights, biometricData, wellnessStats] = await Promise.all([
            moodRes.json(),
            objectivesRes.json(),
            insightsRes.json(),
            biometricRes.json(),
            statsRes.json(),
          ]);

          set((state) => {
            state.moodEntries = moodEntries;
            state.objectives = objectives;
            state.aiInsights = aiInsights;
            state.biometricData = biometricData;
            state.wellnessStats = wellnessStats;
            state.lastSync = new Date().toISOString();
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Erreur de chargement';
            state.isLoading = false;
          });
        }
      },

      addMoodEntry: async (entry: Omit<MoodEntry, 'id'>) => {
        try {
          const authStore = (await import('./authStore')).default;
          const { accessToken } = authStore.getState();

          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/dashboard/mood-entries`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${accessToken}`,
            },
            body: JSON.stringify(entry),
          });

          if (!response.ok) {
            throw new Error('Erreur lors de l\'ajout de l\'entrée d\'humeur');
          }

          const newEntry = await response.json();

          set((state) => {
            state.moodEntries.unshift(newEntry);
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Erreur inconnue';
          });
          throw error;
        }
      },

      updateObjective: async (id: string, updates: Partial<Objective>) => {
        try {
          const authStore = (await import('./authStore')).default;
          const { accessToken } = authStore.getState();

          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/dashboard/objectives/${id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${accessToken}`,
            },
            body: JSON.stringify(updates),
          });

          if (!response.ok) {
            throw new Error('Erreur lors de la mise à jour de l\'objectif');
          }

          const updatedObjective = await response.json();

          set((state) => {
            const index = state.objectives.findIndex(obj => obj.id === id);
            if (index !== -1) {
              state.objectives[index] = updatedObjective;
            }
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Erreur inconnue';
          });
          throw error;
        }
      },

      markInsightAsRead: (id: string) => {
        set((state) => {
          const insight = state.aiInsights.find(i => i.id === id);
          if (insight) {
            insight.readAt = new Date().toISOString();
          }
        });
      },

      syncBiometricData: async () => {
        try {
          const authStore = (await import('./authStore')).default;
          const { accessToken } = authStore.getState();

          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/dashboard/sync-biometric`, {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          });

          if (!response.ok) {
            throw new Error('Erreur lors de la synchronisation des données biométriques');
          }

          const newData = await response.json();

          set((state) => {
            state.biometricData = [...state.biometricData, ...newData];
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Erreur de synchronisation';
          });
        }
      },

      // Actions d'interface
      setDateRange: (start: string, end: string) => {
        set((state) => {
          state.dateRange = { start, end };
        });
      },

      setViewMode: (mode: 'daily' | 'weekly' | 'monthly') => {
        set((state) => {
          state.viewMode = mode;
        });
      },

      setSelectedCategories: (categories: string[]) => {
        set((state) => {
          state.selectedCategories = categories;
        });
      },

      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },

      // Calculateurs
      getMoodTrend: (days: number) => {
        const { moodEntries } = get();
        const recentEntries = moodEntries
          .filter(entry => {
            const entryDate = new Date(entry.date);
            const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
            return entryDate >= cutoffDate;
          })
          .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

        if (recentEntries.length < 2) return 'stable';

        const firstHalf = recentEntries.slice(0, Math.floor(recentEntries.length / 2));
        const secondHalf = recentEntries.slice(Math.floor(recentEntries.length / 2));

        const firstAvg = firstHalf.reduce((sum, entry) => sum + entry.mood, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, entry) => sum + entry.mood, 0) / secondHalf.length;

        const diff = secondAvg - firstAvg;
        
        if (diff > 0.3) return 'up';
        if (diff < -0.3) return 'down';
        return 'stable';
      },

      getProgressForPeriod: (start: string, end: string) => {
        const { objectives } = get();
        const periodObjectives = objectives.filter(obj => {
          const objDate = new Date(obj.createdAt);
          return objDate >= new Date(start) && objDate <= new Date(end);
        });

        if (periodObjectives.length === 0) return 0;

        const totalProgress = periodObjectives.reduce((sum, obj) => sum + obj.progress, 0);
        return totalProgress / periodObjectives.length;
      },

      getBurnoutRisk: () => {
        const { moodEntries, wellnessStats } = get();
        
        if (!wellnessStats) return 'low';

        const recentEntries = moodEntries
          .filter(entry => {
            const entryDate = new Date(entry.date);
            const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            return entryDate >= weekAgo;
          });

        if (recentEntries.length === 0) return 'low';

        const avgStress = recentEntries.reduce((sum, entry) => sum + entry.stress, 0) / recentEntries.length;
        const avgEnergy = recentEntries.reduce((sum, entry) => sum + entry.energy, 0) / recentEntries.length;
        const avgMood = recentEntries.reduce((sum, entry) => sum + entry.mood, 0) / recentEntries.length;

        const riskScore = (avgStress * 0.4) + ((5 - avgEnergy) * 0.3) + ((5 - avgMood) * 0.3);

        if (riskScore >= 3.5) return 'high';
        if (riskScore >= 2.5) return 'medium';
        return 'low';
      },
    })),
    {
      name: 'dashboard-store',
    }
  )
);

export default useDashboardStore;
export { useDashboardStore }; 