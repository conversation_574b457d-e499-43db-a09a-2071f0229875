import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { FEATURE_FLAGS } from '@/lib/config/feature-flags';
import { getAuthService } from '@/services/auth-supabase';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: 'user' | 'professional' | 'admin';
  emailVerified: boolean;
  twoFactorEnabled: boolean;
  createdAt: string;
  lastLoginAt?: string;
  preferences: {
    language: 'fr' | 'en';
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    privacy: {
      profileVisible: boolean;
      dataSharing: boolean;
    };
  };
  subscription?: {
    plan: 'free' | 'premium' | 'professional';
    status: 'active' | 'cancelled' | 'past_due';
    expiresAt?: string;
  };
}

export interface AuthState {
  // État d'authentification
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Tokens
  accessToken: string | null;
  refreshToken: string | null;
  
  // Actions d'authentification
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  refreshAuth: () => Promise<void>;
  
  // Gestion du profil
  updateProfile: (updates: Partial<User>) => Promise<void>;
  updatePreferences: (preferences: Partial<User['preferences']>) => Promise<void>;
  
  // 2FA
  enableTwoFactor: () => Promise<{ qrCode: string; secret: string }>;
  verifyTwoFactor: (token: string) => Promise<void>;
  disableTwoFactor: (token: string) => Promise<void>;
  
  // Réinitialisation
  resetPassword: (email: string) => Promise<void>;
  confirmPasswordReset: (token: string, newPassword: string) => Promise<void>;
  
  // Utilitaires
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
  marketingConsent: boolean;
}

const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      immer((set, get) => ({
        // État initial
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        accessToken: null,
        refreshToken: null,

        // Actions d'authentification
        login: async (email: string, password: string, rememberMe = false) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            // PHASE 2: Utiliser Supabase si activé
            if (FEATURE_FLAGS.USE_SUPABASE_AUTH) {
              const authService = getAuthService();
              if (authService) {
                const result = await authService.login(email, password);
                
                set((state) => {
                  state.user = result.user;
                  state.accessToken = result.accessToken;
                  state.refreshToken = result.refreshToken;
                  state.isAuthenticated = true;
                  state.isLoading = false;
                  state.error = null;
                });
                return;
              }
            }

            // Fallback vers l'ancienne API
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/login`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ email, password, rememberMe }),
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.message || 'Erreur de connexion');
            }

            const data = await response.json();

            set((state) => {
              state.user = data.user;
              state.accessToken = data.accessToken;
              state.refreshToken = data.refreshToken;
              state.isAuthenticated = true;
              state.isLoading = false;
              state.error = null;
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Erreur inconnue';
              state.isLoading = false;
            });
            throw error;
          }
        },

        register: async (userData: RegisterData) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            // PHASE 2: Utiliser Supabase si activé  
            if (FEATURE_FLAGS.USE_SUPABASE_AUTH) {
              const authService = getAuthService();
              if (authService) {
                const result = await authService.register({
                  email: userData.email,
                  password: userData.password,
                  firstName: userData.firstName,
                  lastName: userData.lastName,
                });
                
                set((state) => {
                  state.user = result.user;
                  state.accessToken = result.accessToken;
                  state.refreshToken = result.refreshToken;
                  state.isAuthenticated = true;
                  state.isLoading = false;
                  state.error = null;
                });
                return;
              }
            }

            // Fallback vers l'ancienne API
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/register`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(userData),
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.message || 'Erreur d\'inscription');
            }

            const data = await response.json();

            set((state) => {
              state.user = data.user;
              state.accessToken = data.accessToken;
              state.refreshToken = data.refreshToken;
              state.isAuthenticated = true;
              state.isLoading = false;
              state.error = null;
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Erreur inconnue';
              state.isLoading = false;
            });
            throw error;
          }
        },

        logout: () => {
          // PHASE 2: Logout Supabase si activé
          if (FEATURE_FLAGS.USE_SUPABASE_AUTH) {
            const authService = getAuthService();
            if (authService) {
              authService.logout().catch(console.error);
            }
          }

          set((state) => {
            state.user = null;
            state.accessToken = null;
            state.refreshToken = null;
            state.isAuthenticated = false;
            state.error = null;
          });
        },

        refreshAuth: async () => {
          const { refreshToken } = get();
          if (!refreshToken) {
            throw new Error('Aucun token de rafraîchissement disponible');
          }

          try {
            // PHASE 2: Refresh Supabase si activé
            if (FEATURE_FLAGS.USE_SUPABASE_AUTH) {
              const authService = getAuthService();
              if (authService) {
                const result = await authService.refreshAuth();
                
                set((state) => {
                  state.accessToken = result.accessToken;
                  state.user = result.user;
                });
                return;
              }
            }

            // Fallback vers l'ancienne API
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/refresh`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${refreshToken}`,
              },
            });

            if (!response.ok) {
              throw new Error('Impossible de rafraîchir la session');
            }

            const data = await response.json();

            set((state) => {
              state.accessToken = data.accessToken;
              state.user = data.user;
            });
          } catch (error) {
            set((state) => {
              state.user = null;
              state.accessToken = null;
              state.refreshToken = null;
              state.isAuthenticated = false;
            });
            throw error;
          }
        },

        updateProfile: async (updates: Partial<User>) => {
          const { accessToken } = get();
          
          try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`,
              },
              body: JSON.stringify(updates),
            });

            if (!response.ok) {
              throw new Error('Erreur de mise à jour du profil');
            }

            const updatedUser = await response.json();

            set((state) => {
              state.user = updatedUser;
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Erreur inconnue';
            });
            throw error;
          }
        },

        updatePreferences: async (preferences: Partial<User['preferences']>) => {
          const { accessToken, user } = get();
          
          try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/preferences`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`,
              },
              body: JSON.stringify(preferences),
            });

            if (!response.ok) {
              throw new Error('Erreur de mise à jour des préférences');
            }

            const updatedPreferences = await response.json();

            set((state) => {
              if (state.user) {
                state.user.preferences = { ...state.user.preferences, ...updatedPreferences };
              }
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Erreur inconnue';
            });
            throw error;
          }
        },

        enableTwoFactor: async () => {
          const { accessToken } = get();
          
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/2fa/enable`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${accessToken}`,
            },
          });

          if (!response.ok) {
            throw new Error('Erreur d\'activation de la 2FA');
          }

          return await response.json();
        },

        verifyTwoFactor: async (token: string) => {
          const { accessToken } = get();
          
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/2fa/verify`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${accessToken}`,
            },
            body: JSON.stringify({ token }),
          });

          if (!response.ok) {
            throw new Error('Code de vérification invalide');
          }

          set((state) => {
            if (state.user) {
              state.user.twoFactorEnabled = true;
            }
          });
        },

        disableTwoFactor: async (token: string) => {
          const { accessToken } = get();
          
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/2fa/disable`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${accessToken}`,
            },
            body: JSON.stringify({ token }),
          });

          if (!response.ok) {
            throw new Error('Erreur de désactivation de la 2FA');
          }

          set((state) => {
            if (state.user) {
              state.user.twoFactorEnabled = false;
            }
          });
        },

        resetPassword: async (email: string) => {
          // PHASE 2: Reset password Supabase si activé
          if (FEATURE_FLAGS.USE_SUPABASE_AUTH) {
            const authService = getAuthService();
            if (authService) {
              await authService.resetPassword(email);
              return;
            }
          }

          // Fallback vers l'ancienne API
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/reset-password`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email }),
          });

          if (!response.ok) {
            throw new Error('Erreur d\'envoi de l\'email de réinitialisation');
          }
        },

        confirmPasswordReset: async (token: string, newPassword: string) => {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/reset-password/confirm`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ token, newPassword }),
          });

          if (!response.ok) {
            throw new Error('Erreur de réinitialisation du mot de passe');
          }
        },

        clearError: () => {
          set((state) => {
            state.error = null;
          });
        },

        setLoading: (loading: boolean) => {
          set((state) => {
            state.isLoading = loading;
          });
        },
      })),
      {
        name: 'mindflow-auth',
        partialize: (state) => ({
          user: state.user,
          accessToken: state.accessToken,
          refreshToken: state.refreshToken,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    {
      name: 'auth-store',
    }
  )
);

export default useAuthStore;
export { useAuthStore };
