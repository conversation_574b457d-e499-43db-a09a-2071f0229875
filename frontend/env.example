# Supabase Configuration
# 1. <PERSON><PERSON><PERSON> un projet sur https://supabase.com (nom: mindflow-pro)
# 2. Copier les valeurs depuis Supabase > Settings > API

NEXT_PUBLIC_SUPABASE_URL=https://votre-projet.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=votre_clé_publique_ici
SUPABASE_SERVICE_ROLE_KEY=votre_clé_service_ici

# Feature Flags - Migration Progressive
# Phase 1: Configuration initiale
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_ENABLE_REAL_TIME=false

# Phase 2: Migration des données
NEXT_PUBLIC_MIGRATE_USER_DATA=false
NEXT_PUBLIC_MIGRATE_MOOD_TRACKING=false
NEXT_PUBLIC_MIGRATE_JOURNAL_ENTRIES=false

# Phase 3: Mode dual (SQLite + Supabase)
NEXT_PUBLIC_DUAL_DATABASE_MODE=false

# Backend Configuration
NEXT_PUBLIC_BACKEND_URL=http://localhost:4000
NEXT_PUBLIC_API_BASE_URL=http://localhost:4000/api/v1

# Development Configuration
NODE_ENV=development
NEXT_PUBLIC_NODE_ENV=development

# Instructions de Migration:
# 1. Copier ce fichier vers .env.local
# 2. Configurer les valeurs Supabase
# 3. Exécuter: node setup-supabase.js
# 4. Activer progressivement les feature flags 