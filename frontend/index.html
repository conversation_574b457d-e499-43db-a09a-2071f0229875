<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>MindFlow Pro - Plateforme de Santé Mentale</title>
  <meta name="description" content="Plateforme innovante de suivi et amélioration de la santé mentale avec IA coach intégrée" />
  
  <!-- Preload important fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  
  <!-- SEO Meta tags -->
  <meta property="og:title" content="MindFlow Pro - Santé Mentale" />
  <meta property="og:description" content="Plateforme de suivi et amélioration de la santé mentale" />
  <meta property="og:type" content="website" />
  
  <!-- Security headers -->
  <meta http-equiv="X-Content-Type-Options" content="nosniff" />
  <meta http-equiv="X-Frame-Options" content="DENY" />
  <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
  
  <!-- PWA support -->
  <meta name="theme-color" content="#3B82F6" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico" />
  
  <style>
    /* Loading styles */
    #app {
      min-height: 100vh;
    }
    
    .loading-screen {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 10px;
    }
    
    .loading-subtitle {
      font-size: 14px;
      opacity: 0.8;
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- Loading screen pendant que React se charge -->
    <div class="loading-screen" id="loading-screen">
      <div class="loading-spinner"></div>
      <div class="loading-text">MindFlow Pro</div>
      <div class="loading-subtitle">Chargement de votre espace bien-être...</div>
    </div>
  </div>
  
  <script>
    // Cacher l'écran de chargement une fois React chargé
    window.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen && window.React) {
          loadingScreen.style.display = 'none';
        }
      }, 2000);
    });
    
    // Gestion des erreurs JavaScript
    window.addEventListener('error', (e) => {
      console.error('Erreur frontend:', e.error);
    });
    
    window.addEventListener('unhandledrejection', (e) => {
      console.error('Promise rejetée:', e.reason);
    });
  </script>
  
  <!-- Point d'entrée de l'application React -->
  <script type="module" src="/src/main.tsx"></script>
</body>
</html> 