# =============================================================================
# SUPABASE CONFIGURATION - MINDFLOW PRO
# =============================================================================
# 1. C<PERSON>er votre projet sur https://supabase.com/dashboard
# 2. Nom du projet: mindflow-pro
# 3. Région: West EU (Ireland) 
# 4. Copier les valeurs depuis Settings > API

NEXT_PUBLIC_SUPABASE_URL=https://REMPLACER-PAR-VOTRE-URL.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=REMPLACER-PAR-VOTRE-ANON-KEY
SUPABASE_SERVICE_ROLE_KEY=REMPLACER-PAR-VOTRE-SERVICE-KEY

# =============================================================================
# FEATURE FLAGS - MIGRATION PROGRESSIVE
# =============================================================================
# Phase 1: Mode dual (SQLite + Supabase en parallèle)
NEXT_PUBLIC_DUAL_DATABASE_MODE=true

# Phase 2: Migration des données (activer progressivement)
NEXT_PUBLIC_MIGRATE_USER_DATA=false
NEXT_PUBLIC_MIGRATE_MOOD_TRACKING=false
NEXT_PUBLIC_MIGRATE_JOURNAL_ENTRIES=false

# Phase 3: Basculement final (quand tout fonctionne)
NEXT_PUBLIC_USE_SUPABASE_DATABASE=false
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
NEXT_PUBLIC_ENABLE_REAL_TIME=false

# =============================================================================
# BACKEND CONFIGURATION
# =============================================================================
NEXT_PUBLIC_BACKEND_URL=http://localhost:4000
NEXT_PUBLIC_API_BASE_URL=http://localhost:4000/api/v1

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
NODE_ENV=development
NEXT_PUBLIC_NODE_ENV=development

# =============================================================================
# INSTRUCTIONS DE MIGRATION
# =============================================================================
# 1. Copier ce fichier vers .env.local
# 2. Remplacer les valeurs REMPLACER-PAR-*
# 3. Exécuter le schéma SQL dans Supabase
# 4. Tester: cd frontend && npm run dev
# 5. Activer progressivement les feature flags
# ============================================================================= 