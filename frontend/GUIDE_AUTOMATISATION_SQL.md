# 🚀 GUIDE D'AUTOMATISATION SQL SUPABASE

## Script SQL Complet à Exécuter

Copier-coller ce script dans l'éditeur SQL Supabase : https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf/sql

```sql
-- 🗄️ SCRIPT SQL COMPLET POUR MINDFLOW PRO - SYSTÈME RENDEZ-VOUS
-- Exécuter en une seule fois dans l'interface Supabase

-- =====================================================
-- ÉTAPE 1: SUPPRESSION DES TABLES EXISTANTES
-- =====================================================

DROP TABLE IF EXISTS appointments CASCADE;
DROP TABLE IF EXISTS professionals CASCADE;

-- =====================================================
-- ÉTAPE 2: CRÉATION DES TABLES
-- =====================================================

-- Table des professionnels
CREATE TABLE professionals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    specialties TEXT[] DEFAULT '{}',
    price_per_session DECIMAL(10,2) DEFAULT 0,
    location TEXT,
    bio TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table des rendez-vous
CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    professional_id UUID REFERENCES professionals(id) ON DELETE CASCADE,
    professional_name TEXT NOT NULL,
    professional_role TEXT,
    client_id TEXT NOT NULL,
    client_name TEXT,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    type TEXT DEFAULT 'video',
    status TEXT DEFAULT 'scheduled',
    price DECIMAL(10,2),
    currency TEXT DEFAULT 'EUR',
    notes TEXT,
    meeting_link TEXT,
    reminder_sent BOOLEAN DEFAULT FALSE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- ÉTAPE 3: INDEX ET OPTIMISATIONS
-- =====================================================

CREATE INDEX idx_appointments_date ON appointments(appointment_date);
CREATE INDEX idx_appointments_professional ON appointments(professional_id);
CREATE INDEX idx_appointments_client ON appointments(client_id);
CREATE INDEX idx_appointments_status ON appointments(status);

-- =====================================================
-- ÉTAPE 4: TRIGGERS
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_professionals_updated_at 
    BEFORE UPDATE ON professionals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at 
    BEFORE UPDATE ON appointments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ÉTAPE 5: DONNÉES DE TEST
-- =====================================================

-- Professionnels
INSERT INTO professionals (name, role, email, specialties, price_per_session, location, bio) VALUES
('Dr. Sophie Martin', 'Psychologue clinicienne', '<EMAIL>', 
 ARRAY['TCC', 'Stress', 'Anxiété'], 85.00, 'Paris', 
 'Spécialisée en TCC avec 10 ans d''expérience'),
('Dr. Jean Dupont', 'Psychiatre', '<EMAIL>', 
 ARRAY['Psychiatrie', 'Dépression'], 120.00, 'Lyon', 
 'Psychiatre expérimenté'),
('Marie Leblanc', 'Thérapeute', '<EMAIL>', 
 ARRAY['Phobies', 'Addictions'], 75.00, 'Lyon', 
 'Spécialisée en troubles comportementaux'),
('Dr. Ahmed Benali', 'Psychothérapeute', '<EMAIL>', 
 ARRAY['Thérapie de couple', 'Traumatismes'], 90.00, 'Marseille', 
 'Approche humaniste');

-- Rendez-vous de test
WITH prof_data AS (
  SELECT id, name, role, price_per_session, 
         ROW_NUMBER() OVER (ORDER BY name) as rn 
  FROM professionals
)
INSERT INTO appointments (
  professional_id, professional_name, professional_role, client_id, client_name,
  appointment_date, appointment_time, duration_minutes, type, status, price, currency, notes
)
SELECT 
  p.id, p.name, p.role,
  'demo-user-' || p.rn, 'Client Démo ' || p.rn,
  CURRENT_DATE + (p.rn || ' days')::interval,
  '14:00'::time, 60, 'video', 'scheduled',
  p.price_per_session, 'EUR',
  'Rendez-vous avec ' || p.name
FROM prof_data p;

-- =====================================================
-- ÉTAPE 6: PERMISSIONS (RLS)
-- =====================================================

ALTER TABLE professionals ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public access" ON professionals FOR ALL USING (true);
CREATE POLICY "Public access" ON appointments FOR ALL USING (true);

-- =====================================================
-- ÉTAPE 7: VALIDATION
-- =====================================================

SELECT 'Professionnels:', COUNT(*) FROM professionals;
SELECT 'Rendez-vous:', COUNT(*) FROM appointments;
SELECT 'Premier RDV:', professional_name, appointment_date FROM appointments LIMIT 1;
```

## 🛠️ Instructions d'Automatisation

### 1. Exécution SQL Automatique
```bash
# Dans le terminal, à la racine du projet
node test-supabase-automation.js
```

### 2. Script de Validation Rapide
```bash
# Lancer le script d'automatisation existant
cd frontend && node setup-appointments-supabase-auto.js
```

### 3. Test de l'Interface
```bash
# Démarrer le serveur de développement
npm run dev

# Tester l'interface
# http://localhost:3000/test-appointments-supabase
```

## 📋 Checklist d'Automatisation

### ✅ Étape 1: Configuration
- [ ] Variables d'environnement Supabase configurées
- [ ] Connexion Supabase testée
- [ ] Dépendances @supabase/supabase-js installées

### ✅ Étape 2: Base de Données
- [ ] Script SQL exécuté dans Supabase
- [ ] Tables `professionals` et `appointments` créées
- [ ] Index et triggers configurés
- [ ] Données de test insérées

### ✅ Étape 3: Application
- [ ] Hook `useAppointmentsSupabase.ts` testé
- [ ] Page `/test-appointments-supabase` fonctionnelle
- [ ] Intégration avec page `/appointments` validée

### ✅ Étape 4: Production
- [ ] Permissions RLS configurées
- [ ] Tests d'intégration passés
- [ ] Performance optimisée

## 🧪 Commandes de Test Automatique

```bash
# Test complet automatisé
./test-supabase-automation.js

# Test de connexion uniquement
node -e "
const {createClient} = require('@supabase/supabase-js');
const supabase = createClient('https://kvdrukmoxetoiojazukf.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ');
supabase.from('professionals').select('count').then(r => console.log('✅ Connexion OK:', !r.error));
"

# Test des données
node -e "
const {createClient} = require('@supabase/supabase-js');
const supabase = createClient('https://kvdrukmoxetoiojazukf.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMTk3NjMsImV4cCI6MjA2NjU5NTc2M30.EIEBdbSuEuC3UpMfNsTE0AXIBBz4AWniqyWg2mLnGKQ');
supabase.from('appointments').select('professional_name,appointment_date').limit(3).then(r => console.log('📅 RDV:', r.data));
"
```

## 🎯 Résultats Attendus

Après l'automatisation complète :

1. **Base de données** : 4 professionnels et 4+ rendez-vous
2. **Interface** : Page de test fonctionnelle avec données réelles
3. **Hook React** : `useAppointmentsSupabase` opérationnel
4. **Integration** : Compatible avec l'interface existante `/appointments`

## 🔗 Liens Utiles

- **Dashboard Supabase** : https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf
- **Éditeur SQL** : https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf/sql
- **Table Editor** : https://supabase.com/dashboard/project/kvdrukmoxetoiojazukf/editor
- **Page de test** : http://localhost:3000/test-appointments-supabase

## 🚨 Dépannage

### Erreurs communes :
1. **Tables non créées** → Copier-coller le SQL manuellement
2. **Connexion refusée** → Vérifier les clés API
3. **Données vides** → Réexécuter les INSERT
4. **Hook erreur** → Vérifier les imports et types

### Solutions rapides :
```bash
# Nettoyer et recommencer
cd frontend && node setup-appointments-supabase-auto.js

# Forcer la recréation
# Supprimer les tables dans Supabase et réexécuter le SQL
``` 