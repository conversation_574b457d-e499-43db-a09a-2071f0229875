/**
 * Script de correction pour MindFlow Pro - Frontend
 * Résout les problèmes courants du frontend
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Correction du Frontend MindFlow Pro en cours...');

// Vérifier et créer le fichier .env.local
function fixEnvFile() {
  const envPath = path.join(__dirname, '.env.local');
  const envContent = `NEXT_PUBLIC_API_URL=http://localhost:4000/api/v1
NEXT_PUBLIC_WS_URL=ws://localhost:4000
NEXT_PUBLIC_APP_NAME=MindFlow Pro
NEXT_PUBLIC_ENVIRONMENT=development`;

  try {
    fs.writeFileSync(envPath, envContent, 'utf8');
    console.log('✅ Fichier .env.local créé/mis à jour avec succès');
    return true;
  } catch (error) {
    console.error('❌ Erreur lors de la création du fichier .env.local:', error.message);
    return false;
  }
}

// Vérifier et corriger le fichier vite.config.ts
function fixViteConfig() {
  const configPath = path.join(__dirname, 'vite.config.ts');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Le fichier vite.config.ts n\'existe pas');
    return false;
  }
  
  try {
    const viteConfig = `import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:4000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
});`;
    
    fs.writeFileSync(configPath, viteConfig, 'utf8');
    console.log('✅ Fichier vite.config.ts mis à jour avec succès');
    return true;
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du fichier vite.config.ts:', error.message);
    return false;
  }
}

// Vérifier et corriger le fichier next.config.js
function fixNextConfig() {
  const configPath = path.join(__dirname, 'next.config.js');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ Le fichier next.config.js n\'existe pas');
    return false;
  }
  
  try {
    const nextConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:4000/api/:path*',
      },
    ];
  },
};

module.exports = nextConfig;`;
    
    fs.writeFileSync(configPath, nextConfig, 'utf8');
    console.log('✅ Fichier next.config.js mis à jour avec succès');
    return true;
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du fichier next.config.js:', error.message);
    return false;
  }
}

// Nettoyer le cache
function cleanCache() {
  try {
    if (fs.existsSync(path.join(__dirname, '.next'))) {
      fs.rmSync(path.join(__dirname, '.next'), { recursive: true, force: true });
    }
    
    if (fs.existsSync(path.join(__dirname, 'node_modules', '.vite'))) {
      fs.rmSync(path.join(__dirname, 'node_modules', '.vite'), { recursive: true, force: true });
    }
    
    console.log('✅ Cache nettoyé avec succès');
    return true;
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage du cache:', error.message);
    return false;
  }
}

// Redémarrer les serveurs
function restartServers() {
  try {
    console.log('🔄 Arrêt des serveurs en cours...');
    execSync('pkill -f "node.*enhanced-server.js" || true');
    execSync('pkill -f "node.*vite" || true');
    
    console.log('🔄 Démarrage du serveur backend...');
    execSync('cd ../backend && node enhanced-server.js > backend.log 2>&1 &');
    
    console.log('🔄 Démarrage du serveur frontend...');
    execSync('npm run dev > frontend.log 2>&1 &');
    
    console.log('✅ Serveurs redémarrés avec succès');
    return true;
  } catch (error) {
    console.error('❌ Erreur lors du redémarrage des serveurs:', error.message);
    return false;
  }
}

// Exécuter toutes les corrections
async function runFixes() {
  console.log('🔄 Correction du fichier .env.local...');
  const envFixed = fixEnvFile();
  
  console.log('🔄 Correction du fichier vite.config.ts...');
  const viteFixed = fixViteConfig();
  
  console.log('🔄 Correction du fichier next.config.js...');
  const nextFixed = fixNextConfig();
  
  console.log('🔄 Nettoyage du cache...');
  const cacheFixed = cleanCache();
  
  console.log('🔄 Redémarrage des serveurs...');
  const serversFixed = restartServers();
  
  // Afficher les résultats
  console.log('\n📊 Résultats des corrections:');
  console.log('=======================');
  
  console.log(`\n🔧 Fichier .env.local: ${envFixed ? '✅ OK' : '❌ ERREUR'}`);
  console.log(`🔧 Fichier vite.config.ts: ${viteFixed ? '✅ OK' : '❌ ERREUR'}`);
  console.log(`🔧 Fichier next.config.js: ${nextFixed ? '✅ OK' : '❌ ERREUR'}`);
  console.log(`🔧 Nettoyage du cache: ${cacheFixed ? '✅ OK' : '❌ ERREUR'}`);
  console.log(`🔧 Redémarrage des serveurs: ${serversFixed ? '✅ OK' : '❌ ERREUR'}`);
  
  console.log('\n🚀 Corrections terminées!');
  console.log('Le frontend devrait être accessible à l\'adresse: http://localhost:5173');
  console.log('Le backend devrait être accessible à l\'adresse: http://localhost:4000');
}

runFixes().catch(console.error); 