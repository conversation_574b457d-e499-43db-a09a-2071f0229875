import { test, expect, Page } from '@playwright/test';

/**
 * 🚀 TESTS PHASE 2 - OPTIMISATIONS MINDFLOW PRO
 * Validation des nouvelles fonctionnalités et performances optimisées
 */

test.setTimeout(120000);

test.describe('🎯 Phase 2 - Nouveaux Composants & Optimisations', () => {

  test('📊 Validation Composants Analytics Recharts', async ({ page }) => {
    console.log('🔍 Test composants Analytics avec graphiques Recharts...');
    
    await page.goto('/analytics', { waitUntil: 'networkidle' });
    
    // Vérifier les composants Analytics sont présents
    const analyticsElements = await page.$$eval('*', elements => {
      const found = {
        recharts: elements.some(el => el.className && el.className.includes('recharts')),
        moodChart: elements.some(el => el.textContent && el.textContent.toLowerCase().includes('mood')),
        performanceChart: elements.some(el => el.textContent && el.textContent.toLowerCase().includes('performance')),
        analytics: elements.some(el => el.textContent && el.textContent.toLowerCase().includes('analytics'))
      };
      return found;
    });
    
    expect(analyticsElements.analytics).toBe(true);
    console.log('✅ Page Analytics accessible');
    
    if (analyticsElements.recharts) {
      console.log('✅ Composants Recharts détectés');
    }
    
    if (analyticsElements.moodChart || analyticsElements.performanceChart) {
      console.log('✅ Graphiques de données détectés');
    }
  });

  test('🎥 Validation Télémédecine WebRTC', async ({ page }) => {
    console.log('🔍 Test composants Télémédecine WebRTC...');
    
    await page.goto('/telemedicine-enhanced', { waitUntil: 'networkidle' });
    
    const telemedicineElements = await page.$$eval('*', elements => {
      const found = {
        telemedicine: elements.some(el => el.textContent && el.textContent.toLowerCase().includes('télémédecine')),
        video: elements.some(el => el.tagName === 'VIDEO'),
        controls: elements.some(el => el.textContent && (
          el.textContent.includes('audio') || 
          el.textContent.includes('vidéo') ||
          el.textContent.includes('contrôle')
        )),
        webrtc: elements.some(el => el.getAttribute && (
          el.getAttribute('data-webrtc') ||
          el.getAttribute('data-video-controls')
        ))
      };
      return found;
    });
    
    expect(telemedicineElements.telemedicine).toBe(true);
    console.log('✅ Page Télémédecine Enhanced accessible');
    
    if (telemedicineElements.video || telemedicineElements.controls) {
      console.log('✅ Composants vidéo/contrôles détectés');
    }
  });

  test('📊 Validation Dashboard Optimisé', async ({ page }) => {
    console.log('🔍 Test Dashboard optimisé...');
    
    await page.goto('/dashboard-optimized', { waitUntil: 'networkidle' });
    
    const dashboardElements = await page.$$eval('*', elements => {
      const found = {
        dashboard: elements.some(el => el.textContent && el.textContent.toLowerCase().includes('dashboard')),
        optimized: elements.some(el => el.textContent && el.textContent.toLowerCase().includes('optimis')),
        monitoring: elements.some(el => el.textContent && el.textContent.toLowerCase().includes('monitoring')),
        analytics: elements.some(el => el.textContent && el.textContent.toLowerCase().includes('analytics'))
      };
      return found;
    });
    
    expect(dashboardElements.dashboard).toBe(true);
    console.log('✅ Dashboard optimisé accessible');
    
    if (dashboardElements.monitoring || dashboardElements.analytics) {
      console.log('✅ Fonctionnalités de monitoring détectées');
    }
  });

  test('⚡ Test Performance des Pages Optimisées', async ({ page }) => {
    console.log('🔍 Test performance pages optimisées...');
    
    const pages = [
      { url: '/analytics', name: 'Analytics' },
      { url: '/telemedicine-enhanced', name: 'Télémédecine Enhanced' },
      { url: '/dashboard-optimized', name: 'Dashboard Optimisé' }
    ];
    
    for (const pageInfo of pages) {
      const startTime = Date.now();
      
      try {
        await page.goto(pageInfo.url, { waitUntil: 'networkidle', timeout: 10000 });
        const loadTime = Date.now() - startTime;
        
        console.log(`   ${pageInfo.name}: ${loadTime}ms`);
        expect(loadTime).toBeLessThan(8000); // 8s max pour pages complexes
        
      } catch (error) {
        console.log(`   ${pageInfo.name}: Erreur de chargement - ${error}`);
        // Ne pas faire échouer le test si une page n'est pas encore prête
      }
    }
  });

  test('🧪 Test Intégration Nouvelles Dépendances', async ({ page }) => {
    console.log('🔍 Test intégration dépendances Phase 2...');
    
    await page.goto('/analytics', { waitUntil: 'networkidle' });
    
    // Vérifier qu'il n'y a pas d'erreurs JS critiques
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error' && !msg.text().includes('favicon')) {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.reload({ waitUntil: 'networkidle' });
    await page.waitForTimeout(3000);
    
    console.log(`Erreurs console détectées: ${consoleErrors.length}`);
    
    if (consoleErrors.length > 0) {
      console.log('Erreurs:', consoleErrors.slice(0, 3));
    }
    
    // Permettre quelques erreurs non critiques
    expect(consoleErrors.length).toBeLessThan(5);
  });

  test('🔄 Test Régression Fonctionnalités Existantes', async ({ page }) => {
    console.log('🔍 Test régression fonctionnalités existantes...');
    
    const corePages = [
      { url: '/', name: 'Accueil' },
      { url: '/dashboard', name: 'Dashboard' },
      { url: '/journal', name: 'Journal' },
      { url: '/ai-coach', name: 'IA Coach' }
    ];
    
    let workingPages = 0;
    
    for (const pageInfo of corePages) {
      try {
        await page.goto(pageInfo.url, { waitUntil: 'networkidle', timeout: 8000 });
        
        const hasContent = await page.evaluate(() => {
          return document.body.textContent && document.body.textContent.trim().length > 100;
        });
        
        if (hasContent) {
          console.log(`   ✅ ${pageInfo.name}: Fonctionnel`);
          workingPages++;
        } else {
          console.log(`   ⚠️ ${pageInfo.name}: Contenu minimal`);
        }
        
      } catch (error) {
        console.log(`   ❌ ${pageInfo.name}: Erreur`);
      }
    }
    
    console.log(`Pages fonctionnelles: ${workingPages}/${corePages.length}`);
    expect(workingPages).toBeGreaterThanOrEqual(Math.floor(corePages.length * 0.75)); // 75% minimum
  });

}); 