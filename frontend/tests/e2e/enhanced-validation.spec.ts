import { test, expect } from '@playwright/test';

/**
 * 🚀 VALIDATION AMÉLIORÉE - MINDFLOW PRO
 * Tests pour les nouvelles fonctionnalités Phase 1
 */

test.describe('Validation Nouvelles Fonctionnalités', () => {
  
  test('Analytics Page Validation', async ({ page }) => {
    await page.goto('/analytics');
    await expect(page).toHaveTitle(/MindFlow/);
    
    // Vérifier que la page se charge
    const content = await page.textContent('body');
    expect(content).toContain('Analytics');
  });
  
  test('Telemedicine Enhanced Validation', async ({ page }) => {
    await page.goto('/telemedicine-enhanced'); 
    await expect(page).toHaveTitle(/MindFlow/);
    
    // Vérifier que la page se charge
    const content = await page.textContent('body');
    expect(content).toContain('Télémédecine');
  });
  
  test('Dashboard Optimized Validation', async ({ page }) => {
    await page.goto('/dashboard-optimized');
    await expect(page).toHaveTitle(/MindFlow/);
    
    // Vérifier que la page se charge
    const content = await page.textContent('body');
    expect(content).toContain('Dashboard');
  });

}); 