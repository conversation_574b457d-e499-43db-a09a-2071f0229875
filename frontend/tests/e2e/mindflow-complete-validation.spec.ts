import { test, expect, Page, BrowserContext } from '@playwright/test';

/**
 * 🚀 TESTS COMPLETS - MINDFLOW PRO
 * Validation exhaustive de toutes les fonctionnalités développées
 * 
 * Phases testées :
 * ✅ Phase 1-2: Dashboard + Journal + IA Coaching + Télémédecine
 * ✅ Phase 3: Conformité & Sécurité (HDS, ISO 27001, HIPAA, SOC 2)
 * ✅ Phase 4: Intégrations B2B (Hôpitaux, HL7 FHIR, Laboratoires, SDK)
 * ✅ Phase 8: Performance & Monitoring
 * ✅ Phase 9: Analytics Prédictifs ML (Fonctionnalité phare)
 */

// Configuration des tests
test.setTimeout(120000); // 2 minutes par test

// Données de test globales
const TEST_DATA = {
  testUser: {
    email: '<EMAIL>',
    password: 'TestMindFlow2024!',
    name: 'Utilisateur Test'
  },
  professionalUser: {
    email: '<EMAIL>',
    password: 'DrTestMindFlow2024!',
    name: 'Dr. <PERSON> MindFlow'
  }
};

test.describe('🚀 MindFlow Pro - Validation Complète Production', () => {
  
  test.beforeEach(async ({ page, context }) => {
    // Configuration spécifique pour chaque test
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Aller à la page d'accueil et attendre le chargement
    await page.goto('/', { waitUntil: 'networkidle' });
    
    // Vérifier que l'application se charge correctement
    await expect(page).toHaveTitle(/MindFlow Pro/);
  });

  test('✅ 1. Validation Pages Principales & Navigation Complete', async ({ page }) => {
    console.log('🔍 Test navigation complète des pages principales...');
    
    const criticalPages = [
      { 
        url: '/', 
        name: 'Accueil', 
        expectedContent: 'MindFlow',
        loadTime: 3000
      },
      { 
        url: '/dashboard', 
        name: 'Dashboard Principal', 
        expectedContent: 'Dashboard',
        loadTime: 4000
      },
      { 
        url: '/journal', 
        name: 'Journal Intelligent', 
        expectedContent: 'Journal',
        loadTime: 3000
      },
      { 
        url: '/ai-coach', 
        name: 'IA Coach Avancé', 
        expectedContent: 'IA Coach',
        loadTime: 4000
      },
      { 
        url: '/analytics', 
        name: 'Analytics Comportementaux', 
        expectedContent: 'Analytics',
        loadTime: 3000
      },
      { 
        url: '/appointments', 
        name: 'Système Rendez-vous', 
        expectedContent: 'Rendez-vous',
        loadTime: 3000
      },
      { 
        url: '/professionals', 
        name: 'Répertoire Professionnels', 
        expectedContent: 'Professionnels',
        loadTime: 3000
      },
      { 
        url: '/telemedicine', 
        name: 'Télémédecine Basique', 
        expectedContent: 'Télémédecine',
        loadTime: 3000
      },
      { 
        url: '/telemedicine-advanced', 
        name: 'Télémédecine Avancée Phase 2', 
        expectedContent: 'Télémédecine',
        loadTime: 4000
      },
      { 
        url: '/compliance', 
        name: 'Module Conformité Phase 3', 
        expectedContent: 'Conformité',
        loadTime: 3000
      },
      { 
        url: '/integrations-b2b', 
        name: 'Intégrations B2B Phase 4', 
        expectedContent: 'Intégrations',
        loadTime: 3000
      },
      { 
        url: '/ml-analytics', 
        name: 'ML Analytics Phase 9 - PHARE', 
        expectedContent: 'Analytics Prédictifs',
        loadTime: 5000
      }
    ];

    let passedPages = 0;
    let totalLoadTime = 0;

    for (const pageInfo of criticalPages) {
      console.log(`   🔎 Testing: ${pageInfo.name} (${pageInfo.url})`);
      
      const startTime = Date.now();
      
      try {
        // Navigation avec timeout personnalisé
        await page.goto(pageInfo.url, { 
          waitUntil: 'networkidle',
          timeout: pageInfo.loadTime
        });
        
        const loadTime = Date.now() - startTime;
        totalLoadTime += loadTime;
        
        // Vérifier l'état de la page
        const pageStatus = await page.evaluate(() => {
          return {
            readyState: document.readyState,
            hasError: document.querySelector('.error, [data-testid="error"]') !== null,
            title: document.title,
            bodyText: document.body.textContent?.toLowerCase() || '',
            hasContent: document.body.children.length > 0
          };
        });
        
        // Assertions critiques
        expect(pageStatus.readyState).toBe('complete');
        expect(pageStatus.hasError).toBe(false);
        expect(pageStatus.hasContent).toBe(true);
        
        // Vérifier le contenu spécifique
        if (pageInfo.expectedContent) {
          expect(pageStatus.bodyText).toContain(pageInfo.expectedContent.toLowerCase());
        }
        
        // Vérifier les performances
        expect(loadTime).toBeLessThan(pageInfo.loadTime);
        
        passedPages++;
        console.log(`   ✅ ${pageInfo.name}: OK (${loadTime}ms)`);
        
      } catch (error) {
        console.log(`   ❌ ${pageInfo.name}: FAILED - ${error}`);
        throw error;
      }
    }
    
    // Métriques globales
    const avgLoadTime = totalLoadTime / criticalPages.length;
    console.log(`\n📊 Résultats Navigation:`);
    console.log(`   Pages testées: ${passedPages}/${criticalPages.length}`);
    console.log(`   Temps moyen de chargement: ${Math.round(avgLoadTime)}ms`);
    console.log(`   Temps total: ${Math.round(totalLoadTime)}ms`);
    
    expect(passedPages).toBe(criticalPages.length);
    expect(avgLoadTime).toBeLessThan(3500); // Moins de 3.5s en moyenne
  });

  test('✅ 2. Validation Système Journal Avancé (Phase 1)', async ({ page }) => {
    console.log('🔍 Test système de journal avancé...');
    
    await page.goto('/journal', { waitUntil: 'networkidle' });
    
    // Vérifier les éléments principaux
    await expect(page.locator('h1')).toContainText('Journal');
    
    // Tester les fonctionnalités de filtrage
    const filterElements = await page.$$('[data-testid*="filter"], .filter-button, select');
    expect(filterElements.length).toBeGreaterThan(0);
    
    // Vérifier la présence d'entrées de journal ou interface de création
    const journalElements = await page.$$eval(
      '[data-testid*="journal"], .journal-entry, .entry-card, button:has-text("Nouvelle")', 
      elements => elements.length
    );
    expect(journalElements).toBeGreaterThan(0);
    
    // Tester la création d'une nouvelle entrée si possible
    const newEntryButton = await page.$('button:has-text("Nouvelle"), a:has-text("Nouvelle")');
    if (newEntryButton) {
      await newEntryButton.click();
      await page.waitForTimeout(2000);
      
      // Vérifier la navigation vers la page de création
      const currentUrl = page.url();
      expect(currentUrl).toMatch(/journal.*new|new.*journal|create/);
    }
    
    console.log('   ✅ Système de journal validé avec succès');
  });

  test('✅ 3. Validation IA Coach Avancé (Phase 1)', async ({ page }) => {
    console.log('🔍 Test IA Coach avec analyse sentiment...');
    
    await page.goto('/ai-coach', { waitUntil: 'networkidle' });
    
    // Vérifier les éléments de l'interface IA
    const pageContent = await page.textContent('body');
    expect(pageContent).toMatch(/ia|coach|intelligence|artificielle/i);
    
    // Tester les éléments interactifs
    const interactiveElements = await page.$$eval(
      'button, input, textarea, select', 
      elements => elements.length
    );
    expect(interactiveElements).toBeGreaterThan(2);
    
    // Tester l'interface de chat si présente
    const chatInput = await page.$('textarea, input[type="text"]');
    if (chatInput) {
      await chatInput.fill('Je me sens anxieux aujourd\'hui');
      await page.waitForTimeout(1000);
      
      const sendButton = await page.$('button:has-text("Envoyer"), button[type="submit"]');
      if (sendButton) {
        await sendButton.click();
        await page.waitForTimeout(2000);
        
        // Vérifier qu'une réponse apparaît
        const response = await page.$('.response, .ai-message, .chat-message');
        expect(response).toBeTruthy();
      }
    }
    
    console.log('   ✅ IA Coach validé avec fonctionnalités interactives');
  });

  test('✅ 4. Validation Analytics Comportementaux (Phase 1)', async ({ page }) => {
    console.log('🔍 Test analytics avec graphiques avancés...');
    
    await page.goto('/analytics', { waitUntil: 'networkidle' });
    
    const pageContent = await page.textContent('body');
    expect(pageContent).toMatch(/analytics|analyse|graphique|statistique/i);
    
    // Vérifier les éléments de visualisation
    const chartElements = await page.$$eval(
      'canvas, svg, .chart, .graph, .metric, .recharts-surface', 
      elements => elements.length
    );
    expect(chartElements).toBeGreaterThan(0);
    
    // Vérifier les métriques numériques
    const metrics = await page.$$eval(
      '.metric-value, .stat-number, .percentage, .score', 
      elements => elements.map(el => el.textContent).filter(text => /\d+/.test(text || ''))
    );
    expect(metrics.length).toBeGreaterThan(0);
    
    console.log('   ✅ Analytics validé avec visualisations');
  });

  test('✅ 5. Validation Télémédecine Avancée Phase 2', async ({ page }) => {
    console.log('🔍 Test télémédecine avec outils diagnostiques...');
    
    await page.goto('/telemedicine-advanced', { waitUntil: 'networkidle' });
    
    const pageContent = await page.textContent('body');
    expect(pageContent).toMatch(/télémédecine|telemedicine|consultation|vidéo/i);
    
    // Vérifier les contrôles vidéo avancés
    const videoControls = await page.$$eval(
      'button[aria-label*="vidéo"], .video-control, button:has-text("Caméra"), button:has-text("Micro")', 
      elements => elements.length
    );
    expect(videoControls).toBeGreaterThan(0);
    
    // Vérifier les outils diagnostiques virtuels
    const diagnosticTools = await page.$$eval(
      '.diagnostic-tool, button:has-text("Stéthoscope"), button:has-text("Dermascope"), .medical-tool', 
      elements => elements.length
    );
    expect(diagnosticTools).toBeGreaterThan(0);
    
    // Tester l'activation d'un outil diagnostique
    const stethoscopeButton = await page.$('button:has-text("Stéthoscope"), .stethoscope-tool');
    if (stethoscopeButton) {
      await stethoscopeButton.click();
      await page.waitForTimeout(1000);
      
      // Vérifier l'activation
      const activeTool = await page.$('.tool-active, .diagnostic-active');
      expect(activeTool).toBeTruthy();
    }
    
    console.log('   ✅ Télémédecine avancée validée avec outils diagnostiques');
  });

  test('✅ 6. Validation Conformité Phase 3 - Certifications', async ({ page }) => {
    console.log('🔍 Test module conformité HDS/ISO/HIPAA/SOC2...');
    
    await page.goto('/compliance', { waitUntil: 'networkidle' });
    
    const pageContent = await page.textContent('body');
    expect(pageContent).toMatch(/conformité|compliance|hds|iso|hipaa|soc/i);
    
    // Vérifier les certifications principales
    const certifications = ['HDS', 'ISO 27001', 'HIPAA', 'SOC 2'];
    for (const cert of certifications) {
      const certElement = await page.$(`text=${cert}`);
      expect(certElement).toBeTruthy();
    }
    
    // Vérifier les métriques de conformité
    const complianceMetrics = await page.$$eval(
      '.metric, .progress, .certification-status, .compliance-score', 
      elements => elements.length
    );
    expect(complianceMetrics).toBeGreaterThan(0);
    
    // Vérifier les barres de progression
    const progressBars = await page.$$eval(
      '.progress-bar, [role="progressbar"], .percentage', 
      elements => elements.length
    );
    expect(progressBars).toBeGreaterThan(0);
    
    console.log('   ✅ Module conformité validé avec certifications');
  });

  test('✅ 7. Validation Intégrations B2B Phase 4', async ({ page }) => {
    console.log('🔍 Test intégrations B2B - Hôpitaux/Labs/Pharmacies...');
    
    await page.goto('/integrations-b2b', { waitUntil: 'networkidle' });
    
    const pageContent = await page.textContent('body');
    expect(pageContent).toMatch(/intégration|b2b|hôpitaux|laboratoires|api|hl7|fhir/i);
    
    // Vérifier les catégories d'intégrations
    const integrationCategories = ['Hôpitaux', 'Laboratoires', 'Pharmacies', 'HL7 FHIR', 'SDK'];
    for (const category of integrationCategories) {
      const categoryElement = await page.locator(`text=${category}`).first();
      await expect(categoryElement).toBeVisible();
    }
    
    // Vérifier les connecteurs et statuts
    const connectors = await page.$$eval(
      '.integration-card, .connector, .api-status, .connection-status', 
      elements => elements.length
    );
    expect(connectors).toBeGreaterThan(0);
    
    // Vérifier les métriques d'API
    const apiMetrics = await page.$$eval(
      '.api-calls, .uptime, .response-time, .api-metric', 
      elements => elements.filter(el => /\d+/.test(el.textContent || ''))
    );
    expect(apiMetrics.length).toBeGreaterThan(0);
    
    console.log('   ✅ Intégrations B2B validées avec connecteurs actifs');
  });

  test('✅ 8. Validation ML Analytics Phase 9 - FONCTIONNALITÉ PHARE', async ({ page }) => {
    console.log('🔍 Test ML Analytics - Fonctionnalité phare Phase 9...');
    
    await page.goto('/ml-analytics', { waitUntil: 'networkidle', timeout: 15000 });
    
    // Vérifier le titre principal
    await expect(page.locator('h1')).toContainText('Analytics Prédictifs');
    
    // Vérifier les métriques clés Phase 9
    const pageContent = await page.textContent('body');
    expect(pageContent).toMatch(/prédictif|machine learning|ia|tensorflow|automl/i);
    
    // Vérifier les métriques de performance Phase 9
    const keyMetrics = await page.$$eval(
      '.metric-value, .ml-score, .prediction-accuracy, .roi-metric', 
      elements => elements.map(el => el.textContent).filter(text => /\d+/.test(text || ''))
    );
    expect(keyMetrics.length).toBeGreaterThan(0);
    
    // Vérifier les composants ML spécifiques
    const mlComponents = await page.$$eval(
      '.ml-insight, .prediction-chart, .pattern-detection, .analytics-card', 
      elements => elements.length
    );
    expect(mlComponents).toBeGreaterThan(0);
    
    // Vérifier les graphiques prédictifs
    const predictiveCharts = await page.$$eval(
      'canvas, svg, .recharts-surface, .chart-container', 
      elements => elements.length
    );
    expect(predictiveCharts).toBeGreaterThan(0);
    
    // Test interaction avec un graphique ML
    const firstChart = await page.$('canvas, .recharts-surface');
    if (firstChart) {
      await firstChart.hover();
      await page.waitForTimeout(1000);
      
      // Vérifier les tooltips ou info-bulles
      const tooltip = await page.$('.recharts-tooltip, .chart-tooltip, .ml-tooltip');
      // Note: Ne pas exiger le tooltip car dépend de l'implémentation
    }
    
    console.log('   ✅ ML Analytics Phase 9 validé - Fonctionnalité phare opérationnelle');
  });

  test('✅ 9. Test Performance Globale Application', async ({ page }) => {
    console.log('🔍 Test performance globale et temps de réponse...');
    
    const performanceMetrics = {
      totalLoadTime: 0,
      pagesCount: 0,
      slowPages: [],
      fastPages: []
    };
    
    const testPages = ['/', '/dashboard', '/journal', '/ml-analytics'];
    
    for (const url of testPages) {
      const startTime = Date.now();
      
      await page.goto(url, { waitUntil: 'networkidle' });
      
      const loadTime = Date.now() - startTime;
      performanceMetrics.totalLoadTime += loadTime;
      performanceMetrics.pagesCount++;
      
      if (loadTime > 5000) {
        performanceMetrics.slowPages.push({ url, loadTime });
      } else {
        performanceMetrics.fastPages.push({ url, loadTime });
      }
      
      console.log(`   📊 ${url}: ${loadTime}ms`);
    }
    
    const avgLoadTime = performanceMetrics.totalLoadTime / performanceMetrics.pagesCount;
    
    console.log(`\n📈 Métriques Performance:`);
    console.log(`   Temps moyen: ${Math.round(avgLoadTime)}ms`);
    console.log(`   Pages rapides: ${performanceMetrics.fastPages.length}`);
    console.log(`   Pages lentes: ${performanceMetrics.slowPages.length}`);
    
    // Assertions performance
    expect(avgLoadTime).toBeLessThan(5000); // Moins de 5s en moyenne
    expect(performanceMetrics.slowPages.length).toBeLessThan(2); // Max 1 page lente
    
    console.log('   ✅ Performance globale validée');
  });

  test('✅ 10. Test Responsivité Multi-Device', async ({ page, context }) => {
    console.log('🔍 Test responsivité sur différentes tailles...');
    
    const viewports = [
      { name: 'Mobile', width: 375, height: 667 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1280, height: 720 },
      { name: 'Large Desktop', width: 1920, height: 1080 }
    ];
    
    for (const viewport of viewports) {
      console.log(`   📱 Test ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.goto('/dashboard', { waitUntil: 'networkidle' });
      
      // Vérifier que le contenu s'adapte
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
      const viewportWidth = viewport.width;
      
      // Pas de scroll horizontal indésirable
      expect(bodyWidth).toBeLessThanOrEqual(viewportWidth + 20); // Marge de 20px
      
      // Vérifier la visibilité des éléments critiques
      const criticalElements = await page.$$eval(
        'nav, main, header, .dashboard-content', 
        elements => elements.filter(el => {
          const rect = el.getBoundingClientRect();
          return rect.width > 0 && rect.height > 0;
        }).length
      );
      
      expect(criticalElements).toBeGreaterThan(0);
    }
    
    console.log('   ✅ Responsivité validée sur tous devices');
  });

});

// Tests spécialisés pour fonctionnalités critiques
test.describe('🎯 Tests Critiques Spécialisés', () => {
  
  test('🔐 Test Sécurité et Authentification', async ({ page }) => {
    console.log('🔍 Test sécurité et flux d\'authentification...');
    
    // Tester l'accès aux pages protégées sans authentification
    await page.goto('/dashboard');
    
    // Devrait rediriger vers login ou afficher un message d'erreur approprié
    const currentUrl = page.url();
    const isProtected = currentUrl.includes('login') || currentUrl.includes('auth') || 
                       await page.$('.auth-required, .login-required') !== null;
    
    expect(isProtected).toBeTruthy();
    console.log('   ✅ Protection des pages sécurisées validée');
  });

  test('⚡ Test Connectivité et Gestion Hors Ligne', async ({ page, context }) => {
    console.log('🔍 Test gestion connectivité et mode hors ligne...');
    
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    
    // Simuler la perte de connexion
    await context.setOffline(true);
    
    // Attendre et vérifier la gestion du mode hors ligne
    await page.waitForTimeout(2000);
    
    const offlineIndicator = await page.$('.offline-indicator, .no-connection, .network-error');
    // Note: Ne pas exiger l'indicateur car dépend de l'implémentation
    
    // Restaurer la connexion
    await context.setOffline(false);
    await page.waitForTimeout(1000);
    
    console.log('   ✅ Gestion connectivité testée');
  });

});

// Hooks globaux pour les tests
test.afterEach(async ({ page }, testInfo) => {
  // Capturer des informations supplémentaires en cas d'échec
  if (testInfo.status !== testInfo.expectedStatus) {
    const screenshot = await page.screenshot();
    await testInfo.attach('screenshot', { body: screenshot, contentType: 'image/png' });
    
    const html = await page.content();
    await testInfo.attach('page-content', { body: html, contentType: 'text/html' });
  }
});

test.afterAll(async () => {
  console.log('\n🎉 Tests MindFlow Pro terminés avec succès !');
  console.log('📊 Rapport détaillé disponible dans playwright-report/');
}); 