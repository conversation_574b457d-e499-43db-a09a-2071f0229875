import { test, expect, Page } from '@playwright/test';

/**
 * 🚀 TESTS PERFORMANCE - MINDFLOW PRO
 * Validation des performances et métriques Core Web Vitals
 */

test.setTimeout(180000);

interface CoreWebVitals {
  FCP: number;
  LCP: number;
  FID: number;
  CLS: number;
  TTFB: number;
}

interface ResourceMetrics {
  totalResources: number;
  jsFiles: number;
  cssFiles: number;
  images: number;
  totalSize: number;
  slowResources: Array<{ name: string; duration: number }>;
}

test.describe('🚀 Tests Performance & Core Web Vitals', () => {

  test('📊 Core Web Vitals - Toutes Pages Critiques', async ({ page }) => {
    console.log('🔍 Test Core Web Vitals sur pages critiques...');
    
    const criticalPages = [
      '/dashboard',
      '/journal',
      '/ai-coach',
      '/analytics',
      '/ml-analytics',
      '/appointments',
      '/telemedicine-advanced'
    ];
    
    const performanceMetrics: Array<{ url: string } & CoreWebVitals> = [];
    
    for (const url of criticalPages) {
      console.log(`   📈 Analyse performance: ${url}`);
      
      // Mesurer les Core Web Vitals
      await page.goto(url, { waitUntil: 'networkidle' });
      
      const vitals = await page.evaluate((): Promise<CoreWebVitals> => {
        return new Promise((resolve) => {
          const vitals: CoreWebVitals = {
            FCP: 0,
            LCP: 0,
            FID: 0,
            CLS: 0,
            TTFB: 0
          };
          
          // First Contentful Paint
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (entry.name === 'first-contentful-paint') {
                vitals.FCP = entry.startTime;
              }
            }
          }).observe({ entryTypes: ['paint'] });
          
          // Largest Contentful Paint
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1] as any;
            if (lastEntry) {
              vitals.LCP = lastEntry.startTime;
            }
          }).observe({ entryTypes: ['largest-contentful-paint'] });
          
          // Cumulative Layout Shift
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              const layoutShiftEntry = entry as any;
              if (!layoutShiftEntry.hadRecentInput) {
                vitals.CLS += layoutShiftEntry.value || 0;
              }
            }
          }).observe({ entryTypes: ['layout-shift'] });
          
          // Time to First Byte
          const navigation = performance.getEntriesByType('navigation')[0] as any;
          if (navigation && navigation.responseStart && navigation.requestStart) {
            vitals.TTFB = navigation.responseStart - navigation.requestStart;
          }
          
          setTimeout(() => resolve(vitals), 3000);
        });
      });
      
      performanceMetrics.push({ url, ...vitals });
      
      // Assertions Core Web Vitals
      expect(vitals.FCP).toBeLessThan(2500); // FCP < 2.5s
      expect(vitals.LCP).toBeLessThan(4000); // LCP < 4s
      expect(vitals.CLS).toBeLessThan(0.25); // CLS < 0.25
      expect(vitals.TTFB).toBeLessThan(800);  // TTFB < 800ms
      
      console.log(`     FCP: ${Math.round(vitals.FCP)}ms`);
      console.log(`     LCP: ${Math.round(vitals.LCP)}ms`);
      console.log(`     CLS: ${vitals.CLS.toFixed(3)}`);
      console.log(`     TTFB: ${Math.round(vitals.TTFB)}ms`);
    }
    
    // Calcul des moyennes
    const avgFCP = performanceMetrics.reduce((sum, m) => sum + m.FCP, 0) / performanceMetrics.length;
    const avgLCP = performanceMetrics.reduce((sum, m) => sum + m.LCP, 0) / performanceMetrics.length;
    const avgCLS = performanceMetrics.reduce((sum, m) => sum + m.CLS, 0) / performanceMetrics.length;
    
    console.log(`\n📊 Moyennes Performance:`);
    console.log(`   FCP moyen: ${Math.round(avgFCP)}ms`);
    console.log(`   LCP moyen: ${Math.round(avgLCP)}ms`);
    console.log(`   CLS moyen: ${avgCLS.toFixed(3)}`);
    
    // Assertions moyennes
    expect(avgFCP).toBeLessThan(2000);
    expect(avgLCP).toBeLessThan(3500);
    expect(avgCLS).toBeLessThan(0.1);
  });

  test('🚀 Performance Chargement Ressources', async ({ page }) => {
    console.log('🔍 Test performance chargement ressources...');
    
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    
    const resourceMetrics = await page.evaluate((): ResourceMetrics => {
      const resources = performance.getEntriesByType('resource') as any[];
      
      const metrics: ResourceMetrics = {
        totalResources: resources.length,
        jsFiles: 0,
        cssFiles: 0,
        images: 0,
        totalSize: 0,
        slowResources: []
      };
      
      resources.forEach((resource: any) => {
        const duration = (resource.responseEnd || 0) - (resource.requestStart || 0);
        
        if (resource.name && resource.name.includes('.js')) metrics.jsFiles++;
        if (resource.name && resource.name.includes('.css')) metrics.cssFiles++;
        if (resource.name && resource.name.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) metrics.images++;
        
        if (resource.transferSize) {
          metrics.totalSize += resource.transferSize;
        }
        
        if (duration > 1000) {
          const name = resource.name ? resource.name.split('/').pop() : 'Unknown';
          metrics.slowResources.push({
            name: name || 'Unknown',
            duration: Math.round(duration)
          });
        }
      });
      
      return metrics;
    });
    
    console.log(`   📦 Total ressources: ${resourceMetrics.totalResources}`);
    console.log(`   📜 Fichiers JS: ${resourceMetrics.jsFiles}`);
    console.log(`   🎨 Fichiers CSS: ${resourceMetrics.cssFiles}`);
    console.log(`   🖼️ Images: ${resourceMetrics.images}`);
    console.log(`   💾 Taille totale: ${Math.round(resourceMetrics.totalSize / 1024)}KB`);
    
    if (resourceMetrics.slowResources.length > 0) {
      console.log(`   ⚠️ Ressources lentes:`);
      resourceMetrics.slowResources.forEach(res => {
        console.log(`     ${res.name}: ${res.duration}ms`);
      });
    }
    
    // Assertions ressources
    expect(resourceMetrics.totalResources).toBeLessThan(100); // Max 100 ressources
    expect(resourceMetrics.totalSize).toBeLessThan(5 * 1024 * 1024); // Max 5MB
    expect(resourceMetrics.slowResources.length).toBeLessThan(5); // Max 5 ressources lentes
  });

  test('💾 Performance Mémoire & CPU', async ({ page, context }) => {
    console.log('🔍 Test performance mémoire et CPU...');
    
    // Mesure initiale
    const initialMetrics = await page.evaluate(() => {
      return {
        memory: (performance as any).memory ? {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
        } : null,
        timing: performance.now()
      };
    });
    
    // Navigation intensive pour simuler l'utilisation
    const pages = ['/dashboard', '/journal', '/ai-coach', '/analytics', '/ml-analytics'];
    
    for (const url of pages) {
      await page.goto(url, { waitUntil: 'networkidle' });
      await page.waitForTimeout(2000);
      
      // Simulation d'interactions
      await page.mouse.move(100, 100);
      await page.mouse.move(300, 300);
      await page.keyboard.press('Tab');
    }
    
    // Mesure finale
    const finalMetrics = await page.evaluate(() => {
      return {
        memory: (performance as any).memory ? {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
        } : null,
        timing: performance.now()
      };
    });
    
    if (initialMetrics.memory && finalMetrics.memory) {
      const memoryGrowth = finalMetrics.memory.usedJSHeapSize - initialMetrics.memory.usedJSHeapSize;
      const memoryGrowthMB = memoryGrowth / (1024 * 1024);
      
      console.log(`   🧠 Mémoire initiale: ${Math.round(initialMetrics.memory.usedJSHeapSize / (1024 * 1024))}MB`);
      console.log(`   🧠 Mémoire finale: ${Math.round(finalMetrics.memory.usedJSHeapSize / (1024 * 1024))}MB`);
      console.log(`   📈 Croissance mémoire: ${memoryGrowthMB.toFixed(2)}MB`);
      
      // Assertion: croissance mémoire raisonnable
      expect(memoryGrowthMB).toBeLessThan(50); // Max 50MB de croissance
    }
    
    const executionTime = finalMetrics.timing - initialMetrics.timing;
    console.log(`   ⏱️ Temps d'exécution: ${Math.round(executionTime)}ms`);
  });

  test('🌐 Performance Réseau & Cache', async ({ page }) => {
    console.log('🔍 Test performance réseau et cache...');
    
    // Première visite (cache vide)
    const firstVisitStart = Date.now();
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    const firstVisitTime = Date.now() - firstVisitStart;
    
    // Deuxième visite (avec cache)
    const secondVisitStart = Date.now();
    await page.reload({ waitUntil: 'networkidle' });
    const secondVisitTime = Date.now() - secondVisitStart;
    
    console.log(`   🚀 Première visite: ${firstVisitTime}ms`);
    console.log(`   ⚡ Deuxième visite (cache): ${secondVisitTime}ms`);
    
    // Le cache devrait améliorer les performances
    const cacheImprovement = ((firstVisitTime - secondVisitTime) / firstVisitTime) * 100;
    console.log(`   📈 Amélioration cache: ${cacheImprovement.toFixed(1)}%`);
    
    // Assertions cache
    expect(secondVisitTime).toBeLessThan(firstVisitTime);
    expect(cacheImprovement).toBeGreaterThan(10); // Au moins 10% d'amélioration
    
    // Test des headers de cache
    const response = await page.goto('/dashboard');
    const cacheControl = response?.headers()['cache-control'];
    const etag = response?.headers()['etag'];
    
    console.log(`   🔖 Cache-Control: ${cacheControl || 'Non défini'}`);
    console.log(`   🏷️ ETag: ${etag ? 'Présent' : 'Absent'}`);
  });

  test('📱 Performance Mobile vs Desktop', async ({ page }) => {
    console.log('🔍 Test performance mobile vs desktop...');
    
    // Test Desktop
    await page.setViewportSize({ width: 1280, height: 720 });
    const desktopStart = Date.now();
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    const desktopTime = Date.now() - desktopStart;
    
    // Test Mobile
    await page.setViewportSize({ width: 375, height: 667 });
    const mobileStart = Date.now();
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    const mobileTime = Date.now() - mobileStart;
    
    console.log(`   🖥️ Desktop: ${desktopTime}ms`);
    console.log(`   📱 Mobile: ${mobileTime}ms`);
    
    const performanceDiff = Math.abs(mobileTime - desktopTime);
    console.log(`   📊 Différence: ${performanceDiff}ms`);
    
    // Les performances mobile ne devraient pas être drastiquement différentes
    expect(performanceDiff).toBeLessThan(2000); // Max 2s de différence
    expect(mobileTime).toBeLessThan(6000); // Mobile max 6s
    expect(desktopTime).toBeLessThan(5000); // Desktop max 5s
  });

  test('🔍 Performance Search & Filtering', async ({ page }) => {
    console.log('🔍 Test performance recherche et filtrage...');
    
    await page.goto('/professionals', { waitUntil: 'networkidle' });
    
    // Test performance de recherche
    const searchInput = await page.$('input[type="search"], input[placeholder*="recherch"]');
    if (searchInput) {
      const searchStart = Date.now();
      
      await searchInput.fill('psychologue');
      await page.waitForTimeout(1000); // Attendre les résultats
      
      const searchTime = Date.now() - searchStart;
      console.log(`   🔍 Recherche: ${searchTime}ms`);
      
      // La recherche devrait être rapide
      expect(searchTime).toBeLessThan(2000);
    }
    
    // Test performance de filtrage
    const filterSelect = await page.$('select, .filter-dropdown');
    if (filterSelect) {
      const filterStart = Date.now();
      
      await filterSelect.click();
      await page.waitForTimeout(500);
      
      const filterTime = Date.now() - filterStart;
      console.log(`   🔽 Filtrage: ${filterTime}ms`);
      
      expect(filterTime).toBeLessThan(1000);
    }
  });

});

test.describe('🎯 Tests Performance Spécialisés', () => {

  test('🧠 Performance ML Analytics - Fonctionnalité Phare', async ({ page }) => {
    console.log('🔍 Test performance ML Analytics...');
    
    const mlStart = Date.now();
    await page.goto('/ml-analytics', { waitUntil: 'networkidle', timeout: 15000 });
    const mlLoadTime = Date.now() - mlStart;
    
    console.log(`   🚀 Chargement ML Analytics: ${mlLoadTime}ms`);
    
    // Les analytics ML peuvent prendre plus de temps mais restent raisonnables
    expect(mlLoadTime).toBeLessThan(10000); // Max 10s pour les calculs ML
    
    // Test des interactions avec les graphiques ML
    const chart = await page.$('canvas, .recharts-surface');
    if (chart) {
      const interactionStart = Date.now();
      
      await chart.hover();
      await page.waitForTimeout(500);
      
      const interactionTime = Date.now() - interactionStart;
      console.log(`   📊 Interaction graphique ML: ${interactionTime}ms`);
      
      expect(interactionTime).toBeLessThan(1000);
    }
  });

  test('🎥 Performance Télémédecine - Streaming', async ({ page }) => {
    console.log('🔍 Test performance télémédecine...');
    
    await page.goto('/telemedicine-advanced', { waitUntil: 'networkidle' });
    
    // Test du temps d'initialisation des contrôles vidéo
    const videoControlsStart = Date.now();
    
    const cameraButton = await page.$('.camera-control, button[aria-label*="caméra"]');
    if (cameraButton) {
      await cameraButton.click();
      await page.waitForTimeout(1000);
      
      const initTime = Date.now() - videoControlsStart;
      console.log(`   📹 Initialisation vidéo: ${initTime}ms`);
      
      expect(initTime).toBeLessThan(3000);
    }
    
    // Test des outils diagnostiques
    const toolStart = Date.now();
    const stethoscope = await page.$('.stethoscope-tool, button:has-text("Stéthoscope")');
    if (stethoscope) {
      await stethoscope.click();
      await page.waitForTimeout(500);
      
      const toolTime = Date.now() - toolStart;
      console.log(`   🩺 Activation outil: ${toolTime}ms`);
      
      expect(toolTime).toBeLessThan(1500);
    }
  });

});

test.afterEach(async ({ page }, testInfo) => {
  // Mesurer les métriques finales de performance
  const finalMetrics = await page.evaluate(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as any;
    return {
      domContentLoaded: navigation ? (navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart) : 0,
      loadComplete: navigation ? (navigation.loadEventEnd - navigation.loadEventStart) : 0,
      totalTime: navigation ? (navigation.loadEventEnd - navigation.requestStart) : 0
    };
  });
  
  await testInfo.attach('performance-metrics', {
    body: JSON.stringify(finalMetrics, null, 2),
    contentType: 'application/json'
  });
}); 