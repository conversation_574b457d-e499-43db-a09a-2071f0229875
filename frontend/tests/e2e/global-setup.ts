import { chromium, FullConfig } from '@playwright/test';

/**
 * 🔧 CONFIGURATION GLOBALE PLAYWRIGHT - MINDFLOW PRO
 * Setup initial pour tous les tests
 */

async function globalSetup(config: FullConfig) {
  console.log('🚀 Initialisation des tests MindFlow Pro...');
  
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Vérifier que l'application est accessible
    console.log('🔍 Vérification de l\'accessibilité de l\'application...');
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    // Vérifier que la page se charge correctement
    const title = await page.title();
    console.log(`✅ Application accessible - Titre: ${title}`);
    
    // Préparer les données de test si nécessaire
    console.log('📝 Préparation des données de test...');
    
    // Ici vous pourriez ajouter du code pour :
    // - Nettoyer la base de données de test
    // - Créer des utilisateurs de test
    // - Préparer des données de test spécifiques
    
    console.log('✅ Configuration globale terminée avec succès');
    
  } catch (error) {
    console.error('❌ Erreur lors de la configuration globale:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
}

export default globalSetup; 