import { FullConfig } from '@playwright/test';

/**
 * 🧹 NETTOYAGE GLOBAL PLAYWRIGHT - MINDFLOW PRO
 * Nettoyage après tous les tests
 */

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Nettoyage global des tests MindFlow Pro...');
  
  try {
    // Nettoyage des données de test
    console.log('📝 Nettoyage des données de test...');
    
    // Ici vous pourriez ajouter du code pour :
    // - Nettoyer la base de données de test
    // - Supprimer les fichiers temporaires
    // - Fermer les connexions
    
    console.log('✅ Nettoyage global terminé avec succès');
    
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage global:', error);
    // Ne pas faire échouer les tests à cause du nettoyage
  }
}

export default globalTeardown; 