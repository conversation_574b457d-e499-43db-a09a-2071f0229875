import { test, expect } from '@playwright/test';

/**
 * 🎯 TESTS PHASE 1 - VALIDATION NOUVELLES FONCTIONNALITÉS
 */

test.describe('Phase 1 - Nouvelles Fonctionnalités', () => {
  
  test('Analytics Page Access', async ({ page }) => {
    await page.goto('/analytics');
    await expect(page).toHaveTitle(/MindFlow/);
    
    const content = await page.textContent('body');
    expect(content).toContain('Analytics');
    console.log('✅ Analytics page accessible');
  });
  
  test('Telemedicine Enhanced Access', async ({ page }) => {
    await page.goto('/telemedicine-enhanced'); 
    await expect(page).toHaveTitle(/MindFlow/);
    
    const content = await page.textContent('body');
    expect(content?.toLowerCase()).toMatch(/télémédecine|telemedicine/);
    console.log('✅ Telemedicine Enhanced accessible');
  });
  
  test('Dashboard Optimized Access', async ({ page }) => {
    await page.goto('/dashboard-optimized');
    await expect(page).toHaveTitle(/MindFlow/);
    
    const content = await page.textContent('body');
    expect(content).toContain('Dashboard');
    console.log('✅ Dashboard Optimized accessible');
  });

  test('Performance Basic Check', async ({ page }) => {
    const start = Date.now();
    await page.goto('/analytics', { waitUntil: 'networkidle' });
    const loadTime = Date.now() - start;
    
    console.log(`⏱️ Analytics load time: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(10000); // 10s max
  });

}); 