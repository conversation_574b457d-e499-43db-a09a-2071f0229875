import { test, expect, Page } from '@playwright/test';

/**
 * 🚀 TESTS NOUVELLES FONCTIONNALITÉS PHASE 1 - MINDFLOW PRO
 * Validation des composants Analytics et WebRTC ajoutés
 */

test.setTimeout(60000);

test.describe('🎯 Nouvelles Fonctionnalités Phase 1 - Validation Spécifique', () => {

  test('📊 Validation Page Analytics avec Composants', async ({ page }) => {
    console.log('🔍 Test page Analytics avec nouveaux composants...');
    
    await page.goto('/analytics', { waitUntil: 'networkidle' });
    
    // Vérifier que la page se charge
    await expect(page).toHaveTitle(/MindFlow/);
    
    const analyticsContent = await page.evaluate(() => {
      return {
        pageLoaded: document.readyState === 'complete',
        hasAnalyticsContent: document.body.textContent?.toLowerCase().includes('analytics') || false,
        hasMoodContent: document.body.textContent?.toLowerCase().includes('mood') || false,
        hasChartElements: document.querySelectorAll('svg, canvas, .chart').length,
        hasRechartsElements: document.querySelectorAll('.recharts-wrapper, [class*="recharts"]').length,
        contentLength: document.body.textContent?.length || 0
      };
    });
    
    console.log(`   📈 Page Analytics chargée: ${analyticsContent.pageLoaded}`);
    console.log(`   📊 Contenu Analytics détecté: ${analyticsContent.hasAnalyticsContent}`);
    console.log(`   🎭 Éléments graphiques: ${analyticsContent.hasChartElements}`);
    console.log(`   📈 Composants Recharts: ${analyticsContent.hasRechartsElements}`);
    
    // Assertions de base
    expect(analyticsContent.pageLoaded).toBe(true);
    expect(analyticsContent.contentLength).toBeGreaterThan(100);
    
    if (analyticsContent.hasRechartsElements > 0) {
      console.log('   ✅ Composants Recharts détectés et fonctionnels');
    } else {
      console.log('   ⚠️ Composants Recharts en cours de développement');
    }
  });

  test('🎥 Validation Page Télémédecine Enhanced', async ({ page }) => {
    console.log('🔍 Test page Télémédecine Enhanced...');
    
    await page.goto('/telemedicine-enhanced', { waitUntil: 'networkidle' });
    
    await expect(page).toHaveTitle(/MindFlow/);
    
    const telemedicineContent = await page.evaluate(() => {
      return {
        pageLoaded: document.readyState === 'complete',
        hasTelemedicineContent: document.body.textContent?.toLowerCase().includes('télémédecine') || 
                               document.body.textContent?.toLowerCase().includes('telemedicine') || false,
        hasVideoElements: document.querySelectorAll('video').length,
        hasControlElements: document.querySelectorAll('button, .control').length,
        hasWebRTCElements: document.querySelectorAll('[data-webrtc], [data-video]').length,
        contentLength: document.body.textContent?.length || 0
      };
    });
    
    console.log(`   🎥 Page Télémédecine chargée: ${telemedicineContent.pageLoaded}`);
    console.log(`   📺 Contenu télémédecine: ${telemedicineContent.hasTelemedicineContent}`);
    console.log(`   🎮 Éléments de contrôle: ${telemedicineContent.hasControlElements}`);
    console.log(`   📹 Éléments vidéo: ${telemedicineContent.hasVideoElements}`);
    
    expect(telemedicineContent.pageLoaded).toBe(true);
    expect(telemedicineContent.contentLength).toBeGreaterThan(100);
    
    if (telemedicineContent.hasVideoElements > 0 || telemedicineContent.hasWebRTCElements > 0) {
      console.log('   ✅ Composants WebRTC/Vidéo détectés');
    } else {
      console.log('   ⚠️ Composants WebRTC en cours d\'intégration');
    }
  });

  test('📊 Validation Dashboard Optimisé', async ({ page }) => {
    console.log('🔍 Test Dashboard optimisé...');
    
    await page.goto('/dashboard-optimized', { waitUntil: 'networkidle' });
    
    await expect(page).toHaveTitle(/MindFlow/);
    
    const dashboardContent = await page.evaluate(() => {
      return {
        pageLoaded: document.readyState === 'complete',
        hasDashboardContent: document.body.textContent?.toLowerCase().includes('dashboard') || false,
        hasOptimizedContent: document.body.textContent?.toLowerCase().includes('optimis') || false,
        hasAnalyticsComponents: document.querySelectorAll('[data-component*="Chart"], .chart').length,
        hasMonitoringElements: document.querySelectorAll('[data-monitoring], .monitoring').length,
        contentLength: document.body.textContent?.length || 0
      };
    });
    
    console.log(`   📊 Dashboard optimisé chargé: ${dashboardContent.pageLoaded}`);
    console.log(`   📈 Composants analytics: ${dashboardContent.hasAnalyticsComponents}`);
    console.log(`   📡 Éléments monitoring: ${dashboardContent.hasMonitoringElements}`);
    
    expect(dashboardContent.pageLoaded).toBe(true);
    expect(dashboardContent.contentLength).toBeGreaterThan(100);
    
    console.log('   ✅ Dashboard optimisé accessible');
  });

  test('⚡ Test Performance Nouvelles Pages', async ({ page }) => {
    console.log('🔍 Test performance nouvelles pages...');
    
    const pages = [
      { url: '/analytics', name: 'Analytics' },
      { url: '/telemedicine-enhanced', name: 'Télémédecine Enhanced' },
      { url: '/dashboard-optimized', name: 'Dashboard Optimisé' }
    ];
    
    const results = [];
    
    for (const pageInfo of pages) {
      const startTime = Date.now();
      
      try {
        await page.goto(pageInfo.url, { waitUntil: 'networkidle', timeout: 15000 });
        const loadTime = Date.now() - startTime;
        
        const metrics = await page.evaluate(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as any;
          return {
            domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.navigationStart : 0,
            loadEvent: navigation ? navigation.loadEventEnd - navigation.navigationStart : 0,
            resourceCount: performance.getEntriesByType('resource').length
          };
        });
        
        console.log(`   📊 ${pageInfo.name}:`);
        console.log(`     ⏱️ Load Time: ${loadTime}ms`);
        console.log(`     📦 Resources: ${metrics.resourceCount}`);
        console.log(`     🎯 DOM Ready: ${Math.round(metrics.domContentLoaded)}ms`);
        
        results.push({
          page: pageInfo.name,
          loadTime,
          metrics,
          success: true
        });
        
        // Assertions performance (objectifs Phase 2)
        expect(loadTime).toBeLessThan(8000); // 8s max pour nouvelles pages
        
      } catch (error) {
        console.log(`   ❌ ${pageInfo.name}: Erreur - ${error}`);
        results.push({
          page: pageInfo.name,
          success: false,
          error: error.toString()
        });
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    console.log(`\n📈 Performance Summary: ${successCount}/${pages.length} pages OK`);
    
    expect(successCount).toBeGreaterThanOrEqual(Math.floor(pages.length * 0.67)); // 67% minimum
  });

  test('🔧 Test Intégration Dépendances Phase 1', async ({ page }) => {
    console.log('🔍 Test intégration nouvelles dépendances...');
    
    // Tester page avec le plus de nouvelles dépendances
    await page.goto('/analytics', { waitUntil: 'networkidle' });
    
    // Capturer les erreurs console
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error' && 
          !msg.text().includes('favicon') && 
          !msg.text().includes('404')) {
        consoleErrors.push(msg.text());
      }
    });
    
    // Recharger pour capturer les erreurs de dépendances
    await page.reload({ waitUntil: 'networkidle' });
    await page.waitForTimeout(3000);
    
    const integrationStatus = await page.evaluate(() => {
      return {
        rechartsAvailable: typeof window !== 'undefined' && 
                          (document.querySelectorAll('.recharts-wrapper').length > 0 ||
                           document.querySelectorAll('[class*="recharts"]').length > 0),
        reactQueryAvailable: typeof window !== 'undefined' && 'ReactQuery' in (window as any),
        framerMotionAvailable: typeof window !== 'undefined' && 
                              (document.querySelectorAll('[style*="transform"]').length > 0 ||
                               document.querySelectorAll('[class*="motion"]').length > 0),
        noJSErrors: true // Sera vérifié via consoleErrors
      };
    });
    
    console.log(`   📦 Recharts intégré: ${integrationStatus.rechartsAvailable}`);
    console.log(`   🎬 Framer Motion détecté: ${integrationStatus.framerMotionAvailable}`);
    console.log(`   ❌ Erreurs console: ${consoleErrors.length}`);
    
    if (consoleErrors.length > 0) {
      console.log('   ⚠️ Erreurs détectées:', consoleErrors.slice(0, 3));
    }
    
    // Permettre quelques erreurs non critiques pendant le développement
    expect(consoleErrors.length).toBeLessThan(5);
    
    console.log('   ✅ Intégration dépendances validée');
  });

  test('🔄 Test Régression Pages Existantes', async ({ page }) => {
    console.log('🔍 Test régression pages existantes...');
    
    const existingPages = [
      { url: '/', name: 'Accueil' },
      { url: '/dashboard', name: 'Dashboard Original' },
      { url: '/journal', name: 'Journal' },
      { url: '/ai-coach', name: 'IA Coach' }
    ];
    
    let workingPages = 0;
    
    for (const pageInfo of existingPages) {
      try {
        await page.goto(pageInfo.url, { waitUntil: 'networkidle', timeout: 10000 });
        
        const isWorking = await page.evaluate(() => {
          return document.readyState === 'complete' && 
                 document.body.textContent && 
                 document.body.textContent.trim().length > 50;
        });
        
        if (isWorking) {
          console.log(`   ✅ ${pageInfo.name}: Fonctionnel`);
          workingPages++;
        } else {
          console.log(`   ⚠️ ${pageInfo.name}: Contenu minimal`);
        }
        
      } catch (error) {
        console.log(`   ❌ ${pageInfo.name}: Erreur de chargement`);
      }
    }
    
    console.log(`\n📊 Régression: ${workingPages}/${existingPages.length} pages OK`);
    
    // Au moins 75% des pages existantes doivent fonctionner
    expect(workingPages).toBeGreaterThanOrEqual(Math.floor(existingPages.length * 0.75));
    
    console.log('   ✅ Régression validée - Pages existantes préservées');
  });

}); 