import { test, expect, Page, BrowserContext } from '@playwright/test';

/**
 * 🚀 TESTS OPTIMISATIONS PHASE 2 - MINDFLOW PRO
 * Validation spécialisée des améliorations Phase 2
 * 
 * Nouvelles fonctionnalités testées :
 * ✅ Composants Analytics avec graphiques Recharts
 * ✅ Télémédecine WebRTC avec contrôles avancés  
 * ✅ Performance optimisée (FCP <3.2s, Bundle <2.1MB)
 * ✅ Dashboard optimisé avec monitoring temps réel
 */

test.setTimeout(120000);

test.describe('🚀 Optimisations Phase 2 - Validation Spécialisée', () => {

  test('📊 Validation Nouveaux Composants Analytics', async ({ page }) => {
    console.log('🔍 Test nouveaux composants Analytics avec graphiques...');
    
    await page.goto('/analytics', { waitUntil: 'networkidle' });
    
    // Vérifier présence des nouveaux composants Analytics
    const analyticsComponents = await page.evaluate(() => {
      const components = {
        moodTrendChart: document.querySelector('[data-testid="mood-trend-chart"]') !== null,
        performanceChart: document.querySelector('[data-testid="performance-chart"]') !== null,
        rechartElements: document.querySelectorAll('.recharts-wrapper').length,
        chartTooltips: document.querySelectorAll('.recharts-tooltip-wrapper').length,
        moodMetrics: document.querySelector('[data-metric="mood"]') !== null,
        anxietyMetrics: document.querySelector('[data-metric="anxiety"]') !== null,
        stressMetrics: document.querySelector('[data-metric="stress"]') !== null,
        energyMetrics: document.querySelector('[data-metric="energy"]') !== null,
        sleepMetrics: document.querySelector('[data-metric="sleep"]') !== null,
        hasLineChart: document.querySelector('.recharts-line') !== null,
        hasBarChart: document.querySelector('.recharts-bar') !== null,
        hasPieChart: document.querySelector('.recharts-pie') !== null
      };
      
      return components;
    });
    
    // Assertions composants Analytics
    expect(analyticsComponents.rechartElements).toBeGreaterThan(0);
    console.log(`   📈 Graphiques Recharts détectés: ${analyticsComponents.rechartElements}`);
    
    // Vérifier les 5 métriques principales
    expect(analyticsComponents.moodMetrics).toBe(true);
    expect(analyticsComponents.anxietyMetrics).toBe(true);
    expect(analyticsComponents.stressMetrics).toBe(true);
    expect(analyticsComponents.energyMetrics).toBe(true);
    expect(analyticsComponents.sleepMetrics).toBe(true);
    console.log('   ✅ Toutes les métriques (5/5) détectées');
    
    // Vérifier les types de graphiques
    const chartTypes = [
      analyticsComponents.hasLineChart,
      analyticsComponents.hasBarChart,
      analyticsComponents.hasPieChart
    ].filter(Boolean).length;
    
    expect(chartTypes).toBeGreaterThan(0);
    console.log(`   📊 Types de graphiques supportés: ${chartTypes}`);
    
    // Test interactions avec les graphiques
    if (analyticsComponents.rechartElements > 0) {
      await page.hover('.recharts-wrapper');
      const tooltipVisible = await page.isVisible('.recharts-tooltip-wrapper');
      console.log(`   🎯 Tooltips interactifs: ${tooltipVisible ? 'Fonctionnels' : 'Non détectés'}`);
    }
  });

  test('🎥 Validation Télémédecine WebRTC Avancée', async ({ page }) => {
    console.log('🔍 Test composants Télémédecine WebRTC...');
    
    await page.goto('/telemedicine-enhanced', { waitUntil: 'networkidle' });
    
    // Vérifier présence composants WebRTC
    const webrtcComponents = await page.evaluate(() => {
      const components = {
        videoControls: document.querySelector('[data-testid="video-controls"]') !== null,
        webrtcProvider: document.querySelector('[data-provider="webrtc"]') !== null,
        videoElement: document.querySelector('video') !== null,
        audioControls: document.querySelector('[data-control="audio"]') !== null,
        videoToggle: document.querySelector('[data-control="video"]') !== null,
        screenShare: document.querySelector('[data-control="screen-share"]') !== null,
        recordButton: document.querySelector('[data-control="record"]') !== null,
        qualityIndicator: document.querySelector('[data-indicator="quality"]') !== null,
        connectionStatus: document.querySelector('[data-status="connection"]') !== null,
        professionalControls: document.querySelectorAll('[data-role="professional"]').length,
        hasCallButtons: document.querySelectorAll('button[data-action*="call"]').length
      };
      
      return components;
    });
    
    // Assertions WebRTC
    expect(webrtcComponents.videoControls).toBe(true);
    console.log('   📹 Contrôles vidéo: Détectés');
    
    expect(webrtcComponents.audioControls).toBe(true);
    expect(webrtcComponents.videoToggle).toBe(true);
    expect(webrtcComponents.screenShare).toBe(true);
    expect(webrtcComponents.recordButton).toBe(true);
    console.log('   🎛️ Contrôles complets (4/4): Audio, Vidéo, Partage, Enregistrement');
    
    expect(webrtcComponents.qualityIndicator).toBe(true);
    expect(webrtcComponents.connectionStatus).toBe(true);
    console.log('   📊 Monitoring temps réel: Actif');
    
    expect(webrtcComponents.professionalControls).toBeGreaterThan(0);
    console.log(`   👨‍⚕️ Contrôles professionnels: ${webrtcComponents.professionalControls}`);
    
    // Test simulation d'interactions
    const callButton = page.locator('button[data-action*="call"]').first();
    if (await callButton.isVisible()) {
      await callButton.click();
      await page.waitForTimeout(1000);
      
      const callStateChanged = await page.evaluate(() => {
        return document.querySelector('[data-state="calling"]') !== null ||
               document.querySelector('[data-state="connected"]') !== null;
      });
      
      console.log(`   📞 État d'appel: ${callStateChanged ? 'Réactif' : 'Non détecté'}`);
    }
  });

  test('🚀 Validation Performance Optimisée Phase 2', async ({ page }) => {
    console.log('🔍 Test performances optimisées Phase 2...');
    
    const performanceTest = async (url: string, pageName: string) => {
      const startTime = Date.now();
      
      await page.goto(url, { waitUntil: 'networkidle' });
      
      const loadTime = Date.now() - startTime;
      
      // Mesurer FCP et LCP
      const vitals = await page.evaluate(() => {
        return new Promise<{FCP: number, LCP: number}>((resolve) => {
          const vitals = { FCP: 0, LCP: 0 };
          
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (entry.name === 'first-contentful-paint') {
                vitals.FCP = entry.startTime;
              }
            }
          }).observe({ entryTypes: ['paint'] });
          
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1] as any;
            if (lastEntry) vitals.LCP = lastEntry.startTime;
          }).observe({ entryTypes: ['largest-contentful-paint'] });
          
          setTimeout(() => resolve(vitals), 2000);
        });
      });
      
      // Mesurer taille du bundle
      const bundleSize = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource') as any[];
        return resources.reduce((total: number, resource: any) => {
          return total + (resource.transferSize || 0);
        }, 0);
      });
      
      console.log(`   📊 ${pageName}:`);
      console.log(`     ⏱️ Load Time: ${loadTime}ms`);
      console.log(`     🎨 FCP: ${Math.round(vitals.FCP)}ms`);
      console.log(`     📸 LCP: ${Math.round(vitals.LCP)}ms`);
      console.log(`     💾 Bundle: ${Math.round(bundleSize / 1024)}KB`);
      
      // Assertions optimisations Phase 2
      expect(loadTime).toBeLessThan(4000); // Load < 4s
      expect(vitals.FCP).toBeLessThan(3200); // FCP < 3.2s (objectif Phase 2)
      expect(vitals.LCP).toBeLessThan(4800); // LCP < 4.8s
      expect(bundleSize).toBeLessThan(2.1 * 1024 * 1024); // Bundle < 2.1MB
      
      return { loadTime, FCP: vitals.FCP, LCP: vitals.LCP, bundleSize };
    };
    
    // Test pages optimisées
    const dashboardMetrics = await performanceTest('/dashboard-optimized', 'Dashboard Optimisé');
    const analyticsMetrics = await performanceTest('/analytics', 'Analytics avec Graphiques');
    const telemedicineMetrics = await performanceTest('/telemedicine-enhanced', 'Télémédecine WebRTC');
    
    // Calculs moyennes
    const avgFCP = (dashboardMetrics.FCP + analyticsMetrics.FCP + telemedicineMetrics.FCP) / 3;
    const avgBundle = (dashboardMetrics.bundleSize + analyticsMetrics.bundleSize + telemedicineMetrics.bundleSize) / 3;
    
    console.log(`\n📈 Moyennes Optimisations Phase 2:`);
    console.log(`   FCP moyen: ${Math.round(avgFCP)}ms (objectif: <3200ms)`);
    console.log(`   Bundle moyen: ${Math.round(avgBundle / 1024)}KB (objectif: <2100KB)`);
    
    // Validation objectifs Phase 2
    expect(avgFCP).toBeLessThan(3200);
    expect(avgBundle).toBeLessThan(2.1 * 1024 * 1024);
  });

  test('📊 Validation Dashboard Optimisé avec Monitoring', async ({ page }) => {
    console.log('🔍 Test dashboard optimisé avec monitoring temps réel...');
    
    await page.goto('/dashboard-optimized', { waitUntil: 'networkidle' });
    
    // Vérifier composants dashboard optimisé
    const dashboardFeatures = await page.evaluate(() => {
      return {
        moodTrendChart: document.querySelector('[data-component="MoodTrendChart"]') !== null,
        performanceChart: document.querySelector('[data-component="PerformanceChart"]') !== null,
        realTimeMetrics: document.querySelectorAll('[data-metric-type="realtime"]').length,
        analyticsCards: document.querySelectorAll('[data-card-type="analytics"]').length,
        monitoringIndicators: document.querySelectorAll('[data-indicator="monitoring"]').length,
        loadingOptimizations: document.querySelector('[data-optimization="lazy-loading"]') !== null,
        memoryUsage: document.querySelector('[data-metric="memory"]') !== null,
        performanceMetrics: document.querySelector('[data-metrics="performance"]') !== null
      };
    });
    
    // Assertions dashboard optimisé
    expect(dashboardFeatures.moodTrendChart).toBe(true);
    expect(dashboardFeatures.performanceChart).toBe(true);
    console.log('   📈 Nouveaux graphiques intégrés au dashboard');
    
    expect(dashboardFeatures.realTimeMetrics).toBeGreaterThan(0);
    console.log(`   ⏱️ Métriques temps réel: ${dashboardFeatures.realTimeMetrics}`);
    
    expect(dashboardFeatures.analyticsCards).toBeGreaterThan(0);
    console.log(`   📊 Cartes analytics: ${dashboardFeatures.analyticsCards}`);
    
    // Test interactivité optimisée
    const interactionTest = await page.evaluate(() => {
      const startTime = performance.now();
      
      // Simuler interactions sur les nouveaux composants
      const chartElement = document.querySelector('[data-component="MoodTrendChart"]');
      if (chartElement) {
        chartElement.dispatchEvent(new Event('mouseenter'));
        chartElement.dispatchEvent(new Event('mouseleave'));
      }
      
      const endTime = performance.now();
      return endTime - startTime;
    });
    
    console.log(`   🎯 Réactivité interactions: ${Math.round(interactionTest)}ms`);
    expect(interactionTest).toBeLessThan(100); // Réactivité < 100ms
  });

  test('🧪 Test Intégration Nouvelles Dépendances', async ({ page }) => {
    console.log('🔍 Test intégration nouvelles dépendances Phase 2...');
    
    await page.goto('/analytics', { waitUntil: 'networkidle' });
    
    // Vérifier que les nouvelles librairies sont chargées correctement
    const librariesLoaded = await page.evaluate(() => {
      return {
        recharts: typeof window !== 'undefined' && 'Recharts' in (window as any),
        dateFns: typeof window !== 'undefined' && 'dateFns' in (window as any),
        framerMotion: typeof window !== 'undefined' && 'FramerMotion' in (window as any),
        reactRechartsLoaded: document.querySelector('.recharts-wrapper') !== null,
        animationsWorking: document.querySelector('[style*="transform"]') !== null,
        noConsoleErrors: true // Sera vérifié via les logs console
      };
    });
    
    // Vérifier absence d'erreurs console
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.reload({ waitUntil: 'networkidle' });
    await page.waitForTimeout(2000);
    
    console.log(`   📦 Recharts chargé: ${librariesLoaded.reactRechartsLoaded}`);
    console.log(`   🎬 Animations détectées: ${librariesLoaded.animationsWorking}`);
    console.log(`   ❌ Erreurs console: ${consoleErrors.length}`);
    
    expect(librariesLoaded.reactRechartsLoaded).toBe(true);
    expect(consoleErrors.length).toBeLessThan(3); // Max 2 erreurs non critiques
    
    if (consoleErrors.length > 0) {
      console.log('   ⚠️ Erreurs détectées:', consoleErrors.slice(0, 3));
    }
  });

  test('🎯 Test Régression - Fonctionnalités Existantes', async ({ page }) => {
    console.log('🔍 Test régression sur fonctionnalités existantes...');
    
    const existingFeatures = [
      { url: '/dashboard', name: 'Dashboard Original', selector: '[data-testid="dashboard"]' },
      { url: '/journal', name: 'Journal', selector: '[data-testid="journal"], h1' },
      { url: '/ai-coach', name: 'IA Coach', selector: '[data-testid="ai-coach"], h1' },
      { url: '/appointments', name: 'Rendez-vous', selector: '[data-testid="appointments"], h1' },
      { url: '/professionals', name: 'Professionnels', selector: '[data-testid="professionals"], h1' }
    ];
    
    let regressionCount = 0;
    
    for (const feature of existingFeatures) {
      try {
        await page.goto(feature.url, { waitUntil: 'networkidle', timeout: 10000 });
        
        const isWorking = await page.locator(feature.selector).isVisible();
        
        if (isWorking) {
          console.log(`   ✅ ${feature.name}: Fonctionnel`);
        } else {
          console.log(`   ❌ ${feature.name}: Régression détectée`);
          regressionCount++;
        }
        
      } catch (error) {
        console.log(`   ❌ ${feature.name}: Erreur de chargement`);
        regressionCount++;
      }
    }
    
    console.log(`\n📊 Résumé régression: ${regressionCount}/${existingFeatures.length} problèmes`);
    expect(regressionCount).toBeLessThan(2); // Max 1 régression acceptable
  });

});

test.describe('📈 Métriques & KPIs Phase 2', () => {
  
  test('🎯 Validation KPIs Performance Phase 2', async ({ page }) => {
    console.log('🔍 Validation des KPIs Phase 2...');
    
    const kpiTargets = {
      bundleReduction: 40, // -40%
      fcpImprovement: 60, // -60%
      lcpImprovement: 20, // -20%
      lighthouseScore: 78  // Score 78+
    };
    
    await page.goto('/dashboard-optimized', { waitUntil: 'networkidle' });
    
    // Mesurer et comparer avec baseline
    const currentMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as any;
      const resources = performance.getEntriesByType('resource') as any[];
      
      return {
        loadTime: navigation ? navigation.loadEventEnd - navigation.navigationStart : 0,
        bundleSize: resources.reduce((total: number, r: any) => total + (r.transferSize || 0), 0),
        fcpTime: 0, // Sera mesuré séparément
        resourceCount: resources.length
      };
    });
    
    console.log(`\n📊 Métriques actuelles Phase 2:`);
    console.log(`   Bundle: ${Math.round(currentMetrics.bundleSize / 1024)}KB`);
    console.log(`   Load Time: ${Math.round(currentMetrics.loadTime)}ms`);
    console.log(`   Ressources: ${currentMetrics.resourceCount}`);
    
    // Valider objectifs Phase 2 
    expect(currentMetrics.bundleSize).toBeLessThan(2.2 * 1024 * 1024); // <2.2MB
    expect(currentMetrics.loadTime).toBeLessThan(4000); // <4s
    expect(currentMetrics.resourceCount).toBeLessThan(80); // <80 ressources
    
    console.log('✅ Tous les KPIs Phase 2 validés');
  });

}); 