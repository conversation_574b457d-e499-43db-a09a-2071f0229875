import { test, expect, Page } from '@playwright/test';

/**
 * 🎯 TESTS FLUX UTILISATEUR CRITIQUES - MINDFLOW PRO
 * Tests end-to-end des parcours utilisateur les plus importants
 */

test.setTimeout(180000); // 3 minutes par test pour les flux complets

test.describe('🎯 Flux Utilisateur Critiques', () => {

  test.beforeEach(async ({ page }) => {
    await page.goto('/', { waitUntil: 'networkidle' });
  });

  test('🚀 Flux Complet: Nouveau Patient - Première Consultation', async ({ page }) => {
    console.log('🔍 Test flux complet nouveau patient...');
    
    // 1. Navigation vers la page de rendez-vous
    await page.goto('/appointments', { waitUntil: 'networkidle' });
    await expect(page.locator('h1')).toContainText(/rendez-vous|appointments/i);
    
    // 2. Recherche d'un professionnel
    const searchInput = await page.$('input[placeholder*="recherch"], input[type="search"]');
    if (searchInput) {
      await searchInput.fill('psychologue');
      await page.waitForTimeout(1000);
    }
    
    // 3. Sélection d'un professionnel
    const professionalCard = await page.$('.professional-card, .doctor-card, .therapist-card');
    if (professionalCard) {
      await professionalCard.click();
      await page.waitForTimeout(1000);
    }
    
    // 4. Vérification page de réservation
    const bookingPage = page.url().includes('book') || 
                       await page.$('.booking-form, .appointment-form') !== null;
    expect(bookingPage).toBeTruthy();
    
    console.log('   ✅ Flux nouveau patient testé avec succès');
  });

  test('💬 Flux IA Coach: Session Complète Gestion Stress', async ({ page }) => {
    console.log('🔍 Test session complète IA Coach...');
    
    // 1. Navigation vers IA Coach
    await page.goto('/ai-coach', { waitUntil: 'networkidle' });
    
    // 2. Sélection du thème gestion de stress
    const stressButton = await page.$('button:has-text("Stress"), .theme-stress, button:has-text("Anxiété")');
    if (stressButton) {
      await stressButton.click();
      await page.waitForTimeout(1000);
    }
    
    // 3. Interaction chat avec l'IA
    const chatInput = await page.$('textarea, input[type="text"]');
    if (chatInput) {
      // Première interaction
      await chatInput.fill('Je me sens très stressé au travail ces derniers jours');
      const sendButton = await page.$('button:has-text("Envoyer"), button[type="submit"]');
      if (sendButton) {
        await sendButton.click();
        await page.waitForTimeout(3000);
        
        // Vérifier la réponse de l'IA
        const aiResponse = await page.$('.ai-response, .chat-message, .response');
        expect(aiResponse).toBeTruthy();
        
        // Deuxième interaction
        await chatInput.fill('Quelles techniques de relaxation me conseillez-vous ?');
        await sendButton.click();
        await page.waitForTimeout(3000);
      }
    }
    
    console.log('   ✅ Session IA Coach complète testée');
  });

  test('📊 Flux Analytics: Consultation Données Personnelles', async ({ page }) => {
    console.log('🔍 Test flux consultation analytics...');
    
    // 1. Navigation vers analytics
    await page.goto('/analytics', { waitUntil: 'networkidle' });
    
    // 2. Vérifier les graphiques principaux
    const charts = await page.$$('canvas, svg, .chart');
    expect(charts.length).toBeGreaterThan(0);
    
    // 3. Interaction avec les filtres temporels
    const timeFilter = await page.$('select[name*="period"], .time-filter, button:has-text("Mois")');
    if (timeFilter) {
      await timeFilter.click();
      await page.waitForTimeout(1000);
      
      const weekOption = await page.$('option[value="week"], button:has-text("Semaine")');
      if (weekOption) {
        await weekOption.click();
        await page.waitForTimeout(2000);
        
        // Vérifier que les données se mettent à jour
        const updatedChart = await page.$('canvas, svg');
        expect(updatedChart).toBeTruthy();
      }
    }
    
    // 4. Accès aux détails d'une métrique
    const metricCard = await page.$('.metric-card, .stat-card, .analytics-card');
    if (metricCard) {
      await metricCard.click();
      await page.waitForTimeout(1000);
      
      // Vérifier l'affichage des détails
      const detailView = await page.$('.metric-detail, .expanded-view, .detail-modal');
      expect(detailView).toBeTruthy();
    }
    
    console.log('   ✅ Flux analytics testé avec interactions');
  });

  test('📝 Flux Journal: Création Entrée Complète', async ({ page }) => {
    console.log('🔍 Test création entrée journal complète...');
    
    // 1. Navigation vers journal
    await page.goto('/journal', { waitUntil: 'networkidle' });
    
    // 2. Clic sur nouvelle entrée
    const newEntryButton = await page.$('button:has-text("Nouvelle"), a:has-text("Nouvelle"), .add-entry');
    if (newEntryButton) {
      await newEntryButton.click();
      await page.waitForTimeout(1000);
      
      // 3. Remplissage du formulaire
      const titleInput = await page.$('input[name="title"], input[placeholder*="titre"]');
      if (titleInput) {
        await titleInput.fill('Réflexions du jour - Test Playwright');
      }
      
      const contentInput = await page.$('textarea[name="content"], textarea[placeholder*="contenu"]');
      if (contentInput) {
        await contentInput.fill('Aujourd\'hui a été une journée productive. J\'ai réussi à terminer mes tests Playwright pour MindFlow Pro. Je me sens satisfait du travail accompli.');
      }
      
      // 4. Sélection de l'humeur
      const moodSelector = await page.$('select[name="mood"], .mood-selector');
      if (moodSelector) {
        await moodSelector.selectOption('positive');
      }
      
      // 5. Sauvegarde
      const saveButton = await page.$('button:has-text("Sauvegarder"), button[type="submit"]');
      if (saveButton) {
        await saveButton.click();
        await page.waitForTimeout(2000);
        
        // Vérifier la redirection ou confirmation
        const confirmation = page.url().includes('journal') || 
                            await page.$('.success-message, .confirmation') !== null;
        expect(confirmation).toBeTruthy();
      }
    }
    
    console.log('   ✅ Flux création journal testé');
  });

  test('🏥 Flux Télémédecine: Consultation Virtuelle', async ({ page }) => {
    console.log('🔍 Test flux consultation télémédecine...');
    
    // 1. Navigation vers télémédecine avancée
    await page.goto('/telemedicine-advanced', { waitUntil: 'networkidle' });
    
    // 2. Démarrage d'une session
    const startButton = await page.$('button:has-text("Démarrer"), button:has-text("Commencer"), .start-session');
    if (startButton) {
      await startButton.click();
      await page.waitForTimeout(2000);
    }
    
    // 3. Test des contrôles vidéo
    const cameraButton = await page.$('button[aria-label*="caméra"], .camera-control');
    if (cameraButton) {
      await cameraButton.click();
      await page.waitForTimeout(1000);
    }
    
    const micButton = await page.$('button[aria-label*="micro"], .mic-control');
    if (micButton) {
      await micButton.click();
      await page.waitForTimeout(1000);
    }
    
    // 4. Test outil diagnostique - Stéthoscope virtuel
    const stethoscopeButton = await page.$('button:has-text("Stéthoscope"), .stethoscope-tool');
    if (stethoscopeButton) {
      await stethoscopeButton.click();
      await page.waitForTimeout(1000);
      
      // Vérifier l'activation de l'outil
      const activeTool = await page.$('.tool-active, .diagnostic-active');
      expect(activeTool).toBeTruthy();
    }
    
    // 5. Test de fin de session
    const endButton = await page.$('button:has-text("Terminer"), .end-session');
    if (endButton) {
      await endButton.click();
      await page.waitForTimeout(1000);
    }
    
    console.log('   ✅ Flux télémédecine testé avec outils diagnostiques');
  });

  test('🔒 Flux Conformité: Consultation Statuts Certifications', async ({ page }) => {
    console.log('🔍 Test flux consultation conformité...');
    
    // 1. Navigation vers conformité
    await page.goto('/compliance', { waitUntil: 'networkidle' });
    
    // 2. Vérification des certifications principales
    const certifications = ['HDS', 'ISO 27001', 'HIPAA', 'SOC 2'];
    
    for (const cert of certifications) {
      const certCard = await page.$(`text=${cert}`);
      if (certCard) {
        await certCard.click();
        await page.waitForTimeout(1000);
        
        // Vérifier l'affichage des détails
        const certDetails = await page.$('.cert-details, .certification-info, .compliance-detail');
        if (certDetails) {
          console.log(`   📋 ${cert}: Détails affichés`);
        }
      }
    }
    
    // 3. Consultation du rapport d'audit
    const auditButton = await page.$('button:has-text("Audit"), .audit-report, .compliance-report');
    if (auditButton) {
      await auditButton.click();
      await page.waitForTimeout(2000);
      
      // Vérifier l'affichage du rapport
      const report = await page.$('.audit-report, .compliance-document');
      expect(report).toBeTruthy();
    }
    
    console.log('   ✅ Flux conformité testé avec certifications');
  });

  test('🔗 Flux Intégrations B2B: Gestion Connecteurs', async ({ page }) => {
    console.log('🔍 Test flux intégrations B2B...');
    
    // 1. Navigation vers intégrations B2B
    await page.goto('/integrations-b2b', { waitUntil: 'networkidle' });
    
    // 2. Vérification des catégories d'intégrations
    const categories = ['Hôpitaux', 'Laboratoires', 'Pharmacies', 'SDK'];
    
    for (const category of categories) {
      const categoryTab = await page.$(`button:has-text("${category}"), .tab-${category.toLowerCase()}`);
      if (categoryTab) {
        await categoryTab.click();
        await page.waitForTimeout(1000);
        
        // Vérifier l'affichage des connecteurs
        const connectors = await page.$$('.connector-card, .integration-item');
        expect(connectors.length).toBeGreaterThan(0);
        
        console.log(`   🔌 ${category}: ${connectors.length} connecteurs trouvés`);
      }
    }
    
    // 3. Test de configuration d'un connecteur
    const firstConnector = await page.$('.connector-card, .integration-item');
    if (firstConnector) {
      await firstConnector.click();
      await page.waitForTimeout(1000);
      
      // Vérifier l'affichage des options de configuration
      const configPanel = await page.$('.config-panel, .integration-config, .connector-settings');
      expect(configPanel).toBeTruthy();
    }
    
    console.log('   ✅ Flux intégrations B2B testé');
  });

  test('🧠 Flux ML Analytics: Exploration Prédictions', async ({ page }) => {
    console.log('🔍 Test flux ML Analytics - Fonctionnalité phare...');
    
    // 1. Navigation vers ML Analytics
    await page.goto('/ml-analytics', { waitUntil: 'networkidle', timeout: 15000 });
    
    // 2. Vérification du chargement des composants ML
    await expect(page.locator('h1')).toContainText('Analytics Prédictifs');
    
    // 3. Interaction avec les graphiques prédictifs
    const predictiveCharts = await page.$$('canvas, .recharts-surface');
    if (predictiveCharts.length > 0) {
      await predictiveCharts[0].hover();
      await page.waitForTimeout(1000);
      
      // Vérifier les tooltips ou données détaillées
      console.log('   📈 Graphique prédictif: Interaction testée');
    }
    
    // 4. Test des filtres de modèles ML
    const modelFilter = await page.$('select[name*="model"], .model-selector');
    if (modelFilter) {
      await modelFilter.selectOption('tensorflow');
      await page.waitForTimeout(2000);
      
      // Vérifier la mise à jour des prédictions
      const updatedData = await page.$('.prediction-updated, .ml-result');
      expect(updatedData).toBeTruthy();
    }
    
    // 5. Export de rapport ML
    const exportButton = await page.$('button:has-text("Export"), .export-ml-report');
    if (exportButton) {
      await exportButton.click();
      await page.waitForTimeout(1000);
      
      console.log('   📊 Export ML: Bouton testé');
    }
    
    console.log('   ✅ Flux ML Analytics phare testé avec succès');
  });

});

// Tests de régression pour les fonctionnalités critiques
test.describe('🔄 Tests de Régression', () => {

  test('📱 Régression: Navigation Mobile', async ({ page }) => {
    // Test de régression pour s'assurer que la navigation mobile fonctionne
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    
    // Test du menu burger mobile
    const mobileMenu = await page.$('.mobile-menu, .hamburger, .menu-toggle');
    if (mobileMenu) {
      await mobileMenu.click();
      await page.waitForTimeout(1000);
      
      const menuItems = await page.$$('.nav-item, .menu-item');
      expect(menuItems.length).toBeGreaterThan(0);
    }
    
    console.log('   ✅ Navigation mobile: Régression OK');
  });

  test('⚡ Régression: Performance Chargement', async ({ page }) => {
    // Test de régression pour la performance
    const startTime = Date.now();
    
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    
    const loadTime = Date.now() - startTime;
    
    // Vérifier que le temps de chargement reste acceptable
    expect(loadTime).toBeLessThan(5000); // 5 secondes max
    
    console.log(`   ⏱️ Temps de chargement dashboard: ${loadTime}ms`);
  });

});

test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    // Capturer des données supplémentaires en cas d'échec
    const screenshot = await page.screenshot({ fullPage: true });
    await testInfo.attach('screenshot-full', { body: screenshot, contentType: 'image/png' });
    
    // Capturer les logs de la console
    const logs = await page.evaluate(() => {
      return window.console.toString();
    });
    await testInfo.attach('console-logs', { body: logs, contentType: 'text/plain' });
  }
}); 