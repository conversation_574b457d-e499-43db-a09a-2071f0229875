import { test, expect } from '@playwright/test';

test.describe('🚀 Phase 2B - Tests Complets Post-Corrections', () => {

  test.beforeEach(async ({ page }) => {
    // Navigation vers l'accueil pour commencer les tests
    await page.goto('/', { waitUntil: 'networkidle' });
  });

  test('🎯 Dashboard Optimisé - Titre et Performance', async ({ page }) => {
    console.log('🔍 Test dashboard optimisé avec titre corrigé...');
    
    const startTime = Date.now();
    await page.goto('/dashboard-optimized', { waitUntil: 'networkidle' });
    const loadTime = Date.now() - startTime;
    
    // Vérifier le titre de la page
    await expect(page).toHaveTitle(/Dashboard Optimisé.*MindFlow/);
    console.log(`   ✅ Titre de page corrigé détecté`);
    
    // Vérifier le contenu spécifique du dashboard optimisé
    await expect(page.locator('h1')).toContainText('Dashboard Optimisé');
    
    // Vérifier la présence des badges de nouvelles fonctionnalités
    const featuresBadge = page.locator('text=Nouvelles fonctionnalités');
    const performanceBadge = page.locator('text=Performance optimisée');
    
    await expect(featuresBadge).toBeVisible();
    await expect(performanceBadge).toBeVisible();
    
    // Vérifier les métriques de performance affichées
    const performanceSection = page.locator('text=Optimisations de Performance');
    await expect(performanceSection).toBeVisible();
    
    // Vérifier la présence du graphique d'humeur
    const moodChart = page.locator('text=Tendances d\'Humeur');
    await expect(moodChart).toBeVisible();
    
    console.log(`   ⚡ Temps de chargement: ${loadTime}ms`);
    console.log(`   📊 Contenu dashboard optimisé: OK`);
    
    // Performance validation
    expect(loadTime).toBeLessThan(3000); // < 3s
  });

  test('📊 Composants Recharts - Intégration Fonctionnelle', async ({ page }) => {
    console.log('🔍 Test intégration Recharts corrigée...');
    
    await page.goto('/analytics', { waitUntil: 'networkidle' });
    
    // Attendre le chargement des composants
    await page.waitForTimeout(2000);
    
    // Vérifier la présence d'éléments SVG (Recharts)
    const svgElements = await page.locator('svg').count();
    console.log(`   📈 Éléments SVG détectés: ${svgElements}`);
    
    // Vérifier les containers Recharts spécifiques
    const rechartsContainers = await page.locator('.recharts-wrapper, [class*="recharts"]').count();
    console.log(`   📊 Containers Recharts: ${rechartsContainers}`);
    
    // Nouveau test: Vérifier le composant test Recharts
    const testRechartsSection = page.locator('text=Test Recharts Integration');
    if (await testRechartsSection.isVisible()) {
      console.log(`   ✅ Composant test Recharts détecté`);
      
      // Vérifier qu'il y a au moins un graphique dans la section test
      const testSvg = page.locator('text=Test Recharts Integration').locator('..').locator('svg');
      await expect(testSvg).toBeVisible();
    }
    
    // Vérifier les graphiques de tendances d'humeur
    const moodTrendSection = page.locator('text=Tendances');
    await expect(moodTrendSection).toBeVisible();
    
    // Vérifier les contrôles interactifs
    const timeRangeButtons = page.locator('button').filter({ hasText: /7j|30j|90j/ });
    const buttonCount = await timeRangeButtons.count();
    console.log(`   🎛️  Contrôles temporels: ${buttonCount}`);
    
    // Test d'interaction avec les boutons de plage temporelle
    if (buttonCount > 0) {
      await timeRangeButtons.first().click();
      await page.waitForTimeout(500);
      console.log(`   ✅ Interaction contrôles: OK`);
    }
    
    // Validation finale
    const hasWorkingCharts = svgElements > 0 || rechartsContainers > 0;
    expect(hasWorkingCharts).toBe(true);
    console.log(`   📊 Recharts fonctionnel: ${hasWorkingCharts ? 'OUI' : 'NON'}`);
  });

  test('📹 WebRTC VideoControls - Interface Complète', async ({ page }) => {
    console.log('🔍 Test interface WebRTC corrigée...');
    
    await page.goto('/telemedicine-enhanced', { waitUntil: 'networkidle' });
    
    // Vérifier la présence des contrôles vidéo
    const videoControls = [
      'button[aria-label*="video"], button:has([data-testid*="video"])',
      'button[aria-label*="audio"], button:has([data-testid*="mic"])',
      'button[aria-label*="screen"], button:has([data-testid*="monitor"])'
    ];
    
    let controlsFound = 0;
    
    for (const selector of videoControls) {
      const control = page.locator(selector);
      if (await control.count() > 0) {
        controlsFound++;
      }
    }
    
    // Test plus général: chercher des boutons avec icônes vidéo
    const videoButtons = await page.locator('button').filter({ 
      has: page.locator('svg') 
    }).count();
    
    console.log(`   🎮 Boutons de contrôle détectés: ${videoButtons}`);
    console.log(`   📹 Contrôles spécifiques WebRTC: ${controlsFound}`);
    
    // Vérifier la présence de contenu télémédecine
    const telemedicineContent = page.locator('text=télémédecine, text=vidéo, text=consultation');
    const hasTelemedicineContent = await telemedicineContent.count() > 0;
    
    console.log(`   🏥 Contenu télémédecine: ${hasTelemedicineContent ? 'OUI' : 'NON'}`);
    
    // Vérifier les éléments de session (même si simulés)
    const sessionElements = await page.locator('text=session, text=patient, text=consultation').count();
    console.log(`   📋 Éléments de session: ${sessionElements}`);
    
    // Test d'interaction basique
    const firstButton = page.locator('button').first();
    if (await firstButton.isVisible()) {
      await firstButton.hover();
      console.log(`   ✅ Interaction bouton: OK`);
    }
    
    // Validation
    expect(videoButtons).toBeGreaterThan(0);
  });

  test('🔧 Dashboard Original - Correction Erreurs', async ({ page }) => {
    console.log('🔍 Test correction dashboard original...');
    
    // Capturer les erreurs console
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    
    // Vérifier que la page se charge
    await expect(page.locator('body')).toBeVisible();
    
    // Vérifier la présence de contenu dashboard
    const dashboardContent = await page.evaluate(() => {
      const bodyText = document.body.textContent?.toLowerCase() || '';
      return {
        hasDashboard: bodyText.includes('dashboard') || bodyText.includes('humeur') || bodyText.includes('mood'),
        hasContent: bodyText.length > 100,
        hasCharts: document.querySelectorAll('svg, canvas, .chart').length
      };
    });
    
    console.log(`   📊 Contenu dashboard: ${dashboardContent.hasDashboard ? 'OUI' : 'NON'}`);
    console.log(`   📄 Contenu suffisant: ${dashboardContent.hasContent ? 'OUI' : 'NON'}`);
    console.log(`   📈 Éléments graphiques: ${dashboardContent.hasCharts}`);
    console.log(`   ❌ Erreurs console: ${errors.length}`);
    
    // Les erreurs Supabase sont acceptables (jusqu'à 5)
    const criticalErrors = errors.filter(error => 
      !error.includes('AuthSessionMissingError') && 
      !error.includes('supabase')
    );
    
    console.log(`   🚨 Erreurs critiques: ${criticalErrors.length}`);
    
    if (criticalErrors.length > 0) {
      console.log('   Erreurs critiques détectées:');
      criticalErrors.forEach(error => console.log(`     - ${error}`));
    }
    
    // Validation
    expect(dashboardContent.hasContent).toBe(true);
    expect(criticalErrors.length).toBeLessThan(3); // Tolérance pour erreurs mineures
  });

  test('⚡ Performance Globale - Temps de Chargement', async ({ page }) => {
    console.log('🔍 Test performance globale pages principales...');
    
    const pages = [
      { url: '/', name: 'Accueil' },
      { url: '/dashboard-optimized', name: 'Dashboard Optimisé' },
      { url: '/analytics', name: 'Analytics' },
      { url: '/telemedicine-enhanced', name: 'Télémédecine Enhanced' }
    ];
    
    const performanceResults: any[] = [];
    
    for (const pageInfo of pages) {
      const startTime = Date.now();
      
      try {
        await page.goto(pageInfo.url, { waitUntil: 'networkidle', timeout: 10000 });
        const loadTime = Date.now() - startTime;
        
        // Mesurer la taille du contenu
        const contentSize = await page.evaluate(() => document.body.innerHTML.length);
        
        const result = {
          name: pageInfo.name,
          url: pageInfo.url,
          loadTime,
          contentSize,
          status: 'success'
        };
        
        performanceResults.push(result);
        
        console.log(`   📊 ${pageInfo.name}: ${loadTime}ms (${(contentSize/1024).toFixed(1)}KB)`);
        
      } catch (error) {
        const result = {
          name: pageInfo.name,
          url: pageInfo.url,
          loadTime: 0,
          contentSize: 0,
          status: 'error',
          error: error
        };
        
        performanceResults.push(result);
        console.log(`   ❌ ${pageInfo.name}: Erreur de chargement`);
      }
    }
    
    // Calculer les moyennes
    const successfulPages = performanceResults.filter(r => r.status === 'success');
    const avgLoadTime = successfulPages.reduce((sum, r) => sum + r.loadTime, 0) / successfulPages.length;
    const avgContentSize = successfulPages.reduce((sum, r) => sum + r.contentSize, 0) / successfulPages.length;
    
    console.log(`\n   📊 RÉSULTATS GLOBAUX:`);
    console.log(`   ⚡ Temps moyen: ${avgLoadTime.toFixed(0)}ms`);
    console.log(`   📄 Taille moyenne: ${(avgContentSize/1024).toFixed(1)}KB`);
    console.log(`   ✅ Pages fonctionnelles: ${successfulPages.length}/${pages.length}`);
    
    // Validations
    expect(successfulPages.length).toBeGreaterThanOrEqual(3); // Au moins 3 pages fonctionnelles
    expect(avgLoadTime).toBeLessThan(4000); // Temps moyen < 4s
  });

  test('🔍 Navigation Inter-Pages - Régression', async ({ page }) => {
    console.log('🔍 Test navigation entre pages nouvelles...');
    
    // Démarrer depuis l'accueil
    await page.goto('/', { waitUntil: 'networkidle' });
    
    const navigationSequence = [
      { from: '/', to: '/dashboard-optimized', name: 'Dashboard Optimisé' },
      { from: '/dashboard-optimized', to: '/analytics', name: 'Analytics' },
      { from: '/analytics', to: '/telemedicine-enhanced', name: 'Télémédecine' },
      { from: '/telemedicine-enhanced', to: '/', name: 'Retour Accueil' }
    ];
    
    let navigationSuccessCount = 0;
    
    for (const nav of navigationSequence) {
      try {
        console.log(`   🔄 Navigation: ${nav.from} → ${nav.to}`);
        
        const startTime = Date.now();
        await page.goto(nav.to, { waitUntil: 'networkidle', timeout: 8000 });
        const navTime = Date.now() - startTime;
        
        // Vérifier que la page s'est chargée
        await expect(page.locator('body')).toBeVisible();
        
        // Attendre un peu pour le rendu
        await page.waitForTimeout(500);
        
        const hasContent = await page.evaluate(() => {
          return document.body.textContent && document.body.textContent.length > 50;
        });
        
        if (hasContent) {
          navigationSuccessCount++;
          console.log(`     ✅ ${nav.name}: ${navTime}ms`);
        } else {
          console.log(`     ⚠️  ${nav.name}: Contenu insuffisant`);
        }
        
      } catch (error) {
        console.log(`     ❌ ${nav.name}: Erreur navigation`);
      }
    }
    
    console.log(`\n   📊 Navigation réussie: ${navigationSuccessCount}/${navigationSequence.length}`);
    
    // Validation
    expect(navigationSuccessCount).toBeGreaterThanOrEqual(3); // Au moins 3 navigations réussies
  });

  test('📱 Responsive Design - Mobile/Desktop', async ({ page }) => {
    console.log('🔍 Test responsive design des nouvelles pages...');
    
    const viewports = [
      { width: 375, height: 667, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1920, height: 1080, name: 'Desktop' }
    ];
    
    const testPages = ['/dashboard-optimized', '/analytics'];
    
    for (const viewport of viewports) {
      console.log(`   📱 Test ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      for (const testPage of testPages) {
        await page.goto(testPage, { waitUntil: 'networkidle' });
        
        // Vérifier que le contenu est visible
        const contentVisible = await page.evaluate(() => {
          const body = document.body;
          return body.scrollWidth <= window.innerWidth + 50; // Tolérance de 50px
        });
        
        // Vérifier qu'il n'y a pas de débordement horizontal
        const hasHorizontalScroll = await page.evaluate(() => {
          return document.body.scrollWidth > window.innerWidth;
        });
        
        console.log(`     ${testPage}: Contenu adapté=${contentVisible}, Scroll horizontal=${hasHorizontalScroll}`);
      }
    }
    
    // Remettre la taille par défaut
    await page.setViewportSize({ width: 1280, height: 720 });
  });

});

test.describe('📊 Phase 2B - Métriques de Qualité', () => {

  test('🎯 Score Global Phase 2B', async ({ page }) => {
    console.log('📊 Calcul du score global Phase 2B...');
    
    const metrics = {
      dashboardOptimized: false,
      rechartsWorking: false,
      webrtcInterface: false,
      performanceTarget: false,
      navigationWorking: false
    };
    
    // Test Dashboard Optimisé
    try {
      await page.goto('/dashboard-optimized', { waitUntil: 'networkidle' });
      const hasTitle = await page.title();
      metrics.dashboardOptimized = hasTitle.includes('Dashboard Optimisé');
    } catch (e) {
      console.log('   ❌ Dashboard optimisé: Erreur');
    }
    
    // Test Recharts
    try {
      await page.goto('/analytics', { waitUntil: 'networkidle' });
      await page.waitForTimeout(2000);
      const svgCount = await page.locator('svg').count();
      metrics.rechartsWorking = svgCount > 0;
    } catch (e) {
      console.log('   ❌ Recharts: Erreur');
    }
    
    // Test WebRTC Interface
    try {
      await page.goto('/telemedicine-enhanced', { waitUntil: 'networkidle' });
      const buttonCount = await page.locator('button').count();
      metrics.webrtcInterface = buttonCount >= 3;
    } catch (e) {
      console.log('   ❌ WebRTC: Erreur');
    }
    
    // Test Performance
    try {
      const startTime = Date.now();
      await page.goto('/dashboard-optimized', { waitUntil: 'networkidle' });
      const loadTime = Date.now() - startTime;
      metrics.performanceTarget = loadTime < 3000;
    } catch (e) {
      console.log('   ❌ Performance: Erreur');
    }
    
    // Test Navigation
    try {
      await page.goto('/', { waitUntil: 'networkidle' });
      await page.goto('/analytics', { waitUntil: 'networkidle' });
      metrics.navigationWorking = true;
    } catch (e) {
      console.log('   ❌ Navigation: Erreur');
    }
    
    // Calcul du score
    const successfulMetrics = Object.values(metrics).filter(Boolean).length;
    const totalMetrics = Object.keys(metrics).length;
    const globalScore = Math.round((successfulMetrics / totalMetrics) * 100);
    
    console.log('\n📊 MÉTRIQUES PHASE 2B:');
    console.log(`   ✅ Dashboard Optimisé: ${metrics.dashboardOptimized ? 'OUI' : 'NON'}`);
    console.log(`   ✅ Recharts Fonctionnel: ${metrics.rechartsWorking ? 'OUI' : 'NON'}`);
    console.log(`   ✅ Interface WebRTC: ${metrics.webrtcInterface ? 'OUI' : 'NON'}`);
    console.log(`   ✅ Performance OK: ${metrics.performanceTarget ? 'OUI' : 'NON'}`);
    console.log(`   ✅ Navigation Fluide: ${metrics.navigationWorking ? 'OUI' : 'NON'}`);
    console.log(`\n🎯 SCORE GLOBAL PHASE 2B: ${globalScore}%`);
    
    // Validation finale
    expect(globalScore).toBeGreaterThanOrEqual(70); // Objectif minimum 70%
  });

}); 