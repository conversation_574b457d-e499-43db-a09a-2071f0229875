// Assurez-vous d'avoir installé Playwright : npm install -D @playwright/test
import { test, expect, Page } from '@playwright/test';

const BASE_URL = 'http://localhost:3000';

test.describe('Login - Gestion des erreurs (Toast)', () => {
  test('affiche un toast d\'erreur si le mot de passe est incorrect', async ({ page }: { page: Page }) => {
    await page.goto(`${BASE_URL}/auth/login`);
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'MauvaisMotDePasse123');
    await page.click('button[type="submit"]');

    // Vérifie que le toast d'erreur s'affiche
    await expect(page.locator('text=Login Failed')).toBeVisible();
  });
}); 