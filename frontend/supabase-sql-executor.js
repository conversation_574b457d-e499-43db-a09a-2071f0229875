#!/usr/bin/env node

/**
 * 🛠️ EXÉCUTEUR SQL DIRECT SUPABASE
 * Script qui exécute directement le SQL dans Supabase via l'API Management
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');

console.log('🛠️ EXÉCUTEUR SQL DIRECT SUPABASE');
console.log('=================================\n');

// Configuration Supabase
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://kvdrukmoxetoiojazukf.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2ZHJ1a21veGV0b2lvamF6dWtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAxOTc2MywiZXhwIjoyMDY2NTk1NzYzfQ.eAnZf5uOHXm1nHMrl964K0Ao4DtW_2bL-Mwoqa7ZfC4';
const PROJECT_ID = 'kvdrukmoxetoiojazukf';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

function log(message, type = 'info') {
  const icons = { info: 'ℹ️', success: '✅', error: '❌', warning: '⚠️' };
  console.log(`${icons[type]} ${message}`);
}

async function readSQLFile() {
  try {
    const sqlPath = path.join(__dirname, 'src/sql/appointments-schema.sql');
    const sqlContent = await fs.readFile(sqlPath, 'utf8');
    log('Fichier SQL lu avec succès');
    return sqlContent;
  } catch (error) {
    log(`Erreur lecture SQL: ${error.message}`, 'error');
    return null;
  }
}

async function executeSQLDirect(sqlContent) {
  log('Exécution SQL directe via API Supabase...');
  
  try {
    // Méthode 1: Utiliser une fonction RPC personnalisée
    const { data, error } = await supabase.rpc('exec_sql_admin', {
      sql_query: sqlContent
    });
    
    if (error) {
      log(`Méthode RPC échouée: ${error.message}`, 'warning');
      return await executeSQLAlternative(sqlContent);
    }
    
    log('SQL exécuté avec succès via RPC', 'success');
    return true;
  } catch (error) {
    log(`Erreur RPC: ${error.message}`, 'warning');
    return await executeSQLAlternative(sqlContent);
  }
}

async function executeSQLAlternative(sqlContent) {
  log('Tentative méthode alternative...');
  
  try {
    // Diviser le SQL en statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    log(`${statements.length} statements à exécuter`);
    
    // Exécuter via fetch directement
    const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'apikey': SUPABASE_SERVICE_KEY
      },
      body: JSON.stringify({
        sql_query: sqlContent
      })
    });
    
    if (!response.ok) {
      log(`HTTP Error: ${response.status}`, 'warning');
      return await createTablesManually();
    }
    
    log('SQL exécuté via fetch', 'success');
    return true;
  } catch (error) {
    log(`Erreur fetch: ${error.message}`, 'warning');
    return await createTablesManually();
  }
}

async function createTablesManually() {
  log('Création manuelle des tables...');
  
  const createProfessionalsSQL = `
    CREATE TABLE IF NOT EXISTS professionals (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name TEXT NOT NULL,
      role TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      specialties TEXT[] DEFAULT '{}',
      price_per_session DECIMAL(10,2) DEFAULT 0,
      location TEXT,
      bio TEXT,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
  `;
  
  const createAppointmentsSQL = `
    CREATE TABLE IF NOT EXISTS appointments (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      professional_id UUID REFERENCES professionals(id),
      professional_name TEXT NOT NULL,
      professional_role TEXT,
      client_id TEXT NOT NULL,
      client_name TEXT,
      appointment_date DATE NOT NULL,
      appointment_time TIME NOT NULL,
      duration_minutes INTEGER DEFAULT 60,
      type TEXT DEFAULT 'video',
      status TEXT DEFAULT 'scheduled',
      price DECIMAL(10,2),
      currency TEXT DEFAULT 'EUR',
      notes TEXT,
      meeting_link TEXT,
      reminder_sent BOOLEAN DEFAULT FALSE,
      rating INTEGER,
      feedback TEXT,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
  `;
  
  try {
    // Essayer avec l'API REST directement
    await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec_raw_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'apikey': SUPABASE_SERVICE_KEY
      },
      body: JSON.stringify({ query: createProfessionalsSQL })
    });
    
    await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec_raw_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'apikey': SUPABASE_SERVICE_KEY
      },
      body: JSON.stringify({ query: createAppointmentsSQL })
    });
    
    log('Tables créées manuellement', 'success');
    return true;
  } catch (error) {
    log(`Création manuelle échouée: ${error.message}`, 'error');
    log('⚠️ Les tables doivent être créées dans l\'interface Supabase', 'warning');
    log(`📋 Aller sur: https://supabase.com/dashboard/project/${PROJECT_ID}/editor`);
    return false;
  }
}

async function testTablesExist() {
  log('Vérification de l\'existence des tables...');
  
  try {
    const { data: profData, error: profError } = await supabase
      .from('professionals')
      .select('count')
      .limit(1);
    
    const { data: aptData, error: aptError } = await supabase
      .from('appointments')
      .select('count')
      .limit(1);
    
    if (!profError && !aptError) {
      log('Tables existantes confirmées', 'success');
      return true;
    }
    
    if (profError) log(`Table professionals: ${profError.message}`, 'warning');
    if (aptError) log(`Table appointments: ${aptError.message}`, 'warning');
    
    return false;
  } catch (error) {
    log(`Erreur vérification: ${error.message}`, 'error');
    return false;
  }
}

async function insertSampleData() {
  log('Insertion des données d\'exemple...');
  
  try {
    // Nettoyer les données existantes
    await supabase.from('appointments').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('professionals').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    // Insérer les professionnels
    const professionals = [
      {
        name: 'Dr. Sophie Martin',
        role: 'Psychologue clinicienne',
        email: '<EMAIL>',
        specialties: ['Thérapie cognitive comportementale', 'Gestion du stress', 'Anxiété'],
        price_per_session: 85.00,
        location: '15 rue de la Paix, Paris',
        bio: 'Spécialisée en TCC avec plus de 10 ans d\'expérience'
      },
      {
        name: 'Dr. Jean Dupont',
        role: 'Psychiatre',
        email: '<EMAIL>',
        specialties: ['Psychiatrie générale', 'Troubles bipolaires', 'Dépression'],
        price_per_session: 120.00,
        location: '42 avenue Victor Hugo, Lyon',
        bio: 'Psychiatre expérimenté spécialisé dans les troubles de l\'humeur'
      },
      {
        name: 'Marie Leblanc',
        role: 'Thérapeute comportementale',
        email: '<EMAIL>',
        specialties: ['Thérapie comportementale', 'Phobies', 'Addictions'],
        price_per_session: 75.00,
        location: '8 place Bellecour, Lyon',
        bio: 'Thérapeute spécialisée dans les troubles comportementaux'
      }
    ];
    
    const { data: profData, error: profError } = await supabase
      .from('professionals')
      .insert(professionals)
      .select();
    
    if (profError) {
      log(`Erreur insertion professionnels: ${profError.message}`, 'error');
      return false;
    }
    
    log(`${profData.length} professionnels insérés`, 'success');
    
    // Insérer des rendez-vous
    const appointments = profData.map((prof, i) => {
      const date = new Date();
      date.setDate(date.getDate() + i + 1);
      
      return {
        professional_id: prof.id,
        professional_name: prof.name,
        professional_role: prof.role,
        client_id: 'demo-user-1',
        client_name: 'Utilisateur Démo',
        appointment_date: date.toISOString().split('T')[0],
        appointment_time: '14:00',
        duration_minutes: 60,
        type: 'video',
        status: 'scheduled',
        price: prof.price_per_session,
        currency: 'EUR',
        notes: `Rendez-vous de test avec ${prof.name}`,
        meeting_link: `https://meet.mindflow.com/session-${prof.id}`
      };
    });
    
    const { data: aptData, error: aptError } = await supabase
      .from('appointments')
      .insert(appointments)
      .select();
    
    if (aptError) {
      log(`Erreur insertion rendez-vous: ${aptError.message}`, 'error');
      return false;
    }
    
    log(`${aptData.length} rendez-vous insérés`, 'success');
    return true;
    
  } catch (error) {
    log(`Erreur insertion données: ${error.message}`, 'error');
    return false;
  }
}

async function runValidationTests() {
  log('Exécution des tests de validation...');
  
  const tests = [
    {
      name: 'Connexion Supabase',
      test: async () => {
        const { error } = await supabase.from('professionals').select('count').limit(1);
        return !error;
      }
    },
    {
      name: 'Table professionals',
      test: async () => {
        const { count } = await supabase.from('professionals').select('*', { count: 'exact', head: true });
        return count > 0;
      }
    },
    {
      name: 'Table appointments',
      test: async () => {
        const { count } = await supabase.from('appointments').select('*', { count: 'exact', head: true });
        return count > 0;
      }
    },
    {
      name: 'Requête jointure',
      test: async () => {
        const { data } = await supabase
          .from('appointments')
          .select('*, professionals(name, role)')
          .limit(1);
        return data && data.length > 0;
      }
    },
    {
      name: 'Filtres et tri',
      test: async () => {
        const { data } = await supabase
          .from('appointments')
          .select('*')
          .eq('status', 'scheduled')
          .order('appointment_date', { ascending: true });
        return data && data.length >= 0;
      }
    }
  ];
  
  console.log('\n📊 RÉSULTATS DES TESTS:');
  console.log('═'.repeat(40));
  
  let passed = 0;
  for (const test of tests) {
    try {
      const result = await test.test();
      const status = result ? '✅' : '❌';
      console.log(`${status} ${test.name}`);
      if (result) passed++;
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
  }
  
  console.log(`\n📈 Score: ${passed}/${tests.length} tests réussis`);
  return passed === tests.length;
}

async function main() {
  console.log('🚀 Démarrage de l\'exécution SQL automatique...\n');
  
  const startTime = Date.now();
  
  try {
    // Étape 1: Lire le fichier SQL
    const sqlContent = await readSQLFile();
    if (!sqlContent) {
      log('Impossible de lire le fichier SQL, création manuelle...', 'warning');
    }
    
    // Étape 2: Exécuter le SQL ou créer manuellement
    let tablesCreated = false;
    if (sqlContent) {
      tablesCreated = await executeSQLDirect(sqlContent);
    }
    
    if (!tablesCreated) {
      tablesCreated = await createTablesManually();
    }
    
    // Étape 3: Vérifier les tables
    const tablesExist = await testTablesExist();
    
    // Étape 4: Insérer les données si les tables existent
    if (tablesExist) {
      await insertSampleData();
    } else {
      log('Tables non disponibles, utilisez l\'interface Supabase pour les créer', 'warning');
    }
    
    // Étape 5: Tests de validation
    await runValidationTests();
    
    const duration = Math.round((Date.now() - startTime) / 1000);
    
    console.log('\n🎉 EXÉCUTION TERMINÉE !');
    console.log(`⏱️ Durée: ${duration} secondes`);
    console.log('\n🔗 LIENS UTILES:');
    console.log(`📊 Dashboard Supabase: https://supabase.com/dashboard/project/${PROJECT_ID}`);
    console.log(`🧪 Page de test: http://localhost:3000/test-appointments-supabase`);
    console.log(`📋 Éditeur SQL: https://supabase.com/dashboard/project/${PROJECT_ID}/sql`);
    
    if (!tablesExist) {
      console.log('\n⚠️ ACTIONS REQUISES:');
      console.log('1. Créer les tables manuellement dans l\'éditeur SQL Supabase');
      console.log('2. Copier le contenu de src/sql/appointments-schema.sql');
      console.log('3. Exécuter le SQL dans l\'interface Supabase');
      console.log('4. Relancer ce script');
    }
    
  } catch (error) {
    log(`Erreur critique: ${error.message}`, 'error');
    console.log('\n🔧 SOLUTIONS:');
    console.log('1. Vérifier la connexion Internet');
    console.log('2. Vérifier les clés API Supabase');
    console.log('3. Créer les tables manuellement');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  executeSQLDirect,
  testTablesExist,
  insertSampleData,
  runValidationTests
}; 