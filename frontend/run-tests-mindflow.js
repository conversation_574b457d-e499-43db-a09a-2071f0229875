#!/usr/bin/env node

/**
 * 🚀 SCRIPT D'EXÉCUTION TESTS MINDFLOW PRO
 * Orchestration complète des tests Playwright
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration des couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class MindFlowTestRunner {
  constructor() {
    this.startTime = Date.now();
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      duration: 0
    };
  }

  log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  async checkPrerequisites() {
    this.log('\n🔍 Vérification des prérequis...', 'cyan');
    
    // Vérifier que l'application est démarrée
    try {
      const fetch = (await import('node-fetch')).default;
      const response = await fetch('http://localhost:3000', { timeout: 5000 });
      
      if (response.ok) {
        this.log('✅ Application accessible sur http://localhost:3000', 'green');
        return true;
      } else {
        this.log('❌ Application non accessible - Status: ' + response.status, 'red');
        return false;
      }
    } catch (error) {
      this.log('❌ Application non accessible - Erreur: ' + error.message, 'red');
      this.log('💡 Démarrez l\'application avec: npm run dev', 'yellow');
      return false;
    }
  }

  async runTestSuite(suiteName, command, description) {
    this.log(`\n🎯 ${description}`, 'bright');
    this.log('='.repeat(50), 'blue');

    return new Promise((resolve) => {
      const startTime = Date.now();
      const process = spawn('npx', command.split(' '), {
        stdio: 'inherit',
        shell: true
      });

      process.on('close', (code) => {
        const duration = Date.now() - startTime;
        
        if (code === 0) {
          this.log(`✅ ${suiteName} - Terminé avec succès (${Math.round(duration/1000)}s)`, 'green');
          resolve({ success: true, duration });
        } else {
          this.log(`❌ ${suiteName} - Échec (code: ${code})`, 'red');
          resolve({ success: false, duration });
        }
      });

      process.on('error', (error) => {
        this.log(`❌ Erreur lors de l'exécution de ${suiteName}: ${error.message}`, 'red');
        resolve({ success: false, duration: 0 });
      });
    });
  }

  async runAllTests() {
    this.log('🚀 DÉMARRAGE TESTS COMPLETS MINDFLOW PRO', 'bright');
    this.log('='.repeat(60), 'magenta');

    // Vérifier les prérequis
    const prereqsOk = await this.checkPrerequisites();
    if (!prereqsOk) {
      this.log('\n❌ Prérequis non satisfaits. Arrêt des tests.', 'red');
      process.exit(1);
    }

    const testSuites = [
      {
        name: 'Tests Validation Complète',
        command: 'playwright test tests/e2e/mindflow-complete-validation.spec.ts --reporter=html,json',
        description: 'Validation de toutes les fonctionnalités principales'
      },
      {
        name: 'Tests Flux Critiques',
        command: 'playwright test tests/e2e/critical-user-flows.spec.ts --reporter=html,json',
        description: 'Tests des parcours utilisateur essentiels'
      },
      {
        name: 'Tests Performance',
        command: 'playwright test tests/e2e/performance-tests.spec.ts --reporter=html,json',
        description: 'Validation des performances et Core Web Vitals'
      }
    ];

    let totalSuccess = 0;
    let totalDuration = 0;

    for (const suite of testSuites) {
      const result = await this.runTestSuite(suite.name, suite.command, suite.description);
      
      if (result.success) {
        totalSuccess++;
      }
      
      totalDuration += result.duration;
    }

    // Résumé final
    this.generateFinalReport(totalSuccess, testSuites.length, totalDuration);
  }

  async runSpecificTest(testType) {
    const testCommands = {
      'complete': 'playwright test tests/e2e/mindflow-complete-validation.spec.ts --headed',
      'critical': 'playwright test tests/e2e/critical-user-flows.spec.ts --headed',
      'performance': 'playwright test tests/e2e/performance-tests.spec.ts --headed',
      'mobile': 'playwright test --project=mobile-chrome --headed',
      'desktop': 'playwright test --project=desktop-chrome --headed',
      'debug': 'playwright test --debug',
      'ui': 'playwright test --ui'
    };

    if (!testCommands[testType]) {
      this.log(`❌ Type de test invalide: ${testType}`, 'red');
      this.log('Types disponibles: ' + Object.keys(testCommands).join(', '), 'yellow');
      return;
    }

    const prereqsOk = await this.checkPrerequisites();
    if (!prereqsOk) {
      this.log('\n❌ Application non accessible. Démarrez-la avec: npm run dev', 'red');
      return;
    }

    this.log(`\n🎯 Exécution: ${testType}`, 'bright');
    await this.runTestSuite(`Test ${testType}`, testCommands[testType], `Test spécialisé ${testType}`);
  }

  generateFinalReport(successCount, totalCount, totalDuration) {
    this.log('\n📊 RAPPORT FINAL TESTS MINDFLOW PRO', 'bright');
    this.log('='.repeat(60), 'magenta');
    
    const successRate = (successCount / totalCount * 100).toFixed(1);
    const totalMinutes = Math.round(totalDuration / 60000);
    
    this.log(`📈 Taux de réussite: ${successRate}% (${successCount}/${totalCount} suites)`, 
              successCount === totalCount ? 'green' : 'yellow');
    this.log(`⏱️ Durée totale: ${totalMinutes} minutes`, 'blue');
    
    if (successCount === totalCount) {
      this.log('\n🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS !', 'green');
      this.log('✨ MindFlow Pro est entièrement validé et prêt pour la production !', 'green');
    } else {
      this.log('\n⚠️ Certains tests ont échoué. Consultez les rapports pour plus de détails.', 'yellow');
    }
    
    this.log('\n📄 Rapports disponibles:', 'cyan');
    this.log('   - Rapport HTML: playwright-report/index.html', 'cyan');
    this.log('   - Rapport JSON: test-results/results.json', 'cyan');
    this.log('   - Rapport personnalisé: test-results/mindflow-report.html', 'cyan');
    
    this.log('\n🔧 Commandes utiles:', 'cyan');
    this.log('   - Voir rapport: npm run test:e2e:report', 'cyan');
    this.log('   - Tests en mode UI: npm run test:e2e:ui', 'cyan');
    this.log('   - Debug tests: npm run test:e2e:debug', 'cyan');
    
    this.log('='.repeat(60), 'magenta');
  }

  displayHelp() {
    this.log('\n🚀 AIDE - SCRIPT TESTS MINDFLOW PRO', 'bright');
    this.log('='.repeat(50), 'blue');
    this.log('\nUsage: node run-tests-mindflow.js [option]', 'cyan');
    this.log('\nOptions disponibles:', 'yellow');
    this.log('  all          - Exécuter tous les tests (par défaut)', 'cyan');
    this.log('  complete     - Tests de validation complète uniquement', 'cyan');
    this.log('  critical     - Tests des flux critiques uniquement', 'cyan');
    this.log('  performance  - Tests de performance uniquement', 'cyan');
    this.log('  mobile       - Tests sur mobile uniquement', 'cyan');
    this.log('  desktop      - Tests sur desktop uniquement', 'cyan');
    this.log('  debug        - Mode debug interactif', 'cyan');
    this.log('  ui           - Interface utilisateur Playwright', 'cyan');
    this.log('  help         - Afficher cette aide', 'cyan');
    this.log('\nExemples:', 'yellow');
    this.log('  node run-tests-mindflow.js all', 'green');
    this.log('  node run-tests-mindflow.js critical', 'green');
    this.log('  node run-tests-mindflow.js ui', 'green');
    this.log('='.repeat(50), 'blue');
  }
}

// Exécution principale
async function main() {
  const runner = new MindFlowTestRunner();
  const arg = process.argv[2] || 'all';

  switch (arg) {
    case 'help':
    case '--help':
    case '-h':
      runner.displayHelp();
      break;
    
    case 'all':
      await runner.runAllTests();
      break;
    
    default:
      await runner.runSpecificTest(arg);
      break;
  }
}

// Gestion des erreurs globales
process.on('unhandledRejection', (error) => {
  console.error('❌ Erreur non gérée:', error);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n⏹️ Tests interrompus par l\'utilisateur');
  process.exit(0);
});

// Lancement
main().catch(error => {
  console.error('❌ Erreur fatale:', error);
  process.exit(1);
}); 