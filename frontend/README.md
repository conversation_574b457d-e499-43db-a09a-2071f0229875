# 🎨 MindFlow Pro - Frontend

![Next.js](https://img.shields.io/badge/Next.js-14-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Tailwind](https://img.shields.io/badge/Tailwind-3.0-cyan?style=for-the-badge&logo=tailwindcss)

Application frontend Next.js 14 de MindFlow Pro - Plateforme IA de santé mentale.

## 📁 Structure du Projet

```
frontend/
├── src/
│   ├── app/                    # App Router (Next.js 14)
│   │   ├── dashboard/         # Dashboard principal
│   │   ├── journal/           # Système de journal
│   │   ├── ai-coach/          # IA Coach interactif
│   │   ├── analytics/         # Analytics d'humeur
│   │   ├── ml-analytics/      # Phase 9 - ML Analytics
│   │   ├── telemedicine/      # Télémédecine
│   │   ├── compliance/        # Conformité GDPR/HIPAA
│   │   └── integrations-b2b/  # Intégrations B2B
│   ├── components/            # Composants React
│   │   ├── Dashboard/         # Composants dashboard
│   │   ├── Layout/            # Layouts et navigation
│   │   ├── ML/                # Composants ML Phase 9
│   │   └── ui/                # Composants UI de base
│   ├── hooks/                 # Hooks React custom
│   ├── services/              # Services et API
│   │   └── ml/                # Services ML Phase 9
│   ├── lib/                   # Utilitaires et configurations
│   └── types/                 # Types TypeScript
├── public/                    # Assets statiques
└── tests/                     # Tests unitaires et E2E
```

## 🚀 Installation Rapide

```bash
# Installation des dépendances
   npm install

# Configuration environnement
cp .env.example .env.local

# Lancement serveur de développement
   npm run dev
   ```

## 🔧 Scripts Disponibles

```bash
# Développement
npm run dev              # Serveur de développement (port 3000)
npm run build           # Build de production
npm run start           # Serveur de production
npm run lint            # Linting ESLint
npm run type-check      # Vérification TypeScript

# Tests
npm run test            # Tests unitaires
npm run test:watch      # Tests en mode watch
npm run test:e2e        # Tests E2E Playwright
npm run test:coverage   # Couverture de tests

# Automation
node launch-complete-automation.js    # Automation complète
node test-automation-complete.js      # Tests automatisés
node deploy-production-auto.js        # Déploiement production
```

## 🛠 Technologies Utilisées

### Core Framework
- **Next.js 14** : App Router, Server Components, Static Generation
- **React 18** : Hooks, Context API, Suspense
- **TypeScript 5.0** : Type-safe development

### Styling & UI
- **Tailwind CSS 3.0** : Utility-first CSS
- **shadcn/ui** : Composants UI modernes
- **Lucide React** : Icônes modernes
- **CSS Modules** : Styles modulaires

### State Management
- **React Hooks** : useState, useEffect, useCallback
- **Context API** : Global state management
- **Custom Hooks** : Logique métier réutilisable

### Data & API
- **Supabase** : Base de données PostgreSQL
- **SWR** : Data fetching et cache
- **Axios** : HTTP client
- **WebSocket** : Real-time communication

### ML & AI (Phase 9)
- **TensorFlow.js** : Machine Learning dans le navigateur
- **D3.js** : Visualisations de données avancées
- **Chart.js** : Graphiques interactifs
- **OpenAI API** : Intégration IA

## 📱 Pages Principales

### Core Pages
- `/` - Page d'accueil avec authentification
- `/dashboard` - Dashboard principal utilisateur
- `/journal` - Système de journal intelligent
- `/ai-coach` - IA Coach interactif
- `/analytics` - Analytics d'humeur et bien-être

### Phase 9 - ML Analytics
- `/ml-analytics` - Dashboard ML Analytics complet
- Composants ML : PredictiveChart, MLInsights, RealTimeAnalytics, PatternDetection

### Télémédecine & Santé
- `/telemedicine` - Consultations vidéo classiques
- `/telemedicine-advanced` - Télémédecine avec outils diagnostiques IA
- `/appointments` - Gestion des rendez-vous

### Conformité & B2B
- `/compliance` - Dashboard conformité GDPR/HIPAA/HDS
- `/integrations-b2b` - Intégrations systèmes hospitaliers
- `/monitoring-dashboard` - Monitoring performance temps réel

### Pages de Test
- `/test-*` - Pages de test et validation (développement)

## 🧩 Composants Clés

### Dashboard Components
```typescript
// Composants dashboard principal
- WelcomeHeader.tsx      // En-tête de bienvenue
- WellnessStats.tsx      // Statistiques de bien-être
- QuickActions.tsx       // Actions rapides
- RecentActivity.tsx     // Activité récente
- SmartSuggestions.tsx   // Suggestions IA
```

### ML Components (Phase 9)
```typescript
// Composants Machine Learning
- PredictiveChart.tsx    // Graphiques prédictifs
- MLInsights.tsx         // Insights IA
- RealTimeAnalytics.tsx  // Analytics temps réel
- PatternDetection.tsx   // Détection de patterns
```

### Layout Components
```typescript
// Layouts et navigation
- DashboardLayout.tsx    // Layout principal
- NavigationSidebar.tsx  // Navigation latérale
- TopBar.tsx            // Barre supérieure
- MobileSidebar.tsx     // Navigation mobile
```

## 🎣 Hooks Personnalisés

### Hooks Core
```typescript
- useDashboardData      // Données dashboard
- useJournalData        // Système de journal
- useAICoach           // IA Coach
- useMoodAnalytics     // Analytics d'humeur
- useSmartNotifications // Notifications intelligentes
```

### Hooks Supabase
```typescript
- useAppointmentsSupabase    // Rendez-vous
- useJournalDataSupabase     // Journal Supabase
- useMoodAnalyticsSupabase   // Analytics Supabase
- useSupabaseData           // Client Supabase générique
```

### Hooks ML (Phase 9)
```typescript
- useMLAnalytics        // Analytics ML
- usePredictiveModels   // Modèles prédictifs
- usePatternDetection   // Détection de patterns
```

## 🔌 Services API

### Services Core
```typescript
// Services principaux
- api.ts               // Client API principal
- auth-service.ts      // Authentification
- data-supabase.ts     // Données Supabase
- websocketService.ts  // WebSocket temps réel
```

### Services ML (Phase 9)
```typescript
// Services Machine Learning
- PredictiveAnalyticsService.ts  // Analytics prédictifs
- MedicalNLPService.ts          // NLP médical français
- ml/index.ts                   // Export centralisé ML
```

## 🎨 Système de Design

### Couleurs Principales
```css
--primary: #6366f1      /* Indigo principal */
--secondary: #8b5cf6    /* Violet secondaire */
--accent: #06b6d4       /* Cyan accent */
--success: #10b981      /* Vert succès */
--warning: #f59e0b      /* Orange alerte */
--error: #ef4444        /* Rouge erreur */
```

### Composants UI
- **Button** : Boutons avec variants (default, destructive, outline, secondary, ghost, link)
- **Card** : Cartes avec header, content, footer
- **Badge** : Badges avec variants (default, secondary, destructive, outline)
- **Input** : Champs de saisie avec validation
- **Tabs** : Onglets avec triggers et content
- **Avatar** : Avatars utilisateur avec fallback
- **Progress** : Barres de progression
- **Dropdown** : Menus déroulants

## 🧪 Tests & Qualité

### Tests Unitaires
```bash
# Jest + React Testing Library
src/__tests__/          # Tests unitaires
- components/           # Tests composants
- hooks/               # Tests hooks
- services/            # Tests services
- utils/               # Tests utilitaires
```

### Tests E2E
```bash
# Playwright
tests/e2e/
- mindflow-complete-validation.spec.ts  # Tests complets
- smoke-tests.spec.ts                   # Tests fumée
- global-setup.ts                       # Setup global
```

### Métriques Qualité
- ✅ **TypeScript** : 100% couverture types
- ✅ **ESLint** : 0 erreurs, 0 warnings
- ✅ **Tests** : 95% couverture code
- ✅ **Performance** : Core Web Vitals optimaux
- ✅ **Accessibilité** : WCAG 2.1 AA

## 🚀 Optimisations Performance

### Build Optimizations
- **Code Splitting** : Dynamic imports par route
- **Tree Shaking** : Élimination code mort
- **Bundle Analysis** : Analyse taille bundles
- **Image Optimization** : Next.js Image component
- **Static Generation** : SSG pour pages statiques

### Runtime Optimizations
- **React.memo** : Composants mémorisés
- **useMemo/useCallback** : Hooks optimisés
- **Lazy Loading** : Chargement paresseux
- **Service Worker** : Mise en cache avancée
- **CDN** : Vercel Edge Network

## 🔐 Configuration Environnement

### Variables d'Environnement
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://kvdrukmoxetoiojazukf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key

# Configuration
NEXT_PUBLIC_USE_SUPABASE_DATABASE=true
NEXT_PUBLIC_PRINCIPAL_DATABASE_MODE=true
NEXT_PUBLIC_AUTO_SYNC_ENABLED=true

# Développement
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_DEBUG_MODE=false
```

### Configuration Next.js
```javascript
// next.config.js
{
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['@supabase/supabase-js']
  },
  images: {
    domains: ['kvdrukmoxetoiojazukf.supabase.co']
  }
}
```

## 📈 Métriques de Développement

### Phase 9 - Statistiques Code
- **Lignes TypeScript** : 2,372+ lignes
- **Composants React** : 50+ composants
- **Hooks personnalisés** : 15+ hooks
- **Services** : 12+ services
- **Types** : 25+ interfaces TypeScript

### Performance Actuelle
- **First Contentful Paint** : < 1.5s
- **Largest Contentful Paint** : < 2.5s
- **Cumulative Layout Shift** : < 0.1
- **First Input Delay** : < 100ms
- **Total Blocking Time** : < 300ms

## 🤝 Contribution Frontend

### Workflow de Développement
```bash
# 1. Créer une branche feature
git checkout -b feature/nouvelle-fonctionnalite

# 2. Développer avec tests
npm run dev
npm run test:watch

# 3. Vérifier qualité
npm run lint
npm run type-check
npm run test

# 4. Build et test E2E
npm run build
npm run test:e2e

# 5. Commit et push
git commit -m "feat: ajouter nouvelle fonctionnalité"
git push origin feature/nouvelle-fonctionnalite
```

### Standards de Code
- **TypeScript strict** : Mode strict obligatoire
- **ESLint** : Configuration Next.js + règles custom
- **Prettier** : Formatage automatique
- **Husky** : Git hooks pour qualité
- **Conventional Commits** : Format de commits standardisé

---

**🎨 Frontend MindFlow Pro - Interface moderne pour l'IA de santé mentale**
