/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ['recharts'],
  env: {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    NEXT_PUBLIC_USE_SUPABASE_DATABASE: process.env.NEXT_PUBLIC_USE_SUPABASE_DATABASE,
    NEXT_PUBLIC_PRINCIPAL_DATABASE_MODE: process.env.NEXT_PUBLIC_PRINCIPAL_DATABASE_MODE,
    NEXT_PUBLIC_AUTO_SYNC_ENABLED: process.env.NEXT_PUBLIC_AUTO_SYNC_ENABLED,
  }
};

module.exports = nextConfig;
