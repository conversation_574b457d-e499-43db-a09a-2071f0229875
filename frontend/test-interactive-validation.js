#!/usr/bin/env node

/**
 * 🎭 TEST INTERACTIF AVEC ATTENTE DE CHARGEMENT
 * Script pour tester les pages avec chargement complet
 */

const { chromium } = require('playwright');

async function testPageWithWait(pageUrl, pageName) {
  console.log(`\n🔍 Test de ${pageName}...`);
  
  const browser = await chromium.launch({ 
    headless: false, // Mode visible pour debug
    slowMo: 1000
  });
  
  const page = await browser.newPage();
  
  try {
    // Aller à la page
    await page.goto(`http://localhost:3002${pageUrl}`);
    
    // Attendre que le skeleton loading disparaisse
    console.log('   ⏳ Attente de fin de chargement...');
    await page.waitForTimeout(3000); // 3 secondes
    
    // Vérifier si l'animation de chargement est toujours présente
    const loadingElements = await page.locator('.animate-pulse').count();
    
    if (loadingElements > 0) {
      console.log('   ⚠️ Page encore en chargement, attente supplémentaire...');
      await page.waitForTimeout(5000); // 5 secondes supplémentaires
      
      // Vérifier à nouveau
      const stillLoading = await page.locator('.animate-pulse').count();
      if (stillLoading > 0) {
        console.log('   ❌ Page bloquée en état de chargement');
        // Prendre une capture d'écran pour debug
        await page.screenshot({ path: `debug-${pageName.replace(/[^a-zA-Z0-9]/g, '-')}.png` });
      } else {
        console.log('   ✅ Chargement terminé avec succès');
      }
    } else {
      console.log('   ✅ Page chargée instantanément');
    }
    
    // Récupérer le contenu final
    const content = await page.textContent('body');
    const title = await page.locator('h1, h2, h3').first().textContent();
    
    console.log(`   📄 Titre trouvé: ${title || 'Aucun titre'}`);
    console.log(`   📊 Longueur contenu: ${content?.length || 0} caractères`);
    
    // Compter les éléments interactifs
    const buttons = await page.locator('button').count();
    const links = await page.locator('a').count();
    const inputs = await page.locator('input').count();
    
    console.log(`   🔘 Éléments interactifs: ${buttons + links + inputs} (boutons: ${buttons}, liens: ${links}, inputs: ${inputs})`);
    
    // Attendre une interaction utilisateur
    console.log('   🖱️ Appuyez sur Entrée pour continuer...');
    await new Promise(resolve => {
      process.stdin.once('data', () => resolve());
    });
    
  } catch (error) {
    console.log(`   ❌ Erreur: ${error.message}`);
  } finally {
    await browser.close();
  }
}

async function runInteractiveTest() {
  console.log('🎭 TEST INTERACTIF DES PAGES PROBLÉMATIQUES\n');
  
  const pagesToTest = [
    { url: '/billing', name: 'Module Billing' },
    { url: '/tools', name: 'Module Tools' }
  ];
  
  for (const pageTest of pagesToTest) {
    await testPageWithWait(pageTest.url, pageTest.name);
  }
  
  console.log('\n✨ Tests interactifs terminés');
}

if (require.main === module) {
  runInteractiveTest().catch(console.error);
}
