#!/usr/bin/env node

const { chromium } = require('playwright');

class Phase2BQuickTest {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.results = {
      timestamp: new Date().toISOString(),
      tests: [],
      summary: {}
    };
  }

  async runTest(name, testFn) {
    console.log(`🔍 Test: ${name}...`);
    const startTime = Date.now();
    
    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.tests.push({
        name,
        status: 'success',
        duration,
        details: result
      });
      
      console.log(`   ✅ ${name}: ${duration}ms`);
      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.tests.push({
        name,
        status: 'failed',
        duration,
        error: error.message
      });
      
      console.log(`   ❌ ${name}: ${error.message}`);
      return false;
    }
  }

  async testDashboardOptimized(page) {
    await page.goto(`${this.baseUrl}/dashboard-optimized`, { waitUntil: 'networkidle' });
    
    // Vérifier le titre
    const title = await page.title();
    if (!title.includes('Dashboard Optimisé')) {
      throw new Error('Titre incorrect');
    }

    // Vérifier le contenu
    const content = await page.textContent('body');
    if (!content.includes('Dashboard Optimisé')) {
      throw new Error('Contenu dashboard manquant');
    }

    // Vérifier les badges
    const badges = await page.locator('text=Nouvelles fonctionnalités, text=Performance optimisée').count();
    
    return {
      title: 'OK',
      content: 'OK',
      badges: badges,
      url: page.url()
    };
  }

  async testRechartsIntegration(page) {
    await page.goto(`${this.baseUrl}/analytics`, { waitUntil: 'networkidle' });
    await page.waitForTimeout(3000); // Attendre le rendu des graphiques
    
    // Vérifier les éléments SVG (Recharts)
    const svgCount = await page.locator('svg').count();
    const rechartsElements = await page.locator('.recharts-wrapper, [class*="recharts"]').count();
    
    // Vérifier le composant test
    const testComponent = await page.locator('text=Test Recharts Integration').count();
    
    if (svgCount === 0 && rechartsElements === 0) {
      throw new Error('Aucun graphique Recharts détecté');
    }

    return {
      svgElements: svgCount,
      rechartsElements: rechartsElements,
      testComponent: testComponent > 0,
      working: true
    };
  }

  async testWebRTCInterface(page) {
    await page.goto(`${this.baseUrl}/telemedicine-enhanced`, { waitUntil: 'networkidle' });
    
    // Vérifier les boutons de contrôle
    const buttons = await page.locator('button').count();
    const videoButtons = await page.locator('button').filter({ has: page.locator('svg') }).count();
    
    // Vérifier le contenu télémédecine
    const content = await page.textContent('body');
    const hasTelemedicineContent = content.toLowerCase().includes('télémédecine') || 
                                   content.toLowerCase().includes('vidéo') ||
                                   content.toLowerCase().includes('consultation');

    return {
      totalButtons: buttons,
      videoButtons: videoButtons,
      hasTelemedicineContent: hasTelemedicineContent,
      working: buttons > 0
    };
  }

  async testPerformance(page) {
    const pages = [
      { url: '/', name: 'Accueil' },
      { url: '/dashboard-optimized', name: 'Dashboard Optimisé' },
      { url: '/analytics', name: 'Analytics' }
    ];

    const results = [];

    for (const pageInfo of pages) {
      const startTime = Date.now();
      await page.goto(`${this.baseUrl}${pageInfo.url}`, { waitUntil: 'networkidle' });
      const loadTime = Date.now() - startTime;

      results.push({
        name: pageInfo.name,
        loadTime: loadTime,
        fast: loadTime < 3000
      });
    }

    const avgLoadTime = results.reduce((sum, r) => sum + r.loadTime, 0) / results.length;
    const fastPages = results.filter(r => r.fast).length;

    return {
      pages: results,
      averageLoadTime: Math.round(avgLoadTime),
      fastPagesCount: fastPages,
      performanceGood: avgLoadTime < 3000
    };
  }

  async testNavigation(page) {
    const navigationSequence = [
      { from: '/', to: '/dashboard-optimized' },
      { from: '/dashboard-optimized', to: '/analytics' },
      { from: '/analytics', to: '/telemedicine-enhanced' },
      { from: '/telemedicine-enhanced', to: '/' }
    ];

    let successCount = 0;

    for (const nav of navigationSequence) {
      try {
        await page.goto(`${this.baseUrl}${nav.to}`, { waitUntil: 'networkidle', timeout: 8000 });
        const hasContent = await page.evaluate(() => {
          return document.body.textContent && document.body.textContent.length > 50;
        });
        
        if (hasContent) {
          successCount++;
        }
      } catch (error) {
        // Navigation échouée
      }
    }

    return {
      totalNavigations: navigationSequence.length,
      successfulNavigations: successCount,
      successRate: Math.round((successCount / navigationSequence.length) * 100)
    };
  }

  async run() {
    console.log('🚀 PHASE 2B - TESTS RAPIDES DE VALIDATION\n');
    console.log(`🌐 URL de base: ${this.baseUrl}\n`);

    const browser = await chromium.launch({ headless: true });
    const page = await browser.newPage();

    let successCount = 0;
    const totalTests = 5;

    // Test 1: Dashboard Optimisé
    if (await this.runTest('Dashboard Optimisé - Titre et Contenu', () => this.testDashboardOptimized(page))) {
      successCount++;
    }

    // Test 2: Recharts Integration
    if (await this.runTest('Recharts - Graphiques Fonctionnels', () => this.testRechartsIntegration(page))) {
      successCount++;
    }

    // Test 3: WebRTC Interface
    if (await this.runTest('WebRTC - Interface Télémédecine', () => this.testWebRTCInterface(page))) {
      successCount++;
    }

    // Test 4: Performance
    if (await this.runTest('Performance - Temps de Chargement', () => this.testPerformance(page))) {
      successCount++;
    }

    // Test 5: Navigation
    if (await this.runTest('Navigation - Flux Inter-Pages', () => this.testNavigation(page))) {
      successCount++;
    }

    await browser.close();

    // Calcul du score
    const globalScore = Math.round((successCount / totalTests) * 100);

    console.log('\n📊 RÉSULTATS PHASE 2B:');
    console.log('========================');
    console.log(`✅ Tests réussis: ${successCount}/${totalTests}`);
    console.log(`🎯 Score global: ${globalScore}%`);

    // Détails des résultats
    this.results.tests.forEach(test => {
      if (test.status === 'success' && test.details) {
        console.log(`\n📋 ${test.name}:`);
        Object.entries(test.details).forEach(([key, value]) => {
          if (typeof value === 'object') {
            console.log(`   ${key}: ${JSON.stringify(value)}`);
          } else {
            console.log(`   ${key}: ${value}`);
          }
        });
      }
    });

    // Recommandations
    console.log('\n🔧 RECOMMANDATIONS:');
    if (globalScore >= 80) {
      console.log('✅ Excellent! Phase 2B prête pour production');
    } else if (globalScore >= 60) {
      console.log('⚠️  Bon progrès, quelques ajustements nécessaires');
    } else {
      console.log('❌ Corrections importantes nécessaires');
    }

    // Prochaines étapes
    console.log('\n📝 PROCHAINES ÉTAPES:');
    console.log('1. Analyser les métriques de performance détaillées');
    console.log('2. Optimiser les composants Recharts si nécessaire');
    console.log('3. Finaliser les tests E2E complets');
    console.log('4. Préparer le déploiement de production');

    this.results.summary = {
      totalTests,
      successCount,
      globalScore,
      status: globalScore >= 70 ? 'READY' : 'NEEDS_WORK'
    };

    return this.results;
  }
}

// Exécution
if (require.main === module) {
  const tester = new Phase2BQuickTest();
  tester.run().catch(error => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  });
}

module.exports = Phase2BQuickTest; 