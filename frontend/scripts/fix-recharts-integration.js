#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

class RechartsIntegrationFixer {
  constructor() {
    this.fixes = [];
    this.componentsToCheck = [
      'src/components/Analytics/MoodTrendChart.tsx',
      'src/components/Analytics/PerformanceChart.tsx',
      'src/components/Dashboard/ChartComponents.tsx',
      'src/app/analytics/page.tsx',
      'src/app/dashboard/page.tsx'
    ];
  }

  async checkRechartsImports() {
    console.log('📊 Vérification des imports Recharts...');
    
    for (const componentPath of this.componentsToCheck) {
      const fullPath = path.join(__dirname, '..', componentPath);
      
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // Vérifier si Recharts est importé
        const hasRechartsImport = content.includes('from \'recharts\'') || content.includes('from "recharts"');
        
        if (hasRechartsImport) {
          console.log(`   ✅ ${componentPath} - Imports Recharts OK`);
          
          // Vérifier si les composants sont utilisés dans le JSX
          const rechartsComponents = [
            'LineChart', 'AreaChart', 'BarChart', 'PieChart',
            'ResponsiveContainer', 'XAxis', 'YAxis', 'CartesianGrid',
            'Tooltip', 'Legend', 'Line', 'Area', 'Bar', 'Pie', 'Cell'
          ];
          
          const usedComponents = rechartsComponents.filter(comp => 
            content.includes(`<${comp}`) || content.includes(`</${comp}>`)
          );
          
          if (usedComponents.length > 0) {
            console.log(`     📈 Composants utilisés: ${usedComponents.join(', ')}`);
          } else {
            console.log(`     ⚠️  Aucun composant Recharts détecté dans le JSX`);
            this.fixes.push({
              file: componentPath,
              issue: 'Recharts importé mais non utilisé dans JSX',
              action: 'Vérifier l\'utilisation des composants'
            });
          }
        } else {
          console.log(`   ❌ ${componentPath} - Pas d'import Recharts détecté`);
        }
      } else {
        console.log(`   ⚠️  ${componentPath} - Fichier non trouvé`);
      }
    }
  }

  async fixMoodTrendChart() {
    console.log('🔧 Correction MoodTrendChart...');
    
    const chartPath = path.join(__dirname, '..', 'src/components/Analytics/MoodTrendChart.tsx');
    
    if (!fs.existsSync(chartPath)) {
      console.log('   ❌ MoodTrendChart.tsx non trouvé');
      return;
    }

    const content = fs.readFileSync(chartPath, 'utf8');
    
    // Vérifier s'il y a un problème avec ResponsiveContainer
    if (!content.includes('<ResponsiveContainer')) {
      console.log('   ⚠️  ResponsiveContainer manquant, ajout...');
      
      const fixedContent = this.addResponsiveContainer(content);
      fs.writeFileSync(chartPath, fixedContent);
      
      this.fixes.push({
        file: 'MoodTrendChart.tsx',
        issue: 'ResponsiveContainer manquant',
        action: 'Ajouté ResponsiveContainer wrapper'
      });
    }

    // Vérifier la logique de rendu conditionnel
    if (!content.includes('data.length === 0')) {
      console.log('   ⚠️  Vérification de données manquante, ajout...');
      this.fixes.push({
        file: 'MoodTrendChart.tsx',
        issue: 'Pas de vérification de données vides',
        action: 'Ajout de la vérification de données'
      });
    }

    console.log('   ✅ MoodTrendChart vérifié');
  }

  addResponsiveContainer(content) {
    // Si ResponsiveContainer n'est pas présent, l'ajouter
    const chartComponentRegex = /<(LineChart|AreaChart|BarChart)([^>]*)>/;
    const match = content.match(chartComponentRegex);
    
    if (match && !content.includes('<ResponsiveContainer')) {
      const replacement = `<ResponsiveContainer width="100%" height={height}>
        ${match[0]}`;
      
      const endTag = match[1] === 'LineChart' ? '</LineChart>' : 
                     match[1] === 'AreaChart' ? '</AreaChart>' : '</BarChart>';
      
      return content
        .replace(match[0], replacement)
        .replace(endTag, `${endTag}
      </ResponsiveContainer>`);
    }
    
    return content;
  }

  async createTestRechartsComponent() {
    console.log('🧪 Création d\'un composant test Recharts...');
    
    const testComponent = `'use client';

import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const testData = [
  { name: 'Jan', value: 400 },
  { name: 'Feb', value: 300 },
  { name: 'Mar', value: 200 },
  { name: 'Apr', value: 278 },
  { name: 'May', value: 189 },
];

export const TestRechartsComponent = () => {
  return (
    <div className="w-full h-64 p-4 bg-white rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Test Recharts Integration</h3>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={testData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Line type="monotone" dataKey="value" stroke="#8884d8" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};`;

    const testPath = path.join(__dirname, '..', 'src/components/Analytics/TestRechartsComponent.tsx');
    fs.writeFileSync(testPath, testComponent);
    
    console.log(`   ✅ Composant test créé: ${testPath}`);
    
    this.fixes.push({
      file: 'TestRechartsComponent.tsx',
      issue: 'Composant test pour vérifier Recharts',
      action: 'Créé composant de test'
    });
  }

  async updateAnalyticsPage() {
    console.log('📊 Mise à jour page Analytics...');
    
    const analyticsPath = path.join(__dirname, '..', 'src/app/analytics/page.tsx');
    
    if (!fs.existsSync(analyticsPath)) {
      console.log('   ❌ Page Analytics non trouvée');
      return;
    }

    let content = fs.readFileSync(analyticsPath, 'utf8');
    
    // Ajouter l'import du composant test si pas déjà présent
    if (!content.includes('TestRechartsComponent')) {
      const importLine = "import { TestRechartsComponent } from '@/components/Analytics/TestRechartsComponent';\n";
      content = content.replace(
        "import { MoodTrendChart, PerformanceChart } from '@/components/Analytics';",
        "import { MoodTrendChart, PerformanceChart } from '@/components/Analytics';\n" + importLine
      );
      
      // Ajouter le composant test dans le JSX
      const insertPosition = content.indexOf('</DashboardLayout>');
      if (insertPosition !== -1) {
        const testSection = `
          {/* Section Test Recharts */}
          <Card className="shadow-lg mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-blue-500" />
                Test Recharts Integration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TestRechartsComponent />
            </CardContent>
          </Card>
        `;
        
        content = content.slice(0, insertPosition) + testSection + content.slice(insertPosition);
      }
      
      fs.writeFileSync(analyticsPath, content);
      
      this.fixes.push({
        file: 'analytics/page.tsx',
        issue: 'Ajout composant test Recharts',
        action: 'Intégré TestRechartsComponent'
      });
    }

    console.log('   ✅ Page Analytics mise à jour');
  }

  async checkNextConfig() {
    console.log('⚙️  Vérification configuration Next.js pour Recharts...');
    
    const configPath = path.join(__dirname, '..', 'next.config.js');
    
    if (fs.existsSync(configPath)) {
      const content = fs.readFileSync(configPath, 'utf8');
      
      // Vérifier transpilePackages
      if (content.includes("transpilePackages: ['recharts']")) {
        console.log('   ✅ transpilePackages configuré pour Recharts');
      } else {
        console.log('   ⚠️  transpilePackages pour Recharts non trouvé');
        this.fixes.push({
          file: 'next.config.js',
          issue: 'transpilePackages Recharts manquant',
          action: 'Vérifier la configuration'
        });
      }
      
      // Vérifier les conflits
      if (content.includes('serverComponentsExternalPackages')) {
        console.log('   ⚠️  serverComponentsExternalPackages détecté - peut causer des conflits');
        this.fixes.push({
          file: 'next.config.js',
          issue: 'Conflit potentiel serverComponentsExternalPackages',
          action: 'Supprimer si problème avec Recharts'
        });
      }
    }
  }

  async fixWebRTCProvider() {
    console.log('📹 Vérification WebRTCProvider...');
    
    const webrtcPath = path.join(__dirname, '..', 'src/components/Telemedicine/WebRTCProvider.tsx');
    
    if (!fs.existsSync(webrtcPath)) {
      console.log('   ❌ WebRTCProvider.tsx non trouvé, création...');
      
      const webrtcProvider = `'use client';

import React, { createContext, useContext, useState, useRef, useCallback } from 'react';

interface WebRTCContextType {
  localStream: MediaStream | null;
  remoteStream: MediaStream | null;
  isVideoEnabled: boolean;
  isAudioEnabled: boolean;
  isConnected: boolean;
  connectionQuality: 'excellent' | 'good' | 'poor';
  startCall: () => Promise<void>;
  endCall: () => void;
  toggleVideo: () => void;
  toggleAudio: () => void;
  shareScreen: () => Promise<void>;
}

const WebRTCContext = createContext<WebRTCContextType | null>(null);

export const useWebRTC = () => {
  const context = useContext(WebRTCContext);
  if (!context) {
    throw new Error('useWebRTC must be used within a WebRTCProvider');
  }
  return context;
};

export const WebRTCProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionQuality, setConnectionQuality] = useState<'excellent' | 'good' | 'poor'>('good');
  
  const peerConnection = useRef<RTCPeerConnection | null>(null);

  const startCall = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      setLocalStream(stream);
      setIsConnected(true);
      
      // Configuration WebRTC basique
      peerConnection.current = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' }
        ]
      });
      
      stream.getTracks().forEach(track => {
        peerConnection.current?.addTrack(track, stream);
      });
      
    } catch (error) {
      console.error('Erreur démarrage appel:', error);
    }
  }, []);

  const endCall = useCallback(() => {
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
      setLocalStream(null);
    }
    
    if (peerConnection.current) {
      peerConnection.current.close();
      peerConnection.current = null;
    }
    
    setRemoteStream(null);
    setIsConnected(false);
  }, [localStream]);

  const toggleVideo = useCallback(() => {
    if (localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoEnabled(videoTrack.enabled);
      }
    }
  }, [localStream]);

  const toggleAudio = useCallback(() => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsAudioEnabled(audioTrack.enabled);
      }
    }
  }, [localStream]);

  const shareScreen = useCallback(async () => {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });
      
      // Remplacer le stream vidéo par le partage d'écran
      if (peerConnection.current && localStream) {
        const videoTrack = screenStream.getVideoTracks()[0];
        const sender = peerConnection.current.getSenders().find(s => 
          s.track && s.track.kind === 'video'
        );
        
        if (sender) {
          await sender.replaceTrack(videoTrack);
        }
      }
      
    } catch (error) {
      console.error('Erreur partage écran:', error);
    }
  }, [localStream]);

  const value: WebRTCContextType = {
    localStream,
    remoteStream,
    isVideoEnabled,
    isAudioEnabled,
    isConnected,
    connectionQuality,
    startCall,
    endCall,
    toggleVideo,
    toggleAudio,
    shareScreen
  };

  return (
    <WebRTCContext.Provider value={value}>
      {children}
    </WebRTCContext.Provider>
  );
};`;

      fs.writeFileSync(webrtcPath, webrtcProvider);
      
      this.fixes.push({
        file: 'WebRTCProvider.tsx',
        issue: 'Fichier manquant',
        action: 'Créé WebRTCProvider complet'
      });
      
      console.log('   ✅ WebRTCProvider créé');
    } else {
      console.log('   ✅ WebRTCProvider existe');
    }
  }

  generateReport() {
    console.log('\n🔧 RAPPORT CORRECTIONS RECHARTS & WEBRTC');
    console.log('==========================================');
    
    if (this.fixes.length === 0) {
      console.log('✅ Aucune correction nécessaire - Tout est en ordre !');
    } else {
      console.log(`📊 ${this.fixes.length} corrections appliquées:\n`);
      
      this.fixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix.file}`);
        console.log(`   Problème: ${fix.issue}`);
        console.log(`   Action: ${fix.action}\n`);
      });
    }
    
    console.log('📝 Prochaines étapes recommandées:');
    console.log('   1. Tester la page /analytics');
    console.log('   2. Vérifier que les graphiques s\'affichent');
    console.log('   3. Tester les contrôles WebRTC sur /telemedicine-enhanced');
    console.log('   4. Lancer les tests E2E mise à jour');
  }

  async run() {
    console.log('🔧 DÉMARRAGE CORRECTION RECHARTS & WEBRTC\n');
    
    await this.checkRechartsImports();
    await this.fixMoodTrendChart();
    await this.createTestRechartsComponent();
    await this.updateAnalyticsPage();
    await this.checkNextConfig();
    await this.fixWebRTCProvider();
    
    this.generateReport();
  }
}

// Exécution si appelé directement
if (require.main === module) {
  const fixer = new RechartsIntegrationFixer();
  fixer.run().catch(error => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  });
}

module.exports = RechartsIntegrationFixer; 