
        const { chromium } = require('playwright');
        
        (async () => {
          const browser = await chromium.launch();
          const page = await browser.newPage();
          
          const metrics = {};
          
          // Mesurer FCP et LCP
          page.on('response', response => {
            const timing = response.timing();
            if (response.url().includes('localhost:3000')) {
              metrics.loadTime = timing.responseEnd;
            }
          });
          
          await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
          
          // Évaluer les performance metrics
          const performanceMetrics = await page.evaluate(() => {
            return new Promise((resolve) => {
              new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
                const lcpEntry = entries.find(entry => entry.entryType === 'largest-contentful-paint');
                
                resolve({
                  fcp: fcpEntry ? fcpEntry.startTime : null,
                  lcp: lcpEntry ? lcpEntry.startTime : null,
                  domLoad: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
                });
              }).observe({ entryTypes: ['paint', 'largest-contentful-paint'] });
              
              // Fallback après 5 secondes
              setTimeout(() => resolve({
                domLoad: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
              }), 5000);
            });
          });
          
          console.log(JSON.stringify(performanceMetrics));
          await browser.close();
        })();
      