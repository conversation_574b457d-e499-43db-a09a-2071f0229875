#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

class Phase2BPerformanceAnalyzer {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      coreWebVitals: {},
      bundleAnalysis: {},
      lighthouseScores: {},
      componentAnalysis: {},
      errors: []
    };
    
    this.config = {
      lighthouse: {
        url: 'http://localhost:3000',
        pages: [
          '/',
          '/dashboard-optimized',
          '/analytics', 
          '/telemedicine-enhanced'
        ]
      },
      performance: {
        targetFCP: 3200, // ms
        targetLCP: 4000, // ms
        targetBundle: 2.1, // MB
        targetLighthouse: 78
      }
    };
  }

  async analyzeBundle() {
    console.log('📦 Analyse de la taille du bundle...');
    
    try {
      // Build pour production
      console.log('   🔨 Build production...');
      execSync('npm run build', { 
        cwd: path.join(__dirname, '..'),
        stdio: 'pipe'
      });

      // Analyser les fichiers générés
      const buildDir = path.join(__dirname, '..', '.next');
      const staticDir = path.join(buildDir, 'static');
      
      if (fs.existsSync(staticDir)) {
        const bundleStats = this.calculateBundleSize(staticDir);
        this.results.bundleAnalysis = bundleStats;
        
        console.log(`   📊 Taille totale: ${bundleStats.totalSizeMB.toFixed(2)} MB`);
        console.log(`   📊 JS principal: ${bundleStats.jsMainSizeMB.toFixed(2)} MB`);
        console.log(`   📊 CSS: ${bundleStats.cssSizeMB.toFixed(2)} MB`);
        
        // Vérification par rapport aux objectifs
        if (bundleStats.totalSizeMB <= this.config.performance.targetBundle) {
          console.log(`   ✅ Objectif bundle atteint (< ${this.config.performance.targetBundle} MB)`);
        } else {
          console.log(`   ⚠️  Bundle trop volumineux (objectif: ${this.config.performance.targetBundle} MB)`);
        }
      }
      
    } catch (error) {
      console.error('   ❌ Erreur analyse bundle:', error.message);
      this.results.errors.push(`Bundle analysis: ${error.message}`);
    }
  }

  calculateBundleSize(dir) {
    const stats = {
      totalSize: 0,
      jsMainSize: 0,
      cssSize: 0,
      fileCount: 0
    };

    const analyzeDir = (currentDir) => {
      const files = fs.readdirSync(currentDir);
      
      files.forEach(file => {
        const filePath = path.join(currentDir, file);
        const fileStat = fs.statSync(filePath);
        
        if (fileStat.isDirectory()) {
          analyzeDir(filePath);
        } else {
          stats.totalSize += fileStat.size;
          stats.fileCount++;
          
          if (file.endsWith('.js') && (file.includes('main') || file.includes('index'))) {
            stats.jsMainSize += fileStat.size;
          } else if (file.endsWith('.css')) {
            stats.cssSize += fileStat.size;
          }
        }
      });
    };

    analyzeDir(dir);

    return {
      totalSizeMB: stats.totalSize / (1024 * 1024),
      jsMainSizeMB: stats.jsMainSize / (1024 * 1024),
      cssSizeMB: stats.cssSize / (1024 * 1024),
      fileCount: stats.fileCount
    };
  }

  async measureCoreWebVitals() {
    console.log('🚀 Mesure des Core Web Vitals...');
    
    try {
      // Script de mesure Core Web Vitals basique
      const webVitalsScript = `
        const { chromium } = require('playwright');
        
        (async () => {
          const browser = await chromium.launch();
          const page = await browser.newPage();
          
          const metrics = {};
          
          // Mesurer FCP et LCP
          page.on('response', response => {
            const timing = response.timing();
            if (response.url().includes('localhost:3000')) {
              metrics.loadTime = timing.responseEnd;
            }
          });
          
          await page.goto('${this.config.lighthouse.url}', { waitUntil: 'networkidle' });
          
          // Évaluer les performance metrics
          const performanceMetrics = await page.evaluate(() => {
            return new Promise((resolve) => {
              new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
                const lcpEntry = entries.find(entry => entry.entryType === 'largest-contentful-paint');
                
                resolve({
                  fcp: fcpEntry ? fcpEntry.startTime : null,
                  lcp: lcpEntry ? lcpEntry.startTime : null,
                  domLoad: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
                });
              }).observe({ entryTypes: ['paint', 'largest-contentful-paint'] });
              
              // Fallback après 5 secondes
              setTimeout(() => resolve({
                domLoad: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
              }), 5000);
            });
          });
          
          console.log(JSON.stringify(performanceMetrics));
          await browser.close();
        })();
      `;
      
      // Créer et exécuter le script temporaire
      const scriptPath = path.join(__dirname, 'temp-webvitals.js');
      fs.writeFileSync(scriptPath, webVitalsScript);
      
      const output = execSync(`node ${scriptPath}`, { 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8',
        timeout: 30000
      });
      
      const metrics = JSON.parse(output.trim());
      this.results.coreWebVitals = metrics;
      
      console.log(`   ⚡ DOM Load: ${metrics.domLoad}ms`);
      if (metrics.fcp) console.log(`   🎨 FCP: ${metrics.fcp.toFixed(0)}ms`);
      if (metrics.lcp) console.log(`   📏 LCP: ${metrics.lcp.toFixed(0)}ms`);
      
      // Cleanup
      fs.unlinkSync(scriptPath);
      
    } catch (error) {
      console.error('   ❌ Erreur mesure Web Vitals:', error.message);
      this.results.errors.push(`Core Web Vitals: ${error.message}`);
    }
  }

  async analyzeLighthouse() {
    console.log('🔍 Analyse Lighthouse...');
    
    try {
      // Installer lighthouse si nécessaire
      try {
        execSync('lighthouse --version', { stdio: 'pipe' });
      } catch {
        console.log('   📦 Installation de Lighthouse...');
        execSync('npm install -g lighthouse', { stdio: 'inherit' });
      }

      const lighthouseResults = {};
      
      for (const page of this.config.lighthouse.pages) {
        const url = `${this.config.lighthouse.url}${page}`;
        console.log(`   🔍 Analyse: ${url}`);
        
        try {
          const result = execSync(
            `lighthouse ${url} --only-categories=performance --output=json --quiet --chrome-flags="--headless --no-sandbox"`,
            { encoding: 'utf8', timeout: 60000 }
          );
          
          const lighthouseData = JSON.parse(result);
          const score = lighthouseData.lhr.categories.performance.score * 100;
          
          lighthouseResults[page] = {
            score: Math.round(score),
            fcp: lighthouseData.lhr.audits['first-contentful-paint']?.numericValue,
            lcp: lighthouseData.lhr.audits['largest-contentful-paint']?.numericValue,
            cls: lighthouseData.lhr.audits['cumulative-layout-shift']?.numericValue,
            tti: lighthouseData.lhr.audits['interactive']?.numericValue
          };
          
          console.log(`     📊 Score: ${Math.round(score)}/100`);
          
        } catch (pageError) {
          console.log(`     ⚠️  Erreur analyse ${page}: ${pageError.message}`);
          lighthouseResults[page] = { error: pageError.message };
        }
      }
      
      this.results.lighthouseScores = lighthouseResults;
      
    } catch (error) {
      console.error('   ❌ Erreur Lighthouse:', error.message);
      this.results.errors.push(`Lighthouse: ${error.message}`);
    }
  }

  async analyzeComponents() {
    console.log('🧩 Analyse des composants...');
    
    try {
      const componentsDir = path.join(__dirname, '..', 'src', 'components');
      const componentStats = this.analyzeComponentDirectory(componentsDir);
      
      this.results.componentAnalysis = componentStats;
      
      console.log(`   📊 Total composants: ${componentStats.totalComponents}`);
      console.log(`   📊 Nouveaux composants Phase 1: ${componentStats.newComponents.length}`);
      console.log(`   📊 Taille moyenne: ${componentStats.averageSize.toFixed(1)} KB`);
      
    } catch (error) {
      console.error('   ❌ Erreur analyse composants:', error.message);
      this.results.errors.push(`Component analysis: ${error.message}`);
    }
  }

  analyzeComponentDirectory(dir) {
    const stats = {
      totalComponents: 0,
      totalSize: 0,
      newComponents: [],
      largestComponents: []
    };

    const newComponentPaths = [
      'Analytics/MoodTrendChart.tsx',
      'Analytics/PerformanceChart.tsx', 
      'Telemedicine/VideoControls.tsx',
      'Telemedicine/WebRTCProvider.tsx'
    ];

    const analyzeDir = (currentDir) => {
      if (!fs.existsSync(currentDir)) return;
      
      const files = fs.readdirSync(currentDir);
      
      files.forEach(file => {
        const filePath = path.join(currentDir, file);
        const fileStat = fs.statSync(filePath);
        
        if (fileStat.isDirectory()) {
          analyzeDir(filePath);
        } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          const relativePath = path.relative(path.join(__dirname, '..', 'src', 'components'), filePath);
          const sizeKB = fileStat.size / 1024;
          
          stats.totalComponents++;
          stats.totalSize += sizeKB;
          
          // Vérifier si c'est un nouveau composant
          if (newComponentPaths.some(newPath => relativePath.includes(newPath))) {
            stats.newComponents.push({
              path: relativePath,
              sizeKB: sizeKB.toFixed(1)
            });
          }
          
          // Tracker les plus gros composants
          stats.largestComponents.push({
            path: relativePath,
            sizeKB: sizeKB.toFixed(1)
          });
        }
      });
    };

    analyzeDir(dir);
    
    // Trier par taille
    stats.largestComponents.sort((a, b) => parseFloat(b.sizeKB) - parseFloat(a.sizeKB));
    stats.largestComponents = stats.largestComponents.slice(0, 10);
    
    stats.averageSize = stats.totalComponents > 0 ? stats.totalSize / stats.totalComponents : 0;
    
    return stats;
  }

  generateReport() {
    console.log('\n📊 RAPPORT DE PERFORMANCE PHASE 2B');
    console.log('================================');
    
    // Bundle Analysis
    if (this.results.bundleAnalysis.totalSizeMB) {
      console.log('\n📦 ANALYSE BUNDLE:');
      console.log(`   Taille totale: ${this.results.bundleAnalysis.totalSizeMB.toFixed(2)} MB`);
      console.log(`   Objectif: ${this.config.performance.targetBundle} MB`);
      console.log(`   Status: ${this.results.bundleAnalysis.totalSizeMB <= this.config.performance.targetBundle ? '✅ OBJECTIF ATTEINT' : '⚠️ DÉPASSE'}`);
    }
    
    // Core Web Vitals
    if (this.results.coreWebVitals.domLoad) {
      console.log('\n⚡ CORE WEB VITALS:');
      console.log(`   DOM Load: ${this.results.coreWebVitals.domLoad}ms`);
      if (this.results.coreWebVitals.fcp) {
        console.log(`   FCP: ${this.results.coreWebVitals.fcp.toFixed(0)}ms (objectif: <${this.config.performance.targetFCP}ms)`);
      }
      if (this.results.coreWebVitals.lcp) {
        console.log(`   LCP: ${this.results.coreWebVitals.lcp.toFixed(0)}ms (objectif: <${this.config.performance.targetLCP}ms)`);
      }
    }
    
    // Lighthouse Scores
    if (Object.keys(this.results.lighthouseScores).length > 0) {
      console.log('\n🔍 SCORES LIGHTHOUSE:');
      Object.entries(this.results.lighthouseScores).forEach(([page, data]) => {
        if (data.score) {
          console.log(`   ${page}: ${data.score}/100 ${data.score >= this.config.performance.targetLighthouse ? '✅' : '⚠️'}`);
        }
      });
    }
    
    // Component Analysis
    if (this.results.componentAnalysis.totalComponents) {
      console.log('\n🧩 ANALYSE COMPOSANTS:');
      console.log(`   Total: ${this.results.componentAnalysis.totalComponents}`);
      console.log(`   Nouveaux Phase 1: ${this.results.componentAnalysis.newComponents.length}`);
      console.log(`   Taille moyenne: ${this.results.componentAnalysis.averageSize.toFixed(1)} KB`);
    }
    
    // Erreurs
    if (this.results.errors.length > 0) {
      console.log('\n❌ ERREURS:');
      this.results.errors.forEach(error => console.log(`   ${error}`));
    }
    
    // Score global
    const globalScore = this.calculateGlobalScore();
    console.log(`\n🎯 SCORE GLOBAL PHASE 2B: ${globalScore}%`);
    
    // Sauvegarde
    const reportPath = path.join(__dirname, '..', 'reports', `phase2b-performance-${Date.now()}.json`);
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`\n💾 Rapport sauvegardé: ${reportPath}`);
  }

  calculateGlobalScore() {
    let score = 0;
    let maxScore = 0;
    
    // Bundle size (25 points)
    if (this.results.bundleAnalysis.totalSizeMB) {
      maxScore += 25;
      if (this.results.bundleAnalysis.totalSizeMB <= this.config.performance.targetBundle) {
        score += 25;
      } else {
        score += Math.max(0, 25 - (this.results.bundleAnalysis.totalSizeMB - this.config.performance.targetBundle) * 10);
      }
    }
    
    // Lighthouse scores (50 points)
    const lighthouseScores = Object.values(this.results.lighthouseScores)
      .filter(data => data.score)
      .map(data => data.score);
    
    if (lighthouseScores.length > 0) {
      maxScore += 50;
      const avgLighthouse = lighthouseScores.reduce((a, b) => a + b, 0) / lighthouseScores.length;
      score += (avgLighthouse / 100) * 50;
    }
    
    // Component analysis (25 points)
    if (this.results.componentAnalysis.newComponents) {
      maxScore += 25;
      const newComponentsCount = this.results.componentAnalysis.newComponents.length;
      score += Math.min(25, newComponentsCount * 6); // 6 points par nouveau composant
    }
    
    return maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
  }

  async run() {
    console.log('🚀 DÉMARRAGE ANALYSE PERFORMANCE PHASE 2B\n');
    
    await this.analyzeBundle();
    await this.analyzeComponents();
    await this.measureCoreWebVitals();
    await this.analyzeLighthouse();
    
    this.generateReport();
  }
}

// Exécution si appelé directement
if (require.main === module) {
  const analyzer = new Phase2BPerformanceAnalyzer();
  analyzer.run().catch(error => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  });
}

module.exports = Phase2BPerformanceAnalyzer; 