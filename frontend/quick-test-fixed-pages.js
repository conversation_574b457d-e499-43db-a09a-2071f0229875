
console.log('🧪 Test rapide des pages corrigées...');

const { chromium } = require('playwright');

async function quickTest() {
  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();
  
  // Test billing
  await page.goto('http://localhost:3002/billing');
  await page.waitForTimeout(2000);
  const billingLoading = await page.locator('.animate-pulse').count();
  console.log(billingLoading === 0 ? '✅ Billing: Chargement OK' : '⚠️ Billing: Encore en chargement');
  
  // Test tools
  await page.goto('http://localhost:3002/tools');
  await page.waitForTimeout(2000);
  const toolsLoading = await page.locator('.animate-pulse').count();
  console.log(toolsLoading === 0 ? '✅ Tools: Chargement OK' : '⚠️ Tools: Encore en chargement');
  
  await browser.close();
}

quickTest().catch(console.error);
