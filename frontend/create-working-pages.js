#!/usr/bin/env node

/**
 * 🛠️ CRÉATION DE VERSIONS SIMPLIFIÉES DES PAGES
 * Remplace les pages problématiques par des versions fonctionnelles
 */

const fs = require('fs');

console.log('🛠️ Création de versions simplifiées des pages...');

// Version simplifiée de la page billing
const simpleBillingPage = `'use client';

import React from 'react';
import { 
  CreditCard,
  TrendingUp,
  DollarSign,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  Calendar,
  BarChart3
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function BillingPage() {
  // Données statiques pour démonstration
  const billingMetrics = {
    totalRevenue: { monthly: 45000, yearly: 540000, growth: 15.5 },
    outstandingAmount: 8500,
    paidInvoices: 142,
    pendingInvoices: 23,
    overdueInvoices: 4,
    averagePaymentDelay: 12,
    reimbursementPending: 15600,
    cashFlow: { incoming: 52000, outgoing: 38000, net: 14000 }
  };

  const recentInvoices = [
    { id: 'INV-001', patientName: 'Marie Dubois', amount: 120, status: 'paid', date: '2024-12-28' },
    { id: 'INV-002', patientName: 'Jean Martin', amount: 180, status: 'pending', date: '2024-12-27' },
    { id: 'INV-003', patientName: 'Sophie Leroy', amount: 95, status: 'overdue', date: '2024-12-20' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Facturation & Finance</h1>
          <p className="text-gray-600 mt-1">Gestion complète de votre activité financière</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Nouvelle Facture
          </Button>
          <Button className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Paiements
          </Button>
        </div>
      </div>

      {/* Métriques principales */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenus Mensuels</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{billingMetrics.totalRevenue.monthly.toLocaleString()}€</div>
            <p className="text-xs text-muted-foreground">
              +{billingMetrics.totalRevenue.growth}% par rapport au mois dernier
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Factures Payées</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{billingMetrics.paidInvoices}</div>
            <p className="text-xs text-muted-foreground">
              Ce mois-ci
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Attente</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{billingMetrics.pendingInvoices}</div>
            <p className="text-xs text-muted-foreground">
              Factures en attente
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Retard</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{billingMetrics.overdueInvoices}</div>
            <p className="text-xs text-muted-foreground">
              Nécessitent un suivi
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Factures récentes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Factures Récentes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentInvoices.map((invoice) => (
              <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">{invoice.patientName}</h4>
                  <p className="text-sm text-gray-500">{invoice.id} • {invoice.date}</p>
                </div>
                <div className="flex items-center gap-3">
                  <span className="font-bold">{invoice.amount}€</span>
                  <Badge className={getStatusColor(invoice.status)}>
                    {invoice.status === 'paid' ? 'Payée' : 
                     invoice.status === 'pending' ? 'En attente' : 'En retard'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Graphique de revenus */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Évolution des Revenus
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-end justify-center gap-4 bg-gray-50 rounded-lg p-4">
            <div className="text-center">
              <div className="bg-blue-500 w-12 h-32 rounded mb-2"></div>
              <span className="text-xs">Oct</span>
            </div>
            <div className="text-center">
              <div className="bg-blue-500 w-12 h-40 rounded mb-2"></div>
              <span className="text-xs">Nov</span>
            </div>
            <div className="text-center">
              <div className="bg-blue-600 w-12 h-48 rounded mb-2"></div>
              <span className="text-xs">Déc</span>
            </div>
          </div>
          <p className="text-center text-sm text-gray-600 mt-4">
            Revenus mensuels (en milliers d'euros)
          </p>
        </CardContent>
      </Card>
    </div>
  );
}`;

// Version simplifiée de la page tools
const simpleToolsPage = `'use client';

import React, { useState } from 'react';
import { 
  Mic,
  Calculator,
  Brain,
  BookOpen,
  Users,
  Package,
  Settings,
  Play,
  Pause,
  Square,
  FileText,
  Activity,
  Clock,
  Star
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function ToolsPage() {
  const [activeTab, setActiveTab] = useState('voice-notes');
  const [isRecording, setIsRecording] = useState(false);

  const voiceNotes = [
    {
      id: 'vn-001',
      patientName: 'Marie Dubois',
      duration: 180,
      timestamp: new Date().toISOString(),
      transcription: 'Patient présente des symptômes de fatigue chronique depuis 3 semaines.',
      confidence: 94,
      status: 'completed'
    },
    {
      id: 'vn-002',
      patientName: 'Jean Martin',
      duration: 240,
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      transcription: 'Suivi post-opératoire. Cicatrisation normale.',
      confidence: 96,
      status: 'completed'
    }
  ];

  const calculators = [
    {
      id: 'calc-bmi',
      name: 'Calcul IMC',
      category: 'Anthropométrie',
      usage: 95,
      accuracy: 100
    },
    {
      id: 'calc-dosage',
      name: 'Calcul de Dosage',
      category: 'Pharmacologie',
      usage: 87,
      accuracy: 98
    }
  ];

  const startRecording = () => {
    setIsRecording(true);
    setTimeout(() => setIsRecording(false), 3000);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'voice-notes':
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Mic className="h-5 w-5" />
                  Assistant de Notes Cliniques Vocal → Texte
                </span>
                <Button 
                  onClick={startRecording}
                  disabled={isRecording}
                  className={isRecording ? 'bg-red-600 hover:bg-red-700' : ''}
                >
                  {isRecording ? <Square className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                  {isRecording ? 'Arrêter' : 'Enregistrer'}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {voiceNotes.map((note) => (
                  <div key={note.id} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium">{note.patientName}</h4>
                        <p className="text-sm text-gray-500">
                          {new Date(note.timestamp).toLocaleString('fr-FR')} • {note.duration}s
                        </p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">
                        {note.confidence}% confiance
                      </Badge>
                    </div>
                    <p className="text-sm">{note.transcription}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );

      case 'calculators':
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Calculatrices Médicales
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {calculators.map((calc) => (
                  <div key={calc.id} className="p-4 border rounded-lg hover:bg-gray-50">
                    <h4 className="font-medium">{calc.name}</h4>
                    <p className="text-sm text-gray-600">{calc.category}</p>
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-sm">Usage: {calc.usage}%</span>
                      <Button size="sm">Utiliser</Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );

      default:
        return (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-gray-600">Contenu à venir pour cet onglet</p>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Outils & Productivité</h1>
          <p className="text-gray-600 mt-1">Suite complète d'outils professionnels</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Paramètres
          </Button>
        </div>
      </div>

      {/* Onglets */}
      <div className="flex gap-4 border-b">
        {[
          { id: 'voice-notes', label: 'Notes Vocales', icon: Mic },
          { id: 'calculators', label: 'Calculatrices', icon: Calculator },
          { id: 'ai-recommendations', label: 'IA Recommandations', icon: Brain },
          { id: 'research', label: 'Recherche', icon: BookOpen },
          { id: 'team', label: 'Équipe', icon: Users },
          { id: 'inventory', label: 'Inventaire', icon: Package }
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id)}
            className={\`flex items-center gap-2 px-4 py-2 border-b-2 transition-colors \${
              activeTab === id 
                ? 'border-blue-500 text-blue-600' 
                : 'border-transparent text-gray-600 hover:text-gray-800'
            }\`}
          >
            <Icon className="h-4 w-4" />
            {label}
          </button>
        ))}
      </div>

      {/* Contenu de l'onglet */}
      {renderTabContent()}
    </div>
  );
}`;

// Sauvegarder les nouvelles versions
fs.writeFileSync('src/app/billing/page.tsx', simpleBillingPage);
console.log('✅ Version simplifiée de billing créée');

fs.writeFileSync('src/app/tools/page.tsx', simpleToolsPage);
console.log('✅ Version simplifiée de tools créée');

console.log('\n🎉 Versions simplifiées créées !');
console.log('💡 Les pages devraient maintenant se charger correctement');
