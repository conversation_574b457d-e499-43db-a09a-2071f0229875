const fs = require('fs');
const path = require('path');

/**
 * 📊 REPORTER PERSONNALISÉ MINDFLOW PRO
 * Génère des rapports détaillés pour les tests Playwright
 */

class MindFlowProReporter {
  constructor(options = {}) {
    this.options = options;
    this.results = {
      startTime: new Date(),
      endTime: null,
      tests: [],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        duration: 0
      },
      performance: {
        avgLoadTime: 0,
        slowestPages: [],
        fastestPages: []
      },
      phases: {
        'Phase 1-2': { passed: 0, failed: 0, tests: [] },
        'Phase 3': { passed: 0, failed: 0, tests: [] },
        'Phase 4': { passed: 0, failed: 0, tests: [] },
        'Phase 8': { passed: 0, failed: 0, tests: [] },
        'Phase 9': { passed: 0, failed: 0, tests: [] }
      }
    };
  }

  onBegin(config, suite) {
    console.log('\n🚀 DÉMARRAGE TESTS MINDFLOW PRO');
    console.log('='.repeat(50));
    console.log(`📁 Répertoire de tests: ${config.testDir}`);
    console.log(`🌐 URL de base: ${config.use.baseURL}`);
    console.log(`⏱️ Timeout: ${config.timeout}ms`);
    console.log(`👥 Workers: ${config.workers}`);
    console.log('='.repeat(50));
    
    this.results.startTime = new Date();
  }

  onTestEnd(test, result) {
    const testResult = {
      title: test.title,
      file: test.location.file,
      duration: result.duration,
      status: result.status,
      error: result.error,
      phase: this.identifyPhase(test.title),
      screenshots: result.attachments?.filter(a => a.contentType === 'image/png') || [],
      videos: result.attachments?.filter(a => a.contentType === 'video/webm') || []
    };

    this.results.tests.push(testResult);
    this.results.summary.total++;

    // Mise à jour des statistiques par phase
    const phase = testResult.phase;
    if (this.results.phases[phase]) {
      this.results.phases[phase].tests.push(testResult);
      if (result.status === 'passed') {
        this.results.phases[phase].passed++;
        this.results.summary.passed++;
      } else if (result.status === 'failed') {
        this.results.phases[phase].failed++;
        this.results.summary.failed++;
      } else {
        this.results.summary.skipped++;
      }
    }

    // Affichage en temps réel
    const statusIcon = this.getStatusIcon(result.status);
    const durationText = `${Math.round(result.duration)}ms`;
    const phaseText = `[${phase}]`;
    
    console.log(`${statusIcon} ${phaseText.padEnd(12)} ${test.title.padEnd(50)} ${durationText}`);
    
    if (result.status === 'failed' && result.error) {
      console.log(`   ❌ Erreur: ${result.error.message}`);
    }
  }

  onEnd() {
    this.results.endTime = new Date();
    this.results.summary.duration = this.results.endTime - this.results.startTime;

    this.calculatePerformanceMetrics();
    this.generateConsoleReport();
    this.generateJSONReport();
    this.generateHTMLReport();
  }

  identifyPhase(testTitle) {
    const title = testTitle.toLowerCase();
    
    if (title.includes('ml analytics') || title.includes('phase 9')) {
      return 'Phase 9';
    } else if (title.includes('conformité') || title.includes('phase 3')) {
      return 'Phase 3';
    } else if (title.includes('intégrations') || title.includes('b2b') || title.includes('phase 4')) {
      return 'Phase 4';
    } else if (title.includes('performance') || title.includes('monitoring') || title.includes('phase 8')) {
      return 'Phase 8';
    } else {
      return 'Phase 1-2';
    }
  }

  getStatusIcon(status) {
    switch (status) {
      case 'passed': return '✅';
      case 'failed': return '❌';
      case 'skipped': return '⏭️';
      case 'timedOut': return '⏰';
      default: return '❓';
    }
  }

  calculatePerformanceMetrics() {
    const performanceTests = this.results.tests.filter(t => 
      t.title.toLowerCase().includes('performance') || 
      t.title.toLowerCase().includes('chargement')
    );

    if (performanceTests.length > 0) {
      this.results.performance.avgLoadTime = 
        performanceTests.reduce((sum, t) => sum + t.duration, 0) / performanceTests.length;

      // Top 3 tests les plus lents
      this.results.performance.slowestPages = performanceTests
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 3)
        .map(t => ({ title: t.title, duration: t.duration }));

      // Top 3 tests les plus rapides
      this.results.performance.fastestPages = performanceTests
        .sort((a, b) => a.duration - b.duration)
        .slice(0, 3)
        .map(t => ({ title: t.title, duration: t.duration }));
    }
  }

  generateConsoleReport() {
    console.log('\n📊 RAPPORT FINAL MINDFLOW PRO');
    console.log('='.repeat(70));
    
    // Résumé global
    const passRate = ((this.results.summary.passed / this.results.summary.total) * 100).toFixed(1);
    console.log(`📈 Score Global: ${passRate}% (${this.results.summary.passed}/${this.results.summary.total})`);
    console.log(`⏱️ Durée totale: ${Math.round(this.results.summary.duration / 1000)}s`);
    
    // Résultats par phase
    console.log('\n🎯 RÉSULTATS PAR PHASE:');
    Object.entries(this.results.phases).forEach(([phase, data]) => {
      if (data.tests.length > 0) {
        const phasePassRate = ((data.passed / data.tests.length) * 100).toFixed(1);
        const status = data.failed === 0 ? '✅' : '⚠️';
        console.log(`   ${status} ${phase.padEnd(12)}: ${phasePassRate}% (${data.passed}/${data.tests.length})`);
      }
    });

    // Performance
    if (this.results.performance.avgLoadTime > 0) {
      console.log('\n⚡ PERFORMANCE:');
      console.log(`   Temps moyen: ${Math.round(this.results.performance.avgLoadTime)}ms`);
      
      if (this.results.performance.slowestPages.length > 0) {
        console.log('   Pages les plus lentes:');
        this.results.performance.slowestPages.forEach(page => {
          console.log(`     ${page.title}: ${Math.round(page.duration)}ms`);
        });
      }
    }

    // Échecs
    const failedTests = this.results.tests.filter(t => t.status === 'failed');
    if (failedTests.length > 0) {
      console.log('\n❌ ÉCHECS:');
      failedTests.forEach(test => {
        console.log(`   ${test.title}`);
        if (test.error) {
          console.log(`     └─ ${test.error.message}`);
        }
      });
    }

    console.log('\n🎉 Tests terminés! Rapports disponibles dans test-results/');
    console.log('='.repeat(70));
  }

  generateJSONReport() {
    const reportPath = 'test-results/mindflow-report.json';
    
    // Assurer que le dossier existe
    const dir = path.dirname(reportPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    const report = {
      metadata: {
        project: 'MindFlow Pro',
        version: '2.0.0',
        testRunId: `run-${Date.now()}`,
        environment: process.env.NODE_ENV || 'test',
        timestamp: this.results.startTime.toISOString()
      },
      summary: this.results.summary,
      phases: this.results.phases,
      performance: this.results.performance,
      tests: this.results.tests.map(test => ({
        ...test,
        error: test.error ? test.error.message : null
      }))
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`📄 Rapport JSON généré: ${reportPath}`);
  }

  generateHTMLReport() {
    const reportPath = 'test-results/mindflow-report.html';
    
    const html = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport Tests MindFlow Pro</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .phases {
            padding: 30px;
        }
        .phase {
            background: #f8f9fa;
            margin: 15px 0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .phase.success {
            border-left-color: #28a745;
        }
        .phase.warning {
            border-left-color: #ffc107;
        }
        .phase.error {
            border-left-color: #dc3545;
        }
        .test-list {
            padding: 20px 30px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .test-item.passed {
            border-left: 3px solid #28a745;
        }
        .test-item.failed {
            border-left: 3px solid #dc3545;
        }
        .status-icon {
            font-size: 1.2em;
        }
        .duration {
            color: #6c757d;
            font-size: 0.9em;
        }
        .footer {
            background: #343a40;
            color: white;
            padding: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 MindFlow Pro</h1>
            <p>Rapport de Tests Automatisés</p>
            <p>${new Date(this.results.startTime).toLocaleString('fr-FR')}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${this.results.summary.total}</div>
                <div>Tests Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #28a745">${this.results.summary.passed}</div>
                <div>Réussis</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #dc3545">${this.results.summary.failed}</div>
                <div>Échecs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${((this.results.summary.passed / this.results.summary.total) * 100).toFixed(1)}%</div>
                <div>Taux de Réussite</div>
            </div>
        </div>
        
        <div class="phases">
            <h2>📊 Résultats par Phase</h2>
            ${Object.entries(this.results.phases).map(([phase, data]) => {
              if (data.tests.length === 0) return '';
              const passRate = ((data.passed / data.tests.length) * 100).toFixed(1);
              const statusClass = data.failed === 0 ? 'success' : 'warning';
              return `
                <div class="phase ${statusClass}">
                    <h3>${phase}</h3>
                    <p>Taux de réussite: ${passRate}% (${data.passed}/${data.tests.length})</p>
                    <div style="background: rgba(103,126,234,0.1); padding: 10px; border-radius: 5px; margin-top: 10px;">
                        <strong>Tests:</strong> ${data.tests.map(t => t.title).join(', ')}
                    </div>
                </div>
              `;
            }).join('')}
        </div>
        
        <div class="test-list">
            <h2>📋 Détail des Tests</h2>
            ${this.results.tests.map(test => `
                <div class="test-item ${test.status}">
                    <div>
                        <span class="status-icon">${this.getStatusIcon(test.status)}</span>
                        <strong>[${test.phase}]</strong> ${test.title}
                        ${test.error ? `<br><small style="color: #dc3545;">❌ ${test.error.message}</small>` : ''}
                    </div>
                    <div class="duration">${Math.round(test.duration)}ms</div>
                </div>
            `).join('')}
        </div>
        
        <div class="footer">
            <p>🚀 Tests générés automatiquement par MindFlow Pro Testing Suite</p>
            <p>Durée totale: ${Math.round(this.results.summary.duration / 1000)}s</p>
        </div>
    </div>
</body>
</html>`;

    fs.writeFileSync(reportPath, html);
    console.log(`🌐 Rapport HTML généré: ${reportPath}`);
  }
}

module.exports = MindFlowProReporter; 