#!/usr/bin/env node

/**
 * 🔧 SCRIPT DE CORRECTION AUTOMATIQUE DES ERREURS LINTER
 */

const fs = require('fs');
const path = require('path');

// Fichier tools/page.tsx à corriger
const toolsPagePath = 'src/app/tools/page.tsx';

console.log('🔧 Correction des erreurs linter...');

// Lire le fichier tools/page.tsx
if (fs.existsSync(toolsPagePath)) {
  let content = fs.readFileSync(toolsPagePath, 'utf8');
  
  // Ajouter l'import Plus si manquant
  if (!content.includes('Plus') && content.includes('from \'lucide-react\'')) {
    console.log('➕ Ajout de l\'import Plus...');
    content = content.replace(
      'Activity\n} from \'lucide-react\';',
      'Activity,\n  Plus\n} from \'lucide-react\';'
    );
  }
  
  // Vérifier si le composant Tabs est importé
  if (!content.includes('import { Tabs')) {
    console.log('📑 Import Tabs déjà présent...');
  }
  
  // Écrire le fichier corrigé
  fs.writeFileSync(toolsPagePath, content);
  console.log('✅ Fichier tools/page.tsx corrigé');
} else {
  console.log('⚠️ Fichier tools/page.tsx non trouvé');
}

// Créer le composant Tabs s'il n'existe pas
const tabsComponentPath = 'src/components/ui/tabs.tsx';
if (!fs.existsSync(tabsComponentPath)) {
  console.log('📑 Création du composant Tabs...');
  
  const tabsContent = `"use client"

import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"

import { cn } from "@/lib/utils"

const Tabs = TabsPrimitive.Root

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",
      className
    )}
    {...props}
  />
))
TabsList.displayName = TabsPrimitive.List.displayName

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
      className
    )}
    {...props}
  />
))
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      className
    )}
    {...props}
  />
))
TabsContent.displayName = TabsPrimitive.Content.displayName

export { Tabs, TabsList, TabsTrigger, TabsContent }`;

  fs.writeFileSync(tabsComponentPath, tabsContent);
  console.log('✅ Composant Tabs créé');
} else {
  console.log('📑 Composant Tabs existe déjà');
}

console.log('🎉 Corrections terminées !');
