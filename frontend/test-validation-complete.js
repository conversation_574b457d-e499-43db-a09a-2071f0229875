#!/usr/bin/env node

/**
 * 🚀 SCRIPT DE VALIDATION COMPLÈTE - MINDFLOW PRO
 * Tests automatisés de toutes les nouvelles fonctionnalités
 */

const { chromium } = require('playwright');

// Configuration
const BASE_URL = 'http://localhost:3002';
const TIMEOUT = 30000;

// Couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

const log = (msg, color = colors.reset) => console.log(`${color}${msg}${colors.reset}`);
const success = (msg) => log(`✅ ${msg}`, colors.green);
const error = (msg) => log(`❌ ${msg}`, colors.red);
const info = (msg) => log(`ℹ️  ${msg}`, colors.blue);
const warning = (msg) => log(`⚠️  ${msg}`, colors.yellow);

// Résultats des tests
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// Pages à tester avec leurs critères de validation
const PAGES_TO_TEST = [
  {
    url: '/',
    name: 'Page d\'Accueil',
    validators: ['MindFlow', 'Pro']
  },
  {
    url: '/dashboard',
    name: 'Dashboard Principal',
    validators: ['Dashboard', 'Tableau']
  },
  {
    url: '/appointments',
    name: 'Gestion Rendez-vous',
    validators: ['Rendez-vous', 'Appointments']
  },
  {
    url: '/compliance',
    name: 'Module Conformité (Phase 3)',
    validators: ['Conformité', 'HDS', 'ISO 27001', 'HIPAA']
  },
  {
    url: '/integrations-b2b',
    name: 'Intégrations B2B (Phase 4)',
    validators: ['Intégrations', 'B2B', 'Hôpitaux', 'HL7']
  },
  {
    url: '/billing',
    name: 'Module Billing (Module 6)',
    validators: ['Facturation', 'Billing', 'Revenus']
  },
  {
    url: '/tools',
    name: 'Tools & Productivity (Module 7)',
    validators: ['Outils', 'Tools', 'Notes Vocales']
  },
  {
    url: '/telemedicine-advanced',
    name: 'Télémédecine Avancée (Phase 2)',
    validators: ['Télémédecine', 'HD', '1080p']
  },
  {
    url: '/patients',
    name: 'Gestion Patients',
    validators: ['Patients', 'Gestion']
  },
  {
    url: '/professionals',
    name: 'Gestion Professionnels',
    validators: ['Professionnels', 'Inscription']
  },
  {
    url: '/test-healthcare-modules',
    name: 'Test Modules Healthcare',
    validators: ['Modules', 'Healthcare', 'Phase']
  }
];

async function validatePage(page, pageTest) {
  testResults.total++;
  
  try {
    info(`Test: ${pageTest.name} (${pageTest.url})`);
    
    // Aller à la page
    const response = await page.goto(`${BASE_URL}${pageTest.url}`, { 
      waitUntil: 'networkidle',
      timeout: TIMEOUT 
    });
    
    // Vérifier le statut HTTP
    if (response && response.status() >= 400) {
      throw new Error(`HTTP ${response.status()}`);
    }
    
    // Attendre que la page soit chargée
    await page.waitForLoadState('networkidle');
    
    // Récupérer le contenu de la page
    const pageContent = await page.textContent('body');
    const pageTitle = await page.title();
    
    // Vérifier qu'il n'y a pas d'erreurs évidentes
    if (pageContent.includes('404') || pageContent.includes('500') || pageContent.includes('Error')) {
      throw new Error('Page contient des erreurs');
    }
    
    // Valider les critères spécifiques
    let validatorsFound = 0;
    for (const validator of pageTest.validators) {
      if (pageContent.toLowerCase().includes(validator.toLowerCase()) || 
          pageTitle.toLowerCase().includes(validator.toLowerCase())) {
        validatorsFound++;
      }
    }
    
    // Au moins 50% des validateurs doivent être trouvés
    const validationRatio = validatorsFound / pageTest.validators.length;
    if (validationRatio < 0.5) {
      throw new Error(`Validation insuffisante: ${validatorsFound}/${pageTest.validators.length} critères trouvés`);
    }
    
    // Vérifier la présence d'éléments interactifs
    const buttons = await page.locator('button').count();
    const links = await page.locator('a').count();
    const inputs = await page.locator('input').count();
    
    if (buttons + links + inputs === 0) {
      warning(`Aucun élément interactif trouvé sur ${pageTest.name}`);
    }
    
    success(`${pageTest.name} - Validation réussie (${validatorsFound}/${pageTest.validators.length} critères)`);
    testResults.passed++;
    
    return {
      success: true,
      page: pageTest.name,
      validators: validatorsFound,
      total: pageTest.validators.length,
      interactive: buttons + links + inputs
    };
    
  } catch (err) {
    error(`${pageTest.name} - Échec: ${err.message}`);
    testResults.failed++;
    testResults.errors.push({
      page: pageTest.name,
      error: err.message,
      url: pageTest.url
    });
    
    return {
      success: false,
      page: pageTest.name,
      error: err.message
    };
  }
}

async function runValidationTests() {
  log('\n�� LANCEMENT TESTS DE VALIDATION MINDFLOW PRO', colors.cyan);
  log('=' .repeat(60), colors.cyan);
  
  const browser = await chromium.launch({ 
    headless: true,
    timeout: TIMEOUT 
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Configuration de la page
  page.setDefaultTimeout(TIMEOUT);
  
  const results = [];
  
  try {
    // Tester chaque page
    for (const pageTest of PAGES_TO_TEST) {
      const result = await validatePage(page, pageTest);
      results.push(result);
      
      // Petite pause entre les tests
      await page.waitForTimeout(1000);
    }
    
  } catch (globalError) {
    error(`Erreur globale: ${globalError.message}`);
  } finally {
    await browser.close();
  }
  
  // Afficher le résumé
  log('\n📊 RÉSUMÉ DES TESTS', colors.cyan);
  log('=' .repeat(40), colors.cyan);
  
  log(`Total des tests: ${testResults.total}`);
  success(`Tests réussis: ${testResults.passed}`);
  if (testResults.failed > 0) {
    error(`Tests échoués: ${testResults.failed}`);
  }
  
  const successRate = Math.round((testResults.passed / testResults.total) * 100);
  
  if (successRate >= 80) {
    success(`Taux de réussite: ${successRate}% - EXCELLENT ✨`);
  } else if (successRate >= 60) {
    warning(`Taux de réussite: ${successRate}% - BON 👍`);
  } else {
    error(`Taux de réussite: ${successRate}% - NÉCESSITE DES AMÉLIORATIONS 🔧`);
  }
  
  // Détails des erreurs
  if (testResults.errors.length > 0) {
    log('\n🔍 DÉTAILS DES ERREURS:', colors.yellow);
    testResults.errors.forEach((err, index) => {
      log(`${index + 1}. ${err.page}: ${err.error}`, colors.red);
    });
  }
  
  // Tests spécifiques aux nouvelles fonctionnalités
  log('\n🎯 VALIDATION FONCTIONNALITÉS SPÉCIFIQUES:', colors.cyan);
  
  const phaseResults = results.filter(r => 
    r.page.includes('Conformité') || 
    r.page.includes('B2B') || 
    r.page.includes('Billing') || 
    r.page.includes('Tools')
  );
  
  const phasesValidated = phaseResults.filter(r => r.success).length;
  const totalPhases = phaseResults.length;
  
  if (phasesValidated === totalPhases) {
    success(`Toutes les nouvelles phases validées (${phasesValidated}/${totalPhases}) 🎉`);
  } else {
    warning(`Phases validées: ${phasesValidated}/${totalPhases}`);
  }
  
  log('\n✨ VALIDATION TERMINÉE', colors.cyan);
  
  return {
    success: successRate >= 60,
    rate: successRate,
    results: results,
    errors: testResults.errors
  };
}

// Exécution si script appelé directement
if (require.main === module) {
  runValidationTests()
    .then((result) => {
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Erreur fatale:', error);
      process.exit(1);
    });
}

module.exports = { runValidationTests };
