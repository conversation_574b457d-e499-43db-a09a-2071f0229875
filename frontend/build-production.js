#!/usr/bin/env node

/**
 * Build alternatif pour éviter les erreurs de prerendering
 */

const { execSync } = require('child_process');

try {
    console.log('🔄 Build Next.js en mode SPA...');
    
    // Build en mode SPA pour éviter le prerendering
    execSync('npx next build', { 
        stdio: 'inherit',
        env: { 
            ...process.env, 
            NEXT_BUILD_MODE: 'spa',
            NODE_ENV: 'production'
        }
    });
    
    console.log('✅ Build terminé avec succès !');
    
} catch (error) {
    console.log('❌ Erreur de build:', error.message);
    
    // Tentative avec export statique
    try {
        console.log('🔄 Tentative avec export statique...');
        execSync('npx next build && npx next export', { stdio: 'inherit' });
        console.log('✅ Export statique réussi !');
    } catch (exportError) {
        console.log('❌ Export statique échoué également');
        process.exit(1);
    }
}
