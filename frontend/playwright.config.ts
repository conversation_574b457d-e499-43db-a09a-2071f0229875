import { defineConfig, devices } from '@playwright/test';

/**
 * 🚀 CONFIGURATION PLAYWRIGHT SIMPLIFIÉE - MINDFLOW PRO
 * Tests E2E pour validation complète de l'application
 */
export default defineConfig({
  testDir: './tests/e2e',
  
  /* Configuration globale */
  timeout: 120 * 1000, // 2 minutes par test
  fullyParallel: false, // Tests en série pour éviter conflits
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1,
  workers: process.env.CI ? 1 : 2,
  
  /* Reporters */
  reporter: [
    ['html', { 
      open: 'never',
      outputFolder: 'playwright-report'
    }],
    ['json', { 
      outputFile: 'test-results/results.json' 
    }],
    ['line']
  ],
  
  /* Configuration use globale */
  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 15 * 1000,
    navigationTimeout: 30 * 1000,
  },

  /* Configuration expect */
  expect: {
    timeout: 10 * 1000,
  },

  /* Dossier de sortie */
  outputDir: 'test-results/artifacts',

  /* Projets de test */
  projects: [
    // Tests Desktop Chrome - Principal
    {
      name: 'desktop-chrome',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 },
      },
    },

    // Tests Mobile Chrome
    {
      name: 'mobile-chrome',
      use: { 
        ...devices['Pixel 7'],
      },
    },

    // Tests Firefox
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        viewport: { width: 1280, height: 720 }
      },
    },
  ],
}); 