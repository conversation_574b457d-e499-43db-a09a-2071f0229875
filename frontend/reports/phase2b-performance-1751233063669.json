{"timestamp": "2025-06-29T21:33:50.785Z", "coreWebVitals": {}, "bundleAnalysis": {}, "lighthouseScores": {"/": {"error": "Command failed: lighthouse http://localhost:3000/ --only-categories=performance --output=json --quiet --chrome-flags=\"--headless --no-sandbox\"\nRuntime error encountered: Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 500)\n"}, "/dashboard-optimized": {"error": "Command failed: lighthouse http://localhost:3000/dashboard-optimized --only-categories=performance --output=json --quiet --chrome-flags=\"--headless --no-sandbox\"\nRuntime error encountered: Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 500)\n"}, "/analytics": {"error": "Command failed: lighthouse http://localhost:3000/analytics --only-categories=performance --output=json --quiet --chrome-flags=\"--headless --no-sandbox\"\nRuntime error encountered: Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 500)\n"}, "/telemedicine-enhanced": {"error": "Command failed: lighthouse http://localhost:3000/telemedicine-enhanced --only-categories=performance --output=json --quiet --chrome-flags=\"--headless --no-sandbox\"\nRuntime error encountered: Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 500)\n"}}, "componentAnalysis": {"totalComponents": 50, "totalSize": 225.**********, "newComponents": [{"path": "Analytics/MoodTrendChart.tsx", "sizeKB": "4.9"}, {"path": "Analytics/PerformanceChart.tsx", "sizeKB": "2.1"}, {"path": "Telemedicine/VideoControls.tsx", "sizeKB": "4.9"}, {"path": "Telemedicine/WebRTCProvider.tsx", "sizeKB": "5.3"}], "largestComponents": [{"path": "Dashboard/ChartComponents.tsx", "sizeKB": "29.2"}, {"path": "Dashboard/RealTimeNotifications.tsx", "sizeKB": "10.3"}, {"path": "Dashboard/SmartSuggestions.tsx", "sizeKB": "10.1"}, {"path": "Layout/Navigation.tsx", "sizeKB": "9.8"}, {"path": "Navigation.tsx", "sizeKB": "9.4"}, {"path": "Dashboard/RecentActivity.tsx", "sizeKB": "8.1"}, {"path": "ML/PatternDetection.tsx", "sizeKB": "8.1"}, {"path": "Dashboard/WellnessStats.tsx", "sizeKB": "7.9"}, {"path": "Layout/TopBar.tsx", "sizeKB": "7.9"}, {"path": "Layout/SimpleDashboardLayout.tsx", "sizeKB": "7.8"}], "averageSize": 4.51787109375}, "errors": ["Bundle analysis: Command failed: npm run build\nFailed to compile.\n\n./src/app/dashboard-optimized/page.tsx\nError: \n  \u001b[31mx\u001b[0m You are attempting to export \"metadata\" from a component marked with \"use client\", which is disallowed. Either remove the export, or the \"use client\" directive. Read more: https://nextjs.org/\n  \u001b[31m|\u001b[0m docs/getting-started/react-essentials#the-use-client-directive\n  \u001b[31m|\u001b[0m \n  \u001b[31m|\u001b[0m \n    ,-[\u001b[36;1;4m/Users/<USER>/Desktop/MindFlow Pro/frontend/src/app/dashboard-optimized/page.tsx\u001b[0m:23:1]\n \u001b[2m23\u001b[0m | } from 'lucide-react';\n \u001b[2m24\u001b[0m | \n \u001b[2m25\u001b[0m | // Métadonnées pour la page\n \u001b[2m26\u001b[0m | export const metadata = {\n    : \u001b[31;1m             ^^^^^^^^\u001b[0m\n \u001b[2m27\u001b[0m |   title: 'Dashboard Optimisé - MindFlow Pro',\n \u001b[2m28\u001b[0m |   description: 'Dashboard optimisé avec analytics avancés et performances améliorées'\n \u001b[2m29\u001b[0m | };\n    `----\n\nImport trace for requested module:\n./src/app/dashboard-optimized/page.tsx\n\n\n> Build failed because of webpack errors\n", "Core Web Vitals: Command failed: node /Users/<USER>/Desktop/MindFlow Pro/frontend/scripts/temp-webvitals.js\nnode:internal/modules/cjs/loader:1404\n  throw err;\n  ^\n\nError: Cannot find module '/Users/<USER>/Desktop/MindFlow'\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n    at node:internal/main/run_main_module:36:49 {\n  code: 'MODULE_NOT_FOUND',\n  requireStack: []\n}\n\nNode.js v22.15.1\n"]}