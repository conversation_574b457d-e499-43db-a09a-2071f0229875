{"name": "mindflow-pro-frontend", "version": "2.0.0", "description": "MindFlow Pro - AI-Powered Mental Health Platform Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:critical": "playwright test tests/e2e/critical-user-flows.spec.ts", "test:performance": "playwright test tests/e2e/performance-tests.spec.ts", "test:complete": "playwright test tests/e2e/mindflow-complete-validation.spec.ts", "test:mobile": "playwright test --project=mobile-chrome", "test:desktop": "playwright test --project=desktop-chrome", "test:all-browsers": "playwright test --project=desktop-chrome --project=firefox --project=mobile-chrome", "test:ci": "playwright test --reporter=json,html,github", "test:install": "playwright install chromium firefox", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@headlessui/react": "^1.7.17", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.1", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "@types/moment": "^2.13.0", "@types/node": "^20.12.0", "@types/react": "^18.3.0", "@types/react-big-calendar": "^1.8.0", "@types/react-dom": "^18.3.0", "@types/socket.io-client": "^3.0.0", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "axios": "^1.7.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.0.0", "date-fns": "^2.30.0", "embla-carousel-react": "^8.1.0", "framer-motion": "^10.16.5", "immer": "^10.1.1", "lottie-react": "^2.4.0", "lucide-react": "^0.294.0", "moment": "^2.30.0", "next": "14.0.4", "next-themes": "^0.3.0", "react": "^18.2.0", "react-big-calendar": "^1.12.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.2.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "sonner": "^1.3.1", "swr": "^2.2.5", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.4.0", "vaul": "^0.9.0", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@playwright/test": "^1.53.1", "@storybook/addon-essentials": "^8.1.0", "@storybook/addon-interactions": "^8.1.0", "@storybook/addon-links": "^8.1.0", "@storybook/blocks": "^8.1.0", "@storybook/nextjs": "^8.1.0", "@storybook/react": "^8.1.0", "@storybook/test": "^8.1.0", "@testing-library/jest-dom": "^6.4.0", "@testing-library/react": "^15.0.0", "@testing-library/user-event": "^14.5.0", "autoprefixer": "^10.4.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.0", "prettier": "^3.2.0", "prettier-plugin-tailwindcss": "^0.5.0", "storybook": "^8.1.0", "tailwindcss": "^3.4.0"}, "engines": {"node": ">=18.17.0", "npm": ">=9.0.0"}}