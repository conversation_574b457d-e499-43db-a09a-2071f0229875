/**
 * Script de diagnostic pour MindFlow Pro - Frontend
 * Vérifie l'état du frontend et les connexions avec le backend
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🔍 Diagnostic du Frontend MindFlow Pro en cours...');

// Vérifier si le serveur frontend est en cours d'exécution
function checkFrontendStatus() {
  return new Promise((resolve) => {
    const req = http.request({
      host: 'localhost',
      port: 5173, // Port par défaut de Vite
      path: '/',
      method: 'GET',
      timeout: 3000,
    }, (res) => {
      resolve({
        status: res.statusCode === 200 ? 'OK' : 'ERROR',
        statusCode: res.statusCode,
      });
    });

    req.on('error', (err) => {
      resolve({
        status: 'ERROR',
        error: err.message,
      });
    });

    req.end();
  });
}

// Vérifier la connexion avec le backend
function checkBackendConnection() {
  return new Promise((resolve) => {
    const req = http.request({
      host: 'localhost',
      port: 4000,
      path: '/api/v1/health',
      method: 'GET',
      timeout: 3000,
    }, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({
            status: res.statusCode === 200 ? 'OK' : 'ERROR',
            data: result,
          });
        } catch (e) {
          resolve({
            status: 'ERROR',
            error: 'Invalid JSON response',
          });
        }
      });
    });

    req.on('error', (err) => {
      resolve({
        status: 'ERROR',
        error: err.message,
      });
    });

    req.end();
  });
}

// Vérifier les fichiers de configuration
function checkConfigFiles() {
  const configFiles = [
    { path: path.join(__dirname, '.env.local'), required: false },
    { path: path.join(__dirname, 'package.json'), required: true },
    { path: path.join(__dirname, 'vite.config.ts'), required: true },
    { path: path.join(__dirname, 'tsconfig.json'), required: true },
    { path: path.join(__dirname, 'next.config.js'), required: true },
  ];

  return configFiles.map((file) => {
    const exists = fs.existsSync(file.path);
    return {
      file: file.path,
      exists,
      status: exists || !file.required ? 'OK' : 'ERROR',
    };
  });
}

// Vérifier le contenu du fichier .env.local
function checkEnvFile() {
  const envPath = path.join(__dirname, '.env.local');
  if (!fs.existsSync(envPath)) {
    return {
      status: 'ERROR',
      error: 'Fichier .env.local manquant',
    };
  }

  const content = fs.readFileSync(envPath, 'utf8');
  const requiredVars = [
    'NEXT_PUBLIC_API_URL',
    'NEXT_PUBLIC_WS_URL',
  ];

  const missingVars = [];
  for (const variable of requiredVars) {
    if (!content.includes(variable)) {
      missingVars.push(variable);
    }
  }

  return {
    status: missingVars.length === 0 ? 'OK' : 'ERROR',
    missingVars,
  };
}

// Exécuter tous les diagnostics
async function runDiagnostics() {
  console.log('🔄 Vérification du serveur frontend...');
  const frontendStatus = await checkFrontendStatus();
  
  console.log('🔄 Vérification de la connexion au backend...');
  const backendStatus = await checkBackendConnection();
  
  console.log('🔄 Vérification des fichiers de configuration...');
  const configStatus = checkConfigFiles();
  
  console.log('🔄 Vérification du fichier .env.local...');
  const envStatus = checkEnvFile();
  
  // Afficher les résultats
  console.log('\n📊 Résultats du diagnostic:');
  console.log('=======================');
  
  console.log(`\n🖥️  Statut du serveur frontend: ${frontendStatus.status === 'OK' ? '✅ OK' : '❌ ERREUR'}`);
  if (frontendStatus.status === 'ERROR') {
    console.log(`   Erreur: ${frontendStatus.error || 'Code ' + frontendStatus.statusCode || 'Inconnu'}`);
  }
  
  console.log(`\n🔌 Connexion au backend: ${backendStatus.status === 'OK' ? '✅ OK' : '❌ ERREUR'}`);
  if (backendStatus.status === 'OK') {
    console.log(`   Version: ${backendStatus.data.version || 'N/A'}`);
    console.log(`   Environnement: ${backendStatus.data.environment || 'N/A'}`);
  } else {
    console.log(`   Erreur: ${backendStatus.error || 'Inconnu'}`);
  }
  
  console.log('\n📁 Fichiers de configuration:');
  configStatus.forEach((file) => {
    console.log(`   ${file.exists ? '✅' : '❌'} ${path.basename(file.file)}`);
  });
  
  console.log(`\n🔧 Fichier .env.local: ${envStatus.status === 'OK' ? '✅ OK' : '❌ ERREUR'}`);
  if (envStatus.status === 'ERROR' && envStatus.missingVars) {
    console.log('   Variables manquantes:');
    envStatus.missingVars.forEach(v => {
      console.log(`     - ${v}`);
    });
  }
  
  console.log('\n🔧 Recommandations:');
  if (frontendStatus.status === 'ERROR') {
    console.log('   - Vérifiez que le serveur frontend est démarré');
    console.log('   - Exécutez "npm run dev" dans le dossier frontend');
  }
  
  if (backendStatus.status === 'ERROR') {
    console.log('   - Vérifiez que le serveur backend est démarré');
    console.log('   - Exécutez "node enhanced-server.js" dans le dossier backend');
  }
  
  const missingConfigFiles = configStatus.filter(f => !f.exists && f.status === 'ERROR');
  if (missingConfigFiles.length > 0) {
    console.log('   - Fichiers de configuration manquants:');
    missingConfigFiles.forEach(f => {
      console.log(`     - ${path.basename(f.file)}`);
    });
  }
  
  if (envStatus.status === 'ERROR') {
    if (envStatus.error === 'Fichier .env.local manquant') {
      console.log('   - Créez un fichier .env.local avec les variables suivantes:');
      console.log('     NEXT_PUBLIC_API_URL=http://localhost:4000/api/v1');
      console.log('     NEXT_PUBLIC_WS_URL=ws://localhost:4000');
    } else if (envStatus.missingVars && envStatus.missingVars.length > 0) {
      console.log('   - Ajoutez les variables manquantes au fichier .env.local');
    }
  }
}

runDiagnostics().catch(console.error); 