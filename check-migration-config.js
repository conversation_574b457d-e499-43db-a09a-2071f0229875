#!/usr/bin/env node

/**
 * 🔍 Vérification Configuration Migration Supabase
 * Script pour diagnostiquer l'état de la migration et du mode dual
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 VÉRIFICATION CONFIGURATION MIGRATION SUPABASE');
console.log('='.repeat(60));

// 1. Vérifier .env.local
const envLocalPath = path.join(__dirname, 'frontend', '.env.local');
console.log('\n📁 Fichier .env.local:');
if (fs.existsSync(envLocalPath)) {
  console.log('✅ Fichier .env.local existe');
  
  try {
    const envContent = fs.readFileSync(envLocalPath, 'utf8');
    const lines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    
    console.log(`📊 Variables trouvées: ${lines.length}`);
    
    const keyVariables = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY', 
      'SUPABASE_SERVICE_ROLE_KEY',
      'DUAL_DATABASE_MODE',
      'USE_SUPABASE_DATABASE'
    ];
    
    console.log('\n🔑 Variables clés:');
    keyVariables.forEach(varName => {
      const found = lines.some(line => line.startsWith(varName + '='));
      console.log(`  ${found ? '✅' : '❌'} ${varName}`);
    });
    
  } catch (error) {
    console.log('❌ Erreur lecture .env.local:', error.message);
  }
  
} else {
  console.log('❌ Fichier .env.local manquant');
}

// 2. Vérifier le template
const templatePath = path.join(__dirname, 'frontend', 'env-template.txt');
console.log('\n📋 Template .env:');
if (fs.existsSync(templatePath)) {
  console.log('✅ Template env-template.txt existe');
} else {
  console.log('❌ Template env-template.txt manquant');
}

// 3. Vérifier les composants critiques
console.log('\n🔧 Composants critiques:');
const criticalFiles = [
  'frontend/src/lib/supabase/client.ts',
  'frontend/src/lib/database/index.ts', 
  'frontend/src/lib/config/feature-flags.ts',
  'frontend/src/app/test-supabase/page.tsx'
];

criticalFiles.forEach(filePath => {
  const exists = fs.existsSync(path.join(__dirname, filePath));
  console.log(`  ${exists ? '✅' : '❌'} ${filePath}`);
});

// 4. Statut du serveur
console.log('\n🚀 Serveur Next.js:');
try {
  const packageJsonPath = path.join(__dirname, 'frontend', 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    console.log('✅ Package.json présent');
    
    // Vérifier si le serveur tourne
    console.log('💡 Pour tester la migration:');
    console.log('   1. cd frontend && npm run dev');
    console.log('   2. Ouvrir http://localhost:3000 ou http://localhost:3001');
    console.log('   3. Aller sur /test-supabase');
  }
} catch (error) {
  console.log('❌ Erreur vérification package.json');
}

// 5. Résumé final
console.log('\n📊 RÉSUMÉ:');
const hasEnv = fs.existsSync(envLocalPath);
const hasTemplate = fs.existsSync(templatePath);
const hasComponents = criticalFiles.every(f => fs.existsSync(path.join(__dirname, f)));

if (hasEnv && hasTemplate && hasComponents) {
  console.log('🎉 MIGRATION CONFIGURÉE !');
  console.log('   ➡️  Testez sur: http://localhost:3001/test-supabase');
} else {
  console.log('⚠️  .env.local manquant');
}

console.log('\n🔗 Prochaines étapes:');
console.log('   1. Activer mode dual: DUAL_DATABASE_MODE=true');
console.log('   2. Tester avec: /test-supabase');
console.log('   3. Migration progressive selon feature flags'); 