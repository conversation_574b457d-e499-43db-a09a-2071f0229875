#!/bin/bash

# 🚀 MINDFLOW PRO - LANCEMENT RAPIDE PHASES 5-6-7
# Script de démarrage automatisé pour les prochaines étapes stratégiques

echo "🚀 MINDFLOW PRO - SYSTÈME PHASES 5-6-7"
echo "======================================"
echo ""

# Rendre les scripts exécutables
chmod +x phase5-beta-testing.js
chmod +x phase6-production-deployment.js
chmod +x phase7-ai-expansion.js
chmod +x phases-dashboard.js

echo "✅ Scripts configurés et exécutables"
echo ""

# Afficher le dashboard principal
node phases-dashboard.js

echo ""
echo "🎯 COMMANDES DISPONIBLES:"
echo "========================"
echo "node phases-dashboard.js                    # Dashboard principal"
echo "node phases-dashboard.js 5                  # Lancer Phase 5"
echo "node phases-dashboard.js projections        # Projections économiques"
echo "node phases-dashboard.js timeline           # Timeline détaillée"
echo "node phases-dashboard.js report             # Rapport global"
echo ""
echo "node phase5-beta-testing.js                 # Phase 5 directe"
echo "node phase6-production-deployment.js        # Phase 6 directe"
echo "node phase7-ai-expansion.js                 # Phase 7 directe"
echo ""
echo "🎉 SYSTÈME PRÊT - CHOISISSEZ VOTRE PROCHAINE ACTION !"
