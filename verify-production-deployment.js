#!/usr/bin/env node

/**
 * 🔍 VÉRIFICATION DÉPLOIEMENT PRODUCTION - MINDFLOW PRO
 * Script de vérification post-déploiement pour Vercel
 */

const https = require('https');
const http = require('http');

// Configuration
const PRODUCTION_URL = 'https://mindflow-jv4wmqi61-anderson-archimedes-projects.vercel.app';
const TIMEOUT = 10000; // 10 secondes

// Pages à tester
const PAGES_TO_TEST = [
    '/',
    '/auth/login',
    '/auth/register', 
    '/test-phase4-supabase',
    '/inscription-simple',
    '/api/health/supabase'
];

console.log('🚀 VÉRIFICATION DÉPLOIEMENT PRODUCTION - MINDFLOW PRO\n');
console.log(`🌐 URL de production : ${PRODUCTION_URL}\n`);

/**
 * Teste une URL
 */
function testUrl(url) {
    return new Promise((resolve) => {
        const startTime = Date.now();
        
        const request = https.get(url, { timeout: TIMEOUT }, (res) => {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            resolve({
                url,
                status: res.statusCode,
                responseTime,
                headers: res.headers,
                success: res.statusCode < 500
            });
        });

        request.on('error', (err) => {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            resolve({
                url,
                status: 'ERROR',
                responseTime,
                error: err.message,
                success: false
            });
        });

        request.on('timeout', () => {
            request.destroy();
            resolve({
                url,
                status: 'TIMEOUT',
                responseTime: TIMEOUT,
                error: 'Request timeout',
                success: false
            });
        });
    });
}

/**
 * Formate le résultat d'un test
 */
function formatResult(result) {
    const statusIcon = result.success ? '✅' : '❌';
    const status = result.status === 'ERROR' || result.status === 'TIMEOUT' 
        ? result.status 
        : `HTTP ${result.status}`;
    
    console.log(`${statusIcon} ${result.url}`);
    console.log(`   Statut: ${status}`);
    console.log(`   Temps de réponse: ${result.responseTime}ms`);
    
    if (result.error) {
        console.log(`   Erreur: ${result.error}`);
    }
    
    if (result.headers) {
        console.log(`   Server: ${result.headers.server || 'N/A'}`);
        if (result.headers['x-vercel-id']) {
            console.log(`   Vercel ID: ${result.headers['x-vercel-id']}`);
        }
    }
    
    console.log('');
}

/**
 * Test principal
 */
async function runTests() {
    console.log('📋 Tests de connectivité...\n');
    
    const results = [];
    
    for (const page of PAGES_TO_TEST) {
        const url = `${PRODUCTION_URL}${page}`;
        console.log(`🔄 Test de ${page}...`);
        
        const result = await testUrl(url);
        results.push(result);
        formatResult(result);
    }
    
    // Résumé
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    const successRate = ((successCount / totalCount) * 100).toFixed(1);
    
    console.log('📊 RÉSUMÉ DES TESTS\n');
    console.log(`✅ Tests réussis : ${successCount}/${totalCount}`);
    console.log(`📈 Taux de réussite : ${successRate}%`);
    
    if (successRate >= 80) {
        console.log('🎉 Déploiement validé avec succès !');
    } else if (successRate >= 50) {
        console.log('⚠️  Déploiement partiellement fonctionnel');
    } else {
        console.log('❌ Problèmes détectés avec le déploiement');
    }
    
    // Analyse détaillée
    console.log('\n🔍 ANALYSE DÉTAILLÉE\n');
    
    const errors = results.filter(r => !r.success);
    if (errors.length > 0) {
        console.log('❌ Pages avec erreurs :');
        errors.forEach(error => {
            console.log(`   - ${error.url} : ${error.status} ${error.error || ''}`);
        });
        console.log('');
    }
    
    const avgResponseTime = results
        .filter(r => r.responseTime < TIMEOUT)
        .reduce((sum, r) => sum + r.responseTime, 0) / results.length;
    
    console.log(`⏱️  Temps de réponse moyen : ${avgResponseTime.toFixed(0)}ms`);
    
    // Recommandations
    console.log('\n💡 RECOMMANDATIONS\n');
    
    if (results.some(r => r.status === 401)) {
        console.log('🔒 Protection SSO détectée :');
        console.log('   - Accéder aux paramètres Vercel');
        console.log('   - Security > Password Protection > Disabled');
        console.log('   - URL : https://vercel.com/anderson-archimedes-projects/mindflow-pro/settings/security');
        console.log('');
    }
    
    if (avgResponseTime > 2000) {
        console.log('⚡ Optimisation performance :');
        console.log('   - Vérifier la région de déploiement');
        console.log('   - Optimiser les images et ressources');
        console.log('   - Utiliser le cache Vercel');
        console.log('');
    }
    
    console.log('🔗 Liens utiles :');
    console.log(`   - Application : ${PRODUCTION_URL}`);
    console.log('   - Dashboard Vercel : https://vercel.com/anderson-archimedes-projects/mindflow-pro');
    console.log('   - Logs : https://vercel.com/anderson-archimedes-projects/mindflow-pro/functions');
    
    console.log('\n🎯 Prochaines étapes :');
    console.log('   1. Désactiver la protection SSO si nécessaire');
    console.log('   2. Tester les fonctionnalités utilisateur');
    console.log('   3. Configurer un domaine personnalisé');
    console.log('   4. Mettre en place le monitoring');
}

// Exécution
runTests().catch(console.error); 