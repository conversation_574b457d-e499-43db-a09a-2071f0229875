#!/usr/bin/env node

const http = require('http');
const { spawn } = require('child_process');
const path = require('path');

console.log('🔧 Test des Corrections MindFlow Pro\n');

// Fonction pour tester une URL
function testUrl(url, expectedStatus = 200) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: url,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      console.log(`${res.statusCode === expectedStatus ? '✅' : '❌'} ${url} → ${res.statusCode}`);
      resolve(res.statusCode === expectedStatus);
    });

    req.on('error', (err) => {
      console.log(`❌ ${url} → Erreur: ${err.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`❌ ${url} → Timeout`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// URLs à tester
const urlsToTest = [
  '/',
  '/test-basic', 
  '/test-fix',
  '/dashboard-simple',
  '/diagnostic-env'
];

async function runTests() {
  console.log('📡 Test de connectivité des pages...\n');
  
  let successCount = 0;
  
  for (const url of urlsToTest) {
    const success = await testUrl(url);
    if (success) successCount++;
    await new Promise(resolve => setTimeout(resolve, 500)); // Pause entre les tests
  }
  
  console.log(`\n📊 Résultats: ${successCount}/${urlsToTest.length} pages fonctionnelles`);
  
  if (successCount === urlsToTest.length) {
    console.log('\n🎉 Toutes les pages de test fonctionnent !');
    console.log('✅ Les corrections ont été appliquées avec succès');
    console.log('\n🔗 Pages disponibles:');
    urlsToTest.forEach(url => {
      console.log(`   http://localhost:3000${url}`);
    });
  } else {
    console.log('\n⚠️  Certaines pages ne fonctionnent pas encore');
    console.log('🔍 Vérifiez que le serveur Next.js est démarré');
    console.log('💡 Commande: cd frontend && npm run dev');
  }
}

// Vérifier si le serveur est en cours d'exécution
async function checkServerStatus() {
  console.log('🔍 Vérification du serveur...');
  
  try {
    const success = await testUrl('/', 200);
    if (success) {
      console.log('✅ Serveur Next.js détecté sur localhost:3000\n');
      await runTests();
    } else {
      console.log('❌ Serveur non accessible');
      console.log('💡 Démarrez le serveur avec: cd frontend && npm run dev\n');
    }
  } catch (error) {
    console.log('❌ Erreur lors de la vérification du serveur');
    console.log('💡 Assurez-vous que le serveur est démarré\n');
  }
}

// Fonction principale
async function main() {
  console.log('🏁 Début des tests...\n');
  await checkServerStatus();
  
  console.log('\n📋 Résumé des corrections appliquées:');
  console.log('  ✅ Configuration Next.js simplifiée (next.config.js)');
  console.log('  ✅ Composants error.tsx et not-found.tsx créés');
  console.log('  ✅ Pages de test simplifiées créées');
  console.log('  ✅ Dashboard simplifié créé');
  console.log('  ✅ Variables d\'environnement configurées');
  
  console.log('\n🎯 Prochaines étapes:');
  console.log('  1. Vérifier que toutes les pages se chargent');
  console.log('  2. Tester la connectivité Supabase'); 
  console.log('  3. Valider la migration progressive');
  console.log('  4. Activer les feature flags Supabase');
  
  console.log('\n✨ Tests terminés !');
}

// Exécuter les tests
main().catch(console.error); 