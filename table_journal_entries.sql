
                CREATE TABLE IF NOT EXISTS journal_entries (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    mood_score INTEGER CHECK (mood_score >= 1 AND mood_score <= 10),
                    emotions TEXT[] DEFAULT '{}',
                    tags TEXT[] DEFAULT '{}',
                    is_favorite BOOLEAN DEFAULT FALSE,
                    privacy_level TEXT CHECK (privacy_level IN ('private', 'therapist', 'public')) DEFAULT 'private',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                