#!/usr/bin/env node

/**
 * 🚀 LANCEUR DÉPLOIEMENT COMPLET MINDFLOW PRO
 * 📅 28 Décembre 2024
 * 🎯 Un seul script pour tout déployer : Supabase + Vercel + Git
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 LANCEUR DÉPLOIEMENT COMPLET MINDFLOW PRO');
console.log('=' .repeat(60));
console.log('🎯 Déploiement automatique vers:');
console.log('   🗄️  Supabase (Base de données)');
console.log('   📦 Git (Code source)'); 
console.log('   🌐 Vercel (Production)');
console.log('=' .repeat(60));

async function deployComplete() {
    try {
        console.log('🔥 DÉMARRAGE DU DÉPLOIEMENT...\n');
        
        // Vérification que le script principal existe
        if (!fs.existsSync('setup-database-master.js')) {
            throw new Error('Script setup-database-master.js manquant');
        }
        
        // Installation des dépendances si nécessaire
        console.log('📦 Vérification des dépendances...');
        try {
            await installDependencies();
        } catch (error) {
            console.log('⚠️ Erreur dépendances (continuing...):', error.message);
        }
        
        // Lancement du déploiement principal
        console.log('🚀 Lancement du déploiement principal...\n');
        execSync('node setup-database-master.js', { 
            stdio: 'inherit',
            env: { ...process.env }
        });
        
        console.log('\n🎉 DÉPLOIEMENT COMPLET TERMINÉ AVEC SUCCÈS !');
        console.log('=' .repeat(60));
        console.log('✅ Votre application MindFlow Pro est maintenant en production');
        console.log('🔗 Vérifiez le fichier DEPLOYMENT_SUCCESS_REPORT.md pour les URLs');
        console.log('=' .repeat(60));
        
    } catch (error) {
        console.error('\n❌ ERREUR LORS DU DÉPLOIEMENT:');
        console.error(error.message);
        console.error('\n🔧 Solutions possibles:');
        console.error('1. Vérifiez votre connexion internet');
        console.error('2. Assurez-vous que Git est configuré');
        console.error('3. Vérifiez les clés Supabase');
        console.error('4. Relancez avec: node deploy-production-complet.js');
        process.exit(1);
    }
}

async function installDependencies() {
    console.log('   📥 Installation de node-fetch...');
    try {
        execSync('npm install node-fetch@2', { stdio: 'pipe' });
        console.log('   ✅ node-fetch installé');
    } catch (error) {
        // Continuez même si l'installation échoue
        console.log('   ⚠️ node-fetch déjà installé ou erreur (continuing...)');
    }
}

// Point d'entrée
if (require.main === module) {
    deployComplete().catch(error => {
        console.error('💥 ERREUR FATALE:', error.message);
        process.exit(1);
    });
}
